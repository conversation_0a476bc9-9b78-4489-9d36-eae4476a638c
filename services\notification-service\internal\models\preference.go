package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationPreference represents user notification preferences
type NotificationPreference struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null;uniqueIndex"`
	
	// Email preferences
	EmailEnabled           bool `json:"email_enabled" gorm:"default:true"`
	EmailMarketing         bool `json:"email_marketing" gorm:"default:true"`
	EmailTransactional     bool `json:"email_transactional" gorm:"default:true"`
	EmailReminders         bool `json:"email_reminders" gorm:"default:true"`
	EmailAnnouncements     bool `json:"email_announcements" gorm:"default:true"`
	
	// SMS preferences
	SMSEnabled             bool `json:"sms_enabled" gorm:"default:true"`
	SMSMarketing           bool `json:"sms_marketing" gorm:"default:false"`
	SMSTransactional       bool `json:"sms_transactional" gorm:"default:true"`
	SMSReminders           bool `json:"sms_reminders" gorm:"default:true"`
	SMSUrgent              bool `json:"sms_urgent" gorm:"default:true"`
	
	// In-app preferences
	InAppEnabled           bool `json:"in_app_enabled" gorm:"default:true"`
	InAppMarketing         bool `json:"in_app_marketing" gorm:"default:true"`
	InAppTransactional     bool `json:"in_app_transactional" gorm:"default:true"`
	InAppReminders         bool `json:"in_app_reminders" gorm:"default:true"`
	InAppAnnouncements     bool `json:"in_app_announcements" gorm:"default:true"`
	
	// Push notification preferences
	PushEnabled            bool `json:"push_enabled" gorm:"default:true"`
	PushMarketing          bool `json:"push_marketing" gorm:"default:false"`
	PushTransactional      bool `json:"push_transactional" gorm:"default:true"`
	PushReminders          bool `json:"push_reminders" gorm:"default:true"`
	PushUrgent             bool `json:"push_urgent" gorm:"default:true"`
	
	// Timing preferences
	QuietHoursEnabled      bool      `json:"quiet_hours_enabled" gorm:"default:false"`
	QuietHoursStart        time.Time `json:"quiet_hours_start" gorm:"type:time"`
	QuietHoursEnd          time.Time `json:"quiet_hours_end" gorm:"type:time"`
	Timezone               string    `json:"timezone" gorm:"default:'UTC'"`
	
	// Frequency preferences
	DigestEnabled          bool   `json:"digest_enabled" gorm:"default:false"`
	DigestFrequency        string `json:"digest_frequency" gorm:"default:'DAILY'"` // DAILY, WEEKLY, MONTHLY
	MaxNotificationsPerDay int    `json:"max_notifications_per_day" gorm:"default:50"`
	
	// Contact information
	Email       string `json:"email" gorm:"index"`
	PhoneNumber string `json:"phone_number" gorm:"index"`
	
	// Metadata
	Metadata map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Audit fields
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// BeforeCreate sets the ID if not provided
func (p *NotificationPreference) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

// IsNotificationAllowed checks if a notification type is allowed for the user
func (p *NotificationPreference) IsNotificationAllowed(notificationType NotificationType, category string) bool {
	switch notificationType {
	case NotificationTypeEmail:
		if !p.EmailEnabled {
			return false
		}
		return p.isEmailCategoryAllowed(category)
		
	case NotificationTypeSMS:
		if !p.SMSEnabled {
			return false
		}
		return p.isSMSCategoryAllowed(category)
		
	case NotificationTypeInApp:
		if !p.InAppEnabled {
			return false
		}
		return p.isInAppCategoryAllowed(category)
		
	case NotificationTypePush:
		if !p.PushEnabled {
			return false
		}
		return p.isPushCategoryAllowed(category)
		
	default:
		return false
	}
}

func (p *NotificationPreference) isEmailCategoryAllowed(category string) bool {
	switch category {
	case "marketing":
		return p.EmailMarketing
	case "transactional":
		return p.EmailTransactional
	case "reminders":
		return p.EmailReminders
	case "announcements":
		return p.EmailAnnouncements
	default:
		return p.EmailTransactional // Default to transactional
	}
}

func (p *NotificationPreference) isSMSCategoryAllowed(category string) bool {
	switch category {
	case "marketing":
		return p.SMSMarketing
	case "transactional":
		return p.SMSTransactional
	case "reminders":
		return p.SMSReminders
	case "urgent":
		return p.SMSUrgent
	default:
		return p.SMSTransactional // Default to transactional
	}
}

func (p *NotificationPreference) isInAppCategoryAllowed(category string) bool {
	switch category {
	case "marketing":
		return p.InAppMarketing
	case "transactional":
		return p.InAppTransactional
	case "reminders":
		return p.InAppReminders
	case "announcements":
		return p.InAppAnnouncements
	default:
		return p.InAppTransactional // Default to transactional
	}
}

func (p *NotificationPreference) isPushCategoryAllowed(category string) bool {
	switch category {
	case "marketing":
		return p.PushMarketing
	case "transactional":
		return p.PushTransactional
	case "reminders":
		return p.PushReminders
	case "urgent":
		return p.PushUrgent
	default:
		return p.PushTransactional // Default to transactional
	}
}

// CreatePreferenceRequest represents a request to create notification preferences
type CreatePreferenceRequest struct {
	UserID                 uuid.UUID              `json:"user_id" binding:"required"`
	EmailEnabled           *bool                  `json:"email_enabled"`
	SMSEnabled             *bool                  `json:"sms_enabled"`
	InAppEnabled           *bool                  `json:"in_app_enabled"`
	PushEnabled            *bool                  `json:"push_enabled"`
	Email                  string                 `json:"email"`
	PhoneNumber            string                 `json:"phone_number"`
	Timezone               string                 `json:"timezone"`
	Metadata               map[string]interface{} `json:"metadata"`
}

// ToPreference converts the request to a preference model
func (r *CreatePreferenceRequest) ToPreference() *NotificationPreference {
	preference := &NotificationPreference{
		UserID:      r.UserID,
		Email:       r.Email,
		PhoneNumber: r.PhoneNumber,
		Timezone:    r.Timezone,
		Metadata:    r.Metadata,
		
		// Default values
		EmailEnabled:           true,
		EmailTransactional:     true,
		EmailReminders:         true,
		SMSEnabled:             true,
		SMSTransactional:       true,
		SMSReminders:           true,
		InAppEnabled:           true,
		InAppTransactional:     true,
		InAppReminders:         true,
		PushEnabled:            true,
		PushTransactional:      true,
		PushReminders:          true,
		MaxNotificationsPerDay: 50,
		DigestFrequency:        "DAILY",
	}
	
	if r.EmailEnabled != nil {
		preference.EmailEnabled = *r.EmailEnabled
	}
	if r.SMSEnabled != nil {
		preference.SMSEnabled = *r.SMSEnabled
	}
	if r.InAppEnabled != nil {
		preference.InAppEnabled = *r.InAppEnabled
	}
	if r.PushEnabled != nil {
		preference.PushEnabled = *r.PushEnabled
	}
	
	if preference.Timezone == "" {
		preference.Timezone = "UTC"
	}
	
	return preference
}
