package repository

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/crm-microservices/shared/models"
)

// SessionRepository handles session data operations
type SessionRepository struct {
	db *gorm.DB
}

// NewSessionRepository creates a new session repository
func NewSessionRepository(db *gorm.DB) *SessionRepository {
	return &SessionRepository{db: db}
}

// Create creates a new session
func (r *SessionRepository) Create(session *models.Session) error {
	if err := r.db.Create(session).Error; err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}
	return nil
}

// GetByID retrieves a session by ID
func (r *SessionRepository) GetByID(id uuid.UUID) (*models.Session, error) {
	var session models.Session
	if err := r.db.Where("id = ?", id).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session by ID: %w", err)
	}
	return &session, nil
}

// GetByToken retrieves a session by token
func (r *SessionRepository) GetByToken(token string) (*models.Session, error) {
	var session models.Session
	if err := r.db.Where("token = ?", token).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session by token: %w", err)
	}
	return &session, nil
}

// GetByRefreshToken retrieves a session by refresh token
func (r *SessionRepository) GetByRefreshToken(refreshToken string) (*models.Session, error) {
	var session models.Session
	if err := r.db.Where("refresh_token = ?", refreshToken).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session by refresh token: %w", err)
	}
	return &session, nil
}

// GetByUserID retrieves all sessions for a user
func (r *SessionRepository) GetByUserID(userID uuid.UUID) ([]*models.Session, error) {
	var sessions []*models.Session
	if err := r.db.Where("user_id = ?", userID).Order("created_at DESC").Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get sessions by user ID: %w", err)
	}
	return sessions, nil
}

// GetActiveByUserID retrieves active sessions for a user
func (r *SessionRepository) GetActiveByUserID(userID uuid.UUID) ([]*models.Session, error) {
	var sessions []*models.Session
	if err := r.db.Where("user_id = ? AND is_active = ? AND expires_at > ?", userID, true, time.Now()).Order("created_at DESC").Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get active sessions by user ID: %w", err)
	}
	return sessions, nil
}

// Update updates a session
func (r *SessionRepository) Update(session *models.Session) error {
	if err := r.db.Save(session).Error; err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	return nil
}

// UpdateLastUsed updates the session's last used time
func (r *SessionRepository) UpdateLastUsed(id uuid.UUID) error {
	if err := r.db.Model(&models.Session{}).Where("id = ?", id).Update("last_used_at", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to update last used time: %w", err)
	}
	return nil
}

// Deactivate deactivates a session
func (r *SessionRepository) Deactivate(id uuid.UUID) error {
	if err := r.db.Model(&models.Session{}).Where("id = ?", id).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to deactivate session: %w", err)
	}
	return nil
}

// DeactivateByToken deactivates a session by token
func (r *SessionRepository) DeactivateByToken(token string) error {
	if err := r.db.Model(&models.Session{}).Where("token = ?", token).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to deactivate session by token: %w", err)
	}
	return nil
}

// DeactivateByRefreshToken deactivates a session by refresh token
func (r *SessionRepository) DeactivateByRefreshToken(refreshToken string) error {
	if err := r.db.Model(&models.Session{}).Where("refresh_token = ?", refreshToken).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to deactivate session by refresh token: %w", err)
	}
	return nil
}

// DeactivateAllByUserID deactivates all sessions for a user
func (r *SessionRepository) DeactivateAllByUserID(userID uuid.UUID) error {
	if err := r.db.Model(&models.Session{}).Where("user_id = ?", userID).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to deactivate all sessions for user: %w", err)
	}
	return nil
}

// Delete deletes a session
func (r *SessionRepository) Delete(id uuid.UUID) error {
	if err := r.db.Delete(&models.Session{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}
	return nil
}

// DeleteByToken deletes a session by token
func (r *SessionRepository) DeleteByToken(token string) error {
	if err := r.db.Where("token = ?", token).Delete(&models.Session{}).Error; err != nil {
		return fmt.Errorf("failed to delete session by token: %w", err)
	}
	return nil
}

// DeleteExpired deletes all expired sessions
func (r *SessionRepository) DeleteExpired() error {
	if err := r.db.Where("expires_at < ?", time.Now()).Delete(&models.Session{}).Error; err != nil {
		return fmt.Errorf("failed to delete expired sessions: %w", err)
	}
	return nil
}

// DeleteInactive deletes all inactive sessions
func (r *SessionRepository) DeleteInactive() error {
	if err := r.db.Where("is_active = ?", false).Delete(&models.Session{}).Error; err != nil {
		return fmt.Errorf("failed to delete inactive sessions: %w", err)
	}
	return nil
}

// DeleteOldSessions deletes sessions older than the specified duration
func (r *SessionRepository) DeleteOldSessions(olderThan time.Duration) error {
	cutoff := time.Now().Add(-olderThan)
	if err := r.db.Where("created_at < ?", cutoff).Delete(&models.Session{}).Error; err != nil {
		return fmt.Errorf("failed to delete old sessions: %w", err)
	}
	return nil
}

// CleanupSessions removes expired and inactive sessions
func (r *SessionRepository) CleanupSessions() error {
	// Delete expired sessions
	if err := r.DeleteExpired(); err != nil {
		return err
	}

	// Delete inactive sessions older than 7 days
	cutoff := time.Now().Add(-7 * 24 * time.Hour)
	if err := r.db.Where("is_active = ? AND created_at < ?", false, cutoff).Delete(&models.Session{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup inactive sessions: %w", err)
	}

	return nil
}

// GetSessionCount returns the total number of sessions
func (r *SessionRepository) GetSessionCount() (int64, error) {
	var count int64
	if err := r.db.Model(&models.Session{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to get session count: %w", err)
	}
	return count, nil
}

// GetActiveSessionCount returns the number of active sessions
func (r *SessionRepository) GetActiveSessionCount() (int64, error) {
	var count int64
	if err := r.db.Model(&models.Session{}).Where("is_active = ? AND expires_at > ?", true, time.Now()).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to get active session count: %w", err)
	}
	return count, nil
}

// GetSessionsByDateRange returns sessions created within a date range
func (r *SessionRepository) GetSessionsByDateRange(start, end time.Time) ([]*models.Session, error) {
	var sessions []*models.Session
	if err := r.db.Where("created_at BETWEEN ? AND ?", start, end).Order("created_at DESC").Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get sessions by date range: %w", err)
	}
	return sessions, nil
}

// IsValidSession checks if a session is valid (active and not expired)
func (r *SessionRepository) IsValidSession(token string) (bool, error) {
	var count int64
	if err := r.db.Model(&models.Session{}).Where("token = ? AND is_active = ? AND expires_at > ?", token, true, time.Now()).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check session validity: %w", err)
	}
	return count > 0, nil
}

// GetUserSessionCount returns the number of active sessions for a user
func (r *SessionRepository) GetUserSessionCount(userID uuid.UUID) (int64, error) {
	var count int64
	if err := r.db.Model(&models.Session{}).Where("user_id = ? AND is_active = ? AND expires_at > ?", userID, true, time.Now()).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to get user session count: %w", err)
	}
	return count, nil
}
