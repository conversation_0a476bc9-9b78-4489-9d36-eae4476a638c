package services

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"payment-service/internal/models"
	"payment-service/internal/repository"
	sharedModels "github.com/crm-microservices/shared/models"
)

// PaymentService handles payment business logic
type PaymentService interface {
	GetPayments(req *repository.PaymentListRequest) (*PaymentListResponse, error)
	GetPayment(id uuid.UUID) (*PaymentResponse, error)
	GetPaymentByInvoice(invoiceNumber string) (*PaymentResponse, error)
	CreatePayment(req *PaymentCreateRequest) (*PaymentResponse, error)
	UpdatePayment(id uuid.UUID, req *PaymentUpdateRequest) (*PaymentResponse, error)
	DeletePayment(id uuid.UUID) error
	ProcessPayment(id uuid.UUID, gatewayType string) (*PaymentResponse, error)
	RefundPayment(id uuid.UUID, req *RefundRequest) (*PaymentResponse, error)
	GetPaymentStats() (*repository.PaymentStats, error)
	GetRevenueStats(startDate, endDate time.Time) (*repository.RevenueStats, error)
}

type paymentService struct {
	paymentRepo     repository.PaymentRepository
	transactionRepo repository.TransactionRepository
}

// NewPaymentService creates a new payment service
func NewPaymentService(paymentRepo repository.PaymentRepository, transactionRepo repository.TransactionRepository) PaymentService {
	return &paymentService{
		paymentRepo:     paymentRepo,
		transactionRepo: transactionRepo,
	}
}

// PaymentListResponse represents the response for payment list
type PaymentListResponse struct {
	Payments   []*models.Payment           `json:"payments"`
	Pagination *sharedModels.PaginationResponse  `json:"pagination"`
}

// PaymentResponse represents the response for a single payment
type PaymentResponse struct {
	Payment *models.Payment `json:"payment"`
}

// PaymentCreateRequest represents the request to create a payment
type PaymentCreateRequest struct {
	Amount        float64                       `json:"amount" binding:"required,gt=0"`
	Currency      string                        `json:"currency" binding:"required,len=3"`
	Method        sharedModels.PaymentMethod    `json:"method" binding:"required"`
	StudentID     *uuid.UUID                    `json:"student_id"`
	CourseID      *uuid.UUID                    `json:"course_id"`
	EnrollmentID  *uuid.UUID                    `json:"enrollment_id"`
	Description   string                        `json:"description"`
	DueDate       *time.Time                    `json:"due_date"`
	GatewayType   string                        `json:"gateway_type" binding:"required"`
	Metadata      map[string]interface{}        `json:"metadata"`
}

// PaymentUpdateRequest represents the request to update a payment
type PaymentUpdateRequest struct {
	Status        *sharedModels.PaymentStatus  `json:"status"`
	Description   *string                `json:"description"`
	DueDate       *time.Time             `json:"due_date"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// RefundRequest represents the request to refund a payment
type RefundRequest struct {
	Amount   float64 `json:"amount" binding:"required,gt=0"`
	Reason   string  `json:"reason" binding:"required"`
	Metadata map[string]interface{} `json:"metadata"`
}

// GetPayments retrieves payments with pagination and filtering
func (s *paymentService) GetPayments(req *repository.PaymentListRequest) (*PaymentListResponse, error) {
	// Set default pagination if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	payments, total, err := s.paymentRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get payments: %w", err)
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &PaymentListResponse{
		Payments:   payments,
		Pagination: pagination,
	}, nil
}

// GetPayment retrieves a payment by ID
func (s *paymentService) GetPayment(id uuid.UUID) (*PaymentResponse, error) {
	payment, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	return &PaymentResponse{Payment: payment}, nil
}

// GetPaymentByInvoice retrieves a payment by invoice number
func (s *paymentService) GetPaymentByInvoice(invoiceNumber string) (*PaymentResponse, error) {
	payment, err := s.paymentRepo.GetByInvoiceNumber(invoiceNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment by invoice: %w", err)
	}

	return &PaymentResponse{Payment: payment}, nil
}

// CreatePayment creates a new payment
func (s *paymentService) CreatePayment(req *PaymentCreateRequest) (*PaymentResponse, error) {
	// Validate payment method
	if !req.Method.IsValid() {
		return nil, fmt.Errorf("invalid payment method: %s", req.Method)
	}

	// Create payment model
	payment := &models.Payment{
		Amount:       req.Amount,
		Currency:     req.Currency,
		Status:       sharedModels.PaymentPending,
		Method:       req.Method,
		StudentID:    req.StudentID,
		CourseID:     req.CourseID,
		EnrollmentID: req.EnrollmentID,
		Description:  req.Description,
		DueDate:      req.DueDate,
		GatewayType:  req.GatewayType,
	}

	// Set metadata if provided
	if req.Metadata != nil {
		// Convert metadata to JSON string
		// This would typically use json.Marshal in a real implementation
		payment.Metadata = "{}"
	}

	// Create payment in database
	if err := s.paymentRepo.Create(payment); err != nil {
		return nil, fmt.Errorf("failed to create payment: %w", err)
	}

	return &PaymentResponse{Payment: payment}, nil
}

// UpdatePayment updates a payment
func (s *paymentService) UpdatePayment(id uuid.UUID, req *PaymentUpdateRequest) (*PaymentResponse, error) {
	// Check if payment exists
	_, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("payment not found: %w", err)
	}

	// Prepare updates
	updates := make(map[string]interface{})

	if req.Status != nil {
		if !req.Status.IsValid() {
			return nil, fmt.Errorf("invalid payment status: %s", *req.Status)
		}
		updates["status"] = *req.Status
	}

	if req.Description != nil {
		updates["description"] = *req.Description
	}

	if req.DueDate != nil {
		updates["due_date"] = *req.DueDate
	}

	if req.Metadata != nil {
		// Convert metadata to JSON string
		updates["metadata"] = "{}"
	}

	// Update payment
	if err := s.paymentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update payment: %w", err)
	}

	// Get updated payment
	updatedPayment, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated payment: %w", err)
	}

	return &PaymentResponse{Payment: updatedPayment}, nil
}

// DeletePayment deletes a payment
func (s *paymentService) DeletePayment(id uuid.UUID) error {
	// Check if payment exists
	_, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("payment not found: %w", err)
	}

	// Check if payment can be deleted (only pending payments)
	payment, _ := s.paymentRepo.GetByID(id)
	if payment.Status != sharedModels.PaymentPending {
		return fmt.Errorf("cannot delete payment with status: %s", payment.Status)
	}

	// Delete payment
	if err := s.paymentRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete payment: %w", err)
	}

	return nil
}

// ProcessPayment processes a payment through the specified gateway
func (s *paymentService) ProcessPayment(id uuid.UUID, gatewayType string) (*PaymentResponse, error) {
	// Get payment
	payment, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("payment not found: %w", err)
	}

	// Check if payment can be processed
	if payment.Status != sharedModels.PaymentPending {
		return nil, fmt.Errorf("payment cannot be processed, current status: %s", payment.Status)
	}

	// Create transaction record
	transaction := &models.Transaction{
		PaymentID:   payment.ID,
		Amount:      payment.Amount,
		Currency:    payment.Currency,
		Status:      models.TransactionPending,
		Type:        models.TransactionPayment,
		GatewayType: gatewayType,
	}

	if err := s.transactionRepo.Create(transaction); err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// TODO: Implement actual gateway processing
	// For now, we'll simulate a successful payment
	transaction.Status = models.TransactionCompleted
	transaction.ProcessedAt = &[]time.Time{time.Now()}[0]
	
	if err := s.transactionRepo.Update(transaction.ID, map[string]interface{}{
		"status":       transaction.Status,
		"processed_at": transaction.ProcessedAt,
	}); err != nil {
		return nil, fmt.Errorf("failed to update transaction: %w", err)
	}

	// Update payment status
	if err := s.paymentRepo.Update(payment.ID, map[string]interface{}{
		"status":       sharedModels.PaymentCompleted,
		"payment_date": time.Now(),
	}); err != nil {
		return nil, fmt.Errorf("failed to update payment status: %w", err)
	}

	// Get updated payment
	updatedPayment, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated payment: %w", err)
	}

	return &PaymentResponse{Payment: updatedPayment}, nil
}

// RefundPayment creates a refund for a payment
func (s *paymentService) RefundPayment(id uuid.UUID, req *RefundRequest) (*PaymentResponse, error) {
	// Get payment
	payment, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("payment not found: %w", err)
	}

	// Check if payment can be refunded
	if payment.Status != sharedModels.PaymentCompleted {
		return nil, fmt.Errorf("payment cannot be refunded, current status: %s", payment.Status)
	}

	// Validate refund amount
	if req.Amount > payment.Amount {
		return nil, fmt.Errorf("refund amount cannot exceed payment amount")
	}

	// TODO: Implement actual refund processing
	// For now, we'll simulate a successful refund

	// Update payment status if fully refunded
	if req.Amount == payment.Amount {
		if err := s.paymentRepo.Update(payment.ID, map[string]interface{}{
			"status": sharedModels.PaymentRefunded,
		}); err != nil {
			return nil, fmt.Errorf("failed to update payment status: %w", err)
		}
	}

	// Get updated payment
	updatedPayment, err := s.paymentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated payment: %w", err)
	}

	return &PaymentResponse{Payment: updatedPayment}, nil
}

// GetPaymentStats retrieves payment statistics
func (s *paymentService) GetPaymentStats() (*repository.PaymentStats, error) {
	return s.paymentRepo.GetPaymentStats()
}

// GetRevenueStats retrieves revenue statistics for a period
func (s *paymentService) GetRevenueStats(startDate, endDate time.Time) (*repository.RevenueStats, error) {
	return s.paymentRepo.GetRevenueByPeriod(startDate, endDate)
}
