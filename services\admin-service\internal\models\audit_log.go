package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// AuditLog represents an audit log entry
type AuditLog struct {
	models.BaseModel
	UserID       uuid.UUID           `json:"user_id" gorm:"not null;index"`
	Action       models.AuditAction  `json:"action" gorm:"not null;index"`
	Resource     string              `json:"resource" gorm:"not null;index"` // e.g., "user", "payment", "course"
	ResourceID   *uuid.UUID          `json:"resource_id" gorm:"index"`       // ID of the affected resource
	Details      map[string]interface{} `json:"details" gorm:"type:jsonb"`   // Additional details about the action
	IPAddress    string              `json:"ip_address"`
	UserAgent    string              `json:"user_agent"`
	Timestamp    time.Time           `json:"timestamp" gorm:"autoCreateTime;index"`
	
	// Relationships
	User models.User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// AuditLogCreateRequest represents a request to create an audit log
type AuditLogCreateRequest struct {
	UserID     uuid.UUID              `json:"user_id" binding:"required"`
	Action     models.AuditAction     `json:"action" binding:"required"`
	Resource   string                 `json:"resource" binding:"required"`
	ResourceID *uuid.UUID             `json:"resource_id"`
	Details    map[string]interface{} `json:"details"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
}

// AuditLogResponse represents an audit log response
type AuditLogResponse struct {
	ID         uuid.UUID              `json:"id"`
	UserID     uuid.UUID              `json:"user_id"`
	UserName   string                 `json:"user_name"`
	UserEmail  string                 `json:"user_email"`
	Action     models.AuditAction     `json:"action"`
	Resource   string                 `json:"resource"`
	ResourceID *uuid.UUID             `json:"resource_id"`
	Details    map[string]interface{} `json:"details"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Timestamp  time.Time              `json:"timestamp"`
	CreatedAt  time.Time              `json:"created_at"`
}

// ToResponse converts an AuditLog to AuditLogResponse
func (a *AuditLog) ToResponse() *AuditLogResponse {
	response := &AuditLogResponse{
		ID:         a.ID,
		UserID:     a.UserID,
		Action:     a.Action,
		Resource:   a.Resource,
		ResourceID: a.ResourceID,
		Details:    a.Details,
		IPAddress:  a.IPAddress,
		UserAgent:  a.UserAgent,
		Timestamp:  a.Timestamp,
		CreatedAt:  a.CreatedAt,
	}

	// Include user information if available
	if a.User.ID != uuid.Nil {
		response.UserName = a.User.GetFullName()
		response.UserEmail = a.User.Email
	}

	return response
}

// AuditLogListRequest represents a request to list audit logs
type AuditLogListRequest struct {
	models.PaginationRequest
	UserID     *uuid.UUID         `json:"user_id" form:"user_id"`
	Action     *models.AuditAction `json:"action" form:"action"`
	Resource   *string            `json:"resource" form:"resource"`
	ResourceID *uuid.UUID         `json:"resource_id" form:"resource_id"`
	StartDate  *time.Time         `json:"start_date" form:"start_date"`
	EndDate    *time.Time         `json:"end_date" form:"end_date"`
	IPAddress  *string            `json:"ip_address" form:"ip_address"`
}

// AuditLogListResponse represents a response for audit log list
type AuditLogListResponse struct {
	AuditLogs  []*AuditLogResponse        `json:"audit_logs"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// AuditLogStats represents audit log statistics
type AuditLogStats struct {
	TotalLogs      int64                      `json:"total_logs"`
	LogsByAction   map[models.AuditAction]int64 `json:"logs_by_action"`
	LogsByResource map[string]int64           `json:"logs_by_resource"`
	LogsByUser     map[uuid.UUID]int64        `json:"logs_by_user"`
	RecentActivity []*AuditLogResponse        `json:"recent_activity"`
}
