package main

import (
	"log"
	"notification-service/internal/config"
	"notification-service/internal/database"
	"notification-service/internal/router"
	"notification-service/internal/services"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Verify database connection and tables exist (tables are pre-created)
	if err := database.VerifyDatabaseTables(db); err != nil {
		log.Fatalf("Failed to verify database tables: %v", err)
	}

	// Initialize services
	notificationService := services.NewNotificationService(db, cfg)
	emailService := services.NewEmailService(cfg)
	smsService := services.NewSMSService(cfg)
	templateService := services.NewTemplateService(db)
	queueService := services.NewQueueService(db, cfg)
	schedulerService := services.NewSchedulerService(db, cfg)
	analyticsService := services.NewAnalyticsService(db)

	// Start queue processor
	queueService.StartQueueProcessor(emailService, smsService)

	// Initialize router
	r := router.SetupRouter(notificationService, emailService, smsService, templateService, schedulerService, analyticsService, cfg)

	// Start server
	log.Printf("Starting Notification Service on port %s", cfg.Port)
	if err := r.Run(":" + cfg.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
