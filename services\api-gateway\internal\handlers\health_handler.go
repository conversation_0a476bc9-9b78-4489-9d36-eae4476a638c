package handlers

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/api-gateway/internal/registry"
	"github.com/crm-microservices/shared/utils"
)

// HealthHandler handles health check requests for the API Gateway
type HealthHandler struct {
	serviceRegistry *registry.ServiceRegistry
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(serviceRegistry *registry.ServiceRegistry) *HealthHandler {
	return &HealthHandler{
		serviceRegistry: serviceRegistry,
	}
}

// HealthCheck handles health check requests
// @Summary Health check
// @Description Check API Gateway health status and downstream services
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /health [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	checks := make(map[string]interface{})
	overallStatus := "healthy"

	// Check API Gateway itself
	checks["api_gateway"] = map[string]interface{}{
		"status": "healthy",
		"uptime": time.Since(time.Now().Add(-time.Hour)).String(), // Placeholder
	}

	// Check downstream services
	services := h.serviceRegistry.GetAllServices()
	serviceChecks := make(map[string]interface{})
	
	for name, service := range services {
		serviceStatus := "healthy"
		if !service.Healthy {
			serviceStatus = "unhealthy"
			overallStatus = "degraded" // Gateway is up but some services are down
		}
		
		serviceChecks[name] = map[string]interface{}{
			"status":     serviceStatus,
			"url":        service.URL,
			"last_check": service.LastCheck.Format(time.RFC3339),
		}
	}
	
	checks["services"] = serviceChecks

	// Add service registry stats
	checks["registry"] = h.serviceRegistry.GetServiceStats()

	// Add timestamp
	checks["timestamp"] = time.Now().UTC().Format(time.RFC3339)

	utils.HealthResponse(c, overallStatus, checks)
}

// ReadinessCheck handles readiness check requests
// @Summary Readiness check
// @Description Check if API Gateway is ready to serve requests
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /ready [get]
func (h *HealthHandler) ReadinessCheck(c *gin.Context) {
	checks := make(map[string]interface{})
	overallStatus := "ready"

	// Check if at least the auth service is available
	authService, err := h.serviceRegistry.GetService("auth")
	if err != nil || !authService.Healthy {
		checks["auth_service"] = map[string]interface{}{
			"status": "not_ready",
			"error":  "Auth service is not available",
		}
		overallStatus = "not_ready"
	} else {
		checks["auth_service"] = map[string]interface{}{
			"status": "ready",
		}
	}

	// Check service registry
	stats := h.serviceRegistry.GetServiceStats()
	if stats["healthy_services"].(int) == 0 {
		checks["service_registry"] = map[string]interface{}{
			"status": "not_ready",
			"error":  "No healthy services available",
		}
		overallStatus = "not_ready"
	} else {
		checks["service_registry"] = map[string]interface{}{
			"status": "ready",
			"stats":  stats,
		}
	}

	checks["timestamp"] = time.Now().UTC().Format(time.RFC3339)

	if overallStatus == "ready" {
		utils.SuccessResponse(c, map[string]interface{}{
			"status": overallStatus,
			"checks": checks,
		}, "API Gateway is ready")
	} else {
		c.JSON(503, map[string]interface{}{
			"success": false,
			"error":   "API Gateway is not ready",
			"data": map[string]interface{}{
				"status": overallStatus,
				"checks": checks,
			},
		})
	}
}

// LivenessCheck handles liveness check requests
// @Summary Liveness check
// @Description Check if API Gateway is alive
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /live [get]
func (h *HealthHandler) LivenessCheck(c *gin.Context) {
	// Simple liveness check - if we can respond, we're alive
	utils.SuccessResponse(c, map[string]interface{}{
		"status":    "alive",
		"service":   "api-gateway",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}, "API Gateway is alive")
}

// VersionCheck handles version requests
// @Summary Get service version
// @Description Get API Gateway version information
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /version [get]
func (h *HealthHandler) VersionCheck(c *gin.Context) {
	version := map[string]interface{}{
		"service":     "api-gateway",
		"version":     "1.0.0",
		"build_time":  "2024-01-01T00:00:00Z", // You could set this during build
		"git_commit":  "unknown",               // You could set this during build
		"go_version":  "1.21",
		"environment": "development", // You could get this from config
		"services":    h.getServiceVersions(),
	}

	utils.SuccessResponse(c, version, "Version information retrieved")
}

// getServiceVersions gets version information from downstream services
func (h *HealthHandler) getServiceVersions() map[string]interface{} {
	services := h.serviceRegistry.GetHealthyServices()
	versions := make(map[string]interface{})

	for name, service := range services {
		versions[name] = map[string]interface{}{
			"url":     service.URL,
			"healthy": service.Healthy,
		}
	}

	return versions
}

// MetricsCheck handles metrics requests
// @Summary Get service metrics
// @Description Get API Gateway metrics and downstream service stats
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /metrics [get]
func (h *HealthHandler) MetricsCheck(c *gin.Context) {
	metrics := make(map[string]interface{})

	// Service registry metrics
	metrics["service_registry"] = h.serviceRegistry.GetServiceStats()

	// Gateway metrics
	metrics["gateway"] = map[string]interface{}{
		"name":      "api-gateway",
		"version":   "1.0.0",
		"uptime":    time.Since(time.Now().Add(-time.Hour)).String(), // Placeholder
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// Service health summary
	services := h.serviceRegistry.GetAllServices()
	serviceMetrics := make(map[string]interface{})
	
	for name, service := range services {
		serviceMetrics[name] = map[string]interface{}{
			"healthy":    service.Healthy,
			"last_check": service.LastCheck.Format(time.RFC3339),
			"url":        service.URL,
		}
	}
	
	metrics["services"] = serviceMetrics

	utils.MetricsResponse(c, metrics)
}

// ServicesCheck returns information about registered services
// @Summary Get registered services
// @Description Get information about all registered downstream services
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /services [get]
func (h *HealthHandler) ServicesCheck(c *gin.Context) {
	services := h.serviceRegistry.GetAllServices()
	
	serviceInfo := make(map[string]interface{})
	for name, service := range services {
		serviceInfo[name] = map[string]interface{}{
			"name":       service.Name,
			"url":        service.URL,
			"healthy":    service.Healthy,
			"priority":   service.Priority,
			"timeout":    service.Timeout.String(),
			"last_check": service.LastCheck.Format(time.RFC3339),
		}
	}

	utils.SuccessResponse(c, map[string]interface{}{
		"services": serviceInfo,
		"stats":    h.serviceRegistry.GetServiceStats(),
	}, "Service information retrieved")
}
