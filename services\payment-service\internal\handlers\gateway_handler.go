package handlers

import (
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"payment-service/internal/services"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// GatewayHandler handles payment gateway-related HTTP requests
type GatewayHandler struct {
	gatewayService services.GatewayService
}

// NewGatewayHandler creates a new gateway handler
func NewGatewayHandler(gatewayService services.GatewayService) *GatewayHandler {
	return &GatewayHandler{
		gatewayService: gatewayService,
	}
}

// ProcessPayment handles POST /gateways/process-payment
func (h *GatewayHandler) ProcessPayment(c *gin.Context) {
	var req struct {
		PaymentID   uuid.UUID `json:"payment_id" binding:"required"`
		GatewayType string    `json:"gateway_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.gatewayService.ProcessPayment(req.PaymentID, req.GatewayType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response, "Payment processed successfully"))
}

// ProcessRefund handles POST /gateways/process-refund
func (h *GatewayHandler) ProcessRefund(c *gin.Context) {
	var req struct {
		RefundID    uuid.UUID `json:"refund_id" binding:"required"`
		GatewayType string    `json:"gateway_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.gatewayService.ProcessRefund(req.RefundID, req.GatewayType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response, "Refund processed successfully"))
}

// StripeWebhook handles POST /gateways/stripe/webhook
func (h *GatewayHandler) StripeWebhook(c *gin.Context) {
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Failed to read request body"))
		return
	}

	if err := h.gatewayService.HandleWebhook("stripe", payload); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "Stripe webhook processed successfully"))
}

// PayPalWebhook handles POST /gateways/paypal/webhook
func (h *GatewayHandler) PayPalWebhook(c *gin.Context) {
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Failed to read request body"))
		return
	}

	if err := h.gatewayService.HandleWebhook("paypal", payload); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "PayPal webhook processed successfully"))
}

// UzCardWebhook handles POST /gateways/uzcard/webhook
func (h *GatewayHandler) UzCardWebhook(c *gin.Context) {
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Failed to read request body"))
		return
	}

	if err := h.gatewayService.HandleWebhook("uzcard", payload); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "UzCard webhook processed successfully"))
}

// HumoWebhook handles POST /gateways/humo/webhook
func (h *GatewayHandler) HumoWebhook(c *gin.Context) {
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Failed to read request body"))
		return
	}

	if err := h.gatewayService.HandleWebhook("humo", payload); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "Humo webhook processed successfully"))
}

// PaymeWebhook handles POST /gateways/payme/webhook
func (h *GatewayHandler) PaymeWebhook(c *gin.Context) {
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Failed to read request body"))
		return
	}

	if err := h.gatewayService.HandleWebhook("payme", payload); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "Payme webhook processed successfully"))
}

// ClickWebhook handles POST /gateways/click/webhook
func (h *GatewayHandler) ClickWebhook(c *gin.Context) {
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Failed to read request body"))
		return
	}

	if err := h.gatewayService.HandleWebhook("click", payload); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "Click webhook processed successfully"))
}

// ValidatePaymentMethod handles POST /gateways/validate-payment-method
func (h *GatewayHandler) ValidatePaymentMethod(c *gin.Context) {
	var req struct {
		GatewayType string                 `json:"gateway_type" binding:"required"`
		MethodData  map[string]interface{} `json:"method_data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	validation, err := h.gatewayService.ValidatePaymentMethod(req.GatewayType, req.MethodData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(validation, "Payment method validated successfully"))
}

// GetSupportedGateways handles GET /gateways
func (h *GatewayHandler) GetSupportedGateways(c *gin.Context) {
	gateways := h.gatewayService.GetSupportedGateways()
	c.JSON(http.StatusOK, models.SuccessResponse(gateways, "Supported gateways retrieved successfully"))
}

// GetGatewayInfo handles GET /gateways/:type
func (h *GatewayHandler) GetGatewayInfo(c *gin.Context) {
	gatewayType := c.Param("type")
	if gatewayType == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Gateway type is required"))
		return
	}

	gateways := h.gatewayService.GetSupportedGateways()
	for _, gateway := range gateways {
		if gateway.Type == gatewayType {
			c.JSON(http.StatusOK, models.SuccessResponse(gateway, "Gateway information retrieved successfully"))
			return
		}
	}

	c.JSON(http.StatusNotFound, models.ErrorResponse("Gateway not found"))
}

// TestGatewayConnection handles POST /gateways/:type/test
func (h *GatewayHandler) TestGatewayConnection(c *gin.Context) {
	gatewayType := c.Param("type")
	if gatewayType == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Gateway type is required"))
		return
	}

	// TODO: Implement actual gateway connection testing
	// For now, we'll simulate a successful test
	testResult := map[string]interface{}{
		"gateway_type": gatewayType,
		"status":       "connected",
		"message":      "Gateway connection test successful",
		"tested_at":    "2024-01-01T00:00:00Z",
		"features": map[string]bool{
			"payments": true,
			"refunds":  true,
			"webhooks": true,
		},
	}

	c.JSON(http.StatusOK, models.SuccessResponse(testResult, "Gateway connection tested successfully"))
}

// GetGatewayStats handles GET /gateways/:type/stats
func (h *GatewayHandler) GetGatewayStats(c *gin.Context) {
	gatewayType := c.Param("type")
	if gatewayType == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Gateway type is required"))
		return
	}

	// TODO: Implement actual gateway statistics
	// For now, we'll return mock data
	stats := map[string]interface{}{
		"gateway_type":        gatewayType,
		"total_transactions":  0,
		"successful_payments": 0,
		"failed_payments":     0,
		"total_amount":        0.0,
		"total_fees":          0.0,
		"success_rate":        0.0,
		"average_amount":      0.0,
		"last_transaction":    nil,
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats, "Gateway statistics retrieved successfully"))
}
