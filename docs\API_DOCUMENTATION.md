# 📚 API Documentation - Go Docker Platform

Comprehensive API reference for all microservices in the CRM system.

## 🔐 Authentication

### JWT Token Authentication
All API requests (except authentication endpoints) require a valid JWT token:

```http
Authorization: Bearer <jwt_token>
```

### Token Structure
```json
{
  "sub": "user_id",
  "role": "ADMIN|MANAGER|TEACHER|RECEPTION|CASHIER|STUDENT|ACADEMIC_MANAGER",
  "exp": **********,
  "iat": **********,
  "iss": "crm-auth-service"
}
```

## 🌐 API Gateway (Port: 8080)

**Base URL**: https://crm-api-gateway.onrender.com

### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00Z",
  "services": {
    "auth-service": "healthy",
    "admin-service": "healthy",
    "staff-service": "healthy",
    "payment-service": "healthy",
    "notification-service": "healthy"
  }
}
```

### Route Patterns
- `/api/v1/auth/*` → Auth Service
- `/api/v1/admin/*` → Admin Service  
- `/api/v1/staff/*` → Staff Service
- `/api/v1/payments/*` → Payment Service
- `/api/v1/notifications/*` → Notification Service

## 🔑 Auth Service (Port: 8081)

**Base URL**: https://crm-auth-service.onrender.com

### Authentication Endpoints

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_here",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "ADMIN",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

#### Register
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "Jane",
  "last_name": "Smith",
  "role": "STUDENT"
}
```

#### Refresh Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "refresh_token_here"
}
```

#### Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

### User Management

#### Get Current User
```http
GET /api/v1/auth/me
Authorization: Bearer <token>
```

#### Update Profile
```http
PUT /api/v1/auth/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Updated Name",
  "last_name": "Updated Surname",
  "phone": "+************"
}
```

#### Change Password
```http
PUT /api/v1/auth/password
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_password": "oldpassword",
  "new_password": "newpassword123"
}
```

## 👨‍💼 Admin Service (Port: 8082)

**Base URL**: https://crm-admin-service.onrender.com
**Required Roles**: ADMIN, CASHIER

### User Management

#### List Users
```http
GET /api/v1/admin/users?page=1&limit=10&role=STUDENT
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "uuid",
        "email": "<EMAIL>",
        "role": "STUDENT",
        "first_name": "John",
        "last_name": "Doe",
        "created_at": "2025-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "total_pages": 10
    }
  }
}
```

#### Create User
```http
POST /api/v1/admin/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "New",
  "last_name": "User",
  "role": "TEACHER",
  "phone": "+************"
}
```

#### Update User
```http
PUT /api/v1/admin/users/{user_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Updated",
  "last_name": "Name",
  "role": "MANAGER",
  "is_active": true
}
```

#### Delete User
```http
DELETE /api/v1/admin/users/{user_id}
Authorization: Bearer <token>
```

### Analytics

#### Dashboard Stats
```http
GET /api/v1/admin/analytics/dashboard
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_users": 1250,
    "active_students": 890,
    "total_courses": 45,
    "monthly_revenue": 125000,
    "new_registrations_this_month": 67,
    "active_leads": 234
  }
}
```

#### User Analytics
```http
GET /api/v1/admin/analytics/users?period=month
Authorization: Bearer <token>
```

#### Revenue Analytics
```http
GET /api/v1/admin/analytics/revenue?start_date=2025-01-01&end_date=2025-01-31
Authorization: Bearer <token>
```

### Audit Logs

#### List Audit Logs
```http
GET /api/v1/admin/audit?page=1&limit=20&action=CREATE&user_id=uuid
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "action": "CREATE",
        "resource": "USER",
        "resource_id": "uuid",
        "details": {"field": "value"},
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0...",
        "created_at": "2025-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 500,
      "total_pages": 25
    }
  }
}
```

## 👥 Staff Service (Port: 8083)

**Base URL**: https://crm-staff-service.onrender.com
**Required Roles**: RECEPTION, TEACHER, MANAGER, ACADEMIC_MANAGER

### Lead Management

#### List Leads
```http
GET /api/v1/staff/leads?page=1&limit=10&status=NEW&source=WEBSITE
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "leads": [
      {
        "id": "uuid",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "phone": "+************",
        "status": "NEW",
        "source": "WEBSITE",
        "course_interest": "English",
        "notes": "Interested in evening classes",
        "assigned_to": "uuid",
        "created_at": "2025-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "total_pages": 15
    }
  }
}
```

#### Create Lead
```http
POST /api/v1/staff/leads
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "+************",
  "source": "PHONE_CALL",
  "course_interest": "IELTS",
  "notes": "Wants to start next month"
}
```

#### Update Lead
```http
PUT /api/v1/staff/leads/{lead_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "CONTACTED",
  "notes": "Called and scheduled meeting",
  "assigned_to": "staff_user_id"
}
```

#### Convert Lead to Student
```http
POST /api/v1/staff/leads/{lead_id}/convert
Authorization: Bearer <token>
Content-Type: application/json

{
  "course_id": "uuid",
  "start_date": "2025-02-01",
  "payment_plan": "MONTHLY"
}
```

### Student Management

#### List Students
```http
GET /api/v1/staff/students?page=1&limit=10&status=ACTIVE&course_id=uuid
Authorization: Bearer <token>
```

#### Create Student
```http
POST /api/v1/staff/students
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Student",
  "last_name": "Name",
  "email": "<EMAIL>",
  "phone": "+************",
  "date_of_birth": "1995-05-15",
  "address": "Tashkent, Uzbekistan",
  "emergency_contact": "+************"
}
```

#### Update Student
```http
PUT /api/v1/staff/students/{student_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "ACTIVE",
  "notes": "Excellent progress",
  "emergency_contact": "+************"
}
```

### Course Management

#### List Courses
```http
GET /api/v1/staff/courses?page=1&limit=10&status=ACTIVE&level=INTERMEDIATE
Authorization: Bearer <token>
```

#### Create Course
```http
POST /api/v1/staff/courses
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "IELTS Preparation",
  "description": "Intensive IELTS preparation course",
  "level": "INTERMEDIATE",
  "duration_weeks": 12,
  "price": 1200000,
  "max_students": 15,
  "schedule": "Mon, Wed, Fri 18:00-20:00"
}
```

### Enrollment Management

#### List Enrollments
```http
GET /api/v1/staff/enrollments?page=1&limit=10&status=ACTIVE
Authorization: Bearer <token>
```

#### Create Enrollment
```http
POST /api/v1/staff/enrollments
Authorization: Bearer <token>
Content-Type: application/json

{
  "student_id": "uuid",
  "course_id": "uuid",
  "start_date": "2025-02-01",
  "payment_plan": "MONTHLY",
  "discount_percentage": 10
}
```

## 💳 Payment Service (Port: 8084)

**Base URL**: https://crm-payment-service.onrender.com
**Required Roles**: ADMIN, CASHIER, RECEPTION

### Payment Processing

#### List Payments
```http
GET /api/v1/payments?page=1&limit=10&status=COMPLETED&gateway=UZCARD
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "id": "uuid",
        "student_id": "uuid",
        "enrollment_id": "uuid",
        "amount": 500000,
        "currency": "UZS",
        "gateway": "UZCARD",
        "status": "COMPLETED",
        "transaction_id": "txn_123456",
        "created_at": "2025-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 200,
      "total_pages": 20
    }
  }
}
```

#### Create Payment
```http
POST /api/v1/payments
Authorization: Bearer <token>
Content-Type: application/json

{
  "student_id": "uuid",
  "enrollment_id": "uuid",
  "amount": 500000,
  "currency": "UZS",
  "gateway": "UZCARD",
  "payment_method": "CARD",
  "description": "Monthly course payment"
}
```

#### Process Payment
```http
POST /api/v1/payments/{payment_id}/process
Authorization: Bearer <token>
Content-Type: application/json

{
  "card_number": "8600123456789012",
  "expiry_month": "12",
  "expiry_year": "25",
  "cvv": "123",
  "cardholder_name": "John Doe"
}
```

#### Refund Payment
```http
POST /api/v1/payments/{payment_id}/refund
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 250000,
  "reason": "Course cancellation"
}
```

### Payment Analytics

#### Payment Statistics
```http
GET /api/v1/payments/analytics/stats?period=month
Authorization: Bearer <token>
```

#### Gateway Performance
```http
GET /api/v1/payments/analytics/gateways
Authorization: Bearer <token>
```

## 📧 Notification Service (Port: 8085)

**Base URL**: https://crm-notification-service.onrender.com
**Required Roles**: All authenticated users

### Send Notifications

#### Send Email
```http
POST /api/v1/notifications/email
Authorization: Bearer <token>
Content-Type: application/json

{
  "to": ["<EMAIL>"],
  "subject": "Welcome to CRM System",
  "template": "welcome",
  "data": {
    "first_name": "John",
    "course_name": "English"
  }
}
```

#### Send SMS
```http
POST /api/v1/notifications/sms
Authorization: Bearer <token>
Content-Type: application/json

{
  "to": ["+************"],
  "message": "Your class starts tomorrow at 18:00",
  "template": "class_reminder"
}
```

#### Send Bulk Notification
```http
POST /api/v1/notifications/bulk
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "EMAIL",
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "subject": "Important Announcement",
  "template": "announcement",
  "data": {
    "title": "Holiday Schedule",
    "message": "Classes will be suspended during holidays"
  }
}
```

### Notification History

#### List Notifications
```http
GET /api/v1/notifications?page=1&limit=10&type=EMAIL&status=SENT
Authorization: Bearer <token>
```

#### Get Notification Details
```http
GET /api/v1/notifications/{notification_id}
Authorization: Bearer <token>
```

### Templates

#### List Templates
```http
GET /api/v1/notifications/templates?type=EMAIL
Authorization: Bearer <token>
```

#### Create Template
```http
POST /api/v1/notifications/templates
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "course_completion",
  "type": "EMAIL",
  "subject": "Congratulations on Course Completion!",
  "body": "Dear {{.first_name}}, you have successfully completed {{.course_name}}.",
  "variables": ["first_name", "course_name"]
}
```

## 📊 Common Response Formats

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

### Pagination Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "total_pages": 10,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## 🔧 HTTP Status Codes

- **200 OK**: Successful GET, PUT requests
- **201 Created**: Successful POST requests
- **204 No Content**: Successful DELETE requests
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server errors

## 🚀 Rate Limiting

- **Rate Limit**: 100 requests per minute per IP
- **Headers**: 
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

## 🔐 Security Headers

All responses include security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`

---

**API Documentation complete! 🎉**

For testing, use the provided Postman collection or curl commands with your authentication token.
