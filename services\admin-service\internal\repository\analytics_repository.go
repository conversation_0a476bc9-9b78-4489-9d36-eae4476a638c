package repository

import (
	"fmt"
	"time"

	"admin-service/internal/models"
	"gorm.io/gorm"
	sharedModels "github.com/crm-microservices/shared/models"
)

// AnalyticsRepository handles analytics data operations
type AnalyticsRepository interface {
	GetDashboardAnalytics() (*models.DashboardAnalytics, error)
	GetFinancialReport(startDate, endDate time.Time) (*models.FinancialReport, error)
	GetUserAnalytics(startDate, endDate time.Time) (*models.UserAnalytics, error)
	GetRevenueChart(startDate, endDate time.Time, groupBy string) ([]*models.RevenueDataPoint, error)
	GetStudentChart(startDate, endDate time.Time, groupBy string) ([]*models.StudentDataPoint, error)
	GetSystemMetrics() (*models.SystemMetrics, error)
}

type analyticsRepository struct {
	db *gorm.DB
}

// NewAnalyticsRepository creates a new analytics repository
func NewAnalyticsRepository(db *gorm.DB) AnalyticsRepository {
	return &analyticsRepository{db: db}
}

// GetDashboardAnalytics retrieves dashboard analytics
func (r *analyticsRepository) GetDashboardAnalytics() (*models.DashboardAnalytics, error) {
	analytics := &models.DashboardAnalytics{}

	// Note: Since we don't have student/payment tables yet, we'll use placeholder data
	// These will be updated when staff and payment services are implemented

	// Total students (placeholder - will come from staff service)
	analytics.TotalStudents = 0
	analytics.ActiveStudents = 0

	// Revenue data (placeholder - will come from payment service)
	analytics.TotalRevenue = 0.0
	analytics.MonthlyRevenue = 0.0

	// Conversion rate (placeholder)
	analytics.ConversionRate = 0.0

	// Teacher count (from users with TEACHER role)
	if err := r.db.Model(&sharedModels.User{}).Where("role = ?", sharedModels.RoleTeacher).Count(&analytics.TeacherCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count teachers: %w", err)
	}

	// Group count (placeholder - will come from staff service)
	analytics.GroupCount = 0

	// Recent payments (placeholder)
	analytics.RecentPayments = []*models.RecentPayment{}

	// User stats
	userStats, err := r.getUserStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get user stats: %w", err)
	}
	analytics.UserStats = userStats

	// Revenue chart (placeholder)
	analytics.RevenueChart = []*models.RevenueDataPoint{}

	// Student chart (placeholder)
	analytics.StudentChart = []*models.StudentDataPoint{}

	return analytics, nil
}

// getUserStats retrieves user statistics for dashboard
func (r *analyticsRepository) getUserStats() (*models.UserStats, error) {
	stats := &models.UserStats{
		UsersByRole: make(map[sharedModels.UserRole]int64),
	}

	// Total users
	if err := r.db.Model(&sharedModels.User{}).Count(&stats.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}

	// Users by status
	if err := r.db.Model(&sharedModels.User{}).Where("status = ?", sharedModels.StatusActive).Count(&stats.ActiveUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}

	if err := r.db.Model(&sharedModels.User{}).Where("status = ?", sharedModels.StatusInactive).Count(&stats.InactiveUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count inactive users: %w", err)
	}

	if err := r.db.Model(&sharedModels.User{}).Where("status = ?", sharedModels.StatusSuspended).Count(&stats.SuspendedUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count suspended users: %w", err)
	}

	// Users by role
	roles := []sharedModels.UserRole{
		sharedModels.RoleAdmin, sharedModels.RoleCashier, sharedModels.RoleReception,
		sharedModels.RoleTeacher, sharedModels.RoleManager, sharedModels.RoleAcademicManager,
	}

	for _, role := range roles {
		var count int64
		if err := r.db.Model(&sharedModels.User{}).Where("role = ?", role).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count users by role %s: %w", role, err)
		}
		stats.UsersByRole[role] = count
	}

	// New users today
	if err := r.db.Model(&sharedModels.User{}).Where("DATE(created_at) = CURRENT_DATE").Count(&stats.NewUsersToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count new users today: %w", err)
	}

	// New users this week
	if err := r.db.Model(&sharedModels.User{}).Where("created_at >= DATE_TRUNC('week', CURRENT_DATE)").Count(&stats.NewUsersThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count new users this week: %w", err)
	}

	// Last login stats
	lastLoginStats := &models.LastLoginStats{}

	// Users logged in today
	if err := r.db.Model(&sharedModels.User{}).Where("DATE(last_login_at) = CURRENT_DATE").Count(&lastLoginStats.LoggedInToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count users logged in today: %w", err)
	}

	// Users logged in this week
	if err := r.db.Model(&sharedModels.User{}).Where("last_login_at >= DATE_TRUNC('week', CURRENT_DATE)").Count(&lastLoginStats.LoggedInThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count users logged in this week: %w", err)
	}

	// Users logged in this month
	if err := r.db.Model(&sharedModels.User{}).Where("last_login_at >= DATE_TRUNC('month', CURRENT_DATE)").Count(&lastLoginStats.LoggedInThisMonth).Error; err != nil {
		return nil, fmt.Errorf("failed to count users logged in this month: %w", err)
	}

	// Users never logged in
	if err := r.db.Model(&sharedModels.User{}).Where("last_login_at IS NULL").Count(&lastLoginStats.NeverLoggedIn).Error; err != nil {
		return nil, fmt.Errorf("failed to count users never logged in: %w", err)
	}

	stats.LastLoginStats = lastLoginStats

	return stats, nil
}

// GetFinancialReport retrieves financial report (placeholder for now)
func (r *analyticsRepository) GetFinancialReport(startDate, endDate time.Time) (*models.FinancialReport, error) {
	report := &models.FinancialReport{
		StartDate:        startDate,
		EndDate:          endDate,
		TotalRevenue:     0.0,
		TotalPayments:    0,
		AveragePayment:   0.0,
		RevenueByMethod:  make(map[sharedModels.PaymentMethod]float64),
		PaymentsByMethod: make(map[sharedModels.PaymentMethod]int64),
		RevenueByStatus:  make(map[sharedModels.PaymentStatus]float64),
		PaymentsByStatus: make(map[sharedModels.PaymentStatus]int64),
		DailyRevenue:     []*models.RevenueDataPoint{},
		TopPayments:      []*models.RecentPayment{},
		RefundedAmount:   0.0,
		PendingAmount:    0.0,
	}

	// TODO: Implement actual financial data queries when payment service is available
	return report, nil
}

// GetUserAnalytics retrieves user analytics
func (r *analyticsRepository) GetUserAnalytics(startDate, endDate time.Time) (*models.UserAnalytics, error) {
	analytics := &models.UserAnalytics{
		UsersByRole:   make(map[sharedModels.UserRole]int64),
		UsersByStatus: make(map[sharedModels.UserStatus]int64),
	}

	// Total users
	if err := r.db.Model(&sharedModels.User{}).Count(&analytics.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}

	// Users by role
	roles := []sharedModels.UserRole{
		sharedModels.RoleAdmin, sharedModels.RoleCashier, sharedModels.RoleReception,
		sharedModels.RoleTeacher, sharedModels.RoleManager, sharedModels.RoleAcademicManager,
	}

	for _, role := range roles {
		var count int64
		if err := r.db.Model(&sharedModels.User{}).Where("role = ?", role).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count users by role %s: %w", role, err)
		}
		analytics.UsersByRole[role] = count
	}

	// Users by status
	statuses := []sharedModels.UserStatus{
		sharedModels.StatusActive, sharedModels.StatusInactive, sharedModels.StatusSuspended,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&sharedModels.User{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count users by status %s: %w", status, err)
		}
		analytics.UsersByStatus[status] = count
	}

	// Registration trend (placeholder)
	analytics.RegistrationTrend = []*models.UserRegistrationPoint{}

	// Login activity (placeholder)
	analytics.LoginActivity = []*models.UserLoginPoint{}

	// Most active users (placeholder)
	analytics.MostActiveUsers = []*models.UserActivitySummary{}

	// Recent registrations
	var recentUsers []*sharedModels.User
	if err := r.db.Order("created_at DESC").Limit(10).Find(&recentUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent registrations: %w", err)
	}

	analytics.RecentRegistrations = make([]*sharedModels.UserResponse, len(recentUsers))
	for i, user := range recentUsers {
		analytics.RecentRegistrations[i] = user.ToResponse()
	}

	return analytics, nil
}

// GetRevenueChart retrieves revenue chart data (placeholder)
func (r *analyticsRepository) GetRevenueChart(startDate, endDate time.Time, groupBy string) ([]*models.RevenueDataPoint, error) {
	// TODO: Implement when payment service is available
	return []*models.RevenueDataPoint{}, nil
}

// GetStudentChart retrieves student chart data (placeholder)
func (r *analyticsRepository) GetStudentChart(startDate, endDate time.Time, groupBy string) ([]*models.StudentDataPoint, error) {
	// TODO: Implement when staff service is available
	return []*models.StudentDataPoint{}, nil
}

// GetSystemMetrics retrieves system metrics
func (r *analyticsRepository) GetSystemMetrics() (*models.SystemMetrics, error) {
	metrics := &models.SystemMetrics{
		DatabaseStats:    &models.DatabaseStats{},
		ServiceStats:     &models.ServiceStats{},
		PerformanceStats: &models.PerformanceStats{},
	}

	// Database stats
	var tableCount int64
	if err := r.db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'").Scan(&tableCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count tables: %w", err)
	}
	metrics.DatabaseStats.TotalTables = tableCount

	// TODO: Add more system metrics as needed

	return metrics, nil
}
