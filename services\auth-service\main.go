package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/crm-microservices/auth-service/internal/config"
	"github.com/crm-microservices/auth-service/internal/handlers"
	"github.com/crm-microservices/auth-service/internal/repository"
	"github.com/crm-microservices/auth-service/internal/services"
	"github.com/crm-microservices/shared/database"
	"github.com/crm-microservices/shared/middleware"
	"github.com/crm-microservices/shared/utils"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}

	// Set Gin mode
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize database connection
	var dbManager *database.ConnectionManager
	if cfg.Database.URL != "" {
		// Use DATABASE_URL if provided
		dbManager = database.NewConnectionManagerFromURL(cfg.Database.URL)
	} else {
		// Use individual database config fields
		dbConfig := &database.Config{
			Host:     cfg.Database.Host,
			Port:     cfg.Database.Port,
			User:     cfg.Database.User,
			Password: cfg.Database.Password,
			DBName:   cfg.Database.DBName,
			SSLMode:  cfg.Database.SSLMode,
			TimeZone: cfg.Database.TimeZone,
		}
		dbManager = database.NewConnectionManager(dbConfig)
	}

	log.Printf("Connecting to database...")
	if err := dbManager.Connect(); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer dbManager.Close()
	log.Printf("Database connection established successfully")

	// Initialize database schema and default data
	log.Printf("Initializing database schema...")
	if err := initializeDatabaseSchema(dbManager); err != nil {
		log.Fatalf("Failed to initialize database schema: %v", err)
	}
	log.Printf("Database schema initialized successfully")

	// Initialize JWT manager
	jwtManager := utils.NewJWTManager(
		cfg.JWT.Secret,
		cfg.JWT.AccessTokenExpiry,
		cfg.JWT.RefreshTokenExpiry,
		cfg.JWT.Issuer,
		cfg.JWT.Audience,
	)

	// Initialize password manager
	passwordManager := utils.NewPasswordManager(nil)

	// Initialize repositories
	userRepo := repository.NewUserRepository(dbManager.GetDB())
	sessionRepo := repository.NewSessionRepository(dbManager.GetDB())

	// Initialize services
	authService := services.NewAuthService(userRepo, sessionRepo, jwtManager, passwordManager)
	userService := services.NewUserService(userRepo, passwordManager)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)
	userHandler := handlers.NewUserHandler(userService)
	healthHandler := handlers.NewHealthHandler(dbManager)

	// Setup router
	router := setupRouter(cfg, authHandler, userHandler, healthHandler, jwtManager)

	// Create HTTP server
	server := &http.Server{
		Addr:         cfg.GetServerAddr(),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting Auth Service on %s", cfg.GetServerAddr())
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down Auth Service...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Auth Service stopped")
}

func setupRouter(cfg *config.Config, authHandler *handlers.AuthHandler, userHandler *handlers.UserHandler, healthHandler *handlers.HealthHandler, jwtManager *utils.JWTManager) *gin.Engine {
	router := gin.New()

	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS middleware
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Health check endpoints
	router.GET("/health", healthHandler.HealthCheck)
	router.GET("/ready", healthHandler.ReadinessCheck)
	router.GET("/live", healthHandler.LivenessCheck)
	router.GET("/metrics", healthHandler.MetricsCheck)
	router.GET("/version", healthHandler.VersionCheck)

	// Initialize auth middleware
	authMiddleware := middleware.NewAuthMiddleware(jwtManager)

	// API routes
	api := router.Group("/api/v1")
	{
		// Authentication routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/register", authHandler.Register)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
			auth.POST("/forgot-password", authHandler.ForgotPassword)
			auth.POST("/reset-password", authHandler.ResetPassword)
			auth.GET("/validate", authHandler.ValidateToken)
			auth.GET("/status", authMiddleware.RequireAuth(), authHandler.GetAuthStatus)
			auth.POST("/change-password", authMiddleware.RequireAuth(), authHandler.ChangePassword)
		}

		// User routes (protected)
		users := api.Group("/users")
		users.Use(authMiddleware.RequireAuth())
		{
			users.GET("/profile", userHandler.GetProfile)
			users.PUT("/profile", userHandler.UpdateProfile)
			users.POST("/change-password", userHandler.ChangePassword)
			users.POST("/verify-email", userHandler.VerifyEmail)
			users.POST("/resend-verification", userHandler.ResendVerification)
		}

		// Admin routes (admin only)
		admin := api.Group("/admin")
		admin.Use(authMiddleware.RequireAuth())
		admin.Use(authMiddleware.RequireAdminAccess())
		{
			admin.GET("/users", userHandler.ListUsers)
			admin.GET("/users/:id", userHandler.GetUser)
			admin.POST("/users", userHandler.CreateUser)
			admin.PUT("/users/:id", userHandler.UpdateUser)
			admin.DELETE("/users/:id", userHandler.DeleteUser)
			admin.POST("/users/:id/activate", userHandler.ActivateUser)
			admin.POST("/users/:id/deactivate", userHandler.DeactivateUser)
		}
	}

	return router
}

func initializeDatabaseSchema(dbManager *database.ConnectionManager) error {
	db := dbManager.GetDB()

	// Create tables if they don't exist
	if err := createTables(db); err != nil {
		return fmt.Errorf("failed to create tables: %w", err)
	}

	// Create default admin user if it doesn't exist
	if err := createDefaultAdminUser(db); err != nil {
		return fmt.Errorf("failed to create default admin user: %w", err)
	}

	log.Println("Database schema initialization completed")
	return nil
}

func createTables(db *gorm.DB) error {
	log.Println("Creating database tables...")

	// Create users table
	err := db.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			email VARCHAR(255) UNIQUE NOT NULL,
			username VARCHAR(255) UNIQUE NOT NULL,
			password VARCHAR(255) NOT NULL,
			first_name VARCHAR(255) NOT NULL,
			last_name VARCHAR(255) NOT NULL,
			phone VARCHAR(20) UNIQUE,
			role VARCHAR(20) NOT NULL DEFAULT 'USER',
			status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
			last_login_at TIMESTAMPTZ,
			avatar VARCHAR(500),
			date_of_birth DATE,
			address TEXT,
			city VARCHAR(100),
			country VARCHAR(100),
			email_verified BOOLEAN DEFAULT FALSE,
			email_verified_at TIMESTAMPTZ,
			phone_verified BOOLEAN DEFAULT FALSE,
			phone_verified_at TIMESTAMPTZ,
			password_changed_at TIMESTAMPTZ,
			two_factor_enabled BOOLEAN DEFAULT FALSE,
			two_factor_secret VARCHAR(255),
			metadata JSONB,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		);
	`).Error
	if err != nil {
		return fmt.Errorf("failed to create users table: %w", err)
	}

	// Create user_sessions table
	err = db.Exec(`
		CREATE TABLE IF NOT EXISTS user_sessions (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			user_id UUID NOT NULL,
			token_hash VARCHAR(255) NOT NULL,
			refresh_token_hash VARCHAR(255),
			expires_at TIMESTAMPTZ NOT NULL,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		);
	`).Error
	if err != nil {
		return fmt.Errorf("failed to create user_sessions table: %w", err)
	}

	// Create indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);")

	log.Println("Database tables created successfully")
	return nil
}

func createDefaultAdminUser(db *gorm.DB) error {
	log.Println("Creating default admin user...")

	// Check if admin user already exists
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM users WHERE email = ?", "<EMAIL>").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check for existing admin user: %w", err)
	}

	if count > 0 {
		log.Println("Default admin user already exists")
		return nil
	}

	// Insert default admin user
	err = db.Exec(`
		INSERT INTO users (email, username, password, first_name, last_name, role, status, email_verified)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, "<EMAIL>", "admin", "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
		"System", "Administrator", "SUPER_ADMIN", "ACTIVE", true).Error

	if err != nil {
		return fmt.Errorf("failed to create default admin user: %w", err)
	}

	log.Println("Default admin user created successfully")
	return nil
}
