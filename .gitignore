# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work.sum

# Environment variables
.env
.env.local
.env.development
.env.production
.env.test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
bin/
build/
dist/
*.log

# Docker
.dockerignore

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp

# Coverage reports
coverage.out
coverage.html

# Air (hot reload) temporary files
tmp/

# Service-specific build artifacts
services/*/bin/
services/*/build/
services/*/dist/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# Nuxt.js build output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
docker-compose.override.yml
.env.override

# Certificates
*.pem
*.key
*.crt
*.csr

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Profiling
*.prof
*.pprof

# Vendor directory
vendor/

# Air live reload
.air.toml

# Test databases
test.db
*_test.db

# Migration files (keep structure, ignore data)
# migrations/*.sql

# Documentation build
docs/build/
docs/_build/

# Monitoring data
monitoring/data/
prometheus/data/
grafana/data/

# Secrets and keys
secrets/
keys/
certs/

# Local configuration overrides
config.local.yaml
config.local.json
config.override.*
