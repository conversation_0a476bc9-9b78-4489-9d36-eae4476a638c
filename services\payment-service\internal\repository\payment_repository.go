package repository

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"payment-service/internal/models"
	sharedModels "github.com/crm-microservices/shared/models"
)

// PaymentRepository handles payment data operations
type PaymentRepository interface {
	GetAll(req *PaymentListRequest) ([]*models.Payment, int64, error)
	GetByID(id uuid.UUID) (*models.Payment, error)
	GetByInvoiceNumber(invoiceNumber string) (*models.Payment, error)
	Create(payment *models.Payment) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetByStudentID(studentID uuid.UUID) ([]*models.Payment, error)
	GetByCourseID(courseID uuid.UUID) ([]*models.Payment, error)
	GetByStatus(status sharedModels.PaymentStatus) ([]*models.Payment, error)
	GetByDateRange(startDate, endDate time.Time) ([]*models.Payment, error)
	GetPaymentStats() (*PaymentStats, error)
	GetRevenueByPeriod(startDate, endDate time.Time) (*RevenueStats, error)
}

type paymentRepository struct {
	db *gorm.DB
}

// NewPaymentRepository creates a new payment repository
func NewPaymentRepository(db *gorm.DB) PaymentRepository {
	return &paymentRepository{db: db}
}

// PaymentListRequest represents request parameters for listing payments
type PaymentListRequest struct {
	sharedModels.PaginationRequest
	Status      *sharedModels.PaymentStatus `form:"status"`
	Method      *sharedModels.PaymentMethod `form:"method"`
	StudentID   *uuid.UUID                  `form:"student_id"`
	CourseID    *uuid.UUID                  `form:"course_id"`
	GatewayType *string                     `form:"gateway_type"`
	StartDate   *time.Time                  `form:"start_date"`
	EndDate     *time.Time                  `form:"end_date"`
	Search      *string                     `form:"search"`
}

// PaymentStats represents payment statistics
type PaymentStats struct {
	TotalPayments     int64   `json:"total_payments"`
	TotalRevenue      float64 `json:"total_revenue"`
	SuccessfulPayments int64  `json:"successful_payments"`
	FailedPayments    int64   `json:"failed_payments"`
	PendingPayments   int64   `json:"pending_payments"`
	RefundedPayments  int64   `json:"refunded_payments"`
	AveragePayment    float64 `json:"average_payment"`
	SuccessRate       float64 `json:"success_rate"`
}

// RevenueStats represents revenue statistics
type RevenueStats struct {
	TotalRevenue    float64 `json:"total_revenue"`
	NetRevenue      float64 `json:"net_revenue"`
	TotalRefunds    float64 `json:"total_refunds"`
	TotalFees       float64 `json:"total_fees"`
	PaymentCount    int64   `json:"payment_count"`
	AveragePayment  float64 `json:"average_payment"`
}

// GetAll retrieves payments with pagination and filtering
func (r *paymentRepository) GetAll(req *PaymentListRequest) ([]*models.Payment, int64, error) {
	var payments []*models.Payment
	var total int64

	query := r.db.Model(&models.Payment{}).Preload("Transactions").Preload("Refunds")

	// Apply filters
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Method != nil {
		query = query.Where("method = ?", *req.Method)
	}

	if req.StudentID != nil {
		query = query.Where("student_id = ?", *req.StudentID)
	}

	if req.CourseID != nil {
		query = query.Where("course_id = ?", *req.CourseID)
	}

	if req.GatewayType != nil {
		query = query.Where("gateway_type = ?", *req.GatewayType)
	}

	if req.StartDate != nil {
		query = query.Where("created_at >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("created_at <= ?", *req.EndDate)
	}

	if req.Search != nil && *req.Search != "" {
		searchTerm := "%" + *req.Search + "%"
		query = query.Where("invoice_number ILIKE ? OR description ILIKE ? OR gateway_payment_id ILIKE ?", 
			searchTerm, searchTerm, searchTerm)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()
	
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&payments).Error; err != nil {
		return nil, 0, err
	}

	return payments, total, nil
}

// GetByID retrieves a payment by ID
func (r *paymentRepository) GetByID(id uuid.UUID) (*models.Payment, error) {
	var payment models.Payment
	if err := r.db.Preload("Transactions").Preload("Refunds").First(&payment, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &payment, nil
}

// GetByInvoiceNumber retrieves a payment by invoice number
func (r *paymentRepository) GetByInvoiceNumber(invoiceNumber string) (*models.Payment, error) {
	var payment models.Payment
	if err := r.db.Preload("Transactions").Preload("Refunds").First(&payment, "invoice_number = ?", invoiceNumber).Error; err != nil {
		return nil, err
	}
	return &payment, nil
}

// Create creates a new payment
func (r *paymentRepository) Create(payment *models.Payment) error {
	return r.db.Create(payment).Error
}

// Update updates a payment
func (r *paymentRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	return r.db.Model(&models.Payment{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a payment
func (r *paymentRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&models.Payment{}, "id = ?", id).Error
}

// GetByStudentID retrieves payments by student ID
func (r *paymentRepository) GetByStudentID(studentID uuid.UUID) ([]*models.Payment, error) {
	var payments []*models.Payment
	if err := r.db.Preload("Transactions").Preload("Refunds").Where("student_id = ?", studentID).Order("created_at DESC").Find(&payments).Error; err != nil {
		return nil, err
	}
	return payments, nil
}

// GetByCourseID retrieves payments by course ID
func (r *paymentRepository) GetByCourseID(courseID uuid.UUID) ([]*models.Payment, error) {
	var payments []*models.Payment
	if err := r.db.Preload("Transactions").Preload("Refunds").Where("course_id = ?", courseID).Order("created_at DESC").Find(&payments).Error; err != nil {
		return nil, err
	}
	return payments, nil
}

// GetByStatus retrieves payments by status
func (r *paymentRepository) GetByStatus(status sharedModels.PaymentStatus) ([]*models.Payment, error) {
	var payments []*models.Payment
	if err := r.db.Preload("Transactions").Preload("Refunds").Where("status = ?", status).Order("created_at DESC").Find(&payments).Error; err != nil {
		return nil, err
	}
	return payments, nil
}

// GetByDateRange retrieves payments within a date range
func (r *paymentRepository) GetByDateRange(startDate, endDate time.Time) ([]*models.Payment, error) {
	var payments []*models.Payment
	if err := r.db.Preload("Transactions").Preload("Refunds").
		Where("created_at >= ? AND created_at <= ?", startDate, endDate).
		Order("created_at DESC").Find(&payments).Error; err != nil {
		return nil, err
	}
	return payments, nil
}

// GetPaymentStats retrieves payment statistics
func (r *paymentRepository) GetPaymentStats() (*PaymentStats, error) {
	var stats PaymentStats
	
	// Get total payments and revenue
	if err := r.db.Model(&models.Payment{}).
		Select("COUNT(*) as total_payments, COALESCE(SUM(amount), 0) as total_revenue, COALESCE(AVG(amount), 0) as average_payment").
		Scan(&stats).Error; err != nil {
		return nil, err
	}

	// Get payments by status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	
	if err := r.db.Model(&models.Payment{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, err
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case "COMPLETED":
			stats.SuccessfulPayments = sc.Count
		case "FAILED":
			stats.FailedPayments = sc.Count
		case "PENDING":
			stats.PendingPayments = sc.Count
		case "REFUNDED":
			stats.RefundedPayments = sc.Count
		}
	}

	// Calculate success rate
	if stats.TotalPayments > 0 {
		stats.SuccessRate = float64(stats.SuccessfulPayments) / float64(stats.TotalPayments) * 100
	}

	return &stats, nil
}

// GetRevenueByPeriod retrieves revenue statistics for a specific period
func (r *paymentRepository) GetRevenueByPeriod(startDate, endDate time.Time) (*RevenueStats, error) {
	var stats RevenueStats
	
	// Get revenue from completed payments
	if err := r.db.Model(&models.Payment{}).
		Select("COALESCE(SUM(amount), 0) as total_revenue, COUNT(*) as payment_count, COALESCE(AVG(amount), 0) as average_payment").
		Where("status = ? AND created_at >= ? AND created_at <= ?", "COMPLETED", startDate, endDate).
		Scan(&stats).Error; err != nil {
		return nil, err
	}

	// Get total refunds
	if err := r.db.Table("refunds").
		Select("COALESCE(SUM(amount), 0) as total_refunds").
		Joins("JOIN payments ON refunds.payment_id = payments.id").
		Where("refunds.status = ? AND payments.created_at >= ? AND payments.created_at <= ?", "COMPLETED", startDate, endDate).
		Scan(&stats.TotalRefunds).Error; err != nil {
		return nil, err
	}

	// Get total fees from transactions
	if err := r.db.Table("transactions").
		Select("COALESCE(SUM(gateway_fee), 0) as total_fees").
		Joins("JOIN payments ON transactions.payment_id = payments.id").
		Where("transactions.status = ? AND payments.created_at >= ? AND payments.created_at <= ?", "COMPLETED", startDate, endDate).
		Scan(&stats.TotalFees).Error; err != nil {
		return nil, err
	}

	// Calculate net revenue
	stats.NetRevenue = stats.TotalRevenue - stats.TotalRefunds - stats.TotalFees

	return &stats, nil
}
