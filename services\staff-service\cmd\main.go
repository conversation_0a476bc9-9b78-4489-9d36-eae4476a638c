package main

import (
	"fmt"
	"log"
	"os"

	"staff-service/internal/config"
	"staff-service/internal/database"
	"staff-service/internal/handler"
	"staff-service/internal/repository"
	"staff-service/internal/router"
	"staff-service/internal/service"

	"github.com/joho/godotenv"
)

// @title Staff Service API
// @version 1.0
// @description Staff Service API for managing leads, students, courses, teachers, and enrollments
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Run migrations
	if err := database.RunMigrations(db); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Initialize repositories
	leadRepo := repository.NewLeadRepository(db)
	studentRepo := repository.NewStudentRepository(db)
	courseRepo := repository.NewCourseRepository(db)
	teacherRepo := repository.NewTeacherRepository(db)
	enrollmentRepo := repository.NewEnrollmentRepository(db)
	scheduleRepo := repository.NewScheduleRepository(db)
	attendanceRepo := repository.NewAttendanceRepository(db)
	gradeRepo := repository.NewGradeRepository(db)

	// Initialize services
	leadService := service.NewLeadService(leadRepo, studentRepo, courseRepo)
	studentService := service.NewStudentService(studentRepo, enrollmentRepo, gradeRepo, attendanceRepo)
	courseService := service.NewCourseService(courseRepo, teacherRepo, enrollmentRepo)
	teacherService := service.NewTeacherService(teacherRepo, courseRepo)
	enrollmentService := service.NewEnrollmentService(enrollmentRepo, studentRepo, courseRepo)

	// Initialize handlers
	leadHandler := handler.NewLeadHandler(leadService)
	studentHandler := handler.NewStudentHandler(studentService)
	courseHandler := handler.NewCourseHandler(courseService)
	teacherHandler := handler.NewTeacherHandler(teacherService)
	enrollmentHandler := handler.NewEnrollmentHandler(enrollmentService)

	// Initialize router
	appRouter := router.NewRouter(
		leadHandler,
		studentHandler,
		courseHandler,
		teacherHandler,
		enrollmentHandler,
	)

	// Setup routes
	r := appRouter.SetupRoutes()

	// Start server
	log.Printf("Staff Service starting on port %s", cfg.Port)
	log.Printf("Swagger documentation available at: http://localhost:%s/swagger/index.html", cfg.Port)

	if err := r.Run(":" + cfg.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
