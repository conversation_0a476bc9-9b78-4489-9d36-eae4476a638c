package repository

import (
	"fmt"
	"strings"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GradeRepository handles grade data operations
type GradeRepository interface {
	GetAll(req *models.GradeListRequest) ([]*models.Grade, int64, error)
	GetByID(id uuid.UUID) (*models.Grade, error)
	Create(grade *models.Grade) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.GradeStats, error)
	GetByStudent(studentID uuid.UUID) ([]*models.Grade, error)
	GetByCourse(courseID uuid.UUID) ([]*models.Grade, error)
	GetByStudentAndCourse(studentID, courseID uuid.UUID) ([]*models.Grade, error)
	GetStudentGradeSummary(studentID, courseID uuid.UUID) (*models.StudentGradeSummary, error)
	BulkCreate(grades []*models.Grade) error
	CalculateWeightedAverage(studentID, courseID uuid.UUID) (float64, error)
	GetGradeDistribution(courseID uuid.UUID, assessmentType *models.GradeType) (map[string]int64, error)
	GetTopPerformers(courseID uuid.UUID, limit int) ([]*models.Grade, error)
	GetLowPerformers(courseID uuid.UUID, limit int) ([]*models.Grade, error)
}

type gradeRepository struct {
	db *gorm.DB
}

// NewGradeRepository creates a new grade repository
func NewGradeRepository(db *gorm.DB) GradeRepository {
	return &gradeRepository{db: db}
}

// GetAll retrieves grades with pagination and filtering
func (r *gradeRepository) GetAll(req *models.GradeListRequest) ([]*models.Grade, int64, error) {
	var grades []*models.Grade
	var total int64

	query := r.db.Model(&models.Grade{}).Preload("Student").Preload("Course").Preload("GradedBy")

	// Apply filters
	if req.StudentID != nil {
		query = query.Where("student_id = ?", *req.StudentID)
	}

	if req.CourseID != nil {
		query = query.Where("course_id = ?", *req.CourseID)
	}

	if req.AssessmentType != nil {
		query = query.Where("assessment_type = ?", *req.AssessmentType)
	}

	if req.GradedByID != nil {
		query = query.Where("graded_by_id = ?", *req.GradedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Joins("LEFT JOIN students ON students.id = grades.student_id").
			Joins("LEFT JOIN courses ON courses.id = grades.course_id").
			Where(
				"LOWER(students.first_name) LIKE ? OR LOWER(students.last_name) LIKE ? OR LOWER(courses.name) LIKE ? OR LOWER(assessment_name) LIKE ?",
				searchTerm, searchTerm, searchTerm, searchTerm,
			)
	}

	if req.StartDate != nil {
		query = query.Where("assessment_date >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("assessment_date <= ?", *req.EndDate)
	}

	if req.MinScore != nil {
		query = query.Where("percentage >= ?", *req.MinScore)
	}

	if req.MaxScore != nil {
		query = query.Where("percentage <= ?", *req.MaxScore)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count grades: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("assessment_date DESC, graded_at DESC").Find(&grades).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get grades: %w", err)
	}

	return grades, total, nil
}

// GetByID retrieves a grade by ID
func (r *gradeRepository) GetByID(id uuid.UUID) (*models.Grade, error) {
	var grade models.Grade
	if err := r.db.Preload("Student").Preload("Course").Preload("GradedBy").
		First(&grade, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("grade not found")
		}
		return nil, fmt.Errorf("failed to get grade: %w", err)
	}
	return &grade, nil
}

// Create creates a new grade
func (r *gradeRepository) Create(grade *models.Grade) error {
	// Calculate percentage if not provided
	if grade.Percentage == 0 && grade.MaxScore > 0 {
		grade.Percentage = (grade.Score / grade.MaxScore) * 100
	}

	if err := r.db.Create(grade).Error; err != nil {
		return fmt.Errorf("failed to create grade: %w", err)
	}
	return nil
}

// Update updates a grade
func (r *gradeRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	// Recalculate percentage if score or max_score is updated
	if score, hasScore := updates["score"]; hasScore {
		if maxScore, hasMaxScore := updates["max_score"]; hasMaxScore {
			if ms, ok := maxScore.(float64); ok && ms > 0 {
				if s, ok := score.(float64); ok {
					updates["percentage"] = (s / ms) * 100
				}
			}
		} else {
			// Get current max_score to calculate percentage
			var grade models.Grade
			if err := r.db.First(&grade, "id = ?", id).Error; err == nil && grade.MaxScore > 0 {
				if s, ok := score.(float64); ok {
					updates["percentage"] = (s / grade.MaxScore) * 100
				}
			}
		}
	}

	result := r.db.Model(&models.Grade{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update grade: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("grade not found")
	}
	return nil
}

// Delete soft deletes a grade
func (r *gradeRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Grade{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete grade: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("grade not found")
	}
	return nil
}

// GetStats retrieves grade statistics
func (r *gradeRepository) GetStats() (*models.GradeStats, error) {
	stats := &models.GradeStats{
		GradesByType:   make(map[models.GradeType]int64),
		GradesByLetter: make(map[string]int64),
	}

	// Total grades
	if err := r.db.Model(&models.Grade{}).Count(&stats.TotalGrades).Error; err != nil {
		return nil, fmt.Errorf("failed to count total grades: %w", err)
	}

	// Grades by type
	types := []models.GradeType{
		models.GradeTypeAssignment, models.GradeTypeQuiz, models.GradeTypeExam,
		models.GradeTypeMidterm, models.GradeTypeFinal, models.GradeTypeProject,
		models.GradeTypeParticipation, models.GradeTypeOther,
	}

	for _, gradeType := range types {
		var count int64
		if err := r.db.Model(&models.Grade{}).Where("assessment_type = ?", gradeType).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count grades by type %s: %w", gradeType, err)
		}
		stats.GradesByType[gradeType] = count
	}

	// Grades by letter (calculated from percentage)
	letterGrades := []string{"A", "B", "C", "D", "F"}
	for _, letter := range letterGrades {
		var count int64
		var query *gorm.DB
		
		switch letter {
		case "A":
			query = r.db.Model(&models.Grade{}).Where("percentage >= 90")
		case "B":
			query = r.db.Model(&models.Grade{}).Where("percentage >= 80 AND percentage < 90")
		case "C":
			query = r.db.Model(&models.Grade{}).Where("percentage >= 70 AND percentage < 80")
		case "D":
			query = r.db.Model(&models.Grade{}).Where("percentage >= 60 AND percentage < 70")
		case "F":
			query = r.db.Model(&models.Grade{}).Where("percentage < 60")
		}
		
		if err := query.Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count grades by letter %s: %w", letter, err)
		}
		stats.GradesByLetter[letter] = count
	}

	// Average statistics
	if err := r.db.Model(&models.Grade{}).Select("AVG(score)").Scan(&stats.AverageScore).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average score: %w", err)
	}

	if err := r.db.Model(&models.Grade{}).Select("AVG(percentage)").Scan(&stats.AveragePercentage).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average percentage: %w", err)
	}

	// Calculate average GPA
	var avgGPA float64
	if err := r.db.Raw(`
		SELECT AVG(
			CASE 
				WHEN percentage >= 90 THEN 4.0
				WHEN percentage >= 80 THEN 3.0
				WHEN percentage >= 70 THEN 2.0
				WHEN percentage >= 60 THEN 1.0
				ELSE 0.0
			END
		) FROM grades
	`).Scan(&avgGPA).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average GPA: %w", err)
	}
	stats.AverageGPA = avgGPA

	// Highest and lowest scores
	if err := r.db.Model(&models.Grade{}).Select("MAX(percentage)").Scan(&stats.HighestScore).Error; err != nil {
		return nil, fmt.Errorf("failed to get highest score: %w", err)
	}

	if err := r.db.Model(&models.Grade{}).Select("MIN(percentage)").Scan(&stats.LowestScore).Error; err != nil {
		return nil, fmt.Errorf("failed to get lowest score: %w", err)
	}

	// Passing rate (assuming 60% is passing)
	var passingCount int64
	if err := r.db.Model(&models.Grade{}).Where("percentage >= 60").Count(&passingCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count passing grades: %w", err)
	}

	if stats.TotalGrades > 0 {
		stats.PassingRate = float64(passingCount) / float64(stats.TotalGrades) * 100
	}

	return stats, nil
}

// GetByStudent retrieves grades for a specific student
func (r *gradeRepository) GetByStudent(studentID uuid.UUID) ([]*models.Grade, error) {
	var grades []*models.Grade
	if err := r.db.Preload("Course").Preload("GradedBy").
		Where("student_id = ?", studentID).
		Order("assessment_date DESC").Find(&grades).Error; err != nil {
		return nil, fmt.Errorf("failed to get grades by student: %w", err)
	}
	return grades, nil
}

// GetByCourse retrieves grades for a specific course
func (r *gradeRepository) GetByCourse(courseID uuid.UUID) ([]*models.Grade, error) {
	var grades []*models.Grade
	if err := r.db.Preload("Student").Preload("GradedBy").
		Where("course_id = ?", courseID).
		Order("assessment_date DESC").Find(&grades).Error; err != nil {
		return nil, fmt.Errorf("failed to get grades by course: %w", err)
	}
	return grades, nil
}

// GetByStudentAndCourse retrieves grades for a specific student in a course
func (r *gradeRepository) GetByStudentAndCourse(studentID, courseID uuid.UUID) ([]*models.Grade, error) {
	var grades []*models.Grade
	if err := r.db.Preload("Course").Preload("GradedBy").
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Order("assessment_date DESC").Find(&grades).Error; err != nil {
		return nil, fmt.Errorf("failed to get grades by student and course: %w", err)
	}
	return grades, nil
}

// GetStudentGradeSummary retrieves grade summary for a student in a course
func (r *gradeRepository) GetStudentGradeSummary(studentID, courseID uuid.UUID) (*models.StudentGradeSummary, error) {
	// Get student and course information
	var student models.Student
	if err := r.db.First(&student, "id = ?", studentID).Error; err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	var course models.Course
	if err := r.db.First(&course, "id = ?", courseID).Error; err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	summary := &models.StudentGradeSummary{
		StudentID:   studentID,
		StudentName: student.GetFullName(),
		CourseID:    courseID,
		CourseName:  course.Name,
	}

	// Count total assessments
	if err := r.db.Model(&models.Grade{}).
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Count(&summary.TotalAssessments).Error; err != nil {
		return nil, fmt.Errorf("failed to count total assessments: %w", err)
	}

	// Calculate averages
	if err := r.db.Model(&models.Grade{}).
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Select("AVG(score)").Scan(&summary.AverageScore).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average score: %w", err)
	}

	if err := r.db.Model(&models.Grade{}).
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Select("AVG(percentage)").Scan(&summary.AveragePercentage).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average percentage: %w", err)
	}

	// Calculate weighted average and GPA
	weightedAvg, err := r.CalculateWeightedAverage(studentID, courseID)
	if err == nil {
		summary.AveragePercentage = weightedAvg
	}

	// Calculate GPA based on average percentage
	switch {
	case summary.AveragePercentage >= 90:
		summary.CurrentGPA = 4.0
		summary.LetterGrade = "A"
	case summary.AveragePercentage >= 80:
		summary.CurrentGPA = 3.0
		summary.LetterGrade = "B"
	case summary.AveragePercentage >= 70:
		summary.CurrentGPA = 2.0
		summary.LetterGrade = "C"
	case summary.AveragePercentage >= 60:
		summary.CurrentGPA = 1.0
		summary.LetterGrade = "D"
	default:
		summary.CurrentGPA = 0.0
		summary.LetterGrade = "F"
	}

	// Get last assessment date
	var lastGrade models.Grade
	if err := r.db.Where("student_id = ? AND course_id = ?", studentID, courseID).
		Order("assessment_date DESC").First(&lastGrade).Error; err == nil {
		summary.LastAssessment = &lastGrade.AssessmentDate
	}

	return summary, nil
}

// BulkCreate creates multiple grades
func (r *gradeRepository) BulkCreate(grades []*models.Grade) error {
	if len(grades) == 0 {
		return nil
	}

	// Calculate percentages for all grades
	for _, grade := range grades {
		if grade.Percentage == 0 && grade.MaxScore > 0 {
			grade.Percentage = (grade.Score / grade.MaxScore) * 100
		}
	}

	if err := r.db.Create(&grades).Error; err != nil {
		return fmt.Errorf("failed to bulk create grades: %w", err)
	}
	return nil
}

// CalculateWeightedAverage calculates weighted average for a student in a course
func (r *gradeRepository) CalculateWeightedAverage(studentID, courseID uuid.UUID) (float64, error) {
	var result struct {
		WeightedSum   float64
		TotalWeight   float64
	}

	if err := r.db.Model(&models.Grade{}).
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Select("SUM(percentage * weight) as weighted_sum, SUM(weight) as total_weight").
		Scan(&result).Error; err != nil {
		return 0, fmt.Errorf("failed to calculate weighted average: %w", err)
	}

	if result.TotalWeight == 0 {
		return 0, nil
	}

	return result.WeightedSum / result.TotalWeight, nil
}

// GetGradeDistribution retrieves grade distribution for a course
func (r *gradeRepository) GetGradeDistribution(courseID uuid.UUID, assessmentType *models.GradeType) (map[string]int64, error) {
	distribution := make(map[string]int64)

	query := r.db.Model(&models.Grade{}).Where("course_id = ?", courseID)
	if assessmentType != nil {
		query = query.Where("assessment_type = ?", *assessmentType)
	}

	// Count grades by letter grade ranges
	letterGrades := []struct {
		Letter string
		Min    float64
		Max    float64
	}{
		{"A", 90, 100},
		{"B", 80, 89.99},
		{"C", 70, 79.99},
		{"D", 60, 69.99},
		{"F", 0, 59.99},
	}

	for _, lg := range letterGrades {
		var count int64
		if err := query.Where("percentage >= ? AND percentage <= ?", lg.Min, lg.Max).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count grades for letter %s: %w", lg.Letter, err)
		}
		distribution[lg.Letter] = count
	}

	return distribution, nil
}

// GetTopPerformers retrieves top performing students in a course
func (r *gradeRepository) GetTopPerformers(courseID uuid.UUID, limit int) ([]*models.Grade, error) {
	var grades []*models.Grade

	// Get highest average grades per student
	if err := r.db.Raw(`
		SELECT g.* FROM grades g
		INNER JOIN (
			SELECT student_id, AVG(percentage) as avg_percentage
			FROM grades
			WHERE course_id = ?
			GROUP BY student_id
			ORDER BY avg_percentage DESC
			LIMIT ?
		) top_students ON g.student_id = top_students.student_id
		WHERE g.course_id = ?
	`, courseID, limit, courseID).
		Preload("Student").Preload("Course").Find(&grades).Error; err != nil {
		return nil, fmt.Errorf("failed to get top performers: %w", err)
	}

	return grades, nil
}

// GetLowPerformers retrieves low performing students in a course
func (r *gradeRepository) GetLowPerformers(courseID uuid.UUID, limit int) ([]*models.Grade, error) {
	var grades []*models.Grade

	// Get lowest average grades per student
	if err := r.db.Raw(`
		SELECT g.* FROM grades g
		INNER JOIN (
			SELECT student_id, AVG(percentage) as avg_percentage
			FROM grades
			WHERE course_id = ?
			GROUP BY student_id
			ORDER BY avg_percentage ASC
			LIMIT ?
		) low_students ON g.student_id = low_students.student_id
		WHERE g.course_id = ?
	`, courseID, limit, courseID).
		Preload("Student").Preload("Course").Find(&grades).Error; err != nil {
		return nil, fmt.Errorf("failed to get low performers: %w", err)
	}

	return grades, nil
}
