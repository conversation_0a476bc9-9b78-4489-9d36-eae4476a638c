-- Notification Service Database Initialization Script
-- Note: Notification service uses Redis for primary storage, but this creates PostgreSQL tables for persistent data
-- This database will be shared with one of the existing databases since notification service primarily uses Redis

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- gen_random_uuid() function is already available in PostgreSQL 13+

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS delivery_logs CASCADE;
DROP TABLE IF EXISTS notification_queue CASCADE;
DROP TABLE IF EXISTS notification_preferences CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS notification_templates CASCADE;

-- Create notification_templates table (independent table)
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL, -- EMAIL, SMS, PUSH, IN_APP
    
    -- Template content
    subject TEXT,
    body TEXT NOT NULL,
    html_body TEXT,
    
    -- Template configuration
    variables JSONB, -- Available template variables
    is_active BOOLEAN DEFAULT TRUE,
    language VARCHAR(10) DEFAULT 'en',
    category VARCHAR(100),
    
    -- Metadata
    description TEXT,
    created_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create notifications table (depends on templates)
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id UUID REFERENCES notification_templates(id) ON DELETE SET NULL,
    
    -- Recipient information
    user_id UUID NOT NULL, -- References auth service users (no FK constraint)
    recipient_email VARCHAR(255),
    recipient_phone VARCHAR(20),
    
    -- Notification content
    type VARCHAR(20) NOT NULL, -- EMAIL, SMS, PUSH, IN_APP
    subject TEXT,
    body TEXT NOT NULL,
    html_body TEXT,
    
    -- Status and delivery
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- PENDING, SENT, DELIVERED, FAILED, READ
    priority VARCHAR(10) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    
    -- Scheduling
    scheduled_at TIMESTAMPTZ,
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    
    -- Retry logic
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    next_retry_at TIMESTAMPTZ,
    
    -- Metadata
    metadata JSONB,
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create notification_preferences table (user preferences)
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL, -- References auth service users (no FK constraint)
    
    -- Preference settings
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    
    -- Category preferences
    marketing_emails BOOLEAN DEFAULT TRUE,
    system_notifications BOOLEAN DEFAULT TRUE,
    course_updates BOOLEAN DEFAULT TRUE,
    payment_notifications BOOLEAN DEFAULT TRUE,
    
    -- Timing preferences
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- Frequency settings
    digest_frequency VARCHAR(20) DEFAULT 'DAILY', -- IMMEDIATE, DAILY, WEEKLY, MONTHLY
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create notification_queue table (for batch processing)
CREATE TABLE notification_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Queue information
    queue_name VARCHAR(100) NOT NULL DEFAULT 'default',
    priority INTEGER DEFAULT 0,
    
    -- Processing status
    status VARCHAR(20) NOT NULL DEFAULT 'QUEUED', -- QUEUED, PROCESSING, COMPLETED, FAILED
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    
    -- Scheduling
    scheduled_for TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMPTZ,
    
    -- Error handling
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create delivery_logs table (detailed delivery tracking)
CREATE TABLE delivery_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Delivery details
    delivery_method VARCHAR(20) NOT NULL, -- EMAIL, SMS, PUSH, IN_APP
    provider VARCHAR(50), -- SMTP, Twilio, FCM, etc.
    
    -- Status tracking
    status VARCHAR(20) NOT NULL, -- SENT, DELIVERED, BOUNCED, FAILED, OPENED, CLICKED
    
    -- Provider response
    provider_message_id VARCHAR(255),
    provider_response JSONB,
    
    -- Delivery metrics
    delivered_at TIMESTAMPTZ,
    opened_at TIMESTAMPTZ,
    clicked_at TIMESTAMPTZ,
    
    -- Error information
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for notification_templates table
CREATE INDEX idx_notification_templates_name ON notification_templates(name);
CREATE INDEX idx_notification_templates_type ON notification_templates(type);
CREATE INDEX idx_notification_templates_is_active ON notification_templates(is_active);
CREATE INDEX idx_notification_templates_category ON notification_templates(category);
CREATE INDEX idx_notification_templates_created_at ON notification_templates(created_at);
CREATE INDEX idx_notification_templates_deleted_at ON notification_templates(deleted_at);

-- Create indexes for notifications table
CREATE INDEX idx_notifications_template_id ON notifications(template_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_notifications_sent_at ON notifications(sent_at);
CREATE INDEX idx_notifications_next_retry_at ON notifications(next_retry_at);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_deleted_at ON notifications(deleted_at);

-- Create indexes for notification_preferences table
CREATE INDEX idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX idx_notification_preferences_created_at ON notification_preferences(created_at);
CREATE INDEX idx_notification_preferences_deleted_at ON notification_preferences(deleted_at);

-- Create indexes for notification_queue table
CREATE INDEX idx_notification_queue_notification_id ON notification_queue(notification_id);
CREATE INDEX idx_notification_queue_queue_name ON notification_queue(queue_name);
CREATE INDEX idx_notification_queue_status ON notification_queue(status);
CREATE INDEX idx_notification_queue_priority ON notification_queue(priority);
CREATE INDEX idx_notification_queue_scheduled_for ON notification_queue(scheduled_for);
CREATE INDEX idx_notification_queue_created_at ON notification_queue(created_at);

-- Create indexes for delivery_logs table
CREATE INDEX idx_delivery_logs_notification_id ON delivery_logs(notification_id);
CREATE INDEX idx_delivery_logs_delivery_method ON delivery_logs(delivery_method);
CREATE INDEX idx_delivery_logs_provider ON delivery_logs(provider);
CREATE INDEX idx_delivery_logs_status ON delivery_logs(status);
CREATE INDEX idx_delivery_logs_delivered_at ON delivery_logs(delivered_at);
CREATE INDEX idx_delivery_logs_created_at ON delivery_logs(created_at);

-- Create composite indexes for common queries
CREATE INDEX idx_notifications_user_status ON notifications(user_id, status);
CREATE INDEX idx_notifications_type_status ON notifications(type, status);
CREATE INDEX idx_notification_queue_status_priority ON notification_queue(status, priority);

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_queue_updated_at
    BEFORE UPDATE ON notification_queue
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add check constraints
ALTER TABLE notification_templates ADD CONSTRAINT chk_notification_templates_type
    CHECK (type IN ('EMAIL', 'SMS', 'PUSH', 'IN_APP'));

ALTER TABLE notifications ADD CONSTRAINT chk_notifications_type
    CHECK (type IN ('EMAIL', 'SMS', 'PUSH', 'IN_APP'));

ALTER TABLE notifications ADD CONSTRAINT chk_notifications_status
    CHECK (status IN ('PENDING', 'QUEUED', 'SENT', 'DELIVERED', 'FAILED', 'READ', 'CANCELLED'));

ALTER TABLE notifications ADD CONSTRAINT chk_notifications_priority
    CHECK (priority IN ('LOW', 'NORMAL', 'HIGH', 'URGENT'));

ALTER TABLE notifications ADD CONSTRAINT chk_notifications_retry_count
    CHECK (retry_count >= 0 AND retry_count <= max_retries);

ALTER TABLE notification_preferences ADD CONSTRAINT chk_notification_preferences_digest_frequency
    CHECK (digest_frequency IN ('IMMEDIATE', 'DAILY', 'WEEKLY', 'MONTHLY'));

ALTER TABLE notification_queue ADD CONSTRAINT chk_notification_queue_status
    CHECK (status IN ('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED'));

ALTER TABLE notification_queue ADD CONSTRAINT chk_notification_queue_attempts
    CHECK (attempts >= 0 AND attempts <= max_attempts);

ALTER TABLE delivery_logs ADD CONSTRAINT chk_delivery_logs_delivery_method
    CHECK (delivery_method IN ('EMAIL', 'SMS', 'PUSH', 'IN_APP'));

ALTER TABLE delivery_logs ADD CONSTRAINT chk_delivery_logs_status
    CHECK (status IN ('SENT', 'DELIVERED', 'BOUNCED', 'FAILED', 'OPENED', 'CLICKED', 'UNSUBSCRIBED'));

-- Add unique constraints
ALTER TABLE notification_preferences ADD CONSTRAINT uk_notification_preferences_user_id
    UNIQUE (user_id);

-- Insert default notification templates
INSERT INTO notification_templates (name, type, subject, body, category, created_by_id) VALUES
('welcome_email', 'EMAIL', 'Welcome to CRM Platform', 'Welcome {{first_name}}! Thank you for joining our platform.', 'system', gen_random_uuid()),
('course_enrollment', 'EMAIL', 'Course Enrollment Confirmation', 'You have been enrolled in {{course_name}}.', 'course', gen_random_uuid()),
('payment_confirmation', 'EMAIL', 'Payment Confirmation', 'Your payment of {{amount}} has been processed successfully.', 'payment', gen_random_uuid()),
('password_reset', 'EMAIL', 'Password Reset Request', 'Click the link to reset your password: {{reset_link}}', 'security', gen_random_uuid())
ON CONFLICT (name) DO NOTHING;

COMMIT;
