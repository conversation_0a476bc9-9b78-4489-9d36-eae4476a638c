package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	NotificationTypeEmail NotificationType = "EMAIL"
	NotificationTypeSMS   NotificationType = "SMS"
	NotificationTypeInApp NotificationType = "IN_APP"
	NotificationTypePush  NotificationType = "PUSH"
)

// NotificationStatus represents the status of a notification
type NotificationStatus string

const (
	NotificationStatusPending   NotificationStatus = "PENDING"
	NotificationStatusSent      NotificationStatus = "SENT"
	NotificationStatusDelivered NotificationStatus = "DELIVERED"
	NotificationStatusFailed    NotificationStatus = "FAILED"
	NotificationStatusCancelled NotificationStatus = "CANCELLED"
)

// NotificationPriority represents the priority of a notification
type NotificationPriority string

const (
	NotificationPriorityLow    NotificationPriority = "LOW"
	NotificationPriorityNormal NotificationPriority = "NORMAL"
	NotificationPriorityHigh   NotificationPriority = "HIGH"
	NotificationPriorityUrgent NotificationPriority = "URGENT"
)

// Notification represents a notification record
type Notification struct {
	ID          uuid.UUID            `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Type        NotificationType     `json:"type" gorm:"not null;index"`
	Status      NotificationStatus   `json:"status" gorm:"not null;default:'PENDING';index"`
	Priority    NotificationPriority `json:"priority" gorm:"not null;default:'NORMAL'"`
	
	// Recipient information
	Recipient   string `json:"recipient" gorm:"not null;index"` // email, phone, user_id
	RecipientID *uuid.UUID `json:"recipient_id" gorm:"type:uuid;index"` // user ID if available
	
	// Message content
	Subject     string `json:"subject" gorm:"type:text"`
	Message     string `json:"message" gorm:"type:text;not null"`
	HTMLContent string `json:"html_content" gorm:"type:text"`
	
	// Template information
	TemplateID   *uuid.UUID             `json:"template_id" gorm:"type:uuid"`
	TemplateData map[string]interface{} `json:"template_data" gorm:"type:jsonb"`
	
	// Scheduling
	ScheduledAt *time.Time `json:"scheduled_at" gorm:"index"`
	SentAt      *time.Time `json:"sent_at"`
	DeliveredAt *time.Time `json:"delivered_at"`
	
	// Retry information
	RetryCount    int        `json:"retry_count" gorm:"default:0"`
	MaxRetries    int        `json:"max_retries" gorm:"default:3"`
	NextRetryAt   *time.Time `json:"next_retry_at"`
	LastError     string     `json:"last_error" gorm:"type:text"`
	
	// Metadata
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	Source      string                 `json:"source" gorm:"index"` // which service sent this
	Reference   string                 `json:"reference"`           // external reference ID
	
	// Tracking
	OpenedAt    *time.Time `json:"opened_at"`
	ClickedAt   *time.Time `json:"clicked_at"`
	
	// Audit fields
	CreatedAt time.Time      `json:"created_at" gorm:"index"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	
	// Relationships
	DeliveryLogs []DeliveryLog `json:"delivery_logs,omitempty" gorm:"foreignKey:NotificationID"`
	Template     *NotificationTemplate `json:"template,omitempty" gorm:"foreignKey:TemplateID"`
}

// BeforeCreate sets the ID if not provided
func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

// IsValid validates the notification
func (n *Notification) IsValid() bool {
	if n.Recipient == "" || n.Message == "" {
		return false
	}
	
	switch n.Type {
	case NotificationTypeEmail, NotificationTypeSMS, NotificationTypeInApp, NotificationTypePush:
		return true
	default:
		return false
	}
}

// CanRetry checks if the notification can be retried
func (n *Notification) CanRetry() bool {
	return n.Status == NotificationStatusFailed && n.RetryCount < n.MaxRetries
}

// MarkAsSent marks the notification as sent
func (n *Notification) MarkAsSent() {
	n.Status = NotificationStatusSent
	now := time.Now()
	n.SentAt = &now
}

// MarkAsDelivered marks the notification as delivered
func (n *Notification) MarkAsDelivered() {
	n.Status = NotificationStatusDelivered
	now := time.Now()
	n.DeliveredAt = &now
}

// MarkAsFailed marks the notification as failed
func (n *Notification) MarkAsFailed(errorMsg string) {
	n.Status = NotificationStatusFailed
	n.LastError = errorMsg
	n.RetryCount++
	
	if n.CanRetry() {
		// Calculate next retry time with exponential backoff
		retryDelay := time.Duration(n.RetryCount*n.RetryCount) * time.Minute
		nextRetry := time.Now().Add(retryDelay)
		n.NextRetryAt = &nextRetry
	}
}

// CreateNotificationRequest represents a request to create a notification
type CreateNotificationRequest struct {
	Type        NotificationType       `json:"type" binding:"required"`
	Recipient   string                 `json:"recipient" binding:"required"`
	RecipientID *uuid.UUID             `json:"recipient_id"`
	Subject     string                 `json:"subject"`
	Message     string                 `json:"message"`
	HTMLContent string                 `json:"html_content"`
	TemplateID  *uuid.UUID             `json:"template_id"`
	TemplateData map[string]interface{} `json:"template_data"`
	ScheduledAt *time.Time             `json:"scheduled_at"`
	Priority    NotificationPriority   `json:"priority"`
	Metadata    map[string]interface{} `json:"metadata"`
	Source      string                 `json:"source"`
	Reference   string                 `json:"reference"`
}

// ToNotification converts the request to a notification model
func (r *CreateNotificationRequest) ToNotification() *Notification {
	notification := &Notification{
		Type:         r.Type,
		Recipient:    r.Recipient,
		RecipientID:  r.RecipientID,
		Subject:      r.Subject,
		Message:      r.Message,
		HTMLContent:  r.HTMLContent,
		TemplateID:   r.TemplateID,
		TemplateData: r.TemplateData,
		ScheduledAt:  r.ScheduledAt,
		Priority:     r.Priority,
		Metadata:     r.Metadata,
		Source:       r.Source,
		Reference:    r.Reference,
		Status:       NotificationStatusPending,
		MaxRetries:   3,
	}
	
	if notification.Priority == "" {
		notification.Priority = NotificationPriorityNormal
	}
	
	return notification
}
