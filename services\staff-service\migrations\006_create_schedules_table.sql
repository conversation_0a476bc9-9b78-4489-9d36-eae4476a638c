-- Create schedules table
CREATE TABLE IF NOT EXISTS schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL,
    teacher_id UUID NOT NULL,
    day_of_week INTEGER NOT NULL, -- 0=Sunday, 1=Monday, ..., 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    room VARCHAR(100),
    max_capacity INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT true,
    effective_from DATE NOT NULL,
    effective_to DATE,
    notes TEXT,
    created_by_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_schedules_course FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT fk_schedules_teacher FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE
);

-- Create indexes for schedules table
CREATE INDEX IF NOT EXISTS idx_schedules_course_id ON schedules(course_id);
CREATE INDEX IF NOT EXISTS idx_schedules_teacher_id ON schedules(teacher_id);
CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_start_time ON schedules(start_time);
CREATE INDEX IF NOT EXISTS idx_schedules_effective_from ON schedules(effective_from);
CREATE INDEX IF NOT EXISTS idx_schedules_is_active ON schedules(is_active);
CREATE INDEX IF NOT EXISTS idx_schedules_created_at ON schedules(created_at);
CREATE INDEX IF NOT EXISTS idx_schedules_deleted_at ON schedules(deleted_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_schedules_course_day_time ON schedules(course_id, day_of_week, start_time);
CREATE INDEX IF NOT EXISTS idx_schedules_teacher_day_time ON schedules(teacher_id, day_of_week, start_time);

-- Add constraints
ALTER TABLE schedules ADD CONSTRAINT chk_schedules_day_of_week 
    CHECK (day_of_week >= 0 AND day_of_week <= 6);

ALTER TABLE schedules ADD CONSTRAINT chk_schedules_times 
    CHECK (end_time > start_time);

ALTER TABLE schedules ADD CONSTRAINT chk_schedules_max_capacity 
    CHECK (max_capacity > 0);

ALTER TABLE schedules ADD CONSTRAINT chk_schedules_effective_dates 
    CHECK (effective_to IS NULL OR effective_to >= effective_from);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to check for schedule conflicts
CREATE OR REPLACE FUNCTION check_schedule_conflict()
RETURNS TRIGGER AS $$
BEGIN
    -- Check for teacher conflicts (same teacher, same day, overlapping times)
    IF EXISTS (
        SELECT 1 FROM schedules 
        WHERE teacher_id = NEW.teacher_id 
        AND day_of_week = NEW.day_of_week
        AND is_active = true
        AND (NEW.id IS NULL OR id != NEW.id) -- Exclude current record for updates
        AND (
            (NEW.start_time >= start_time AND NEW.start_time < end_time) OR
            (NEW.end_time > start_time AND NEW.end_time <= end_time) OR
            (NEW.start_time <= start_time AND NEW.end_time >= end_time)
        )
        AND (
            (NEW.effective_from <= COALESCE(effective_to, '9999-12-31') AND 
             COALESCE(NEW.effective_to, '9999-12-31') >= effective_from)
        )
        AND deleted_at IS NULL
    ) THEN
        RAISE EXCEPTION 'Schedule conflict: Teacher already has a class at this time';
    END IF;
    
    -- Check for room conflicts (same room, same day, overlapping times)
    IF NEW.room IS NOT NULL AND EXISTS (
        SELECT 1 FROM schedules 
        WHERE room = NEW.room 
        AND day_of_week = NEW.day_of_week
        AND is_active = true
        AND (NEW.id IS NULL OR id != NEW.id) -- Exclude current record for updates
        AND (
            (NEW.start_time >= start_time AND NEW.start_time < end_time) OR
            (NEW.end_time > start_time AND NEW.end_time <= end_time) OR
            (NEW.start_time <= start_time AND NEW.end_time >= end_time)
        )
        AND (
            (NEW.effective_from <= COALESCE(effective_to, '9999-12-31') AND 
             COALESCE(NEW.effective_to, '9999-12-31') >= effective_from)
        )
        AND deleted_at IS NULL
    ) THEN
        RAISE EXCEPTION 'Schedule conflict: Room is already booked at this time';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for schedule conflict checking
CREATE TRIGGER check_schedule_conflict_trigger 
    BEFORE INSERT OR UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION check_schedule_conflict();
