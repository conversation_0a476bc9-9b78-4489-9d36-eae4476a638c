package service

import (
	"fmt"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/repository"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
)

// LeadService handles lead business logic
type LeadService interface {
	GetAll(req *models.LeadListRequest) (*models.LeadListResponse, error)
	GetByID(id uuid.UUID) (*models.LeadResponse, error)
	Create(req *models.LeadCreateRequest, createdBy uuid.UUID) (*models.LeadResponse, error)
	Update(id uuid.UUID, req *models.LeadUpdateRequest) (*models.LeadResponse, error)
	Delete(id uuid.UUID) error
	GetStats() (*models.LeadStats, error)
	AssignLead(id uuid.UUID, req *models.LeadAssignRequest) (*models.LeadResponse, error)
	ConvertToStudent(id uuid.UUID, req *models.LeadConvertRequest, convertedBy uuid.UUID) (*models.LeadResponse, error)
	GetFollowUpsDue(date time.Time) ([]*models.LeadResponse, error)
	GetByAssignedUser(userID uuid.UUID) ([]*models.LeadResponse, error)
	BulkUpdateStatus(leadIDs []uuid.UUID, status models.LeadStatus, updatedBy uuid.UUID) error
	UpdateFollowUp(id uuid.UUID, nextFollowUp time.Time, notes string) (*models.LeadResponse, error)
}

type leadService struct {
	leadRepo    repository.LeadRepository
	studentRepo repository.StudentRepository
	courseRepo  repository.CourseRepository
}

// NewLeadService creates a new lead service
func NewLeadService(leadRepo repository.LeadRepository, studentRepo repository.StudentRepository, courseRepo repository.CourseRepository) LeadService {
	return &leadService{
		leadRepo:    leadRepo,
		studentRepo: studentRepo,
		courseRepo:  courseRepo,
	}
}

// GetAll retrieves all leads with pagination and filtering
func (s *leadService) GetAll(req *models.LeadListRequest) (*models.LeadListResponse, error) {
	leads, total, err := s.leadRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get leads: %w", err)
	}

	leadResponses := make([]*models.LeadResponse, len(leads))
	for i, lead := range leads {
		leadResponses[i] = lead.ToResponse()
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &models.LeadListResponse{
		Leads:      leadResponses,
		Pagination: pagination,
	}, nil
}

// GetByID retrieves a lead by ID
func (s *leadService) GetByID(id uuid.UUID) (*models.LeadResponse, error) {
	lead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}

	return lead.ToResponse(), nil
}

// Create creates a new lead
func (s *leadService) Create(req *models.LeadCreateRequest, createdBy uuid.UUID) (*models.LeadResponse, error) {
	// Validate lead source
	if !req.Source.IsValid() {
		return nil, fmt.Errorf("invalid lead source: %s", req.Source)
	}

	// Check if email already exists
	if existingLead, err := s.leadRepo.GetByEmail(req.Email); err == nil && existingLead != nil {
		return nil, fmt.Errorf("lead with email %s already exists", req.Email)
	}

	lead := &models.Lead{
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		Email:            req.Email,
		Phone:            req.Phone,
		DateOfBirth:      req.DateOfBirth,
		Address:          req.Address,
		City:             req.City,
		Country:          req.Country,
		Status:           models.LeadStatusNew,
		Source:           req.Source,
		InterestedCourse: req.InterestedCourse,
		Budget:           req.Budget,
		Notes:            req.Notes,
		CreatedByUserID:  createdBy,
	}

	if err := s.leadRepo.Create(lead); err != nil {
		return nil, fmt.Errorf("failed to create lead: %w", err)
	}

	// Reload with associations
	createdLead, err := s.leadRepo.GetByID(lead.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created lead: %w", err)
	}

	return createdLead.ToResponse(), nil
}

// Update updates a lead
func (s *leadService) Update(id uuid.UUID, req *models.LeadUpdateRequest) (*models.LeadResponse, error) {
	// Check if lead exists
	existingLead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}

	// Validate status if provided
	if req.Status != nil && !req.Status.IsValid() {
		return nil, fmt.Errorf("invalid lead status: %s", *req.Status)
	}

	// Validate source if provided
	if req.Source != nil && !req.Source.IsValid() {
		return nil, fmt.Errorf("invalid lead source: %s", *req.Source)
	}

	// Check email uniqueness if email is being updated
	if req.Email != nil && *req.Email != existingLead.Email {
		if existingEmailLead, err := s.leadRepo.GetByEmail(*req.Email); err == nil && existingEmailLead != nil {
			return nil, fmt.Errorf("lead with email %s already exists", *req.Email)
		}
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.FirstName != nil {
		updates["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		updates["last_name"] = *req.LastName
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.DateOfBirth != nil {
		updates["date_of_birth"] = *req.DateOfBirth
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.City != nil {
		updates["city"] = *req.City
	}
	if req.Country != nil {
		updates["country"] = *req.Country
	}
	if req.Status != nil {
		updates["status"] = *req.Status
		// Update last contacted time if status changes to contacted
		if *req.Status == models.LeadStatusContacted {
			updates["last_contacted_at"] = time.Now()
		}
		// Update converted time if status changes to converted
		if *req.Status == models.LeadStatusConverted {
			updates["converted_at"] = time.Now()
		}
	}
	if req.Source != nil {
		updates["source"] = *req.Source
	}
	if req.InterestedCourse != nil {
		updates["interested_course"] = *req.InterestedCourse
	}
	if req.Budget != nil {
		updates["budget"] = *req.Budget
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}
	if req.NextFollowUpAt != nil {
		updates["next_follow_up_at"] = *req.NextFollowUpAt
	}

	updates["updated_at"] = time.Now()

	if err := s.leadRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update lead: %w", err)
	}

	// Get updated lead
	updatedLead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated lead: %w", err)
	}

	return updatedLead.ToResponse(), nil
}

// Delete deletes a lead
func (s *leadService) Delete(id uuid.UUID) error {
	// Check if lead exists
	if _, err := s.leadRepo.GetByID(id); err != nil {
		return fmt.Errorf("failed to get lead: %w", err)
	}

	if err := s.leadRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete lead: %w", err)
	}

	return nil
}

// GetStats retrieves lead statistics
func (s *leadService) GetStats() (*models.LeadStats, error) {
	stats, err := s.leadRepo.GetStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get lead stats: %w", err)
	}

	return stats, nil
}

// AssignLead assigns a lead to a user
func (s *leadService) AssignLead(id uuid.UUID, req *models.LeadAssignRequest) (*models.LeadResponse, error) {
	// Check if lead exists
	if _, err := s.leadRepo.GetByID(id); err != nil {
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}

	updates := map[string]interface{}{
		"assigned_to_id": req.AssignedToID,
		"updated_at":     time.Now(),
	}

	if err := s.leadRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to assign lead: %w", err)
	}

	// Get updated lead
	updatedLead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated lead: %w", err)
	}

	return updatedLead.ToResponse(), nil
}

// ConvertToStudent converts a lead to a student
func (s *leadService) ConvertToStudent(id uuid.UUID, req *models.LeadConvertRequest, convertedBy uuid.UUID) (*models.LeadResponse, error) {
	// Check if lead exists and can be converted
	lead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}

	if !lead.CanBeConverted() {
		return nil, fmt.Errorf("lead cannot be converted - status must be QUALIFIED")
	}

	// Check if course exists
	_, err = s.courseRepo.GetByID(req.CourseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	// Create student from lead
	student := &models.Student{
		StudentID:      "", // Will be generated by repository
		FirstName:      lead.FirstName,
		LastName:       lead.LastName,
		Email:          lead.Email,
		Phone:          lead.Phone,
		DateOfBirth:    lead.DateOfBirth,
		Address:        lead.Address,
		City:           lead.City,
		Country:        lead.Country,
		Status:         models.StudentStatusActive,
		EnrollmentDate: time.Now(),
		CreatedByUserID: convertedBy,
	}

	if err := s.studentRepo.Create(student); err != nil {
		return nil, fmt.Errorf("failed to create student: %w", err)
	}

	// Update lead status to converted
	updates := map[string]interface{}{
		"status":          models.LeadStatusConverted,
		"converted_at":    time.Now(),
		"converted_to_id": student.ID,
		"updated_at":      time.Now(),
	}

	if req.Notes != "" {
		updates["notes"] = lead.Notes + "\n\nConversion Notes: " + req.Notes
	}

	if err := s.leadRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update lead status: %w", err)
	}

	// Get updated lead
	updatedLead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated lead: %w", err)
	}

	return updatedLead.ToResponse(), nil
}

// GetFollowUpsDue retrieves leads with follow-ups due
func (s *leadService) GetFollowUpsDue(date time.Time) ([]*models.LeadResponse, error) {
	leads, err := s.leadRepo.GetFollowUpsDue(date)
	if err != nil {
		return nil, fmt.Errorf("failed to get follow-ups due: %w", err)
	}

	responses := make([]*models.LeadResponse, len(leads))
	for i, lead := range leads {
		responses[i] = lead.ToResponse()
	}

	return responses, nil
}

// GetByAssignedUser retrieves leads assigned to a user
func (s *leadService) GetByAssignedUser(userID uuid.UUID) ([]*models.LeadResponse, error) {
	leads, err := s.leadRepo.GetByAssignedUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get leads by assigned user: %w", err)
	}

	responses := make([]*models.LeadResponse, len(leads))
	for i, lead := range leads {
		responses[i] = lead.ToResponse()
	}

	return responses, nil
}

// BulkUpdateStatus updates status for multiple leads
func (s *leadService) BulkUpdateStatus(leadIDs []uuid.UUID, status models.LeadStatus, updatedBy uuid.UUID) error {
	if !status.IsValid() {
		return fmt.Errorf("invalid lead status: %s", status)
	}

	if err := s.leadRepo.BulkUpdateStatus(leadIDs, status, updatedBy); err != nil {
		return fmt.Errorf("failed to bulk update lead status: %w", err)
	}

	return nil
}

// UpdateFollowUp updates follow-up information for a lead
func (s *leadService) UpdateFollowUp(id uuid.UUID, nextFollowUp time.Time, notes string) (*models.LeadResponse, error) {
	// Check if lead exists
	lead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}

	updates := map[string]interface{}{
		"next_follow_up_at":  nextFollowUp,
		"last_contacted_at":  time.Now(),
		"updated_at":         time.Now(),
	}

	if notes != "" {
		existingNotes := lead.Notes
		if existingNotes != "" {
			updates["notes"] = existingNotes + "\n\n" + time.Now().Format("2006-01-02 15:04") + ": " + notes
		} else {
			updates["notes"] = time.Now().Format("2006-01-02 15:04") + ": " + notes
		}
	}

	if err := s.leadRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update follow-up: %w", err)
	}

	// Get updated lead
	updatedLead, err := s.leadRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated lead: %w", err)
	}

	return updatedLead.ToResponse(), nil
}
