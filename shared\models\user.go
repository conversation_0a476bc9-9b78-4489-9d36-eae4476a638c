package models

import (
	"time"

	"github.com/google/uuid"
)

// User represents a user in the system
type User struct {
	BaseModel
	Email       string     `json:"email" gorm:"uniqueIndex;not null"`
	Username    string     `json:"username" gorm:"uniqueIndex;not null"`
	Password    string     `json:"-" gorm:"not null"` // Hidden from JSON
	FirstName   string     `json:"first_name" gorm:"not null"`
	LastName    string     `json:"last_name" gorm:"not null"`
	Phone       string     `json:"phone" gorm:"uniqueIndex"`
	Role        UserRole   `json:"role" gorm:"not null"`
	Status      UserStatus `json:"status" gorm:"default:ACTIVE"`
	LastLoginAt *time.Time `json:"last_login_at"`
	Avatar      string     `json:"avatar"`
	
	// Profile information
	DateOfBirth *time.Time `json:"date_of_birth"`
	Address     string     `json:"address"`
	City        string     `json:"city"`
	Country     string     `json:"country"`
	
	// System fields
	EmailVerified   bool       `json:"email_verified" gorm:"default:false"`
	EmailVerifiedAt *time.Time `json:"email_verified_at"`
	PhoneVerified   bool       `json:"phone_verified" gorm:"default:false"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at"`
	
	// Security
	PasswordChangedAt *time.Time `json:"password_changed_at"`
	TwoFactorEnabled  bool       `json:"two_factor_enabled" gorm:"default:false"`
	TwoFactorSecret   string     `json:"-"` // Hidden from JSON
	
	// Metadata
	Metadata map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Relationships
	Sessions []Session `json:"sessions,omitempty" gorm:"foreignKey:UserID"`
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	return u.FirstName + " " + u.LastName
}

// IsActive checks if the user is active
func (u *User) IsActive() bool {
	return u.Status == StatusActive
}

// HasRole checks if the user has a specific role
func (u *User) HasRole(role UserRole) bool {
	return u.Role == role
}

// HasAnyRole checks if the user has any of the specified roles
func (u *User) HasAnyRole(roles ...UserRole) bool {
	for _, role := range roles {
		if u.Role == role {
			return true
		}
	}
	return false
}

// IsAdmin checks if the user is an admin
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// CanAccessAdminService checks if user can access admin service
func (u *User) CanAccessAdminService() bool {
	return u.HasAnyRole(RoleAdmin, RoleCashier)
}

// CanAccessStaffService checks if user can access staff service
func (u *User) CanAccessStaffService() bool {
	return u.HasAnyRole(RoleReception, RoleTeacher, RoleManager, RoleAcademicManager)
}

// Session represents a user session
type Session struct {
	BaseModel
	UserID       uuid.UUID `json:"user_id" gorm:"not null;index"`
	Token        string    `json:"-" gorm:"uniqueIndex;not null"` // Hidden from JSON
	RefreshToken string    `json:"-" gorm:"uniqueIndex;not null"` // Hidden from JSON
	ExpiresAt    time.Time `json:"expires_at" gorm:"not null"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	LastUsedAt   time.Time `json:"last_used_at" gorm:"autoUpdateTime"`
	
	// Relationships
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// IsExpired checks if the session is expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsValid checks if the session is valid (active and not expired)
func (s *Session) IsValid() bool {
	return s.IsActive && !s.IsExpired()
}

// UserCreateRequest represents a request to create a user
type UserCreateRequest struct {
	Email     string   `json:"email" binding:"required,email"`
	Username  string   `json:"username" binding:"required,min=3,max=50"`
	Password  string   `json:"password" binding:"required,min=8"`
	FirstName string   `json:"first_name" binding:"required,min=1,max=100"`
	LastName  string   `json:"last_name" binding:"required,min=1,max=100"`
	Phone     string   `json:"phone" binding:"required"`
	Role      UserRole `json:"role" binding:"required"`
	
	// Optional fields
	DateOfBirth *time.Time `json:"date_of_birth"`
	Address     string     `json:"address"`
	City        string     `json:"city"`
	Country     string     `json:"country"`
}

// UserUpdateRequest represents a request to update a user
type UserUpdateRequest struct {
	Email     *string    `json:"email" binding:"omitempty,email"`
	Username  *string    `json:"username" binding:"omitempty,min=3,max=50"`
	FirstName *string    `json:"first_name" binding:"omitempty,min=1,max=100"`
	LastName  *string    `json:"last_name" binding:"omitempty,min=1,max=100"`
	Phone     *string    `json:"phone"`
	Role      *UserRole  `json:"role"`
	Status    *UserStatus `json:"status"`
	
	// Optional fields
	DateOfBirth *time.Time `json:"date_of_birth"`
	Address     *string    `json:"address"`
	City        *string    `json:"city"`
	Country     *string    `json:"country"`
}

// UserResponse represents a user response (without sensitive data)
type UserResponse struct {
	ID              uuid.UUID  `json:"id"`
	Email           string     `json:"email"`
	Username        string     `json:"username"`
	FirstName       string     `json:"first_name"`
	LastName        string     `json:"last_name"`
	FullName        string     `json:"full_name"`
	Phone           string     `json:"phone"`
	Role            UserRole   `json:"role"`
	Status          UserStatus `json:"status"`
	LastLoginAt     *time.Time `json:"last_login_at"`
	Avatar          string     `json:"avatar"`
	DateOfBirth     *time.Time `json:"date_of_birth"`
	Address         string     `json:"address"`
	City            string     `json:"city"`
	Country         string     `json:"country"`
	EmailVerified   bool       `json:"email_verified"`
	EmailVerifiedAt *time.Time `json:"email_verified_at"`
	PhoneVerified   bool       `json:"phone_verified"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at"`
	TwoFactorEnabled bool      `json:"two_factor_enabled"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// ToResponse converts a User to UserResponse
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:               u.ID,
		Email:            u.Email,
		Username:         u.Username,
		FirstName:        u.FirstName,
		LastName:         u.LastName,
		FullName:         u.GetFullName(),
		Phone:            u.Phone,
		Role:             u.Role,
		Status:           u.Status,
		LastLoginAt:      u.LastLoginAt,
		Avatar:           u.Avatar,
		DateOfBirth:      u.DateOfBirth,
		Address:          u.Address,
		City:             u.City,
		Country:          u.Country,
		EmailVerified:    u.EmailVerified,
		EmailVerifiedAt:  u.EmailVerifiedAt,
		PhoneVerified:    u.PhoneVerified,
		PhoneVerifiedAt:  u.PhoneVerifiedAt,
		TwoFactorEnabled: u.TwoFactorEnabled,
		CreatedAt:        u.CreatedAt,
		UpdatedAt:        u.UpdatedAt,
	}
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	User         *UserResponse `json:"user"`
	AccessToken  string        `json:"access_token"`
	RefreshToken string        `json:"refresh_token"`
	ExpiresAt    time.Time     `json:"expires_at"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// ChangePasswordRequest represents a change password request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// ResetPasswordRequest represents a reset password request
type ResetPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ConfirmResetPasswordRequest represents a confirm reset password request
type ConfirmResetPasswordRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// UserListRequest represents a request to list users
type UserListRequest struct {
	PaginationRequest
	Role   *UserRole   `json:"role" form:"role"`
	Status *UserStatus `json:"status" form:"status"`
	Search string      `json:"search" form:"search"`
}

// UserListResponse represents a response for user list
type UserListResponse struct {
	Users      []*UserResponse     `json:"users"`
	Pagination *PaginationResponse `json:"pagination"`
}
