package database

import (
	"fmt"
	"log"
	"time"

	"staff-service/internal/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(databaseURL string) (*gorm.DB, error) {
	// Configure GORM logger
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// Open database connection
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established successfully")
	return db, nil
}

// RunMigrations runs database migrations
func RunMigrations(db *gorm.DB) error {
	log.Println("Running database migrations...")

	// Auto-migrate all models
	err := db.AutoMigrate(
		&models.Lead{},
		&models.Student{},
		&models.Course{},
		&models.Teacher{},
		&models.Enrollment{},
		&models.Schedule{},
		&models.Attendance{},
		&models.Grade{},
	)
	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	// Create indexes for better performance
	if err := createIndexes(db); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// createIndexes creates database indexes for better performance
func createIndexes(db *gorm.DB) error {
	// Lead indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_leads_source ON leads(source)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_leads_assigned_to_id ON leads(assigned_to_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_leads_next_follow_up_at ON leads(next_follow_up_at)").Error; err != nil {
		return err
	}

	// Student indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_students_email ON students(email)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_students_status ON students(status)").Error; err != nil {
		return err
	}

	// Course indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_courses_course_code ON courses(course_code)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_courses_status ON courses(status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_courses_level ON courses(level)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_courses_instructor_id ON courses(instructor_id)").Error; err != nil {
		return err
	}

	// Teacher indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_teachers_email ON teachers(email)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_teachers_teacher_id ON teachers(teacher_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers(user_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_teachers_status ON teachers(status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_teachers_department ON teachers(department)").Error; err != nil {
		return err
	}

	// Enrollment indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_enrollments_student_id ON enrollments(student_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_enrollments_course_id ON enrollments(course_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_enrollments_payment_status ON enrollments(payment_status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_enrollments_enrollment_date ON enrollments(enrollment_date)").Error; err != nil {
		return err
	}

	// Schedule indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_schedules_course_id ON schedules(course_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_schedules_teacher_id ON schedules(teacher_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_schedules_start_time ON schedules(start_time)").Error; err != nil {
		return err
	}

	// Attendance indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_attendances_student_id ON attendances(student_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_attendances_course_id ON attendances(course_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_attendances_schedule_id ON attendances(schedule_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_attendances_date ON attendances(date)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_attendances_status ON attendances(status)").Error; err != nil {
		return err
	}

	// Grade indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_grades_student_id ON grades(student_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_grades_course_id ON grades(course_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_grades_assessment_type ON grades(assessment_type)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_grades_assessment_date ON grades(assessment_date)").Error; err != nil {
		return err
	}

	// Composite indexes for common queries
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_enrollments_student_course ON enrollments(student_id, course_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_attendances_student_course_date ON attendances(student_id, course_id, date)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_grades_student_course ON grades(student_id, course_id)").Error; err != nil {
		return err
	}

	return nil
}

// Close closes the database connection
func Close(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Close(); err != nil {
		return fmt.Errorf("failed to close database connection: %w", err)
	}

	log.Println("Database connection closed")
	return nil
}

// HealthCheck checks if the database is healthy
func HealthCheck(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}
