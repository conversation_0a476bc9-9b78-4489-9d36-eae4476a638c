package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the payment service
type Config struct {
	Port        string
	Environment string
	DatabaseURL string
	JWTSecret   string
	
	// Redis configuration
	RedisURL string
	
	// Service URLs
	AuthServiceURL  string
	AdminServiceURL string
	StaffServiceURL string
	
	// Payment Gateway Configuration
	StripeSecretKey      string
	StripePublishableKey string
	StripeWebhookSecret  string
	
	PayPalClientID     string
	PayPalClientSecret string
	PayPalWebhookID    string
	PayPalMode         string // sandbox or live
	
	// Local Payment Gateways (Uzbekistan)
	UzCardMerchantID string
	UzCardSecretKey  string
	
	HumoMerchantID string
	HumoSecretKey  string
	
	PaymeMerchantID string
	PaymeSecretKey  string
	
	ClickMerchantID     string
	ClickSecretKey      string
	ClickServiceID      string
	ClickMerchantUserID string
	
	// Pagination defaults
	DefaultPageSize int
	MaxPageSize     int
	
	// Payment Configuration
	DefaultCurrency      string
	MaxPaymentAmount     float64
	PaymentTimeoutMinutes int
	RefundTimeoutDays    int
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	cfg := &Config{
		Port:        getEnv("PORT", "8084"),
		Environment: getEnv("ENVIRONMENT", "development"),
		DatabaseURL: getEnv("DATABASE_URL", "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"),
		JWTSecret:   getEnv("JWT_SECRET", "your-super-secret-jwt-key-change-this-in-production"),
		
		// Redis
		RedisURL: getEnv("REDIS_URL", "redis://localhost:6379"),
		
		// Service URLs
		AuthServiceURL:  getEnv("AUTH_SERVICE_URL", "http://localhost:8081"),
		AdminServiceURL: getEnv("ADMIN_SERVICE_URL", "http://localhost:8082"),
		StaffServiceURL: getEnv("STAFF_SERVICE_URL", "http://localhost:8083"),
		
		// Stripe Configuration
		StripeSecretKey:      getEnv("STRIPE_SECRET_KEY", ""),
		StripePublishableKey: getEnv("STRIPE_PUBLISHABLE_KEY", ""),
		StripeWebhookSecret:  getEnv("STRIPE_WEBHOOK_SECRET", ""),
		
		// PayPal Configuration
		PayPalClientID:     getEnv("PAYPAL_CLIENT_ID", ""),
		PayPalClientSecret: getEnv("PAYPAL_CLIENT_SECRET", ""),
		PayPalWebhookID:    getEnv("PAYPAL_WEBHOOK_ID", ""),
		PayPalMode:         getEnv("PAYPAL_MODE", "sandbox"),
		
		// UzCard Configuration
		UzCardMerchantID: getEnv("UZCARD_MERCHANT_ID", ""),
		UzCardSecretKey:  getEnv("UZCARD_SECRET_KEY", ""),
		
		// Humo Configuration
		HumoMerchantID: getEnv("HUMO_MERCHANT_ID", ""),
		HumoSecretKey:  getEnv("HUMO_SECRET_KEY", ""),
		
		// Payme Configuration
		PaymeMerchantID: getEnv("PAYME_MERCHANT_ID", ""),
		PaymeSecretKey:  getEnv("PAYME_SECRET_KEY", ""),
		
		// Click Configuration
		ClickMerchantID:     getEnv("CLICK_MERCHANT_ID", ""),
		ClickSecretKey:      getEnv("CLICK_SECRET_KEY", ""),
		ClickServiceID:      getEnv("CLICK_SERVICE_ID", ""),
		ClickMerchantUserID: getEnv("CLICK_MERCHANT_USER_ID", ""),
		
		// Pagination
		DefaultPageSize: getEnvAsInt("DEFAULT_PAGE_SIZE", 10),
		MaxPageSize:     getEnvAsInt("MAX_PAGE_SIZE", 100),
		
		// Payment Configuration
		DefaultCurrency:       getEnv("DEFAULT_CURRENCY", "USD"),
		MaxPaymentAmount:      getEnvAsFloat("MAX_PAYMENT_AMOUNT", 100000.0),
		PaymentTimeoutMinutes: getEnvAsInt("PAYMENT_TIMEOUT_MINUTES", 30),
		RefundTimeoutDays:     getEnvAsInt("REFUND_TIMEOUT_DAYS", 30),
	}

	return cfg, nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return fallback
}

// getEnvAsFloat gets an environment variable as float64 with a fallback value
func getEnvAsFloat(key string, fallback float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return fallback
}
