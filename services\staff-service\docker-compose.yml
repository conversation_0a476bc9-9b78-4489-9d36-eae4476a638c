version: '3.8'

services:
  staff-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      - JWT_SECRET=your-secret-key-change-in-production
      - GIN_MODE=release
      - LOG_LEVEL=info
      - ALLOWED_ORIGINS=*
      - ADMIN_SERVICE_URL=http://admin-service:8081
      - MAX_FILE_SIZE=10485760
      - UPLOAD_PATH=./uploads
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_RPS=100
    volumes:
      - ./uploads:/root/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - crm-network

  # Optional: Local PostgreSQL for development
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=staff_service_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - crm-network
    profiles:
      - dev

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - crm-network
    profiles:
      - dev

volumes:
  postgres_data:
  redis_data:

networks:
  crm-network:
    external: true
