import { useState, useEffect } from 'react'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  AcademicCapIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import {
  Student,
  StudentStatus,
  StudentCreateRequest,
  StudentUpdateRequest,
  StudentFilters,
  StudentStats
} from '@/types/student'

// Mock data for development
const mockStudents: Student[] = [
  {
    id: '1',
    student_id: 'STU001',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    full_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998901234567',
    date_of_birth: '2005-03-15',
    gender: 'MA<PERSON>',
    address: '123 Main St',
    city: 'Tashkent',
    country: 'Uzbekistan',
    status: 'ACTIVE',
    enrollment_date: '2024-01-15',
    parent_name: '<PERSON>',
    parent_phone: '+998901234568',
    parent_email: '<EMAIL>',
    emergency_contact: 'Bob Doe',
    emergency_phone: '+998901234569',
    grade_level: '10th Grade',
    previous_school: 'ABC High School',
    notes: 'Excellent student with strong math skills',
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
    created_by: 'admin'
  },
  {
    id: '2',
    student_id: 'STU002',
    first_name: 'Sarah',
    last_name: 'Smith',
    full_name: 'Sarah Smith',
    email: '<EMAIL>',
    phone: '+998901234570',
    date_of_birth: '2006-07-22',
    gender: 'FEMALE',
    address: '456 Oak Ave',
    city: 'Tashkent',
    country: 'Uzbekistan',
    status: 'ACTIVE',
    enrollment_date: '2024-02-01',
    parent_name: 'Mike Smith',
    parent_phone: '+998901234571',
    parent_email: '<EMAIL>',
    emergency_contact: 'Lisa Smith',
    emergency_phone: '+998901234572',
    grade_level: '9th Grade',
    previous_school: 'XYZ Middle School',
    notes: 'Great potential in science subjects',
    created_at: '2024-02-01T00:00:00Z',
    updated_at: '2024-02-01T00:00:00Z',
    created_by: 'staff'
  },
  {
    id: '3',
    student_id: 'STU003',
    first_name: 'Ahmed',
    last_name: 'Hassan',
    full_name: 'Ahmed Hassan',
    phone: '+998901234573',
    date_of_birth: '2004-11-10',
    gender: 'MALE',
    address: '789 Pine St',
    city: 'Samarkand',
    country: 'Uzbekistan',
    status: 'GRADUATED',
    enrollment_date: '2023-09-01',
    graduation_date: '2024-06-15',
    parent_name: 'Fatima Hassan',
    parent_phone: '+998901234574',
    emergency_contact: 'Omar Hassan',
    emergency_phone: '+998901234575',
    grade_level: '12th Grade',
    previous_school: 'DEF High School',
    notes: 'Graduated with honors',
    created_at: '2023-09-01T00:00:00Z',
    updated_at: '2024-06-15T00:00:00Z',
    created_by: 'admin'
  }
]

const mockStats: StudentStats = {
  total_students: 156,
  active_students: 142,
  new_enrollments_this_month: 23,
  graduated_students: 14,
  pending_payments: 8,
  total_revenue: 45600
}

const StudentsPage = () => {
  const [students, setStudents] = useState<Student[]>([])
  const [stats, setStats] = useState<StudentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<StudentFilters>({
    search: '',
    status: 'ALL',
    grade_level: 'ALL',
    enrollment_status: 'ALL',
    payment_status: 'ALL'
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)

  // Load students on component mount
  useEffect(() => {
    loadStudents()
    loadStats()
  }, [])

  const loadStudents = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setStudents(mockStudents)
    } catch (error) {
      console.error('Failed to load students:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  // Filter students based on search and filters
  const filteredStudents = students.filter(student => {
    const searchTerm = filters.search || '';
    const matchesSearch = !searchTerm ||
      student.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.student_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.phone.includes(searchTerm)

    const matchesStatus = !filters.status || filters.status === 'ALL' || student.status === filters.status
    const matchesGradeLevel = !filters.grade_level || filters.grade_level === 'ALL' || student.grade_level === filters.grade_level

    return matchesSearch && matchesStatus && matchesGradeLevel
  })

  const handleCreateStudent = async (studentData: StudentCreateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newStudent: Student = {
        id: Date.now().toString(),
        student_id: `STU${String(students.length + 1).padStart(3, '0')}`,
        ...studentData,
        full_name: `${studentData.first_name} ${studentData.last_name}`,
        status: 'ACTIVE',
        enrollment_date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: 'current_user'
      }

      setStudents(prev => [...prev, newStudent])
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create student:', error)
    }
  }

  const handleUpdateStudent = async (studentId: string, studentData: StudentUpdateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setStudents(prev => prev.map(student =>
        student.id === studentId
          ? {
              ...student,
              ...studentData,
              full_name: studentData.first_name && studentData.last_name
                ? `${studentData.first_name} ${studentData.last_name}`
                : student.full_name,
              updated_at: new Date().toISOString()
            }
          : student
      ))
      setShowEditModal(false)
      setSelectedStudent(null)
    } catch (error) {
      console.error('Failed to update student:', error)
    }
  }

  const handleDeleteStudent = async (studentId: string) => {
    if (!confirm('Are you sure you want to delete this student? This action cannot be undone.')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setStudents(prev => prev.filter(student => student.id !== studentId))
    } catch (error) {
      console.error('Failed to delete student:', error)
    }
  }

  const getStatusBadgeColor = (status: StudentStatus) => {
    const colors = {
      ACTIVE: 'bg-green-100 text-green-800',
      INACTIVE: 'bg-gray-100 text-gray-800',
      GRADUATED: 'bg-blue-100 text-blue-800',
      SUSPENDED: 'bg-red-100 text-red-800',
      TRANSFERRED: 'bg-yellow-100 text-yellow-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    return age
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Student Management</h1>
          <p className="text-gray-600 mt-1">
            Manage student records, enrollment, and academic progress
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Student
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <UserGroupIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Students</p>
                <p className="text-xl font-semibold text-gray-900">{stats.total_students}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <AcademicCapIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Students</p>
                <p className="text-xl font-semibold text-gray-900">{stats.active_students}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <PlusIcon className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">New This Month</p>
                <p className="text-xl font-semibold text-gray-900">{stats.new_enrollments_this_month}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <AcademicCapIcon className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Graduated</p>
                <p className="text-xl font-semibold text-gray-900">{stats.graduated_students}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <CurrencyDollarIcon className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Pending Payments</p>
                <p className="text-xl font-semibold text-gray-900">{stats.pending_payments}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ChartBarIcon className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-xl font-semibold text-gray-900">${stats.total_revenue.toLocaleString()}</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search students..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as StudentStatus | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="GRADUATED">Graduated</option>
            <option value="SUSPENDED">Suspended</option>
            <option value="TRANSFERRED">Transferred</option>
          </select>

          <select
            value={filters.grade_level}
            onChange={(e) => setFilters(prev => ({ ...prev, grade_level: e.target.value }))}
            className="input"
          >
            <option value="ALL">All Grades</option>
            <option value="9th Grade">9th Grade</option>
            <option value="10th Grade">10th Grade</option>
            <option value="11th Grade">11th Grade</option>
            <option value="12th Grade">12th Grade</option>
          </select>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4" />
            {filteredStudents.length} of {students.length} students
          </div>
        </div>
      </div>

      {/* Students Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-head">Student</th>
                <th className="table-head">Student ID</th>
                <th className="table-head">Grade</th>
                <th className="table-head">Status</th>
                <th className="table-head">Parent Contact</th>
                <th className="table-head">Enrollment Date</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredStudents.length === 0 ? (
                <tr>
                  <td colSpan={7} className="table-cell text-center py-8 text-gray-500">
                    No students found matching your criteria
                  </td>
                </tr>
              ) : (
                filteredStudents.map((student) => (
                  <tr key={student.id} className="table-row">
                    <td className="table-cell">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-primary-600 font-medium text-sm">
                            {student.first_name[0]}{student.last_name[0]}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{student.full_name}</div>
                          <div className="text-sm text-gray-500">{student.email || 'No email'}</div>
                          <div className="text-xs text-gray-400">Age: {calculateAge(student.date_of_birth)}</div>
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="font-mono text-sm font-medium text-gray-900">
                        {student.student_id}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {student.grade_level || 'Not specified'}
                      </div>
                    </td>
                    <td className="table-cell">
                      <Badge className={getStatusBadgeColor(student.status)}>
                        {student.status}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm">
                        <div className="text-gray-900">{student.parent_name}</div>
                        <div className="text-gray-500">{student.parent_phone}</div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {formatDate(student.enrollment_date)}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedStudent(student)
                            setShowViewModal(true)
                          }}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedStudent(student)
                            setShowEditModal(true)
                          }}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteStudent(student.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <StudentCreateModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateStudent}
        />
      )}

      {showEditModal && selectedStudent && (
        <StudentEditModal
          student={selectedStudent}
          onClose={() => {
            setShowEditModal(false)
            setSelectedStudent(null)
          }}
          onSubmit={(studentData) => handleUpdateStudent(selectedStudent.id, studentData)}
        />
      )}

      {showViewModal && selectedStudent && (
        <StudentViewModal
          student={selectedStudent}
          onClose={() => {
            setShowViewModal(false)
            setSelectedStudent(null)
          }}
        />
      )}
    </div>
  )
}

// Modal Components (placeholder implementations)
const StudentCreateModal = ({ onClose }: {
  onClose: () => void
  onSubmit?: (data: StudentCreateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Add New Student</h2>
        <p className="text-gray-600 mb-4">Student registration form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Add Student</Button>
        </div>
      </div>
    </div>
  )
}

const StudentEditModal = ({ student, onClose }: {
  student: Student
  onClose: () => void
  onSubmit?: (data: StudentUpdateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Edit Student: {student.full_name}</h2>
        <p className="text-gray-600 mb-4">Student editing form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Save Changes</Button>
        </div>
      </div>
    </div>
  )
}

const StudentViewModal = ({ student, onClose }: {
  student: Student
  onClose: () => void
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Student Profile: {student.full_name}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Personal Information</h3>
            <div className="space-y-1">
              <p><strong>Student ID:</strong> {student.student_id}</p>
              <p><strong>Name:</strong> {student.full_name}</p>
              <p><strong>Email:</strong> {student.email || 'Not provided'}</p>
              <p><strong>Phone:</strong> {student.phone}</p>
              <p><strong>Date of Birth:</strong> {new Date(student.date_of_birth).toLocaleDateString()}</p>
              <p><strong>Gender:</strong> {student.gender}</p>
              <p><strong>Address:</strong> {student.address}, {student.city}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Academic Information</h3>
            <div className="space-y-1">
              <p><strong>Status:</strong> {student.status}</p>
              <p><strong>Grade Level:</strong> {student.grade_level || 'Not specified'}</p>
              <p><strong>Enrollment Date:</strong> {new Date(student.enrollment_date).toLocaleDateString()}</p>
              {student.graduation_date && (
                <p><strong>Graduation Date:</strong> {new Date(student.graduation_date).toLocaleDateString()}</p>
              )}
              <p><strong>Previous School:</strong> {student.previous_school || 'Not specified'}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Parent/Guardian</h3>
            <div className="space-y-1">
              <p><strong>Name:</strong> {student.parent_name}</p>
              <p><strong>Phone:</strong> {student.parent_phone}</p>
              <p><strong>Email:</strong> {student.parent_email || 'Not provided'}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Emergency Contact</h3>
            <div className="space-y-1">
              <p><strong>Name:</strong> {student.emergency_contact}</p>
              <p><strong>Phone:</strong> {student.emergency_phone}</p>
            </div>
          </div>
          {student.notes && (
            <div className="md:col-span-2">
              <h3 className="font-medium text-gray-900 mb-2">Notes</h3>
              <p className="text-gray-600">{student.notes}</p>
            </div>
          )}
        </div>
        <div className="mt-6">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}

export default StudentsPage
