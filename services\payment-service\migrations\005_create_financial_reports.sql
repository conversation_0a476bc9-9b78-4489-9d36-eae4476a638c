-- Create financial_reports table
CREATE TABLE IF NOT EXISTS financial_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Report details
    title VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    period VARCHAR(20) NOT NULL,
    
    -- Date range
    start_date TIMES<PERSON>MP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Report data
    total_revenue DECIMAL(12,2) DEFAULT 0 CHECK (total_revenue >= 0),
    total_refunds DECIMAL(12,2) DEFAULT 0 CHECK (total_refunds >= 0),
    net_revenue DECIMAL(12,2) DEFAULT 0,
    total_fees DECIMAL(12,2) DEFAULT 0 CHECK (total_fees >= 0),
    
    -- Transaction counts
    total_payments INTEGER DEFAULT 0 CHECK (total_payments >= 0),
    successful_payments INTEGER DEFAULT 0 CHECK (successful_payments >= 0),
    failed_payments INTEGER DEFAULT 0 CHECK (failed_payments >= 0),
    refund_count INTEGER DEFAULT 0 CHECK (refund_count >= 0),
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    generated_at TIMESTAMP WITH TIME ZONE,
    generated_by_id UUID,
    
    -- File information
    file_path VARCHAR(500),
    file_size BIGINT DEFAULT 0 CHECK (file_size >= 0),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create reconciliation_records table
CREATE TABLE IF NOT EXISTS reconciliation_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Reconciliation details
    gateway_type VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    
    -- System records
    system_revenue DECIMAL(12,2) DEFAULT 0 CHECK (system_revenue >= 0),
    system_count INTEGER DEFAULT 0 CHECK (system_count >= 0),
    
    -- Gateway records
    gateway_revenue DECIMAL(12,2) DEFAULT 0 CHECK (gateway_revenue >= 0),
    gateway_count INTEGER DEFAULT 0 CHECK (gateway_count >= 0),
    
    -- Differences
    revenue_diff DECIMAL(12,2) DEFAULT 0,
    count_diff INTEGER DEFAULT 0,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    reconciled_at TIMESTAMP WITH TIME ZONE,
    reconciled_by_id UUID,
    
    -- Notes
    notes TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for financial_reports table
CREATE INDEX IF NOT EXISTS idx_financial_reports_type ON financial_reports(type);
CREATE INDEX IF NOT EXISTS idx_financial_reports_period ON financial_reports(period);
CREATE INDEX IF NOT EXISTS idx_financial_reports_status ON financial_reports(status);
CREATE INDEX IF NOT EXISTS idx_financial_reports_start_date ON financial_reports(start_date);
CREATE INDEX IF NOT EXISTS idx_financial_reports_end_date ON financial_reports(end_date);
CREATE INDEX IF NOT EXISTS idx_financial_reports_generated_by_id ON financial_reports(generated_by_id);
CREATE INDEX IF NOT EXISTS idx_financial_reports_created_at ON financial_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_financial_reports_deleted_at ON financial_reports(deleted_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_financial_reports_type_period ON financial_reports(type, period);
CREATE INDEX IF NOT EXISTS idx_financial_reports_status_created ON financial_reports(status, created_at);
CREATE INDEX IF NOT EXISTS idx_financial_reports_date_range ON financial_reports(start_date, end_date);

-- Create indexes for reconciliation_records table
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_gateway_type ON reconciliation_records(gateway_type);
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_date ON reconciliation_records(date);
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_status ON reconciliation_records(status);
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_reconciled_by_id ON reconciliation_records(reconciled_by_id);
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_created_at ON reconciliation_records(created_at);
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_deleted_at ON reconciliation_records(deleted_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_gateway_date ON reconciliation_records(gateway_type, date);
CREATE INDEX IF NOT EXISTS idx_reconciliation_records_status_created ON reconciliation_records(status, created_at);

-- Add unique constraint for reconciliation records
ALTER TABLE reconciliation_records ADD CONSTRAINT uk_reconciliation_records_gateway_date 
UNIQUE (gateway_type, date);

-- Add check constraints for valid enum values
ALTER TABLE financial_reports ADD CONSTRAINT chk_financial_reports_type 
CHECK (type IN ('REVENUE', 'PAYMENTS', 'REFUNDS', 'GATEWAY', 'RECONCILIATION'));

ALTER TABLE financial_reports ADD CONSTRAINT chk_financial_reports_period 
CHECK (period IN ('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY', 'CUSTOM'));

ALTER TABLE financial_reports ADD CONSTRAINT chk_financial_reports_status 
CHECK (status IN ('PENDING', 'GENERATING', 'COMPLETED', 'FAILED'));

ALTER TABLE reconciliation_records ADD CONSTRAINT chk_reconciliation_records_status 
CHECK (status IN ('PENDING', 'MATCHED', 'MISMATCH', 'RESOLVED'));

-- Add comments for documentation
COMMENT ON TABLE financial_reports IS 'Financial reports and analytics';
COMMENT ON TABLE reconciliation_records IS 'Payment reconciliation records with gateways';
