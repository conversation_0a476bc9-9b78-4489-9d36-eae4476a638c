package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"notification-service/internal/config"
	"notification-service/internal/database"
	"notification-service/internal/models"
	"notification-service/internal/router"
	"notification-service/internal/services"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// APITestSuite defines the integration test suite for the API
type APITestSuite struct {
	suite.Suite
	app    *gin.Engine
	db     *gorm.DB
	config *config.Config
	token  string
}

// SetupSuite sets up the test suite
func (suite *APITestSuite) SetupSuite() {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)
	
	// Create in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)
	
	// Run migrations
	err = database.RunMigrations(db)
	suite.Require().NoError(err)
	
	// Create test config
	cfg := &config.Config{
		JWTSecret:            "test-secret",
		MaxRetries:           3,
		RetryDelaySeconds:    60,
		BatchSize:            100,
		QueueProcessInterval: 30,
		SMTPHost:             "localhost",
		SMTPPort:             587,
		SMTPUser:             "<EMAIL>",
		SMTPPassword:         "password",
		SMTPFrom:             "<EMAIL>",
	}
	
	// Initialize services
	notificationService := services.NewNotificationService(db, cfg)
	emailService := services.NewEmailService(cfg)
	smsService := services.NewSMSService(cfg)
	templateService := services.NewTemplateService(db)
	schedulerService := services.NewSchedulerService(db, cfg)
	analyticsService := services.NewAnalyticsService(db)
	
	// Setup router
	app := router.SetupRouter(
		notificationService,
		emailService,
		smsService,
		templateService,
		schedulerService,
		analyticsService,
		cfg,
	)
	
	suite.app = app
	suite.db = db
	suite.config = cfg
	
	// Generate test JWT token
	suite.token = suite.generateTestToken()
}

// TearDownSuite cleans up after the test suite
func (suite *APITestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// SetupTest sets up each test
func (suite *APITestSuite) SetupTest() {
	// Clean up tables before each test
	suite.db.Exec("DELETE FROM notifications")
	suite.db.Exec("DELETE FROM notification_templates")
	suite.db.Exec("DELETE FROM notification_preferences")
	suite.db.Exec("DELETE FROM delivery_logs")
	suite.db.Exec("DELETE FROM notification_queue")
}

// generateTestToken generates a test JWT token
func (suite *APITestSuite) generateTestToken() string {
	claims := jwt.MapClaims{
		"user_id": uuid.New().String(),
		"role":    "ADMIN",
		"email":   "<EMAIL>",
		"exp":     time.Now().Add(time.Hour).Unix(),
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, _ := token.SignedString([]byte(suite.config.JWTSecret))
	return tokenString
}

// makeRequest makes an HTTP request with authentication
func (suite *APITestSuite) makeRequest(method, path string, body interface{}) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		reqBody, _ = json.Marshal(body)
	}
	
	req := httptest.NewRequest(method, path, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.token)
	
	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)
	
	return w
}

// TestHealthCheck tests the health check endpoint
func (suite *APITestSuite) TestHealthCheck() {
	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)
	
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "healthy", response["status"])
	assert.Equal(suite.T(), "notification-service", response["service"])
}

// TestCreateNotification tests notification creation via API
func (suite *APITestSuite) TestCreateNotification() {
	payload := map[string]interface{}{
		"type":      "EMAIL",
		"recipient": "<EMAIL>",
		"subject":   "Test Subject",
		"message":   "Test Message",
		"source":    "test",
	}
	
	w := suite.makeRequest("POST", "/api/v1/notifications", payload)
	
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["data"])
	
	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "EMAIL", data["type"])
	assert.Equal(suite.T(), "<EMAIL>", data["recipient"])
	assert.Equal(suite.T(), "Test Subject", data["subject"])
	assert.Equal(suite.T(), "Test Message", data["message"])
}

// TestCreateNotificationInvalidData tests notification creation with invalid data
func (suite *APITestSuite) TestCreateNotificationInvalidData() {
	payload := map[string]interface{}{
		"type":      "EMAIL",
		"recipient": "", // Invalid: empty recipient
		"subject":   "Test Subject",
		"message":   "Test Message",
	}
	
	w := suite.makeRequest("POST", "/api/v1/notifications", payload)
	
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response["error"].(map[string]interface{})["message"], "Failed to create notification")
}

// TestGetNotifications tests notification listing via API
func (suite *APITestSuite) TestGetNotifications() {
	// Create test notifications
	notifications := []models.Notification{
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Subject:   "Test Subject 1",
			Message:   "Test Message 1",
			Status:    models.NotificationStatusPending,
		},
		{
			Type:      models.NotificationTypeSMS,
			Recipient: "+1234567890",
			Message:   "Test SMS Message",
			Status:    models.NotificationStatusSent,
		},
	}
	
	for i := range notifications {
		err := suite.db.Create(&notifications[i]).Error
		suite.Require().NoError(err)
	}
	
	w := suite.makeRequest("GET", "/api/v1/notifications", nil)
	
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	
	data := response["data"].(map[string]interface{})
	notificationsList := data["notifications"].([]interface{})
	assert.Len(suite.T(), notificationsList, 2)
	
	pagination := data["pagination"].(map[string]interface{})
	assert.Equal(suite.T(), float64(2), pagination["total"])
}

// TestGetNotificationsWithFilters tests notification listing with filters
func (suite *APITestSuite) TestGetNotificationsWithFilters() {
	// Create test notifications
	notifications := []models.Notification{
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Subject:   "Test Subject 1",
			Message:   "Test Message 1",
			Status:    models.NotificationStatusPending,
		},
		{
			Type:      models.NotificationTypeSMS,
			Recipient: "+1234567890",
			Message:   "Test SMS Message",
			Status:    models.NotificationStatusSent,
		},
	}
	
	for i := range notifications {
		err := suite.db.Create(&notifications[i]).Error
		suite.Require().NoError(err)
	}
	
	// Test with type filter
	w := suite.makeRequest("GET", "/api/v1/notifications?type=EMAIL", nil)
	
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	
	data := response["data"].(map[string]interface{})
	notificationsList := data["notifications"].([]interface{})
	assert.Len(suite.T(), notificationsList, 1)
	
	notification := notificationsList[0].(map[string]interface{})
	assert.Equal(suite.T(), "EMAIL", notification["type"])
}

// TestCreateTemplate tests template creation via API
func (suite *APITestSuite) TestCreateTemplate() {
	payload := map[string]interface{}{
		"name":        "Welcome Email",
		"type":        "EMAIL",
		"subject":     "Welcome to {{company_name}}!",
		"body":        "Hello {{user_name}}, welcome to our platform!",
		"html_body":   "<h1>Hello {{user_name}}</h1><p>Welcome to our platform!</p>",
		"description": "Welcome email template for new users",
		"variables":   []string{"company_name", "user_name"},
		"is_active":   true,
		"is_default":  false,
	}
	
	w := suite.makeRequest("POST", "/api/v1/templates", payload)
	
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["data"])
	
	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "Welcome Email", data["name"])
	assert.Equal(suite.T(), "EMAIL", data["type"])
	assert.Equal(suite.T(), "Welcome to {{company_name}}!", data["subject"])
}

// TestGetTemplates tests template listing via API
func (suite *APITestSuite) TestGetTemplates() {
	// Create test templates
	createdByID := uuid.New()
	templates := []models.NotificationTemplate{
		{
			Name:        "Email Template 1",
			Type:        models.NotificationTypeEmail,
			Body:        "Email body 1",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
		{
			Name:        "SMS Template 1",
			Type:        models.NotificationTypeSMS,
			Body:        "SMS body 1",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
	}
	
	for i := range templates {
		err := suite.db.Create(&templates[i]).Error
		suite.Require().NoError(err)
	}
	
	w := suite.makeRequest("GET", "/api/v1/templates", nil)
	
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	
	data := response["data"].(map[string]interface{})
	templatesList := data["templates"].([]interface{})
	assert.Len(suite.T(), templatesList, 2)
}

// TestScheduleNotification tests notification scheduling via API
func (suite *APITestSuite) TestScheduleNotification() {
	scheduledTime := time.Now().Add(1 * time.Hour)
	
	payload := map[string]interface{}{
		"type":         "EMAIL",
		"recipient":    "<EMAIL>",
		"subject":      "Scheduled Test",
		"message":      "This is a scheduled notification",
		"scheduled_at": scheduledTime.Format(time.RFC3339),
	}
	
	w := suite.makeRequest("POST", "/api/v1/schedule/notification", payload)
	
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["data"])
	
	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "EMAIL", data["type"])
	assert.Equal(suite.T(), "<EMAIL>", data["recipient"])
	assert.NotNil(suite.T(), data["scheduled_at"])
}

// TestGetAnalytics tests analytics endpoint
func (suite *APITestSuite) TestGetAnalytics() {
	// Create test notifications for analytics
	now := time.Now()
	notifications := []models.Notification{
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Message:   "Test Message 1",
			Status:    models.NotificationStatusDelivered,
			CreatedAt: now,
		},
		{
			Type:      models.NotificationTypeSMS,
			Recipient: "+1234567890",
			Message:   "Test SMS Message",
			Status:    models.NotificationStatusSent,
			CreatedAt: now,
		},
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Message:   "Test Message 2",
			Status:    models.NotificationStatusFailed,
			CreatedAt: now,
		},
	}
	
	for i := range notifications {
		err := suite.db.Create(&notifications[i]).Error
		suite.Require().NoError(err)
	}
	
	w := suite.makeRequest("GET", "/api/v1/analytics/notifications", nil)
	
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["data"])
	
	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), float64(3), data["total_notifications"])
	assert.Equal(suite.T(), float64(1), data["delivered_notifications"])
	assert.Equal(suite.T(), float64(1), data["sent_notifications"])
	assert.Equal(suite.T(), float64(1), data["failed_notifications"])
}

// TestUnauthorizedAccess tests API access without authentication
func (suite *APITestSuite) TestUnauthorizedAccess() {
	req := httptest.NewRequest("GET", "/api/v1/notifications", nil)
	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)
	
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response["error"].(map[string]interface{})["message"], "Authorization header is required")
}

// TestSuite runs the API integration test suite
func TestAPIIntegrationSuite(t *testing.T) {
	suite.Run(t, new(APITestSuite))
}
