package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AttendanceRepository handles attendance data operations
type AttendanceRepository interface {
	GetAll(req *models.AttendanceListRequest) ([]*models.Attendance, int64, error)
	GetByID(id uuid.UUID) (*models.Attendance, error)
	Create(attendance *models.Attendance) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.AttendanceStats, error)
	GetByStudent(studentID uuid.UUID) ([]*models.Attendance, error)
	GetByCourse(courseID uuid.UUID) ([]*models.Attendance, error)
	GetBySchedule(scheduleID uuid.UUID) ([]*models.Attendance, error)
	GetByDateRange(startDate, endDate time.Time) ([]*models.Attendance, error)
	GetStudentAttendanceSummary(studentID, courseID uuid.UUID) (*models.StudentAttendanceSummary, error)
	GetCourseAttendanceSummary(courseID uuid.UUID) (*models.CourseAttendanceSummary, error)
	BulkCreate(attendances []*models.Attendance) error
	BulkUpdateStatus(attendanceIDs []uuid.UUID, status models.AttendanceStatus, updatedBy uuid.UUID) error
	GetAttendanceRate(studentID, courseID uuid.UUID) (float64, error)
	GetAbsentStudents(courseID uuid.UUID, date time.Time) ([]*models.Attendance, error)
}

type attendanceRepository struct {
	db *gorm.DB
}

// NewAttendanceRepository creates a new attendance repository
func NewAttendanceRepository(db *gorm.DB) AttendanceRepository {
	return &attendanceRepository{db: db}
}

// GetAll retrieves attendance records with pagination and filtering
func (r *attendanceRepository) GetAll(req *models.AttendanceListRequest) ([]*models.Attendance, int64, error) {
	var attendances []*models.Attendance
	var total int64

	query := r.db.Model(&models.Attendance{}).Preload("Student").Preload("Course").Preload("Schedule").Preload("RecordedBy")

	// Apply filters
	if req.StudentID != nil {
		query = query.Where("student_id = ?", *req.StudentID)
	}

	if req.CourseID != nil {
		query = query.Where("course_id = ?", *req.CourseID)
	}

	if req.ScheduleID != nil {
		query = query.Where("schedule_id = ?", *req.ScheduleID)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.RecordedByID != nil {
		query = query.Where("recorded_by_id = ?", *req.RecordedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Joins("LEFT JOIN students ON students.id = attendances.student_id").
			Joins("LEFT JOIN courses ON courses.id = attendances.course_id").
			Where(
				"LOWER(students.first_name) LIKE ? OR LOWER(students.last_name) LIKE ? OR LOWER(courses.name) LIKE ? OR LOWER(courses.course_code) LIKE ?",
				searchTerm, searchTerm, searchTerm, searchTerm,
			)
	}

	if req.StartDate != nil {
		query = query.Where("date >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("date <= ?", *req.EndDate)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count attendance records: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("date DESC, recorded_at DESC").Find(&attendances).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get attendance records: %w", err)
	}

	return attendances, total, nil
}

// GetByID retrieves an attendance record by ID
func (r *attendanceRepository) GetByID(id uuid.UUID) (*models.Attendance, error) {
	var attendance models.Attendance
	if err := r.db.Preload("Student").Preload("Course").Preload("Schedule").Preload("RecordedBy").
		First(&attendance, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("attendance record not found")
		}
		return nil, fmt.Errorf("failed to get attendance record: %w", err)
	}
	return &attendance, nil
}

// Create creates a new attendance record
func (r *attendanceRepository) Create(attendance *models.Attendance) error {
	if err := r.db.Create(attendance).Error; err != nil {
		return fmt.Errorf("failed to create attendance record: %w", err)
	}
	return nil
}

// Update updates an attendance record
func (r *attendanceRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Attendance{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update attendance record: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("attendance record not found")
	}
	return nil
}

// Delete soft deletes an attendance record
func (r *attendanceRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Attendance{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete attendance record: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("attendance record not found")
	}
	return nil
}

// GetStats retrieves attendance statistics
func (r *attendanceRepository) GetStats() (*models.AttendanceStats, error) {
	stats := &models.AttendanceStats{
		AttendanceByStatus: make(map[models.AttendanceStatus]int64),
	}

	// Total records
	if err := r.db.Model(&models.Attendance{}).Count(&stats.TotalRecords).Error; err != nil {
		return nil, fmt.Errorf("failed to count total attendance records: %w", err)
	}

	// Attendance by status
	statuses := []models.AttendanceStatus{
		models.AttendanceStatusPresent, models.AttendanceStatusAbsent,
		models.AttendanceStatusLate, models.AttendanceStatusExcused,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Attendance{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count attendance by status %s: %w", status, err)
		}
		stats.AttendanceByStatus[status] = count
	}

	// Calculate individual counts
	stats.PresentCount = stats.AttendanceByStatus[models.AttendanceStatusPresent]
	stats.AbsentCount = stats.AttendanceByStatus[models.AttendanceStatusAbsent]
	stats.LateCount = stats.AttendanceByStatus[models.AttendanceStatusLate]
	stats.ExcusedCount = stats.AttendanceByStatus[models.AttendanceStatusExcused]

	// Calculate overall attendance rate
	if stats.TotalRecords > 0 {
		presentAndLate := stats.PresentCount + stats.LateCount
		stats.OverallAttendanceRate = float64(presentAndLate) / float64(stats.TotalRecords) * 100
	}

	return stats, nil
}

// GetByStudent retrieves attendance records for a specific student
func (r *attendanceRepository) GetByStudent(studentID uuid.UUID) ([]*models.Attendance, error) {
	var attendances []*models.Attendance
	if err := r.db.Preload("Course").Preload("Schedule").Preload("RecordedBy").
		Where("student_id = ?", studentID).
		Order("date DESC").Find(&attendances).Error; err != nil {
		return nil, fmt.Errorf("failed to get attendance by student: %w", err)
	}
	return attendances, nil
}

// GetByCourse retrieves attendance records for a specific course
func (r *attendanceRepository) GetByCourse(courseID uuid.UUID) ([]*models.Attendance, error) {
	var attendances []*models.Attendance
	if err := r.db.Preload("Student").Preload("Schedule").Preload("RecordedBy").
		Where("course_id = ?", courseID).
		Order("date DESC").Find(&attendances).Error; err != nil {
		return nil, fmt.Errorf("failed to get attendance by course: %w", err)
	}
	return attendances, nil
}

// GetBySchedule retrieves attendance records for a specific schedule
func (r *attendanceRepository) GetBySchedule(scheduleID uuid.UUID) ([]*models.Attendance, error) {
	var attendances []*models.Attendance
	if err := r.db.Preload("Student").Preload("Course").Preload("RecordedBy").
		Where("schedule_id = ?", scheduleID).
		Order("date DESC").Find(&attendances).Error; err != nil {
		return nil, fmt.Errorf("failed to get attendance by schedule: %w", err)
	}
	return attendances, nil
}

// GetByDateRange retrieves attendance records within a date range
func (r *attendanceRepository) GetByDateRange(startDate, endDate time.Time) ([]*models.Attendance, error) {
	var attendances []*models.Attendance
	if err := r.db.Preload("Student").Preload("Course").Preload("Schedule").Preload("RecordedBy").
		Where("date >= ? AND date <= ?", startDate, endDate).
		Order("date DESC").Find(&attendances).Error; err != nil {
		return nil, fmt.Errorf("failed to get attendance by date range: %w", err)
	}
	return attendances, nil
}

// GetStudentAttendanceSummary retrieves attendance summary for a student in a course
func (r *attendanceRepository) GetStudentAttendanceSummary(studentID, courseID uuid.UUID) (*models.StudentAttendanceSummary, error) {
	// Get student and course information
	var student models.Student
	if err := r.db.First(&student, "id = ?", studentID).Error; err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	var course models.Course
	if err := r.db.First(&course, "id = ?", courseID).Error; err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	summary := &models.StudentAttendanceSummary{
		StudentID:   studentID,
		StudentName: student.GetFullName(),
		CourseID:    courseID,
		CourseName:  course.Name,
	}

	// Count total classes
	if err := r.db.Model(&models.Attendance{}).
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Count(&summary.TotalClasses).Error; err != nil {
		return nil, fmt.Errorf("failed to count total classes: %w", err)
	}

	// Count by status
	statuses := []models.AttendanceStatus{
		models.AttendanceStatusPresent, models.AttendanceStatusAbsent,
		models.AttendanceStatusLate, models.AttendanceStatusExcused,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Attendance{}).
			Where("student_id = ? AND course_id = ? AND status = ?", studentID, courseID, status).
			Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count attendance by status %s: %w", status, err)
		}

		switch status {
		case models.AttendanceStatusPresent:
			summary.PresentCount = count
		case models.AttendanceStatusAbsent:
			summary.AbsentCount = count
		case models.AttendanceStatusLate:
			summary.LateCount = count
		case models.AttendanceStatusExcused:
			summary.ExcusedCount = count
		}
	}

	// Calculate attendance rate
	if summary.TotalClasses > 0 {
		presentAndLate := summary.PresentCount + summary.LateCount
		summary.AttendanceRate = float64(presentAndLate) / float64(summary.TotalClasses) * 100
	}

	// Get last attendance date
	var lastAttendance models.Attendance
	if err := r.db.Where("student_id = ? AND course_id = ?", studentID, courseID).
		Order("date DESC").First(&lastAttendance).Error; err == nil {
		summary.LastAttendance = &lastAttendance.Date
	}

	return summary, nil
}

// GetCourseAttendanceSummary retrieves attendance summary for a course
func (r *attendanceRepository) GetCourseAttendanceSummary(courseID uuid.UUID) (*models.CourseAttendanceSummary, error) {
	// Get course information
	var course models.Course
	if err := r.db.First(&course, "id = ?", courseID).Error; err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	summary := &models.CourseAttendanceSummary{
		CourseID:   courseID,
		CourseName: course.Name,
	}

	// Count total students enrolled in the course
	if err := r.db.Model(&models.Enrollment{}).
		Where("course_id = ? AND status = ?", courseID, models.EnrollmentStatusActive).
		Count(&summary.TotalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count total students: %w", err)
	}

	// Count total classes
	if err := r.db.Model(&models.Attendance{}).
		Where("course_id = ?", courseID).
		Count(&summary.TotalClasses).Error; err != nil {
		return nil, fmt.Errorf("failed to count total classes: %w", err)
	}

	// Calculate average attendance rate
	var avgAttendance float64
	if err := r.db.Raw(`
		SELECT AVG(attendance_rate) FROM (
			SELECT
				student_id,
				(COUNT(CASE WHEN status IN ('PRESENT', 'LATE') THEN 1 END) * 100.0 / COUNT(*)) as attendance_rate
			FROM attendances
			WHERE course_id = ?
			GROUP BY student_id
		) as student_rates
	`, courseID).Scan(&avgAttendance).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average attendance: %w", err)
	}
	summary.AverageAttendance = avgAttendance

	// Calculate highest and lowest attendance rates
	var highestAttendance, lowestAttendance float64
	if err := r.db.Raw(`
		SELECT
			MAX(attendance_rate) as highest,
			MIN(attendance_rate) as lowest
		FROM (
			SELECT
				student_id,
				(COUNT(CASE WHEN status IN ('PRESENT', 'LATE') THEN 1 END) * 100.0 / COUNT(*)) as attendance_rate
			FROM attendances
			WHERE course_id = ?
			GROUP BY student_id
		) as student_rates
	`, courseID).Row().Scan(&highestAttendance, &lowestAttendance); err != nil {
		return nil, fmt.Errorf("failed to calculate attendance range: %w", err)
	}
	summary.HighestAttendance = highestAttendance
	summary.LowestAttendance = lowestAttendance

	// Determine trend direction (simplified - comparing last 2 weeks vs previous 2 weeks)
	now := time.Now()
	twoWeeksAgo := now.AddDate(0, 0, -14)
	fourWeeksAgo := now.AddDate(0, 0, -28)

	var recentRate, previousRate float64

	// Recent 2 weeks
	if err := r.db.Raw(`
		SELECT
			(COUNT(CASE WHEN status IN ('PRESENT', 'LATE') THEN 1 END) * 100.0 / COUNT(*)) as rate
		FROM attendances
		WHERE course_id = ? AND date >= ?
	`, courseID, twoWeeksAgo).Scan(&recentRate).Error; err != nil {
		recentRate = 0
	}

	// Previous 2 weeks
	if err := r.db.Raw(`
		SELECT
			(COUNT(CASE WHEN status IN ('PRESENT', 'LATE') THEN 1 END) * 100.0 / COUNT(*)) as rate
		FROM attendances
		WHERE course_id = ? AND date >= ? AND date < ?
	`, courseID, fourWeeksAgo, twoWeeksAgo).Scan(&previousRate).Error; err != nil {
		previousRate = 0
	}

	// Determine trend
	if recentRate > previousRate+2 {
		summary.TrendDirection = "IMPROVING"
	} else if recentRate < previousRate-2 {
		summary.TrendDirection = "DECLINING"
	} else {
		summary.TrendDirection = "STABLE"
	}

	return summary, nil
}

// BulkCreate creates multiple attendance records
func (r *attendanceRepository) BulkCreate(attendances []*models.Attendance) error {
	if len(attendances) == 0 {
		return nil
	}

	if err := r.db.Create(&attendances).Error; err != nil {
		return fmt.Errorf("failed to bulk create attendance records: %w", err)
	}
	return nil
}

// BulkUpdateStatus updates status for multiple attendance records
func (r *attendanceRepository) BulkUpdateStatus(attendanceIDs []uuid.UUID, status models.AttendanceStatus, updatedBy uuid.UUID) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	result := r.db.Model(&models.Attendance{}).Where("id IN ?", attendanceIDs).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to bulk update attendance status: %w", result.Error)
	}

	return nil
}

// GetAttendanceRate calculates attendance rate for a student in a course
func (r *attendanceRepository) GetAttendanceRate(studentID, courseID uuid.UUID) (float64, error) {
	var totalClasses, presentClasses int64

	// Count total classes
	if err := r.db.Model(&models.Attendance{}).
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		Count(&totalClasses).Error; err != nil {
		return 0, fmt.Errorf("failed to count total classes: %w", err)
	}

	// Count present classes (including late)
	if err := r.db.Model(&models.Attendance{}).
		Where("student_id = ? AND course_id = ? AND status IN ?",
			studentID, courseID, []models.AttendanceStatus{models.AttendanceStatusPresent, models.AttendanceStatusLate}).
		Count(&presentClasses).Error; err != nil {
		return 0, fmt.Errorf("failed to count present classes: %w", err)
	}

	if totalClasses == 0 {
		return 0, nil
	}

	return float64(presentClasses) / float64(totalClasses) * 100, nil
}

// GetAbsentStudents retrieves students who were absent on a specific date for a course
func (r *attendanceRepository) GetAbsentStudents(courseID uuid.UUID, date time.Time) ([]*models.Attendance, error) {
	var attendances []*models.Attendance
	if err := r.db.Preload("Student").Preload("Course").Preload("RecordedBy").
		Where("course_id = ? AND date = ? AND status = ?", courseID, date, models.AttendanceStatusAbsent).
		Order("student_id").Find(&attendances).Error; err != nil {
		return nil, fmt.Errorf("failed to get absent students: %w", err)
	}
	return attendances, nil
}
