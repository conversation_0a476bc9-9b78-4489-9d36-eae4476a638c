package services

import (
	"fmt"
	"log"
	"notification-service/internal/config"
	"notification-service/internal/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SchedulerService handles notification scheduling operations
type SchedulerService struct {
	db     *gorm.DB
	config *config.Config
}

// NewSchedulerService creates a new scheduler service
func NewSchedulerService(db *gorm.DB, config *config.Config) *SchedulerService {
	return &SchedulerService{
		db:     db,
		config: config,
	}
}

// ScheduleNotification schedules a notification for future delivery
func (s *SchedulerService) ScheduleNotification(req *models.CreateNotificationRequest, scheduledAt time.Time) (*models.Notification, error) {
	// Set the scheduled time
	req.ScheduledAt = &scheduledAt
	
	// Create the notification
	notification := req.ToNotification()
	
	// Validate notification
	if !notification.IsValid() {
		return nil, fmt.<PERSON><PERSON>rf("invalid notification data")
	}
	
	// Create notification in database
	if err := s.db.Create(notification).Error; err != nil {
		return nil, fmt.Errorf("failed to create scheduled notification: %w", err)
	}
	
	// Add to queue with scheduled time
	queueItem := &models.NotificationQueue{
		NotificationID: notification.ID,
		Priority:       models.GetPriorityFromNotification(notification),
		ScheduledAt:    scheduledAt,
		MaxRetries:     notification.MaxRetries,
		Status:         models.QueueStatusPending,
	}
	
	if err := s.db.Create(queueItem).Error; err != nil {
		return nil, fmt.Errorf("failed to add scheduled notification to queue: %w", err)
	}
	
	log.Printf("Notification scheduled for %s: %s", scheduledAt.Format(time.RFC3339), notification.ID)
	return notification, nil
}

// ScheduleRecurringNotification creates a recurring notification schedule
func (s *SchedulerService) ScheduleRecurringNotification(req *models.CreateNotificationRequest, schedule *RecurringSchedule) error {
	// Validate schedule
	if err := schedule.Validate(); err != nil {
		return fmt.Errorf("invalid recurring schedule: %w", err)
	}
	
	// Generate notification instances for the next period
	instances := schedule.GenerateInstances(time.Now(), schedule.EndDate)
	
	for _, scheduledTime := range instances {
		// Create a copy of the request for each instance
		instanceReq := *req
		instanceReq.ScheduledAt = &scheduledTime
		
		// Add metadata to indicate this is part of a recurring schedule
		if instanceReq.Metadata == nil {
			instanceReq.Metadata = make(map[string]interface{})
		}
		instanceReq.Metadata["recurring_schedule_id"] = schedule.ID
		instanceReq.Metadata["recurring_type"] = schedule.Type
		instanceReq.Metadata["recurring_interval"] = schedule.Interval
		
		// Schedule the notification
		if _, err := s.ScheduleNotification(&instanceReq, scheduledTime); err != nil {
			log.Printf("Failed to schedule recurring notification instance for %s: %v", scheduledTime, err)
			continue
		}
	}
	
	// Save the recurring schedule
	if err := s.saveRecurringSchedule(schedule); err != nil {
		return fmt.Errorf("failed to save recurring schedule: %w", err)
	}
	
	log.Printf("Recurring notification scheduled: %d instances created", len(instances))
	return nil
}

// CancelScheduledNotification cancels a scheduled notification
func (s *SchedulerService) CancelScheduledNotification(notificationID uuid.UUID) error {
	// Update notification status
	err := s.db.Model(&models.Notification{}).
		Where("id = ? AND status = ?", notificationID, models.NotificationStatusPending).
		Update("status", models.NotificationStatusCancelled).Error
	
	if err != nil {
		return fmt.Errorf("failed to cancel notification: %w", err)
	}
	
	// Update queue item status
	err = s.db.Model(&models.NotificationQueue{}).
		Where("notification_id = ? AND status = ?", notificationID, models.QueueStatusPending).
		Update("status", models.QueueStatusCancelled).Error
	
	if err != nil {
		return fmt.Errorf("failed to cancel queue item: %w", err)
	}
	
	return nil
}

// GetScheduledNotifications retrieves scheduled notifications
func (s *SchedulerService) GetScheduledNotifications(filters map[string]interface{}, page, limit int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64
	
	query := s.db.Model(&models.Notification{}).
		Where("scheduled_at IS NOT NULL AND scheduled_at > ?", time.Now())
	
	// Apply filters
	if recipient, ok := filters["recipient"]; ok {
		query = query.Where("recipient = ?", recipient)
	}
	if notificationType, ok := filters["type"]; ok {
		query = query.Where("type = ?", notificationType)
	}
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	
	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count scheduled notifications: %w", err)
	}
	
	// Apply pagination
	offset := (page - 1) * limit
	err := query.Order("scheduled_at ASC").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error
	
	if err != nil {
		return nil, 0, fmt.Errorf("failed to retrieve scheduled notifications: %w", err)
	}
	
	return notifications, total, nil
}

// ProcessScheduledNotifications processes notifications that are ready to be sent
func (s *SchedulerService) ProcessScheduledNotifications() error {
	now := time.Now()
	
	// Find notifications that are ready to be processed
	var queueItems []models.NotificationQueue
	err := s.db.Preload("Notification").
		Where("status = ? AND scheduled_at <= ?", models.QueueStatusPending, now).
		Order("priority ASC, scheduled_at ASC").
		Limit(s.config.BatchSize).
		Find(&queueItems).Error
	
	if err != nil {
		return fmt.Errorf("failed to fetch scheduled notifications: %w", err)
	}
	
	log.Printf("Processing %d scheduled notifications", len(queueItems))
	
	for _, item := range queueItems {
		// Update scheduled_at to now for immediate processing
		item.ScheduledAt = now
		if err := s.db.Save(&item).Error; err != nil {
			log.Printf("Failed to update queue item schedule: %v", err)
			continue
		}
	}
	
	return nil
}

// RecurringSchedule represents a recurring notification schedule
type RecurringSchedule struct {
	ID        uuid.UUID              `json:"id"`
	Type      string                 `json:"type"`      // DAILY, WEEKLY, MONTHLY, YEARLY
	Interval  int                    `json:"interval"`  // Every N days/weeks/months/years
	DaysOfWeek []int                 `json:"days_of_week,omitempty"` // For weekly: 0=Sunday, 1=Monday, etc.
	DayOfMonth int                   `json:"day_of_month,omitempty"` // For monthly: 1-31
	TimeOfDay  string                `json:"time_of_day"` // HH:MM format
	StartDate  time.Time             `json:"start_date"`
	EndDate    *time.Time            `json:"end_date,omitempty"`
	Timezone   string                `json:"timezone"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time             `json:"created_at"`
}

// Validate validates the recurring schedule
func (rs *RecurringSchedule) Validate() error {
	if rs.Type == "" {
		return fmt.Errorf("schedule type is required")
	}
	
	validTypes := []string{"DAILY", "WEEKLY", "MONTHLY", "YEARLY"}
	isValidType := false
	for _, validType := range validTypes {
		if rs.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid schedule type: %s", rs.Type)
	}
	
	if rs.Interval <= 0 {
		return fmt.Errorf("interval must be positive")
	}
	
	if rs.TimeOfDay == "" {
		return fmt.Errorf("time of day is required")
	}
	
	// Validate time format
	if _, err := time.Parse("15:04", rs.TimeOfDay); err != nil {
		return fmt.Errorf("invalid time format, use HH:MM: %w", err)
	}
	
	if rs.StartDate.IsZero() {
		return fmt.Errorf("start date is required")
	}
	
	if rs.EndDate != nil && rs.EndDate.Before(rs.StartDate) {
		return fmt.Errorf("end date must be after start date")
	}
	
	return nil
}

// GenerateInstances generates notification instances for the schedule
func (rs *RecurringSchedule) GenerateInstances(from time.Time, until *time.Time) []time.Time {
	var instances []time.Time
	
	// Parse time of day
	timeOfDay, err := time.Parse("15:04", rs.TimeOfDay)
	if err != nil {
		return instances
	}
	
	// Load timezone
	loc, err := time.LoadLocation(rs.Timezone)
	if err != nil {
		loc = time.UTC
	}
	
	current := rs.StartDate
	if from.After(current) {
		current = from
	}
	
	// Set time of day
	current = time.Date(current.Year(), current.Month(), current.Day(), 
		timeOfDay.Hour(), timeOfDay.Minute(), 0, 0, loc)
	
	maxInstances := 100 // Prevent infinite loops
	count := 0
	
	for count < maxInstances {
		// Check if we've reached the end date
		if until != nil && current.After(*until) {
			break
		}
		if rs.EndDate != nil && current.After(*rs.EndDate) {
			break
		}
		
		// Check if this instance should be included
		if current.After(from) || current.Equal(from) {
			if rs.shouldIncludeInstance(current) {
				instances = append(instances, current)
			}
		}
		
		// Move to next instance
		current = rs.getNextInstance(current)
		count++
	}
	
	return instances
}

// shouldIncludeInstance checks if an instance should be included based on schedule rules
func (rs *RecurringSchedule) shouldIncludeInstance(t time.Time) bool {
	switch rs.Type {
	case "WEEKLY":
		if len(rs.DaysOfWeek) > 0 {
			weekday := int(t.Weekday())
			for _, day := range rs.DaysOfWeek {
				if day == weekday {
					return true
				}
			}
			return false
		}
		return true
	case "MONTHLY":
		if rs.DayOfMonth > 0 {
			return t.Day() == rs.DayOfMonth
		}
		return true
	default:
		return true
	}
}

// getNextInstance calculates the next instance time
func (rs *RecurringSchedule) getNextInstance(current time.Time) time.Time {
	switch rs.Type {
	case "DAILY":
		return current.AddDate(0, 0, rs.Interval)
	case "WEEKLY":
		return current.AddDate(0, 0, 7*rs.Interval)
	case "MONTHLY":
		return current.AddDate(0, rs.Interval, 0)
	case "YEARLY":
		return current.AddDate(rs.Interval, 0, 0)
	default:
		return current.AddDate(0, 0, 1)
	}
}

// saveRecurringSchedule saves a recurring schedule to the database
func (s *SchedulerService) saveRecurringSchedule(schedule *RecurringSchedule) error {
	// For now, we'll store this in the metadata of notifications
	// In a production system, you might want a dedicated table for recurring schedules
	log.Printf("Recurring schedule saved: %s", schedule.ID)
	return nil
}

// CreateReminderSchedule creates a reminder notification schedule
func (s *SchedulerService) CreateReminderSchedule(baseNotification *models.CreateNotificationRequest, reminderTimes []time.Duration) error {
	if baseNotification.ScheduledAt == nil {
		return fmt.Errorf("base notification must have a scheduled time for reminders")
	}
	
	baseTime := *baseNotification.ScheduledAt
	
	for i, reminderTime := range reminderTimes {
		reminderScheduledAt := baseTime.Add(-reminderTime)
		
		// Skip if reminder time is in the past
		if reminderScheduledAt.Before(time.Now()) {
			continue
		}
		
		// Create reminder notification
		reminderReq := *baseNotification
		reminderReq.Subject = fmt.Sprintf("Reminder: %s", baseNotification.Subject)
		reminderReq.Message = fmt.Sprintf("Reminder: %s", baseNotification.Message)
		reminderReq.ScheduledAt = &reminderScheduledAt
		
		// Add metadata to indicate this is a reminder
		if reminderReq.Metadata == nil {
			reminderReq.Metadata = make(map[string]interface{})
		}
		reminderReq.Metadata["is_reminder"] = true
		reminderReq.Metadata["reminder_index"] = i
		reminderReq.Metadata["original_scheduled_at"] = baseTime
		
		// Schedule the reminder
		if _, err := s.ScheduleNotification(&reminderReq, reminderScheduledAt); err != nil {
			log.Printf("Failed to schedule reminder %d: %v", i, err)
			continue
		}
	}
	
	return nil
}
