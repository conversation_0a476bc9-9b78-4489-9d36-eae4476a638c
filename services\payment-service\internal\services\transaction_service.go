package services

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"payment-service/internal/models"
	"payment-service/internal/repository"
	sharedModels "github.com/crm-microservices/shared/models"
)

// TransactionService handles transaction business logic
type TransactionService interface {
	GetTransactions(req *repository.TransactionListRequest) (*TransactionListResponse, error)
	GetTransaction(id uuid.UUID) (*TransactionResponse, error)
	GetTransactionsByPayment(paymentID uuid.UUID) (*TransactionListResponse, error)
	CreateTransaction(req *TransactionCreateRequest) (*TransactionResponse, error)
	UpdateTransaction(id uuid.UUID, req *TransactionUpdateRequest) (*TransactionResponse, error)
	RetryTransaction(id uuid.UUID) (*TransactionResponse, error)
	GetTransactionStats() (*repository.TransactionStats, error)
	GetFailedTransactions() ([]*models.Transaction, error)
}

type transactionService struct {
	transactionRepo repository.TransactionRepository
	paymentRepo     repository.PaymentRepository
}

// NewTransactionService creates a new transaction service
func NewTransactionService(transactionRepo repository.TransactionRepository) TransactionService {
	return &transactionService{
		transactionRepo: transactionRepo,
	}
}

// TransactionListResponse represents the response for transaction list
type TransactionListResponse struct {
	Transactions []*models.Transaction       `json:"transactions"`
	Pagination   *sharedModels.PaginationResponse  `json:"pagination"`
}

// TransactionResponse represents the response for a single transaction
type TransactionResponse struct {
	Transaction *models.Transaction `json:"transaction"`
}

// TransactionCreateRequest represents the request to create a transaction
type TransactionCreateRequest struct {
	PaymentID     uuid.UUID                  `json:"payment_id" binding:"required"`
	Amount        float64                    `json:"amount" binding:"required,gt=0"`
	Currency      string                     `json:"currency" binding:"required,len=3"`
	Type          models.TransactionType     `json:"type" binding:"required"`
	GatewayType   string                     `json:"gateway_type" binding:"required"`
	GatewayTxnID  string                     `json:"gateway_txn_id"`
	Metadata      map[string]interface{}     `json:"metadata"`
}

// TransactionUpdateRequest represents the request to update a transaction
type TransactionUpdateRequest struct {
	Status          *models.TransactionStatus `json:"status"`
	GatewayTxnID    *string                   `json:"gateway_txn_id"`
	GatewayResponse *string                   `json:"gateway_response"`
	GatewayFee      *float64                  `json:"gateway_fee"`
	FailureReason   *string                   `json:"failure_reason"`
	Metadata        map[string]interface{}    `json:"metadata"`
}

// GetTransactions retrieves transactions with pagination and filtering
func (s *transactionService) GetTransactions(req *repository.TransactionListRequest) (*TransactionListResponse, error) {
	// Set default pagination if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	transactions, total, err := s.transactionRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &TransactionListResponse{
		Transactions: transactions,
		Pagination:   pagination,
	}, nil
}

// GetTransaction retrieves a transaction by ID
func (s *transactionService) GetTransaction(id uuid.UUID) (*TransactionResponse, error) {
	transaction, err := s.transactionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	return &TransactionResponse{Transaction: transaction}, nil
}

// GetTransactionsByPayment retrieves transactions for a specific payment
func (s *transactionService) GetTransactionsByPayment(paymentID uuid.UUID) (*TransactionListResponse, error) {
	transactions, err := s.transactionRepo.GetByPaymentID(paymentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions for payment: %w", err)
	}

	return &TransactionListResponse{
		Transactions: transactions,
		Pagination:   nil, // No pagination for this endpoint
	}, nil
}

// CreateTransaction creates a new transaction
func (s *transactionService) CreateTransaction(req *TransactionCreateRequest) (*TransactionResponse, error) {
	// Validate transaction type
	if !req.Type.IsValid() {
		return nil, fmt.Errorf("invalid transaction type: %s", req.Type)
	}

	// Create transaction model
	transaction := &models.Transaction{
		PaymentID:    req.PaymentID,
		Amount:       req.Amount,
		Currency:     req.Currency,
		Status:       models.TransactionPending,
		Type:         req.Type,
		GatewayType:  req.GatewayType,
		GatewayTxnID: req.GatewayTxnID,
	}

	// Set metadata if provided
	if req.Metadata != nil {
		// Convert metadata to JSON string
		transaction.Metadata = "{}"
	}

	// Create transaction in database
	if err := s.transactionRepo.Create(transaction); err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	return &TransactionResponse{Transaction: transaction}, nil
}

// UpdateTransaction updates a transaction
func (s *transactionService) UpdateTransaction(id uuid.UUID, req *TransactionUpdateRequest) (*TransactionResponse, error) {
	// Check if transaction exists
	_, err := s.transactionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("transaction not found: %w", err)
	}

	// Prepare updates
	updates := make(map[string]interface{})

	if req.Status != nil {
		if !req.Status.IsValid() {
			return nil, fmt.Errorf("invalid transaction status: %s", *req.Status)
		}
		updates["status"] = *req.Status
		
		// Set processed_at if status is completed or failed
		if *req.Status == models.TransactionCompleted || *req.Status == models.TransactionFailed {
			updates["processed_at"] = time.Now()
		}
	}

	if req.GatewayTxnID != nil {
		updates["gateway_txn_id"] = *req.GatewayTxnID
	}

	if req.GatewayResponse != nil {
		updates["gateway_response"] = *req.GatewayResponse
	}

	if req.GatewayFee != nil {
		updates["gateway_fee"] = *req.GatewayFee
	}

	if req.FailureReason != nil {
		updates["failure_reason"] = *req.FailureReason
	}

	if req.Metadata != nil {
		// Convert metadata to JSON string
		updates["metadata"] = "{}"
	}

	// Update transaction
	if err := s.transactionRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update transaction: %w", err)
	}

	// Get updated transaction
	updatedTransaction, err := s.transactionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated transaction: %w", err)
	}

	return &TransactionResponse{Transaction: updatedTransaction}, nil
}

// RetryTransaction retries a failed transaction
func (s *transactionService) RetryTransaction(id uuid.UUID) (*TransactionResponse, error) {
	// Get transaction
	transaction, err := s.transactionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("transaction not found: %w", err)
	}

	// Check if transaction can be retried
	if transaction.Status != models.TransactionFailed {
		return nil, fmt.Errorf("transaction cannot be retried, current status: %s", transaction.Status)
	}

	// Check retry limit
	if transaction.RetryCount >= 3 {
		return nil, fmt.Errorf("transaction has exceeded maximum retry attempts")
	}

	// Check if transaction is not too old (24 hours)
	if time.Since(transaction.CreatedAt) > 24*time.Hour {
		return nil, fmt.Errorf("transaction is too old to retry")
	}

	// Update transaction for retry
	updates := map[string]interface{}{
		"status":      models.TransactionPending,
		"retry_count": transaction.RetryCount + 1,
		"failure_reason": "",
	}

	if err := s.transactionRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update transaction for retry: %w", err)
	}

	// TODO: Implement actual gateway retry logic
	// For now, we'll simulate a retry attempt

	// Get updated transaction
	updatedTransaction, err := s.transactionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated transaction: %w", err)
	}

	return &TransactionResponse{Transaction: updatedTransaction}, nil
}

// GetTransactionStats retrieves transaction statistics
func (s *transactionService) GetTransactionStats() (*repository.TransactionStats, error) {
	return s.transactionRepo.GetTransactionStats()
}

// GetFailedTransactions retrieves all failed transactions
func (s *transactionService) GetFailedTransactions() ([]*models.Transaction, error) {
	return s.transactionRepo.GetFailedTransactions()
}
