import { useState, useEffect } from 'react'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  BanknotesIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import {
  Payment,
  PaymentMethod,
  TransactionType,
  TransactionStatus,
  PaymentCreateRequest,
  PaymentUpdateRequest,
  PaymentFilters,
  PaymentStats
} from '@/types/student'

// Mock data for development
const mockPayments: Payment[] = [
  {
    id: '1',
    transaction_id: 'TXN001',
    student_id: '1',
    enrollment_id: 'ENR001',
    amount: 1200,
    method: 'CARD',
    type: 'PAYMENT',
    status: 'COMPLETED',
    description: 'Course fee payment for Advanced Mathematics',
    reference_number: 'REF001',
    student_name: '<PERSON>',
    student_id_number: 'STU001',
    course_name: 'Advanced Mathematics',
    course_code: 'MATH101',
    payment_date: '2024-01-15T10:30:00Z',
    due_date: '2024-01-10T00:00:00Z',
    processed_by: 'admin',
    processor_name: 'Admin User',
    notes: 'Payment processed successfully',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    transaction_id: 'TXN002',
    student_id: '2',
    enrollment_id: 'ENR002',
    amount: 500,
    method: 'CASH',
    type: 'PARTIAL_PAYMENT',
    status: 'COMPLETED',
    description: 'Partial payment for English Literature course',
    student_name: 'Sarah Smith',
    student_id_number: 'STU002',
    course_name: 'English Literature',
    course_code: 'ENG201',
    payment_date: '2024-01-20T14:15:00Z',
    due_date: '2024-01-15T00:00:00Z',
    processed_by: 'staff',
    processor_name: 'Staff Member',
    notes: 'Remaining balance: $500',
    created_at: '2024-01-20T14:15:00Z',
    updated_at: '2024-01-20T14:15:00Z'
  },
  {
    id: '3',
    transaction_id: 'TXN003',
    student_id: '3',
    amount: 150,
    method: 'BANK_TRANSFER',
    type: 'REFUND',
    status: 'COMPLETED',
    description: 'Refund for cancelled course',
    reference_number: 'REF003',
    student_name: 'Ahmed Hassan',
    student_id_number: 'STU003',
    payment_date: '2024-01-25T09:45:00Z',
    processed_by: 'admin',
    processor_name: 'Admin User',
    notes: 'Course cancelled, full refund processed',
    created_at: '2024-01-25T09:45:00Z',
    updated_at: '2024-01-25T09:45:00Z'
  },
  {
    id: '4',
    transaction_id: 'TXN004',
    student_id: '4',
    enrollment_id: 'ENR004',
    amount: 800,
    method: 'ONLINE',
    type: 'PAYMENT',
    status: 'PENDING',
    description: 'Online payment for Computer Science course',
    reference_number: 'REF004',
    student_name: 'Maria Garcia',
    student_id_number: 'STU004',
    course_name: 'Computer Science Fundamentals',
    course_code: 'CS101',
    payment_date: '2024-01-28T16:20:00Z',
    due_date: '2024-01-25T00:00:00Z',
    processed_by: 'system',
    processor_name: 'Online System',
    notes: 'Payment processing in progress',
    created_at: '2024-01-28T16:20:00Z',
    updated_at: '2024-01-28T16:20:00Z'
  }
]

const mockStats: PaymentStats = {
  total_revenue: 156800,
  monthly_revenue: 23400,
  pending_payments: 8,
  overdue_payments: 3,
  total_transactions: 342,
  average_payment_amount: 458.5,
  refunds_this_month: 2,
  collection_rate: 94.2
}

const PaymentsPage = () => {
  const [payments, setPayments] = useState<Payment[]>([])
  const [stats, setStats] = useState<PaymentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<PaymentFilters>({
    search: '',
    status: 'ALL',
    method: 'ALL',
    type: 'ALL',
    date_from: '',
    date_to: ''
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)

  // Load payments on component mount
  useEffect(() => {
    loadPayments()
    loadStats()
  }, [])

  const loadPayments = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setPayments(mockPayments)
    } catch (error) {
      console.error('Failed to load payments:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  // Filter payments based on search and filters
  const filteredPayments = payments.filter(payment => {
    const matchesSearch =
      payment.student_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      payment.student_id_number.toLowerCase().includes(filters.search.toLowerCase()) ||
      payment.transaction_id.toLowerCase().includes(filters.search.toLowerCase()) ||
      payment.reference_number?.toLowerCase().includes(filters.search.toLowerCase()) ||
      payment.course_name?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = filters.status === 'ALL' || payment.status === filters.status
    const matchesMethod = filters.method === 'ALL' || payment.method === filters.method
    const matchesType = filters.type === 'ALL' || payment.type === filters.type

    let matchesDateRange = true
    if (filters.date_from && filters.date_to) {
      const paymentDate = new Date(payment.payment_date)
      const fromDate = new Date(filters.date_from)
      const toDate = new Date(filters.date_to)
      matchesDateRange = paymentDate >= fromDate && paymentDate <= toDate
    }

    return matchesSearch && matchesStatus && matchesMethod && matchesType && matchesDateRange
  })

  const handleCreatePayment = async (paymentData: PaymentCreateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newPayment: Payment = {
        id: Date.now().toString(),
        transaction_id: `TXN${String(payments.length + 1).padStart(3, '0')}`,
        ...paymentData,
        status: 'PENDING',
        student_name: 'Student Name', // Would come from API
        student_id_number: 'STU000',
        payment_date: new Date().toISOString(),
        processed_by: 'current_user',
        processor_name: 'Current User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      setPayments(prev => [...prev, newPayment])
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create payment:', error)
    }
  }

  const handleUpdatePayment = async (paymentId: string, paymentData: PaymentUpdateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setPayments(prev => prev.map(payment =>
        payment.id === paymentId
          ? {
              ...payment,
              ...paymentData,
              updated_at: new Date().toISOString()
            }
          : payment
      ))
      setShowEditModal(false)
      setSelectedPayment(null)
    } catch (error) {
      console.error('Failed to update payment:', error)
    }
  }

  const handleDeletePayment = async (paymentId: string) => {
    if (!confirm('Are you sure you want to delete this payment record? This action cannot be undone.')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setPayments(prev => prev.filter(payment => payment.id !== paymentId))
    } catch (error) {
      console.error('Failed to delete payment:', error)
    }
  }

  const getStatusBadgeColor = (status: TransactionStatus) => {
    const colors = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      COMPLETED: 'bg-green-100 text-green-800',
      FAILED: 'bg-red-100 text-red-800',
      CANCELLED: 'bg-gray-100 text-gray-800',
      REFUNDED: 'bg-blue-100 text-blue-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const getMethodBadgeColor = (method: PaymentMethod) => {
    const colors = {
      CASH: 'bg-green-100 text-green-800',
      CARD: 'bg-blue-100 text-blue-800',
      BANK_TRANSFER: 'bg-purple-100 text-purple-800',
      ONLINE: 'bg-indigo-100 text-indigo-800',
      CHECK: 'bg-yellow-100 text-yellow-800'
    }
    return colors[method] || 'bg-gray-100 text-gray-800'
  }

  const getTypeBadgeColor = (type: TransactionType) => {
    const colors = {
      PAYMENT: 'bg-green-100 text-green-800',
      REFUND: 'bg-red-100 text-red-800',
      PARTIAL_PAYMENT: 'bg-yellow-100 text-yellow-800',
      LATE_FEE: 'bg-orange-100 text-orange-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
          <p className="text-gray-600 mt-1">
            Process payments, manage transactions, and view financial reports
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            className="flex items-center gap-2"
          >
            <DocumentTextIcon className="h-4 w-4" />
            Generate Report
          </Button>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center gap-2"
          >
            <PlusIcon className="h-4 w-4" />
            Record Payment
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-xl font-semibold text-gray-900">{formatCurrency(stats.total_revenue)}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <ChartBarIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Monthly Revenue</p>
                <p className="text-xl font-semibold text-gray-900">{formatCurrency(stats.monthly_revenue)}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ClockIcon className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Pending Payments</p>
                <p className="text-xl font-semibold text-gray-900">{stats.pending_payments}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Overdue Payments</p>
                <p className="text-xl font-semibold text-gray-900">{stats.overdue_payments}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BanknotesIcon className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Transactions</p>
                <p className="text-xl font-semibold text-gray-900">{stats.total_transactions}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <CreditCardIcon className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Payment</p>
                <p className="text-xl font-semibold text-gray-900">{formatCurrency(stats.average_payment_amount)}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Collection Rate</p>
                <p className="text-xl font-semibold text-gray-900">{stats.collection_rate}%</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gray-100 rounded-lg">
                <CurrencyDollarIcon className="h-5 w-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Refunds This Month</p>
                <p className="text-xl font-semibold text-gray-900">{stats.refunds_this_month}</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search payments..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as TransactionStatus | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Status</option>
            <option value="PENDING">Pending</option>
            <option value="COMPLETED">Completed</option>
            <option value="FAILED">Failed</option>
            <option value="CANCELLED">Cancelled</option>
            <option value="REFUNDED">Refunded</option>
          </select>

          <select
            value={filters.method}
            onChange={(e) => setFilters(prev => ({ ...prev, method: e.target.value as PaymentMethod | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Methods</option>
            <option value="CASH">Cash</option>
            <option value="CARD">Card</option>
            <option value="BANK_TRANSFER">Bank Transfer</option>
            <option value="ONLINE">Online</option>
            <option value="CHECK">Check</option>
          </select>

          <select
            value={filters.type}
            onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value as TransactionType | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Types</option>
            <option value="PAYMENT">Payment</option>
            <option value="REFUND">Refund</option>
            <option value="PARTIAL_PAYMENT">Partial Payment</option>
            <option value="LATE_FEE">Late Fee</option>
          </select>

          <Input
            type="date"
            placeholder="From Date"
            value={filters.date_from}
            onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
          />

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4" />
            {filteredPayments.length} of {payments.length} payments
          </div>
        </div>
      </div>

      {/* Payments Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-head">Transaction</th>
                <th className="table-head">Student</th>
                <th className="table-head">Course</th>
                <th className="table-head">Amount</th>
                <th className="table-head">Method</th>
                <th className="table-head">Type</th>
                <th className="table-head">Status</th>
                <th className="table-head">Date</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredPayments.length === 0 ? (
                <tr>
                  <td colSpan={9} className="table-cell text-center py-8 text-gray-500">
                    No payments found matching your criteria
                  </td>
                </tr>
              ) : (
                filteredPayments.map((payment) => (
                  <tr key={payment.id} className="table-row">
                    <td className="table-cell">
                      <div>
                        <div className="font-medium text-gray-900">{payment.transaction_id}</div>
                        {payment.reference_number && (
                          <div className="text-sm text-gray-500">Ref: {payment.reference_number}</div>
                        )}
                        <div className="text-xs text-gray-400">
                          By: {payment.processor_name}
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div>
                        <div className="font-medium text-gray-900">{payment.student_name}</div>
                        <div className="text-sm text-gray-500">{payment.student_id_number}</div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm">
                        {payment.course_name ? (
                          <>
                            <div className="text-gray-900">{payment.course_name}</div>
                            <div className="text-gray-500">{payment.course_code}</div>
                          </>
                        ) : (
                          <span className="text-gray-400">No course</span>
                        )}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="font-medium text-gray-900">
                        {formatCurrency(payment.amount)}
                      </div>
                      {payment.late_fee && (
                        <div className="text-xs text-red-600">
                          +{formatCurrency(payment.late_fee)} late fee
                        </div>
                      )}
                    </td>
                    <td className="table-cell">
                      <Badge className={getMethodBadgeColor(payment.method)}>
                        {payment.method.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <Badge className={getTypeBadgeColor(payment.type)}>
                        {payment.type.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <Badge className={getStatusBadgeColor(payment.status)}>
                        {payment.status}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {formatDate(payment.payment_date)}
                      </div>
                      {payment.due_date && (
                        <div className="text-xs text-gray-500">
                          Due: {new Date(payment.due_date).toLocaleDateString()}
                        </div>
                      )}
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedPayment(payment)
                            setShowViewModal(true)
                          }}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedPayment(payment)
                            setShowEditModal(true)
                          }}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeletePayment(payment.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <PaymentCreateModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreatePayment}
        />
      )}

      {showEditModal && selectedPayment && (
        <PaymentEditModal
          payment={selectedPayment}
          onClose={() => {
            setShowEditModal(false)
            setSelectedPayment(null)
          }}
          onSubmit={(paymentData) => handleUpdatePayment(selectedPayment.id, paymentData)}
        />
      )}

      {showViewModal && selectedPayment && (
        <PaymentViewModal
          payment={selectedPayment}
          onClose={() => {
            setShowViewModal(false)
            setSelectedPayment(null)
          }}
        />
      )}
    </div>
  )
}

// Modal Components (placeholder implementations)
const PaymentCreateModal = ({ onClose, onSubmit }: {
  onClose: () => void
  onSubmit: (data: PaymentCreateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Record New Payment</h2>
        <p className="text-gray-600 mb-4">Payment recording form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Record Payment</Button>
        </div>
      </div>
    </div>
  )
}

const PaymentEditModal = ({ payment, onClose, onSubmit }: {
  payment: Payment
  onClose: () => void
  onSubmit: (data: PaymentUpdateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Edit Payment: {payment.transaction_id}</h2>
        <p className="text-gray-600 mb-4">Payment editing form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Save Changes</Button>
        </div>
      </div>
    </div>
  )
}

const PaymentViewModal = ({ payment, onClose }: {
  payment: Payment
  onClose: () => void
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Payment Details: {payment.transaction_id}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Transaction Information</h3>
            <div className="space-y-1">
              <p><strong>Transaction ID:</strong> {payment.transaction_id}</p>
              <p><strong>Amount:</strong> {formatCurrency(payment.amount)}</p>
              <p><strong>Method:</strong> {payment.method.replace('_', ' ')}</p>
              <p><strong>Type:</strong> {payment.type.replace('_', ' ')}</p>
              <p><strong>Status:</strong> {payment.status}</p>
              {payment.reference_number && (
                <p><strong>Reference:</strong> {payment.reference_number}</p>
              )}
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Student Information</h3>
            <div className="space-y-1">
              <p><strong>Student:</strong> {payment.student_name}</p>
              <p><strong>Student ID:</strong> {payment.student_id_number}</p>
              {payment.course_name && (
                <>
                  <p><strong>Course:</strong> {payment.course_name}</p>
                  <p><strong>Course Code:</strong> {payment.course_code}</p>
                </>
              )}
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Dates</h3>
            <div className="space-y-1">
              <p><strong>Payment Date:</strong> {formatDate(payment.payment_date)}</p>
              {payment.due_date && (
                <p><strong>Due Date:</strong> {formatDate(payment.due_date)}</p>
              )}
              <p><strong>Created:</strong> {formatDate(payment.created_at)}</p>
              <p><strong>Updated:</strong> {formatDate(payment.updated_at)}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Processing</h3>
            <div className="space-y-1">
              <p><strong>Processed By:</strong> {payment.processor_name}</p>
              {payment.late_fee && (
                <p><strong>Late Fee:</strong> {formatCurrency(payment.late_fee)}</p>
              )}
              {payment.discount && (
                <p><strong>Discount:</strong> {formatCurrency(payment.discount)}</p>
              )}
            </div>
          </div>
          {payment.description && (
            <div className="md:col-span-2">
              <h3 className="font-medium text-gray-900 mb-2">Description</h3>
              <p className="text-gray-600">{payment.description}</p>
            </div>
          )}
          {payment.notes && (
            <div className="md:col-span-2">
              <h3 className="font-medium text-gray-900 mb-2">Notes</h3>
              <p className="text-gray-600">{payment.notes}</p>
            </div>
          )}
        </div>
        <div className="mt-6">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}

export default PaymentsPage
