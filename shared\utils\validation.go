package utils

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"unicode"

	"github.com/google/uuid"
)

var (
	ErrInvalidEmail    = errors.New("invalid email format")
	ErrInvalidPhone    = errors.New("invalid phone number format")
	ErrInvalidUUID     = errors.New("invalid UUID format")
	ErrInvalidUsername = errors.New("invalid username format")
	ErrEmptyField      = errors.New("field cannot be empty")
	ErrFieldTooLong    = errors.New("field exceeds maximum length")
	ErrFieldTooShort   = errors.New("field is below minimum length")
)

// ValidationError represents a validation error with field information
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error on field '%s': %s", e.Field, e.Message)
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	if len(e) == 0 {
		return "no validation errors"
	}
	
	var messages []string
	for _, err := range e {
		messages = append(messages, err.Error())
	}
	return strings.Join(messages, "; ")
}

// HasErrors checks if there are any validation errors
func (e ValidationErrors) HasErrors() bool {
	return len(e) > 0
}

// AddError adds a validation error
func (e *ValidationErrors) AddError(field, message, value string) {
	*e = append(*e, ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	})
}

// Validator provides validation functions
type Validator struct {
	errors ValidationErrors
}

// NewValidator creates a new validator
func NewValidator() *Validator {
	return &Validator{
		errors: make(ValidationErrors, 0),
	}
}

// GetErrors returns all validation errors
func (v *Validator) GetErrors() ValidationErrors {
	return v.errors
}

// HasErrors checks if there are any validation errors
func (v *Validator) HasErrors() bool {
	return v.errors.HasErrors()
}

// AddError adds a validation error
func (v *Validator) AddError(field, message, value string) {
	v.errors.AddError(field, message, value)
}

// ValidateRequired validates that a field is not empty
func (v *Validator) ValidateRequired(field, value string) {
	if strings.TrimSpace(value) == "" {
		v.AddError(field, "field is required", value)
	}
}

// ValidateEmail validates email format
func (v *Validator) ValidateEmail(field, email string) {
	if email == "" {
		return // Skip validation for empty emails unless required
	}
	
	if !IsValidEmail(email) {
		v.AddError(field, "invalid email format", email)
	}
}

// ValidatePhone validates phone number format
func (v *Validator) ValidatePhone(field, phone string) {
	if phone == "" {
		return // Skip validation for empty phones unless required
	}
	
	if !IsValidPhone(phone) {
		v.AddError(field, "invalid phone number format", phone)
	}
}

// ValidateUUID validates UUID format
func (v *Validator) ValidateUUID(field, id string) {
	if id == "" {
		return // Skip validation for empty UUIDs unless required
	}
	
	if !IsValidUUID(id) {
		v.AddError(field, "invalid UUID format", id)
	}
}

// ValidateUsername validates username format
func (v *Validator) ValidateUsername(field, username string) {
	if username == "" {
		return // Skip validation for empty usernames unless required
	}
	
	if !IsValidUsername(username) {
		v.AddError(field, "invalid username format (3-50 characters, alphanumeric and underscore only)", username)
	}
}

// ValidateLength validates string length
func (v *Validator) ValidateLength(field, value string, min, max int) {
	length := len(strings.TrimSpace(value))
	
	if min > 0 && length < min {
		v.AddError(field, fmt.Sprintf("minimum length is %d characters", min), value)
	}
	
	if max > 0 && length > max {
		v.AddError(field, fmt.Sprintf("maximum length is %d characters", max), value)
	}
}

// ValidateMinLength validates minimum string length
func (v *Validator) ValidateMinLength(field, value string, min int) {
	if len(strings.TrimSpace(value)) < min {
		v.AddError(field, fmt.Sprintf("minimum length is %d characters", min), value)
	}
}

// ValidateMaxLength validates maximum string length
func (v *Validator) ValidateMaxLength(field, value string, max int) {
	if len(strings.TrimSpace(value)) > max {
		v.AddError(field, fmt.Sprintf("maximum length is %d characters", max), value)
	}
}

// ValidatePattern validates string against a regex pattern
func (v *Validator) ValidatePattern(field, value, pattern, message string) {
	if value == "" {
		return // Skip validation for empty values unless required
	}
	
	matched, err := regexp.MatchString(pattern, value)
	if err != nil {
		v.AddError(field, "invalid pattern validation", value)
		return
	}
	
	if !matched {
		v.AddError(field, message, value)
	}
}

// IsValidEmail checks if email format is valid
func IsValidEmail(email string) bool {
	if email == "" {
		return false
	}
	
	// Basic email regex pattern
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	matched, err := regexp.MatchString(pattern, email)
	if err != nil {
		return false
	}
	
	// Additional checks
	if len(email) > 254 { // RFC 5321 limit
		return false
	}
	
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}
	
	localPart, domain := parts[0], parts[1]
	
	// Local part checks
	if len(localPart) > 64 { // RFC 5321 limit
		return false
	}
	
	// Domain checks
	if len(domain) > 253 { // RFC 1035 limit
		return false
	}
	
	return matched
}

// IsValidPhone checks if phone number format is valid
func IsValidPhone(phone string) bool {
	if phone == "" {
		return false
	}
	
	// Remove common separators
	cleaned := strings.ReplaceAll(phone, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")
	cleaned = strings.ReplaceAll(cleaned, "(", "")
	cleaned = strings.ReplaceAll(cleaned, ")", "")
	cleaned = strings.ReplaceAll(cleaned, ".", "")
	
	// Check for valid phone patterns
	patterns := []string{
		`^\+?[1-9]\d{1,14}$`,           // International format
		`^[0-9]{10,15}$`,               // Basic numeric
		`^\+998[0-9]{9}$`,              // Uzbekistan format
		`^998[0-9]{9}$`,                // Uzbekistan without +
		`^[0-9]{9}$`,                   // Local format
	}
	
	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, cleaned); matched {
			return true
		}
	}
	
	return false
}

// IsValidUUID checks if UUID format is valid
func IsValidUUID(id string) bool {
	if id == "" {
		return false
	}
	
	_, err := uuid.Parse(id)
	return err == nil
}

// IsValidUsername checks if username format is valid
func IsValidUsername(username string) bool {
	if username == "" {
		return false
	}
	
	// Length check
	if len(username) < 3 || len(username) > 50 {
		return false
	}
	
	// Pattern check: alphanumeric and underscore only
	pattern := `^[a-zA-Z0-9_]+$`
	matched, err := regexp.MatchString(pattern, username)
	if err != nil {
		return false
	}
	
	// Must start with letter or number
	if !unicode.IsLetter(rune(username[0])) && !unicode.IsDigit(rune(username[0])) {
		return false
	}
	
	return matched
}

// SanitizeString removes potentially harmful characters from string
func SanitizeString(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")
	
	// Trim whitespace
	input = strings.TrimSpace(input)
	
	// Remove control characters except tab, newline, and carriage return
	var result strings.Builder
	for _, r := range input {
		if unicode.IsControl(r) && r != '\t' && r != '\n' && r != '\r' {
			continue
		}
		result.WriteRune(r)
	}
	
	return result.String()
}

// NormalizeEmail normalizes email address
func NormalizeEmail(email string) string {
	email = strings.TrimSpace(email)
	email = strings.ToLower(email)
	return email
}

// NormalizePhone normalizes phone number
func NormalizePhone(phone string) string {
	// Remove all non-digit characters except +
	var result strings.Builder
	for _, r := range phone {
		if unicode.IsDigit(r) || r == '+' {
			result.WriteRune(r)
		}
	}
	
	normalized := result.String()
	
	// Handle Uzbekistan phone numbers
	if strings.HasPrefix(normalized, "998") && len(normalized) == 12 {
		normalized = "+" + normalized
	} else if len(normalized) == 9 && !strings.HasPrefix(normalized, "+") {
		normalized = "+998" + normalized
	}
	
	return normalized
}

// ValidateStruct validates a struct using reflection and tags
func ValidateStruct(s interface{}) ValidationErrors {
	// This is a placeholder for struct validation
	// In a real implementation, you would use reflection to validate struct fields
	// based on validation tags
	return ValidationErrors{}
}

// ValidatePasswordComplexity validates password complexity
func ValidatePasswordComplexity(password string) error {
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}

	var hasUpper, hasLower, hasDigit, hasSpecial bool

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	if !hasUpper {
		return errors.New("password must contain at least one uppercase letter")
	}
	if !hasLower {
		return errors.New("password must contain at least one lowercase letter")
	}
	if !hasDigit {
		return errors.New("password must contain at least one digit")
	}
	if !hasSpecial {
		return errors.New("password must contain at least one special character")
	}

	return nil
}

// ParseUUID parses a string to UUID
func ParseUUID(s string) (uuid.UUID, error) {
	return uuid.Parse(s)
}

// FormatValidationError formats validation errors from gin binding
func FormatValidationError(err error) string {
	if err == nil {
		return ""
	}

	// For now, just return the error message
	// In a more sophisticated implementation, you could parse
	// gin validation errors and format them nicely
	return err.Error()
}
