import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { 
  HomeIcon,
  UsersIcon,
  AcademicCapIcon,
  UserGroupIcon,
  CreditCardIcon,
  BellIcon,
  ChartBarIcon,
  BookOpenIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import { NavLink } from 'react-router-dom'
import { useAuth } from '@/store/auth'
import { cn } from '@/utils/cn'

interface SidebarProps {
  open: boolean
  setOpen: (open: boolean) => void
}

const Sidebar = ({ open, setOpen }: SidebarProps) => {
  const { user } = useAuth()

  // Navigation items based on user role
  const getNavigationItems = () => {
    const baseItems = [
      { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, current: false },
    ]

    const roleBasedItems = []

    // Admin and Cashier can access user management
    if (user?.role === 'ADMIN' || user?.role === 'CASHIER') {
      roleBasedItems.push({
        name: 'Users', 
        href: '/users', 
        icon: UsersIcon, 
        current: false
      })
    }

    // Staff roles can access student and lead management
    if (['RECEPTION', 'TEACHER', 'MANAGER', 'ACADEMIC_MANAGER', 'ADMIN'].includes(user?.role || '')) {
      roleBasedItems.push({
        name: 'Students', 
        href: '/students', 
        icon: AcademicCapIcon, 
        current: false
      })
    }

    // Reception, Manager, Academic Manager, and Admin can access leads
    if (['RECEPTION', 'MANAGER', 'ACADEMIC_MANAGER', 'ADMIN'].includes(user?.role || '')) {
      roleBasedItems.push({
        name: 'Leads', 
        href: '/leads', 
        icon: UserGroupIcon, 
        current: false
      })
    }

    // Academic roles can access course management
    if (['TEACHER', 'MANAGER', 'ACADEMIC_MANAGER', 'ADMIN'].includes(user?.role || '')) {
      roleBasedItems.push({
        name: 'Courses', 
        href: '/courses', 
        icon: BookOpenIcon, 
        current: false
      })
    }

    // Admin and Cashier can access payments
    if (user?.role === 'ADMIN' || user?.role === 'CASHIER') {
      roleBasedItems.push({
        name: 'Payments', 
        href: '/payments', 
        icon: CreditCardIcon, 
        current: false
      })
    }

    // All users can access notifications
    roleBasedItems.push({
      name: 'Notifications', 
      href: '/notifications', 
      icon: BellIcon, 
      current: false
    })

    // Admin can access analytics and settings
    if (user?.role === 'ADMIN') {
      roleBasedItems.push(
        {
          name: 'Analytics', 
          href: '/analytics', 
          icon: ChartBarIcon, 
          current: false
        },
        {
          name: 'Settings', 
          href: '/settings', 
          icon: Cog6ToothIcon, 
          current: false
        }
      )
    }

    return [...baseItems, ...roleBasedItems]
  }

  const navigation = getNavigationItems()

  const SidebarContent = () => (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
      <div className="flex h-16 shrink-0 items-center">
        <div className="flex items-center">
          <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">CRM</span>
          </div>
          <span className="ml-3 text-lg font-semibold text-gray-900">
            Go Docker Platform
          </span>
        </div>
      </div>
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item) => (
                <li key={item.name}>
                  <NavLink
                    to={item.href}
                    className={({ isActive }) =>
                      cn(
                        isActive
                          ? 'bg-primary-50 text-primary-600'
                          : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50',
                        'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                      )
                    }
                    onClick={() => setOpen(false)}
                  >
                    {({ isActive }) => (
                      <>
                        <item.icon
                          className={cn(
                            isActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-primary-600',
                            'h-6 w-6 shrink-0'
                          )}
                          aria-hidden="true"
                        />
                        {item.name}
                      </>
                    )}
                  </NavLink>
                </li>
              ))}
            </ul>
          </li>
          
          {/* User info at bottom */}
          <li className="mt-auto">
            <div className="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-900">
              {user?.avatar ? (
                <img
                  className="h-8 w-8 rounded-full bg-gray-50"
                  src={user.avatar}
                  alt={user.full_name}
                />
              ) : (
                <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">
                    {user?.first_name?.[0]}{user?.last_name?.[0]}
                  </span>
                </div>
              )}
              <div className="flex flex-col">
                <span className="text-sm">{user?.full_name}</span>
                <span className="text-xs text-gray-500 capitalize">
                  {user?.role?.toLowerCase().replace('_', ' ')}
                </span>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </div>
  )

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button type="button" className="-m-2.5 p-2.5" onClick={() => setOpen(false)}>
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <SidebarContent />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4">
          <SidebarContent />
        </div>
      </div>
    </>
  )
}

export default Sidebar
