package handlers

import (
	"net/http"
	"notification-service/internal/middleware"
	"notification-service/internal/models"
	"notification-service/internal/services"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SchedulerHandler handles scheduling-related HTTP requests
type SchedulerHandler struct {
	schedulerService *services.SchedulerService
}

// NewSchedulerHandler creates a new scheduler handler
func NewSchedulerHandler(schedulerService *services.SchedulerService) *SchedulerHandler {
	return &SchedulerHandler{
		schedulerService: schedulerService,
	}
}

// ScheduleNotification schedules a notification for future delivery
func (h *SchedulerHandler) ScheduleNotification(c *gin.Context) {
	var req struct {
		models.CreateNotificationRequest
		ScheduledAt string `json:"scheduled_at" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	// Parse scheduled time
	scheduledAt, err := time.Parse(time.RFC3339, req.ScheduledAt)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SCHEDULE_TIME",
				"message": "Invalid scheduled time format, use RFC3339",
				"details": err.Error(),
			},
		})
		return
	}
	
	// Check if scheduled time is in the future
	if scheduledAt.Before(time.Now()) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SCHEDULE_TIME",
				"message": "Scheduled time must be in the future",
			},
		})
		return
	}
	
	// Set source from authenticated user if not provided
	if req.Source == "" {
		if userID, exists := middleware.GetUserID(c); exists {
			req.Source = "user:" + userID
		}
	}
	
	notification, err := h.schedulerService.ScheduleNotification(&req.CreateNotificationRequest, scheduledAt)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "SCHEDULE_FAILED",
				"message": "Failed to schedule notification",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    notification,
		"message": "Notification scheduled successfully",
	})
}

// ScheduleRecurringNotification creates a recurring notification schedule
func (h *SchedulerHandler) ScheduleRecurringNotification(c *gin.Context) {
	var req struct {
		models.CreateNotificationRequest
		Schedule services.RecurringSchedule `json:"schedule" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	// Set source from authenticated user if not provided
	if req.Source == "" {
		if userID, exists := middleware.GetUserID(c); exists {
			req.Source = "user:" + userID
		}
	}
	
	// Generate UUID for schedule if not provided
	if req.Schedule.ID == uuid.Nil {
		req.Schedule.ID = uuid.New()
	}
	
	// Set created time
	req.Schedule.CreatedAt = time.Now()
	
	err := h.schedulerService.ScheduleRecurringNotification(&req.CreateNotificationRequest, &req.Schedule)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "RECURRING_SCHEDULE_FAILED",
				"message": "Failed to create recurring notification schedule",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    req.Schedule,
		"message": "Recurring notification schedule created successfully",
	})
}

// CancelScheduledNotification cancels a scheduled notification
func (h *SchedulerHandler) CancelScheduledNotification(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_ID",
				"message": "Invalid notification ID",
			},
		})
		return
	}
	
	err = h.schedulerService.CancelScheduledNotification(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "CANCEL_FAILED",
				"message": "Failed to cancel scheduled notification",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Scheduled notification cancelled successfully",
	})
}

// GetScheduledNotifications retrieves scheduled notifications
func (h *SchedulerHandler) GetScheduledNotifications(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	// Parse filters
	filters := make(map[string]interface{})
	
	if recipient := c.Query("recipient"); recipient != "" {
		filters["recipient"] = recipient
	}
	if notificationType := c.Query("type"); notificationType != "" {
		filters["type"] = notificationType
	}
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	
	notifications, total, err := h.schedulerService.GetScheduledNotifications(filters, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "RETRIEVAL_FAILED",
				"message": "Failed to retrieve scheduled notifications",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"notifications": notifications,
			"pagination": gin.H{
				"page":        page,
				"limit":       limit,
				"total":       total,
				"total_pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// CreateReminderSchedule creates reminder notifications for an event
func (h *SchedulerHandler) CreateReminderSchedule(c *gin.Context) {
	var req struct {
		models.CreateNotificationRequest
		ReminderTimes []string `json:"reminder_times" binding:"required"` // e.g., ["24h", "1h", "15m"]
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	// Parse reminder times
	var reminderDurations []time.Duration
	for _, timeStr := range req.ReminderTimes {
		duration, err := time.ParseDuration(timeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "INVALID_REMINDER_TIME",
					"message": "Invalid reminder time format: " + timeStr,
					"details": err.Error(),
				},
			})
			return
		}
		reminderDurations = append(reminderDurations, duration)
	}
	
	// Set source from authenticated user if not provided
	if req.Source == "" {
		if userID, exists := middleware.GetUserID(c); exists {
			req.Source = "user:" + userID
		}
	}
	
	err := h.schedulerService.CreateReminderSchedule(&req.CreateNotificationRequest, reminderDurations)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "REMINDER_SCHEDULE_FAILED",
				"message": "Failed to create reminder schedule",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Reminder schedule created successfully",
		"data": gin.H{
			"reminder_count": len(reminderDurations),
			"reminder_times": req.ReminderTimes,
		},
	})
}
