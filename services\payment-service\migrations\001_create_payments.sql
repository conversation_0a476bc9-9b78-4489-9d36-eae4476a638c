-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    method VARCHAR(20) NOT NULL,
    
    -- Reference information
    student_id UUID,
    course_id UUID,
    enrollment_id UUID,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    
    -- Gateway information
    gateway_type VARCHAR(50) NOT NULL,
    gateway_payment_id VARCHAR(255),
    gateway_response TEXT,
    
    -- Payment details
    description TEXT,
    payment_date TIMESTAMP WITH TIME ZONE,
    due_date TIMESTAMP WITH TIME ZONE,
    
    -- User information
    processed_by_id UUID,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for payments table
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_method ON payments(method);
CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
CREATE INDEX IF NOT EXISTS idx_payments_course_id ON payments(course_id);
CREATE INDEX IF NOT EXISTS idx_payments_enrollment_id ON payments(enrollment_id);
CREATE INDEX IF NOT EXISTS idx_payments_gateway_type ON payments(gateway_type);
CREATE INDEX IF NOT EXISTS idx_payments_gateway_payment_id ON payments(gateway_payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_processed_by_id ON payments(processed_by_id);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_deleted_at ON payments(deleted_at);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_due_date ON payments(due_date);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_payments_status_created ON payments(status, created_at);
CREATE INDEX IF NOT EXISTS idx_payments_student_status ON payments(student_id, status);
CREATE INDEX IF NOT EXISTS idx_payments_gateway_status ON payments(gateway_type, status);

-- Add check constraints for valid enum values
ALTER TABLE payments ADD CONSTRAINT chk_payments_status 
CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED'));

ALTER TABLE payments ADD CONSTRAINT chk_payments_method 
CHECK (method IN ('CASH', 'CARD', 'UZCARD', 'HUMO', 'PAYME', 'CLICK'));

ALTER TABLE payments ADD CONSTRAINT chk_payments_currency 
CHECK (currency IN ('USD', 'UZS', 'EUR'));

-- Add comments for documentation
COMMENT ON TABLE payments IS 'Payment records for all transactions';
COMMENT ON COLUMN payments.id IS 'Unique identifier for the payment';
COMMENT ON COLUMN payments.amount IS 'Payment amount in the specified currency';
COMMENT ON COLUMN payments.currency IS 'Currency code (USD, UZS, EUR)';
COMMENT ON COLUMN payments.status IS 'Payment status (PENDING, COMPLETED, FAILED, CANCELLED, REFUNDED)';
COMMENT ON COLUMN payments.method IS 'Payment method (CASH, CARD, UZCARD, HUMO, PAYME, CLICK)';
COMMENT ON COLUMN payments.student_id IS 'Reference to the student making the payment';
COMMENT ON COLUMN payments.course_id IS 'Reference to the course being paid for';
COMMENT ON COLUMN payments.enrollment_id IS 'Reference to the enrollment record';
COMMENT ON COLUMN payments.invoice_number IS 'Unique invoice number for the payment';
COMMENT ON COLUMN payments.gateway_type IS 'Payment gateway used (stripe, paypal, uzcard, etc.)';
COMMENT ON COLUMN payments.gateway_payment_id IS 'Payment ID from the gateway';
COMMENT ON COLUMN payments.gateway_response IS 'Raw response from the payment gateway';
COMMENT ON COLUMN payments.description IS 'Description of what the payment is for';
COMMENT ON COLUMN payments.payment_date IS 'When the payment was actually processed';
COMMENT ON COLUMN payments.due_date IS 'When the payment is due';
COMMENT ON COLUMN payments.processed_by_id IS 'ID of the user who processed the payment';
COMMENT ON COLUMN payments.metadata IS 'Additional metadata in JSON format';
