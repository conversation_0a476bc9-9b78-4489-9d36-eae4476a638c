package services

import (
	"fmt"
	"log"
	"notification-service/internal/config"
	"notification-service/internal/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationService handles notification operations
type NotificationService struct {
	db     *gorm.DB
	config *config.Config
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB, config *config.Config) *NotificationService {
	return &NotificationService{
		db:     db,
		config: config,
	}
}

// CreateNotification creates a new notification
func (s *NotificationService) CreateNotification(req *models.CreateNotificationRequest) (*models.Notification, error) {
	notification := req.ToNotification()
	
	// Validate notification
	if !notification.IsValid() {
		return nil, fmt.Errorf("invalid notification data")
	}
	
	// Check user preferences if recipient ID is provided
	if notification.RecipientID != nil {
		allowed, err := s.checkUserPreferences(*notification.RecipientID, notification.Type, "transactional")
		if err != nil {
			log.Printf("Error checking user preferences: %v", err)
			// Continue with notification if preference check fails
		} else if !allowed {
			return nil, fmt.Errorf("user has disabled %s notifications", notification.Type)
		}
	}
	
	// Process template if template ID is provided
	if notification.TemplateID != nil {
		if err := s.processTemplate(notification); err != nil {
			return nil, fmt.Errorf("failed to process template: %w", err)
		}
	}
	
	// Create notification in database
	if err := s.db.Create(notification).Error; err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}
	
	// Add to queue for processing
	if err := s.addToQueue(notification); err != nil {
		log.Printf("Failed to add notification to queue: %v", err)
		// Don't fail the creation, just log the error
	}
	
	return notification, nil
}

// GetNotification retrieves a notification by ID
func (s *NotificationService) GetNotification(id uuid.UUID) (*models.Notification, error) {
	var notification models.Notification
	err := s.db.Preload("DeliveryLogs").Preload("Template").First(&notification, "id = ?", id).Error
	if err != nil {
		return nil, fmt.Errorf("notification not found: %w", err)
	}
	return &notification, nil
}

// GetNotifications retrieves notifications with pagination and filters
func (s *NotificationService) GetNotifications(filters map[string]interface{}, page, limit int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64
	
	query := s.db.Model(&models.Notification{})
	
	// Apply filters
	if recipient, ok := filters["recipient"]; ok {
		query = query.Where("recipient = ?", recipient)
	}
	if notificationType, ok := filters["type"]; ok {
		query = query.Where("type = ?", notificationType)
	}
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if source, ok := filters["source"]; ok {
		query = query.Where("source = ?", source)
	}
	if recipientID, ok := filters["recipient_id"]; ok {
		query = query.Where("recipient_id = ?", recipientID)
	}
	if startDate, ok := filters["start_date"]; ok {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok {
		query = query.Where("created_at <= ?", endDate)
	}
	
	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}
	
	// Apply pagination
	offset := (page - 1) * limit
	err := query.Preload("DeliveryLogs").Preload("Template").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error
	
	if err != nil {
		return nil, 0, fmt.Errorf("failed to retrieve notifications: %w", err)
	}
	
	return notifications, total, nil
}

// UpdateNotificationStatus updates the status of a notification
func (s *NotificationService) UpdateNotificationStatus(id uuid.UUID, status models.NotificationStatus) error {
	updates := map[string]interface{}{
		"status": status,
	}
	
	switch status {
	case models.NotificationStatusSent:
		updates["sent_at"] = time.Now()
	case models.NotificationStatusDelivered:
		updates["delivered_at"] = time.Now()
	}
	
	err := s.db.Model(&models.Notification{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("failed to update notification status: %w", err)
	}
	
	return nil
}

// MarkNotificationAsFailed marks a notification as failed with error message
func (s *NotificationService) MarkNotificationAsFailed(id uuid.UUID, errorMsg string) error {
	var notification models.Notification
	if err := s.db.First(&notification, "id = ?", id).Error; err != nil {
		return fmt.Errorf("notification not found: %w", err)
	}
	
	notification.MarkAsFailed(errorMsg)
	
	if err := s.db.Save(&notification).Error; err != nil {
		return fmt.Errorf("failed to update notification: %w", err)
	}
	
	return nil
}

// DeleteNotification soft deletes a notification
func (s *NotificationService) DeleteNotification(id uuid.UUID) error {
	err := s.db.Delete(&models.Notification{}, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}
	return nil
}

// GetNotificationStats returns notification statistics
func (s *NotificationService) GetNotificationStats(startDate, endDate time.Time) (map[string]interface{}, error) {
	var stats struct {
		TotalNotifications      int64 `json:"total_notifications"`
		SentNotifications       int64 `json:"sent_notifications"`
		DeliveredNotifications  int64 `json:"delivered_notifications"`
		FailedNotifications     int64 `json:"failed_notifications"`
		EmailNotifications      int64 `json:"email_notifications"`
		SMSNotifications        int64 `json:"sms_notifications"`
		InAppNotifications      int64 `json:"in_app_notifications"`
		PushNotifications       int64 `json:"push_notifications"`
	}
	
	query := s.db.Model(&models.Notification{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate)
	
	// Get total count
	if err := query.Count(&stats.TotalNotifications).Error; err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	
	// Get counts by status
	query.Where("status = ?", models.NotificationStatusSent).Count(&stats.SentNotifications)
	query.Where("status = ?", models.NotificationStatusDelivered).Count(&stats.DeliveredNotifications)
	query.Where("status = ?", models.NotificationStatusFailed).Count(&stats.FailedNotifications)
	
	// Reset query for type counts
	query = s.db.Model(&models.Notification{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate)
	
	// Get counts by type
	query.Where("type = ?", models.NotificationTypeEmail).Count(&stats.EmailNotifications)
	query.Where("type = ?", models.NotificationTypeSMS).Count(&stats.SMSNotifications)
	query.Where("type = ?", models.NotificationTypeInApp).Count(&stats.InAppNotifications)
	query.Where("type = ?", models.NotificationTypePush).Count(&stats.PushNotifications)
	
	// Calculate rates
	result := map[string]interface{}{
		"total_notifications":     stats.TotalNotifications,
		"sent_notifications":      stats.SentNotifications,
		"delivered_notifications": stats.DeliveredNotifications,
		"failed_notifications":    stats.FailedNotifications,
		"email_notifications":     stats.EmailNotifications,
		"sms_notifications":       stats.SMSNotifications,
		"in_app_notifications":    stats.InAppNotifications,
		"push_notifications":      stats.PushNotifications,
	}
	
	if stats.TotalNotifications > 0 {
		result["delivery_rate"] = float64(stats.DeliveredNotifications) / float64(stats.TotalNotifications) * 100
		result["failure_rate"] = float64(stats.FailedNotifications) / float64(stats.TotalNotifications) * 100
	}
	
	return result, nil
}

// processTemplate processes template data and updates notification content
func (s *NotificationService) processTemplate(notification *models.Notification) error {
	var template models.NotificationTemplate
	err := s.db.First(&template, "id = ? AND is_active = true", notification.TemplateID).Error
	if err != nil {
		return fmt.Errorf("template not found or inactive: %w", err)
	}
	
	// Verify template type matches notification type
	if template.Type != notification.Type {
		return fmt.Errorf("template type %s does not match notification type %s", template.Type, notification.Type)
	}
	
	// Process template content with data
	if notification.Subject == "" && template.Subject != "" {
		notification.Subject = s.processTemplateContent(template.Subject, notification.TemplateData)
	}
	
	if notification.Message == "" {
		notification.Message = s.processTemplateContent(template.Body, notification.TemplateData)
	}
	
	if notification.HTMLContent == "" && template.HTMLBody != "" {
		notification.HTMLContent = s.processTemplateContent(template.HTMLBody, notification.TemplateData)
	}
	
	return nil
}

// processTemplateContent replaces template variables with actual data
func (s *NotificationService) processTemplateContent(content string, data map[string]interface{}) string {
	// Simple template processing - replace {{variable}} with actual values
	// In production, you might want to use a more sophisticated template engine
	result := content

	if data != nil {
		for key, value := range data {
			placeholder := fmt.Sprintf("{{%s}}", key)
			replacement := fmt.Sprintf("%v", value)
			// Simple string replacement for template variables
			for i := 0; i < len(result); i++ {
				if i+len(placeholder) <= len(result) && result[i:i+len(placeholder)] == placeholder {
					result = result[:i] + replacement + result[i+len(placeholder):]
					i += len(replacement) - 1
				}
			}
		}
	}

	return result
}

// checkUserPreferences checks if user allows this type of notification
func (s *NotificationService) checkUserPreferences(userID uuid.UUID, notificationType models.NotificationType, category string) (bool, error) {
	var preference models.NotificationPreference
	err := s.db.First(&preference, "user_id = ?", userID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No preferences set, allow by default
			return true, nil
		}
		return false, err
	}
	
	return preference.IsNotificationAllowed(notificationType, category), nil
}

// addToQueue adds notification to processing queue
func (s *NotificationService) addToQueue(notification *models.Notification) error {
	queueItem := &models.NotificationQueue{
		NotificationID: notification.ID,
		Priority:       models.GetPriorityFromNotification(notification),
		ScheduledAt:    time.Now(),
		MaxRetries:     notification.MaxRetries,
		Status:         models.QueueStatusPending,
	}
	
	if notification.ScheduledAt != nil {
		queueItem.ScheduledAt = *notification.ScheduledAt
	}
	
	return s.db.Create(queueItem).Error
}
