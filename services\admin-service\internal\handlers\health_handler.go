package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HealthHandler handles health check requests
type HealthHandler struct {
	db *gorm.DB
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(db *gorm.DB) *HealthHandler {
	return &HealthHandler{
		db: db,
	}
}

// HealthCheck handles GET /health
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"service":   "admin-service",
		"version":   "1.0.0",
	}

	// Check database connection
	sqlDB, err := h.db.DB()
	if err != nil {
		health["status"] = "unhealthy"
		health["database"] = "connection_error"
		c.JSON(http.StatusServiceUnavailable, health)
		return
	}

	if err := sqlDB.Ping(); err != nil {
		health["status"] = "unhealthy"
		health["database"] = "ping_failed"
		c.JSON(http.StatusServiceUnavailable, health)
		return
	}

	health["database"] = "healthy"

	// Get database stats
	stats := sqlDB.Stats()
	health["database_stats"] = map[string]interface{}{
		"open_connections": stats.OpenConnections,
		"in_use":          stats.InUse,
		"idle":            stats.Idle,
	}

	c.JSON(http.StatusOK, health)
}

// ReadinessCheck handles GET /ready
func (h *HealthHandler) ReadinessCheck(c *gin.Context) {
	ready := map[string]interface{}{
		"status":    "ready",
		"timestamp": time.Now().UTC(),
		"service":   "admin-service",
	}

	// Check if service is ready to accept requests
	sqlDB, err := h.db.DB()
	if err != nil {
		ready["status"] = "not_ready"
		ready["reason"] = "database_connection_error"
		c.JSON(http.StatusServiceUnavailable, ready)
		return
	}

	if err := sqlDB.Ping(); err != nil {
		ready["status"] = "not_ready"
		ready["reason"] = "database_ping_failed"
		c.JSON(http.StatusServiceUnavailable, ready)
		return
	}

	c.JSON(http.StatusOK, ready)
}

// LivenessCheck handles GET /live
func (h *HealthHandler) LivenessCheck(c *gin.Context) {
	live := map[string]interface{}{
		"status":    "alive",
		"timestamp": time.Now().UTC(),
		"service":   "admin-service",
		"uptime":    time.Since(time.Now()).String(), // This would be calculated from service start time
	}

	c.JSON(http.StatusOK, live)
}
