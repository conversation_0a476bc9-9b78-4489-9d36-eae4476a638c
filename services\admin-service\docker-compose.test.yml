version: '3.8'

services:
  admin-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    environment:
      - PORT=8082
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8081
    depends_on:
      - redis
    networks:
      - crm-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - crm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  crm-network:
    driver: bridge
