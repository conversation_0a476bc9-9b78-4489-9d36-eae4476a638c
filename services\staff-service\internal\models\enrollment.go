package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// EnrollmentStatus represents the status of an enrollment
type EnrollmentStatus string

const (
	EnrollmentStatusActive    EnrollmentStatus = "ACTIVE"
	EnrollmentStatusCompleted EnrollmentStatus = "COMPLETED"
	EnrollmentStatusDropped   EnrollmentStatus = "DROPPED"
	EnrollmentStatusSuspended EnrollmentStatus = "SUSPENDED"
	EnrollmentStatusPending   EnrollmentStatus = "PENDING"
)

// IsValid checks if the enrollment status is valid
func (es EnrollmentStatus) IsValid() bool {
	switch es {
	case EnrollmentStatusActive, EnrollmentStatusCompleted, EnrollmentStatusDropped,
		 EnrollmentStatusSuspended, EnrollmentStatusPending:
		return true
	}
	return false
}

// Enrollment represents a student's enrollment in a course
type Enrollment struct {
	models.BaseModel
	StudentID        uuid.UUID        `json:"student_id" gorm:"not null;index"`
	Student          *Student         `json:"student,omitempty" gorm:"foreignKey:StudentID"`
	CourseID         uuid.UUID        `json:"course_id" gorm:"not null;index"`
	Course           *Course          `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	
	// Enrollment details
	Status           EnrollmentStatus `json:"status" gorm:"not null;default:'PENDING';index"`
	EnrollmentDate   time.Time        `json:"enrollment_date" gorm:"not null;index"`
	CompletionDate   *time.Time       `json:"completion_date"`
	DropDate         *time.Time       `json:"drop_date"`
	
	// Academic performance
	FinalGrade       *float64         `json:"final_grade"` // 0-100 scale
	AttendanceRate   *float64         `json:"attendance_rate"` // 0-100 percentage
	
	// Payment information
	TotalFee         float64          `json:"total_fee" gorm:"not null"`
	PaidAmount       float64          `json:"paid_amount" gorm:"default:0"`
	Currency         string           `json:"currency" gorm:"not null;default:'USD';size:3"`
	PaymentStatus    string           `json:"payment_status" gorm:"not null;default:'PENDING'"` // PENDING, PARTIAL, PAID, OVERDUE
	
	// Notes and comments
	Notes            string           `json:"notes" gorm:"type:text"`
	
	// Metadata (no foreign key constraint - references auth service)
	EnrolledByUserID uuid.UUID        `json:"enrolled_by_id" gorm:"column:enrolled_by_id;not null"`
	EnrolledBy       *models.User     `json:"enrolled_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
}

// IsActive checks if the enrollment is active
func (e *Enrollment) IsActive() bool {
	return e.Status == EnrollmentStatusActive
}

// IsCompleted checks if the enrollment is completed
func (e *Enrollment) IsCompleted() bool {
	return e.Status == EnrollmentStatusCompleted
}

// GetOutstandingAmount returns the outstanding payment amount
func (e *Enrollment) GetOutstandingAmount() float64 {
	return e.TotalFee - e.PaidAmount
}

// IsPaymentComplete checks if payment is complete
func (e *Enrollment) IsPaymentComplete() bool {
	return e.PaymentStatus == "PAID"
}

// EnrollmentCreateRequest represents a request to create an enrollment
type EnrollmentCreateRequest struct {
	StudentID uuid.UUID `json:"student_id" binding:"required"`
	CourseID  uuid.UUID `json:"course_id" binding:"required"`
	TotalFee  float64   `json:"total_fee" binding:"required,min=0"`
	Currency  string    `json:"currency" binding:"required,len=3"`
	Notes     string    `json:"notes"`
}

// EnrollmentUpdateRequest represents a request to update an enrollment
type EnrollmentUpdateRequest struct {
	Status         *EnrollmentStatus `json:"status"`
	CompletionDate *time.Time        `json:"completion_date"`
	DropDate       *time.Time        `json:"drop_date"`
	FinalGrade     *float64          `json:"final_grade" binding:"omitempty,min=0,max=100"`
	AttendanceRate *float64          `json:"attendance_rate" binding:"omitempty,min=0,max=100"`
	PaidAmount     *float64          `json:"paid_amount" binding:"omitempty,min=0"`
	PaymentStatus  *string           `json:"payment_status"`
	Notes          *string           `json:"notes"`
}

// EnrollmentResponse represents an enrollment response
type EnrollmentResponse struct {
	ID               uuid.UUID        `json:"id"`
	StudentID        uuid.UUID        `json:"student_id"`
	StudentName      string           `json:"student_name"`
	StudentEmail     string           `json:"student_email"`
	CourseID         uuid.UUID        `json:"course_id"`
	CourseName       string           `json:"course_name"`
	CourseCode       string           `json:"course_code"`
	Status           EnrollmentStatus `json:"status"`
	EnrollmentDate   time.Time        `json:"enrollment_date"`
	CompletionDate   *time.Time       `json:"completion_date"`
	DropDate         *time.Time       `json:"drop_date"`
	FinalGrade       *float64         `json:"final_grade"`
	AttendanceRate   *float64         `json:"attendance_rate"`
	TotalFee         float64          `json:"total_fee"`
	PaidAmount       float64          `json:"paid_amount"`
	OutstandingAmount float64         `json:"outstanding_amount"`
	Currency         string           `json:"currency"`
	PaymentStatus    string           `json:"payment_status"`
	Notes            string           `json:"notes"`
	EnrolledByID     uuid.UUID        `json:"enrolled_by_id"`
	EnrolledByName   string           `json:"enrolled_by_name"`
	CreatedAt        time.Time        `json:"created_at"`
	UpdatedAt        time.Time        `json:"updated_at"`
}

// ToResponse converts an Enrollment to EnrollmentResponse
func (e *Enrollment) ToResponse() *EnrollmentResponse {
	response := &EnrollmentResponse{
		ID:                e.ID,
		StudentID:         e.StudentID,
		CourseID:          e.CourseID,
		Status:            e.Status,
		EnrollmentDate:    e.EnrollmentDate,
		CompletionDate:    e.CompletionDate,
		DropDate:          e.DropDate,
		FinalGrade:        e.FinalGrade,
		AttendanceRate:    e.AttendanceRate,
		TotalFee:          e.TotalFee,
		PaidAmount:        e.PaidAmount,
		OutstandingAmount: e.GetOutstandingAmount(),
		Currency:          e.Currency,
		PaymentStatus:     e.PaymentStatus,
		Notes:             e.Notes,
		EnrolledByID:      e.EnrolledByUserID,
		CreatedAt:         e.CreatedAt,
		UpdatedAt:         e.UpdatedAt,
	}

	// Include student information if available
	if e.Student != nil {
		response.StudentName = e.Student.GetFullName()
		response.StudentEmail = e.Student.Email
	}

	// Include course information if available
	if e.Course != nil {
		response.CourseName = e.Course.Name
		response.CourseCode = e.Course.CourseCode
	}

	// Include enrolled by user information if available
	if e.EnrolledBy != nil {
		response.EnrolledByName = e.EnrolledBy.GetFullName()
	}

	return response
}

// EnrollmentListRequest represents a request to list enrollments
type EnrollmentListRequest struct {
	models.PaginationRequest
	StudentID     *uuid.UUID        `json:"student_id" form:"student_id"`
	CourseID      *uuid.UUID        `json:"course_id" form:"course_id"`
	Status        *EnrollmentStatus `json:"status" form:"status"`
	PaymentStatus *string           `json:"payment_status" form:"payment_status"`
	EnrolledByID  *uuid.UUID        `json:"enrolled_by_id" form:"enrolled_by_id"`
	Search        string            `json:"search" form:"search"`
	StartDate     *time.Time        `json:"start_date" form:"start_date"`
	EndDate       *time.Time        `json:"end_date" form:"end_date"`
}

// EnrollmentListResponse represents a response for enrollment list
type EnrollmentListResponse struct {
	Enrollments []*EnrollmentResponse      `json:"enrollments"`
	Pagination  *models.PaginationResponse `json:"pagination"`
}

// EnrollmentStats represents enrollment statistics
type EnrollmentStats struct {
	TotalEnrollments       int64                         `json:"total_enrollments"`
	EnrollmentsByStatus    map[EnrollmentStatus]int64    `json:"enrollments_by_status"`
	EnrollmentsByPayment   map[string]int64              `json:"enrollments_by_payment"`
	TotalRevenue           float64                       `json:"total_revenue"`
	OutstandingAmount      float64                       `json:"outstanding_amount"`
	AverageGrade           *float64                      `json:"average_grade"`
	AverageAttendance      *float64                      `json:"average_attendance"`
	NewEnrollmentsToday    int64                         `json:"new_enrollments_today"`
	NewEnrollmentsThisWeek int64                         `json:"new_enrollments_this_week"`
	CompletionRate         float64                       `json:"completion_rate"`
	DropoutRate            float64                       `json:"dropout_rate"`
}

// EnrollmentPaymentUpdateRequest represents a request to update enrollment payment status
type EnrollmentPaymentUpdateRequest struct {
	PaymentStatus string  `json:"payment_status" binding:"required"`
	AmountPaid    float64 `json:"amount_paid" binding:"min=0"`
	PaymentDate   *time.Time `json:"payment_date"`
	PaymentMethod string  `json:"payment_method"`
	Notes         string  `json:"notes"`
}

// BulkEnrollmentPaymentUpdateRequest represents a request to bulk update enrollment payment status
type BulkEnrollmentPaymentUpdateRequest struct {
	EnrollmentIDs []uuid.UUID `json:"enrollment_ids" binding:"required,min=1"`
	PaymentStatus string      `json:"payment_status" binding:"required"`
	PaymentDate   *time.Time  `json:"payment_date"`
	PaymentMethod string      `json:"payment_method"`
	Notes         string      `json:"notes"`
}

// EnrollmentCompletionRequest represents a request to mark enrollment as completed
type EnrollmentCompletionRequest struct {
	CompletionDate *time.Time `json:"completion_date"`
	FinalGrade     *float64   `json:"final_grade" binding:"omitempty,min=0,max=100"`
	Notes          string     `json:"notes"`
}

// EnrollmentDropRequest represents a request to drop an enrollment
type EnrollmentDropRequest struct {
	DropDate   *time.Time `json:"drop_date"`
	DropReason string     `json:"drop_reason" binding:"required"`
	RefundAmount *float64 `json:"refund_amount" binding:"omitempty,min=0"`
	Notes      string     `json:"notes"`
}
