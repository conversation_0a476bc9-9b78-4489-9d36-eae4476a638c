package repository

import (
	"fmt"
	"time"

	"admin-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
	sharedModels "github.com/crm-microservices/shared/models"
)

// AuditRepository handles audit log data operations
type AuditRepository interface {
	Create(auditLog *models.AuditLog) error
	GetAll(req *models.AuditLogListRequest) ([]*models.AuditLog, int64, error)
	GetByID(id uuid.UUID) (*models.AuditLog, error)
	GetStats(startDate, endDate *time.Time) (*models.AuditLogStats, error)
	GetRecentActivity(limit int) ([]*models.AuditLog, error)
	DeleteOldLogs(olderThan time.Time) (int64, error)
}

type auditRepository struct {
	db *gorm.DB
}

// NewAuditRepository creates a new audit repository
func NewAuditRepository(db *gorm.DB) AuditRepository {
	return &auditRepository{db: db}
}

// Create creates a new audit log entry
func (r *auditRepository) Create(auditLog *models.AuditLog) error {
	if err := r.db.Create(auditLog).Error; err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}
	return nil
}

// GetAll retrieves audit logs with pagination and filtering
func (r *auditRepository) GetAll(req *models.AuditLogListRequest) ([]*models.AuditLog, int64, error) {
	var auditLogs []*models.AuditLog
	var total int64

	query := r.db.Model(&models.AuditLog{}).Preload("User")

	// Apply filters
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	if req.Action != nil {
		query = query.Where("action = ?", *req.Action)
	}

	if req.Resource != nil {
		query = query.Where("resource = ?", *req.Resource)
	}

	if req.ResourceID != nil {
		query = query.Where("resource_id = ?", *req.ResourceID)
	}

	if req.StartDate != nil {
		query = query.Where("timestamp >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("timestamp <= ?", *req.EndDate)
	}

	if req.IPAddress != nil {
		query = query.Where("ip_address = ?", *req.IPAddress)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count audit logs: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("timestamp DESC").Find(&auditLogs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get audit logs: %w", err)
	}

	return auditLogs, total, nil
}

// GetByID retrieves an audit log by ID
func (r *auditRepository) GetByID(id uuid.UUID) (*models.AuditLog, error) {
	var auditLog models.AuditLog
	if err := r.db.Preload("User").First(&auditLog, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("audit log not found")
		}
		return nil, fmt.Errorf("failed to get audit log: %w", err)
	}
	return &auditLog, nil
}

// GetStats retrieves audit log statistics
func (r *auditRepository) GetStats(startDate, endDate *time.Time) (*models.AuditLogStats, error) {
	stats := &models.AuditLogStats{
		LogsByAction:   make(map[sharedModels.AuditAction]int64),
		LogsByResource: make(map[string]int64),
		LogsByUser:     make(map[uuid.UUID]int64),
	}

	query := r.db.Model(&models.AuditLog{})

	// Apply date filters
	if startDate != nil {
		query = query.Where("timestamp >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("timestamp <= ?", *endDate)
	}

	// Total logs
	if err := query.Count(&stats.TotalLogs).Error; err != nil {
		return nil, fmt.Errorf("failed to count total logs: %w", err)
	}

	// Logs by action
	actions := []sharedModels.AuditAction{
		sharedModels.ActionCreate, sharedModels.ActionRead, sharedModels.ActionUpdate,
		sharedModels.ActionDelete, sharedModels.ActionLogin, sharedModels.ActionLogout,
	}

	for _, action := range actions {
		var count int64
		actionQuery := r.db.Model(&models.AuditLog{})
		if startDate != nil {
			actionQuery = actionQuery.Where("timestamp >= ?", *startDate)
		}
		if endDate != nil {
			actionQuery = actionQuery.Where("timestamp <= ?", *endDate)
		}
		if err := actionQuery.Where("action = ?", action).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count logs by action %s: %w", action, err)
		}
		stats.LogsByAction[action] = count
	}

	// Logs by resource
	var resourceResults []struct {
		Resource string `json:"resource"`
		Count    int64  `json:"count"`
	}

	resourceQuery := r.db.Model(&models.AuditLog{}).Select("resource, COUNT(*) as count").Group("resource")
	if startDate != nil {
		resourceQuery = resourceQuery.Where("timestamp >= ?", *startDate)
	}
	if endDate != nil {
		resourceQuery = resourceQuery.Where("timestamp <= ?", *endDate)
	}

	if err := resourceQuery.Scan(&resourceResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get logs by resource: %w", err)
	}

	for _, result := range resourceResults {
		stats.LogsByResource[result.Resource] = result.Count
	}

	// Logs by user
	var userResults []struct {
		UserID uuid.UUID `json:"user_id"`
		Count  int64     `json:"count"`
	}

	userQuery := r.db.Model(&models.AuditLog{}).Select("user_id, COUNT(*) as count").Group("user_id").Limit(10)
	if startDate != nil {
		userQuery = userQuery.Where("timestamp >= ?", *startDate)
	}
	if endDate != nil {
		userQuery = userQuery.Where("timestamp <= ?", *endDate)
	}

	if err := userQuery.Scan(&userResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get logs by user: %w", err)
	}

	for _, result := range userResults {
		stats.LogsByUser[result.UserID] = result.Count
	}

	// Recent activity
	recentLogs, err := r.GetRecentActivity(10)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activity: %w", err)
	}

	stats.RecentActivity = make([]*models.AuditLogResponse, len(recentLogs))
	for i, log := range recentLogs {
		stats.RecentActivity[i] = log.ToResponse()
	}

	return stats, nil
}

// GetRecentActivity retrieves recent audit log activity
func (r *auditRepository) GetRecentActivity(limit int) ([]*models.AuditLog, error) {
	var auditLogs []*models.AuditLog
	if err := r.db.Preload("User").Order("timestamp DESC").Limit(limit).Find(&auditLogs).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent activity: %w", err)
	}
	return auditLogs, nil
}

// DeleteOldLogs deletes audit logs older than the specified date
func (r *auditRepository) DeleteOldLogs(olderThan time.Time) (int64, error) {
	result := r.db.Where("timestamp < ?", olderThan).Delete(&models.AuditLog{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to delete old logs: %w", result.Error)
	}
	return result.RowsAffected, nil
}
