import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { authService } from '@/services/auth';
import type { User, LoginRequest, AuthContextType } from '@/types/auth';
import { toast } from 'react-hot-toast';

interface AuthState extends AuthContextType {
  // Additional state
  error: string | null;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Internal methods
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
  
  // Initialize auth state
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,

      // Actions
      setUser: (user) => {
        set({ 
          user, 
          isAuthenticated: !!user 
        });
      },

      setToken: (token) => {
        set({ token });
        if (token) {
          authService.setAuthToken(token);
        } else {
          authService.clearTokens();
        }
      },

      setLoading: (isLoading) => {
        set({ isLoading });
      },

      setError: (error) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      updateUser: (user) => {
        set({ user });
      },

      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authService.login(credentials);
          
          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          toast.success(`Welcome back, ${response.user.first_name}!`);
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Login failed';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: () => {
        try {
          authService.logout();
        } catch (error) {
          console.warn('Logout error:', error);
        } finally {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          toast.success('Logged out successfully');
        }
      },

      refreshToken: async () => {
        try {
          const response = await authService.refreshToken();
          
          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            error: null,
          });
        } catch (error: any) {
          console.warn('Token refresh failed:', error);
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      initialize: async () => {
        console.log('Auth: Starting initialization...');
        try {
          set({ isLoading: true });

          // Check if user is authenticated
          if (!authService.isAuthenticated()) {
            console.log('Auth: No token found, setting unauthenticated state');
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false,
            });
            return;
          }

          console.log('Auth: Token found, trying to get current user...');

          // Add timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Auth initialization timeout')), 10000)
          );

          // Try to get current user with timeout
          const user = await Promise.race([
            authService.getCurrentUser(),
            timeoutPromise
          ]) as User;

          const token = authService.getToken();
          console.log('Auth: Successfully got user data');

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          console.warn('Auth initialization failed:', error);

          // Don't try to refresh on timeout or network errors, just clear auth
          console.log('Auth: Clearing auth state due to error');
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          authService.clearTokens();
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors for easier access
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    token: store.token,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    login: store.login,
    logout: store.logout,
    refreshToken: store.refreshToken,
    updateUser: store.updateUser,
    clearError: store.clearError,
    initialize: store.initialize,
  };
};

export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
