-- Payment Service Database Initialization Script
-- Database: ep-floral-rice (Payment Service)
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- gen_random_uuid() function is already available in PostgreSQL 13+

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS refunds CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS payments CASCADE;

-- Create payments table (main payment records)
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    method VARCHAR(20) NOT NULL,
    
    -- External references (no FK constraints - different services)
    user_id UUID NOT NULL, -- References auth service users
    enrollment_id UUID, -- References staff service enrollments
    course_id UUID, -- References staff service courses
    
    -- Payment gateway information
    gateway_type VARCHAR(20) NOT NULL, -- STRIPE, PAYPAL, BANK_TRANSFER, CASH
    gateway_payment_id VARCHAR(255), -- External payment ID from gateway
    gateway_customer_id VARCHAR(255), -- Customer ID in payment gateway
    
    -- Payment details
    description TEXT,
    payment_date TIMESTAMPTZ,
    due_date TIMESTAMPTZ,
    
    -- Processing information
    processed_by_id UUID, -- References auth service users (no FK constraint)
    
    -- Metadata
    metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create transactions table (individual payment attempts/transactions)
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    
    -- Transaction details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    
    -- Gateway information
    gateway_type VARCHAR(20) NOT NULL,
    gateway_transaction_id VARCHAR(255),
    gateway_response JSONB,
    
    -- Processing details
    processed_at TIMESTAMPTZ,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Metadata
    metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create refunds table (refund records)
CREATE TABLE refunds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    transaction_id UUID REFERENCES transactions(id) ON DELETE SET NULL,
    
    -- Refund details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    reason TEXT,
    
    -- Gateway information
    gateway_refund_id VARCHAR(255),
    gateway_response JSONB,
    
    -- Processing details
    processed_at TIMESTAMPTZ,
    processed_by_id UUID, -- References auth service users (no FK constraint)
    
    -- Metadata
    metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for payments table
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_enrollment_id ON payments(enrollment_id);
CREATE INDEX idx_payments_course_id ON payments(course_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_method ON payments(method);
CREATE INDEX idx_payments_gateway_type ON payments(gateway_type);
CREATE INDEX idx_payments_gateway_payment_id ON payments(gateway_payment_id);
CREATE INDEX idx_payments_payment_date ON payments(payment_date);
CREATE INDEX idx_payments_due_date ON payments(due_date);
CREATE INDEX idx_payments_created_at ON payments(created_at);
CREATE INDEX idx_payments_deleted_at ON payments(deleted_at);

-- Create indexes for transactions table
CREATE INDEX idx_transactions_payment_id ON transactions(payment_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_gateway_type ON transactions(gateway_type);
CREATE INDEX idx_transactions_gateway_transaction_id ON transactions(gateway_transaction_id);
CREATE INDEX idx_transactions_processed_at ON transactions(processed_at);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);

-- Create indexes for refunds table
CREATE INDEX idx_refunds_payment_id ON refunds(payment_id);
CREATE INDEX idx_refunds_transaction_id ON refunds(transaction_id);
CREATE INDEX idx_refunds_status ON refunds(status);
CREATE INDEX idx_refunds_gateway_refund_id ON refunds(gateway_refund_id);
CREATE INDEX idx_refunds_processed_at ON refunds(processed_at);
CREATE INDEX idx_refunds_created_at ON refunds(created_at);
CREATE INDEX idx_refunds_deleted_at ON refunds(deleted_at);

-- Create composite indexes for common queries
CREATE INDEX idx_payments_user_status ON payments(user_id, status);
CREATE INDEX idx_transactions_payment_status ON transactions(payment_id, status);
CREATE INDEX idx_refunds_payment_status ON refunds(payment_id, status);

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_payments_updated_at
    BEFORE UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_refunds_updated_at
    BEFORE UPDATE ON refunds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add check constraints
ALTER TABLE payments ADD CONSTRAINT chk_payments_status
    CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED'));

ALTER TABLE payments ADD CONSTRAINT chk_payments_method
    CHECK (method IN ('CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER', 'PAYPAL', 'STRIPE', 'CASH', 'CHECK'));

ALTER TABLE payments ADD CONSTRAINT chk_payments_gateway_type
    CHECK (gateway_type IN ('STRIPE', 'PAYPAL', 'BANK_TRANSFER', 'CASH', 'CHECK'));

ALTER TABLE payments ADD CONSTRAINT chk_payments_amount
    CHECK (amount > 0);

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_status
    CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'));

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_gateway_type
    CHECK (gateway_type IN ('STRIPE', 'PAYPAL', 'BANK_TRANSFER', 'CASH', 'CHECK'));

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_amount
    CHECK (amount > 0);

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_retry_count
    CHECK (retry_count >= 0);

ALTER TABLE refunds ADD CONSTRAINT chk_refunds_status
    CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'));

ALTER TABLE refunds ADD CONSTRAINT chk_refunds_amount
    CHECK (amount > 0);

-- Add currency constraints
ALTER TABLE payments ADD CONSTRAINT chk_payments_currency
    CHECK (currency IN ('USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR'));

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_currency
    CHECK (currency IN ('USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR'));

ALTER TABLE refunds ADD CONSTRAINT chk_refunds_currency
    CHECK (currency IN ('USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR'));

COMMIT;
