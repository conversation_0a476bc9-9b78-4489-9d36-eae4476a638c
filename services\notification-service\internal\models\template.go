package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationTemplate represents a notification template
type NotificationTemplate struct {
	ID          uuid.UUID        `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string           `json:"name" gorm:"not null;uniqueIndex;index"`
	Type        NotificationType `json:"type" gorm:"not null;index"`
	
	// Template content
	Subject     string `json:"subject" gorm:"type:text"`
	Body        string `json:"body" gorm:"type:text;not null"`
	HTMLBody    string `json:"html_body" gorm:"type:text"`
	
	// Template metadata
	Description string                 `json:"description" gorm:"type:text"`
	Variables   []string               `json:"variables" gorm:"type:jsonb"` // List of template variables
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Template settings
	IsActive    bool `json:"is_active" gorm:"default:true"`
	IsDefault   bool `json:"is_default" gorm:"default:false"`
	Version     int  `json:"version" gorm:"default:1"`
	
	// Audit fields
	CreatedByID uuid.UUID      `json:"created_by_id" gorm:"type:uuid;not null"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	
	// Relationships
	Notifications []Notification `json:"notifications,omitempty" gorm:"foreignKey:TemplateID"`
}

// BeforeCreate sets the ID if not provided
func (t *NotificationTemplate) BeforeCreate(tx *gorm.DB) error {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	return nil
}

// IsValid validates the template
func (t *NotificationTemplate) IsValid() bool {
	if t.Name == "" || t.Body == "" {
		return false
	}
	
	switch t.Type {
	case NotificationTypeEmail, NotificationTypeSMS, NotificationTypeInApp, NotificationTypePush:
		return true
	default:
		return false
	}
}

// CreateTemplateRequest represents a request to create a template
type CreateTemplateRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        NotificationType       `json:"type" binding:"required"`
	Subject     string                 `json:"subject"`
	Body        string                 `json:"body" binding:"required"`
	HTMLBody    string                 `json:"html_body"`
	Description string                 `json:"description"`
	Variables   []string               `json:"variables"`
	Metadata    map[string]interface{} `json:"metadata"`
	IsActive    *bool                  `json:"is_active"`
	IsDefault   *bool                  `json:"is_default"`
}

// ToTemplate converts the request to a template model
func (r *CreateTemplateRequest) ToTemplate(createdByID uuid.UUID) *NotificationTemplate {
	template := &NotificationTemplate{
		Name:        r.Name,
		Type:        r.Type,
		Subject:     r.Subject,
		Body:        r.Body,
		HTMLBody:    r.HTMLBody,
		Description: r.Description,
		Variables:   r.Variables,
		Metadata:    r.Metadata,
		CreatedByID: createdByID,
		IsActive:    true,
		IsDefault:   false,
		Version:     1,
	}
	
	if r.IsActive != nil {
		template.IsActive = *r.IsActive
	}
	
	if r.IsDefault != nil {
		template.IsDefault = *r.IsDefault
	}
	
	return template
}

// UpdateTemplateRequest represents a request to update a template
type UpdateTemplateRequest struct {
	Name        *string                `json:"name"`
	Subject     *string                `json:"subject"`
	Body        *string                `json:"body"`
	HTMLBody    *string                `json:"html_body"`
	Description *string                `json:"description"`
	Variables   []string               `json:"variables"`
	Metadata    map[string]interface{} `json:"metadata"`
	IsActive    *bool                  `json:"is_active"`
	IsDefault   *bool                  `json:"is_default"`
}

// ApplyUpdates applies the updates to the template
func (t *NotificationTemplate) ApplyUpdates(req *UpdateTemplateRequest) {
	if req.Name != nil {
		t.Name = *req.Name
	}
	if req.Subject != nil {
		t.Subject = *req.Subject
	}
	if req.Body != nil {
		t.Body = *req.Body
	}
	if req.HTMLBody != nil {
		t.HTMLBody = *req.HTMLBody
	}
	if req.Description != nil {
		t.Description = *req.Description
	}
	if req.Variables != nil {
		t.Variables = req.Variables
	}
	if req.Metadata != nil {
		t.Metadata = req.Metadata
	}
	if req.IsActive != nil {
		t.IsActive = *req.IsActive
	}
	if req.IsDefault != nil {
		t.IsDefault = *req.IsDefault
	}
	
	// Increment version on update
	t.Version++
}
