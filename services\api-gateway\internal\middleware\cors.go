package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
)

// CORSMiddleware provides CORS (Cross-Origin Resource Sharing) functionality
type CORSMiddleware struct {
	allowedOrigins []string
	allowedMethods []string
	allowedHeaders []string
	maxAge         int
}

// NewCORSMiddleware creates a new CORS middleware
func NewCORSMiddleware(allowedOrigins []string) *CORSMiddleware {
	return &CORSMiddleware{
		allowedOrigins: allowedOrigins,
		allowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
		allowedHeaders: []string{
			"Origin",
			"Content-Type",
			"Accept",
			"Authorization",
			"X-Requested-With",
			"X-Request-ID",
			"X-Correlation-ID",
			"X-User-ID",
			"X-User-Role",
		},
		maxAge: 86400, // 24 hours
	}
}

// Handler returns the CORS middleware handler
func (cm *CORSMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.GetHeader("Origin")

		// Check if origin is allowed
		if cm.isOriginAllowed(origin) {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		} else if len(cm.allowedOrigins) == 1 && cm.allowedOrigins[0] == "*" {
			c.Header("Access-Control-Allow-Origin", "*")
		}

		// Set other CORS headers
		c.Header("Access-Control-Allow-Methods", strings.Join(cm.allowedMethods, ", "))
		c.Header("Access-Control-Allow-Headers", strings.Join(cm.allowedHeaders, ", "))
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// isOriginAllowed checks if the origin is in the allowed list
func (cm *CORSMiddleware) isOriginAllowed(origin string) bool {
	for _, allowedOrigin := range cm.allowedOrigins {
		if allowedOrigin == "*" || allowedOrigin == origin {
			return true
		}
		// Support wildcard subdomains (e.g., *.example.com)
		if strings.HasPrefix(allowedOrigin, "*.") {
			domain := strings.TrimPrefix(allowedOrigin, "*.")
			if strings.HasSuffix(origin, domain) {
				return true
			}
		}
	}
	return false
}

// SetAllowedOrigins updates the allowed origins
func (cm *CORSMiddleware) SetAllowedOrigins(origins []string) {
	cm.allowedOrigins = origins
}

// SetAllowedMethods updates the allowed methods
func (cm *CORSMiddleware) SetAllowedMethods(methods []string) {
	cm.allowedMethods = methods
}

// SetAllowedHeaders updates the allowed headers
func (cm *CORSMiddleware) SetAllowedHeaders(headers []string) {
	cm.allowedHeaders = headers
}

// SetMaxAge updates the max age for preflight requests
func (cm *CORSMiddleware) SetMaxAge(maxAge int) {
	cm.maxAge = maxAge
}
