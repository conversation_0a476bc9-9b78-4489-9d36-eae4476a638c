package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"payment-service/internal/repository"
	"payment-service/internal/services"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// PaymentHandler handles payment-related HTTP requests
type PaymentHandler struct {
	paymentService services.PaymentService
}

// NewPaymentHandler creates a new payment handler
func NewPaymentHandler(paymentService services.PaymentService) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
	}
}

// GetPayments handles GET /payments
func (h *PaymentHandler) GetPayments(c *gin.Context) {
	var req repository.PaymentListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	if status := c.Query("status"); status != "" {
		paymentStatus := models.PaymentStatus(status)
		if paymentStatus.IsValid() {
			req.Status = &paymentStatus
		}
	}

	if method := c.Query("method"); method != "" {
		paymentMethod := models.PaymentMethod(method)
		if paymentMethod.IsValid() {
			req.Method = &paymentMethod
		}
	}

	if studentID := c.Query("student_id"); studentID != "" {
		if id, err := uuid.Parse(studentID); err == nil {
			req.StudentID = &id
		}
	}

	if courseID := c.Query("course_id"); courseID != "" {
		if id, err := uuid.Parse(courseID); err == nil {
			req.CourseID = &id
		}
	}

	if gatewayType := c.Query("gateway_type"); gatewayType != "" {
		req.GatewayType = &gatewayType
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = &date
		}
	}

	if search := c.Query("search"); search != "" {
		req.Search = &search
	}

	response, err := h.paymentService.GetPayments(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.PaginatedResponse(response.Payments, response.Pagination, "Payments retrieved successfully"))
}

// GetPayment handles GET /payments/:id
func (h *PaymentHandler) GetPayment(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid payment ID"))
		return
	}

	response, err := h.paymentService.GetPayment(id)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse("Payment not found"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Payment, "Payment retrieved successfully"))
}

// CreatePayment handles POST /payments
func (h *PaymentHandler) CreatePayment(c *gin.Context) {
	var req services.PaymentCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.paymentService.CreatePayment(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse(response.Payment, "Payment created successfully"))
}

// UpdatePayment handles PUT /payments/:id
func (h *PaymentHandler) UpdatePayment(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid payment ID"))
		return
	}

	var req services.PaymentUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.paymentService.UpdatePayment(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Payment, "Payment updated successfully"))
}

// DeletePayment handles DELETE /payments/:id
func (h *PaymentHandler) DeletePayment(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid payment ID"))
		return
	}

	if err := h.paymentService.DeletePayment(id); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil, "Payment deleted successfully"))
}

// ProcessPayment handles POST /payments/:id/process
func (h *PaymentHandler) ProcessPayment(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid payment ID"))
		return
	}

	var req struct {
		GatewayType string `json:"gateway_type" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.paymentService.ProcessPayment(id, req.GatewayType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Payment, "Payment processed successfully"))
}

// RefundPayment handles POST /payments/:id/refund
func (h *PaymentHandler) RefundPayment(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid payment ID"))
		return
	}

	var req services.RefundRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.paymentService.RefundPayment(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Payment, "Payment refunded successfully"))
}

// GetPaymentStats handles GET /payments/stats
func (h *PaymentHandler) GetPaymentStats(c *gin.Context) {
	stats, err := h.paymentService.GetPaymentStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats, "Payment statistics retrieved successfully"))
}

// GetFinancialReport handles GET /reports/financial
func (h *PaymentHandler) GetFinancialReport(c *gin.Context) {
	// Parse date range parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to last 30 days
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	stats, err := h.paymentService.GetRevenueStats(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats, "Financial report retrieved successfully"))
}

// GetRevenueReport handles GET /reports/revenue
func (h *PaymentHandler) GetRevenueReport(c *gin.Context) {
	// Parse date range parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to current month
		now := time.Now()
		startDate = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	stats, err := h.paymentService.GetRevenueStats(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats, "Revenue report retrieved successfully"))
}
