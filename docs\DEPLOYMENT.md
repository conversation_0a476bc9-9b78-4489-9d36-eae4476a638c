# 🚀 Deployment Guide - Go Docker Platform

This guide provides comprehensive instructions for deploying the CRM microservices system to the Render platform.

## 📋 Overview

The Go Docker Platform is deployed as 6 independent microservices on Render, each with its own dedicated Neon PostgreSQL database. This architecture ensures scalability, reliability, and easy maintenance.

## 🏗️ Deployment Architecture

### Production Services
| Service | Render URL | Database | Status |
|---------|------------|----------|--------|
| API Gateway | https://crm-api-gateway.onrender.com | N/A | ✅ Live |
| Auth Service | https://crm-auth-service.onrender.com | ep-wandering-fire | ✅ Live |
| Admin Service | https://crm-admin-service.onrender.com | ep-red-heart | ✅ Live |
| Staff Service | https://crm-staff-service.onrender.com | ep-shy-fire | ✅ Live |
| Payment Service | https://crm-payment-service.onrender.com | ep-floral-rice | ✅ Live |
| Notification Service | https://crm-notification-service.onrender.com | ep-tight-fog | ✅ Live |

### Database Configuration
Each service has its own dedicated Neon PostgreSQL database with the following configuration:
- **Provider**: Neon PostgreSQL
- **SSL Mode**: Required
- **Connection Pooling**: Enabled
- **Auto-scaling**: Enabled
- **Backup**: Automated daily backups

## 🔧 Prerequisites

### Required Accounts
1. **GitHub Account** - For source code repository
2. **Render Account** - For service deployment
3. **Neon Account** - For PostgreSQL databases

### Required Tools
- Git (for repository management)
- Web browser (for Render dashboard)
- curl or Postman (for testing)

## 📦 Database Setup

### Step 1: Create Neon Databases

1. **Login to Neon Console**: https://console.neon.tech
2. **Create Project**: "CRM Microservices"
3. **Create Databases** for each service:

```sql
-- Auth Service Database (ep-wandering-fire)
CREATE DATABASE neondb;
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Admin Service Database (ep-red-heart)
CREATE DATABASE neondb;
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Staff Service Database (ep-shy-fire)
CREATE DATABASE neondb;
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Payment Service Database (ep-floral-rice)
CREATE DATABASE neondb;
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Notification Service Database (ep-tight-fog)
CREATE DATABASE neondb;
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

### Step 2: Initialize Database Schemas

Run the initialization scripts for each database:

```bash
# Initialize Auth Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-auth-database.sql

# Initialize Admin Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-admin-database.sql

# Initialize Staff Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-staff-database.sql

# Initialize Payment Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-payment-database.sql

# Initialize Notification Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-notification-database.sql
```

## 🚀 Service Deployment

### Step 1: Prepare Repository

1. **Fork Repository**: https://github.com/MrFarrukhT/Go-Docker-Platform
2. **Clone Your Fork**:
```bash
git clone https://github.com/YOUR_USERNAME/Go-Docker-Platform.git
cd Go-Docker-Platform
```

### Step 2: Deploy Services to Render

#### Auth Service Deployment
1. **Create New Web Service** in Render
2. **Configuration**:
   - **Name**: `crm-auth-service`
   - **Repository**: Your forked repository
   - **Branch**: `main`
   - **Root Directory**: `services/auth-service`
   - **Build Command**: `go build -o main .`
   - **Start Command**: `./main`

3. **Environment Variables**:
```env
PORT=8081
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
REDIS_URL=redis://localhost:6379
AUTO_MIGRATE=false
```

#### Admin Service Deployment
1. **Create New Web Service** in Render
2. **Configuration**:
   - **Name**: `crm-admin-service`
   - **Repository**: Your forked repository
   - **Branch**: `main`
   - **Root Directory**: `services/admin-service`
   - **Build Command**: `go build -o main .`
   - **Start Command**: `./main`

3. **Environment Variables**:
```env
PORT=8082
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
AUTO_MIGRATE=false
```

#### Staff Service Deployment
1. **Create New Web Service** in Render
2. **Configuration**:
   - **Name**: `crm-staff-service`
   - **Repository**: Your forked repository
   - **Branch**: `main`
   - **Root Directory**: `services/staff-service`
   - **Build Command**: `go build -o main .`
   - **Start Command**: `./main`

3. **Environment Variables**:
```env
PORT=8083
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
AUTO_MIGRATE=false
```

#### Payment Service Deployment
1. **Create New Web Service** in Render
2. **Configuration**:
   - **Name**: `crm-payment-service`
   - **Repository**: Your forked repository
   - **Branch**: `main`
   - **Root Directory**: `services/payment-service`
   - **Build Command**: `go build -o main .`
   - **Start Command**: `./main`

3. **Environment Variables**:
```env
PORT=8084
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
STAFF_SERVICE_URL=https://crm-staff-service.onrender.com
AUTO_MIGRATE=false
```

#### Notification Service Deployment
1. **Create New Web Service** in Render
2. **Configuration**:
   - **Name**: `crm-notification-service`
   - **Repository**: Your forked repository
   - **Branch**: `main`
   - **Root Directory**: `services/notification-service`
   - **Build Command**: `go build -o main .`
   - **Start Command**: `./main`

3. **Environment Variables**:
```env
PORT=8085
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
AUTO_MIGRATE=false
```

#### API Gateway Deployment
1. **Create New Web Service** in Render
2. **Configuration**:
   - **Name**: `crm-api-gateway`
   - **Repository**: Your forked repository
   - **Branch**: `main`
   - **Root Directory**: `services/api-gateway`
   - **Build Command**: `go build -o main .`
   - **Start Command**: `./main`

3. **Environment Variables**:
```env
PORT=8080
ENVIRONMENT=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
STAFF_SERVICE_URL=https://crm-staff-service.onrender.com
PAYMENT_SERVICE_URL=https://crm-payment-service.onrender.com
NOTIFICATION_SERVICE_URL=https://crm-notification-service.onrender.com
```

## ✅ Deployment Verification

### Step 1: Health Checks
```bash
# Check API Gateway
curl https://crm-api-gateway.onrender.com/health

# Check Auth Service
curl https://crm-auth-service.onrender.com/health

# Check Admin Service
curl https://crm-admin-service.onrender.com/health

# Check Staff Service
curl https://crm-staff-service.onrender.com/health

# Check Payment Service
curl https://crm-payment-service.onrender.com/health

# Check Notification Service
curl https://crm-notification-service.onrender.com/health
```

### Step 2: Database Connectivity
```bash
# Test database connections through services
curl https://crm-auth-service.onrender.com/health/db
curl https://crm-admin-service.onrender.com/health/db
curl https://crm-staff-service.onrender.com/health/db
curl https://crm-payment-service.onrender.com/health/db
curl https://crm-notification-service.onrender.com/health/db
```

### Step 3: Service Integration
```bash
# Test service-to-service communication
curl -X POST https://crm-api-gateway.onrender.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 🔧 Configuration Management

### Environment Variables
All services use environment variables for configuration. Key variables include:

- **PORT**: Service port (8080-8085)
- **ENVIRONMENT**: production
- **DATABASE_URL**: Neon PostgreSQL connection string
- **JWT_SECRET**: Shared secret for JWT token validation
- **AUTO_MIGRATE**: false (tables pre-created)
- **Service URLs**: Inter-service communication endpoints

### Security Configuration
- **TLS**: All connections use HTTPS/TLS encryption
- **Database**: SSL required for all database connections
- **JWT**: RS256 signing with secure secret management
- **CORS**: Configured for production domains
- **Rate Limiting**: Implemented at API Gateway level

## 🔄 Deployment Updates

### Automatic Deployment
Render automatically deploys when changes are pushed to the main branch:

1. **Push Changes**: `git push origin main`
2. **Automatic Build**: Render detects changes and rebuilds
3. **Health Check**: Render verifies service health before switching traffic
4. **Zero Downtime**: Rolling deployment ensures no service interruption

### Manual Deployment
To manually trigger deployment:

1. **Render Dashboard**: Go to service page
2. **Manual Deploy**: Click "Manual Deploy" button
3. **Select Branch**: Choose branch to deploy
4. **Deploy**: Confirm deployment

## 📊 Monitoring & Maintenance

### Health Monitoring
- **Automatic Health Checks**: Render monitors `/health` endpoints
- **Uptime Monitoring**: 99.9% uptime SLA
- **Auto-restart**: Failed services automatically restart
- **Alerting**: Email notifications for service issues

### Performance Monitoring
- **Response Time**: Monitor API response times
- **Database Performance**: Track query performance
- **Resource Usage**: Monitor CPU and memory usage
- **Error Rates**: Track error rates and patterns

### Maintenance Tasks
- **Database Backups**: Automated daily backups
- **Log Rotation**: Automatic log management
- **Security Updates**: Regular dependency updates
- **Performance Optimization**: Ongoing performance tuning

## 🆘 Troubleshooting

### Common Issues

1. **Service Not Starting**
   - Check environment variables
   - Verify database connectivity
   - Review service logs in Render dashboard

2. **Database Connection Errors**
   - Verify connection string format
   - Check database status in Neon console
   - Ensure SSL mode is required

3. **Service Communication Errors**
   - Verify service URLs in environment variables
   - Check service health endpoints
   - Review network connectivity

### Debug Commands
```bash
# Check service logs
curl https://api.render.com/v1/services/srv-xxxxx/logs

# Test database connection
psql "postgresql://connection-string" -c "SELECT 1;"

# Verify service endpoints
curl -I https://service-url.onrender.com/health
```

## 🎯 Best Practices

### Security
- Use strong JWT secrets
- Enable SSL for all connections
- Regularly update dependencies
- Monitor for security vulnerabilities

### Performance
- Use connection pooling
- Implement caching strategies
- Monitor resource usage
- Optimize database queries

### Reliability
- Implement health checks
- Use graceful shutdowns
- Handle errors properly
- Monitor service dependencies

---

**Deployment completed successfully! 🎉**

Your CRM microservices system is now live and operational on Render platform with high availability, security, and performance.
