package services

import (
	"fmt"
	"notification-service/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TemplateService handles notification template operations
type TemplateService struct {
	db *gorm.DB
}

// NewTemplateService creates a new template service
func NewTemplateService(db *gorm.DB) *TemplateService {
	return &TemplateService{
		db: db,
	}
}

// CreateTemplate creates a new notification template
func (s *TemplateService) CreateTemplate(req *models.CreateTemplateRequest, createdByID uuid.UUID) (*models.NotificationTemplate, error) {
	template := req.ToTemplate(createdByID)
	
	// Validate template
	if !template.IsValid() {
		return nil, fmt.Errorf("invalid template data")
	}
	
	// Check if template name already exists
	var existingTemplate models.NotificationTemplate
	err := s.db.Where("name = ? AND deleted_at IS NULL", template.Name).First(&existingTemplate).Error
	if err == nil {
		return nil, fmt.Errorf("template with name '%s' already exists", template.Name)
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing template: %w", err)
	}
	
	// If this is set as default, unset other defaults for the same type
	if template.IsDefault {
		if err := s.unsetDefaultTemplates(template.Type); err != nil {
			return nil, fmt.Errorf("failed to unset existing default templates: %w", err)
		}
	}
	
	// Create template in database
	if err := s.db.Create(template).Error; err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}
	
	return template, nil
}

// GetTemplate retrieves a template by ID
func (s *TemplateService) GetTemplate(id uuid.UUID) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	err := s.db.First(&template, "id = ?", id).Error
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}
	return &template, nil
}

// GetTemplateByName retrieves a template by name
func (s *TemplateService) GetTemplateByName(name string) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	err := s.db.Where("name = ? AND is_active = true", name).First(&template).Error
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}
	return &template, nil
}

// GetTemplates retrieves templates with pagination and filters
func (s *TemplateService) GetTemplates(filters map[string]interface{}, page, limit int) ([]models.NotificationTemplate, int64, error) {
	var templates []models.NotificationTemplate
	var total int64
	
	query := s.db.Model(&models.NotificationTemplate{})
	
	// Apply filters
	if templateType, ok := filters["type"]; ok {
		query = query.Where("type = ?", templateType)
	}
	if isActive, ok := filters["is_active"]; ok {
		query = query.Where("is_active = ?", isActive)
	}
	if isDefault, ok := filters["is_default"]; ok {
		query = query.Where("is_default = ?", isDefault)
	}
	if createdByID, ok := filters["created_by_id"]; ok {
		query = query.Where("created_by_id = ?", createdByID)
	}
	if search, ok := filters["search"]; ok {
		searchTerm := fmt.Sprintf("%%%s%%", search)
		query = query.Where("name ILIKE ? OR description ILIKE ?", searchTerm, searchTerm)
	}
	
	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}
	
	// Apply pagination
	offset := (page - 1) * limit
	err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&templates).Error
	
	if err != nil {
		return nil, 0, fmt.Errorf("failed to retrieve templates: %w", err)
	}
	
	return templates, total, nil
}

// UpdateTemplate updates an existing template
func (s *TemplateService) UpdateTemplate(id uuid.UUID, req *models.UpdateTemplateRequest) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}
	
	// Check if name is being changed and if it conflicts
	if req.Name != nil && *req.Name != template.Name {
		var existingTemplate models.NotificationTemplate
		err := s.db.Where("name = ? AND id != ? AND deleted_at IS NULL", *req.Name, id).First(&existingTemplate).Error
		if err == nil {
			return nil, fmt.Errorf("template with name '%s' already exists", *req.Name)
		} else if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check existing template: %w", err)
		}
	}
	
	// If setting as default, unset other defaults for the same type
	if req.IsDefault != nil && *req.IsDefault && !template.IsDefault {
		if err := s.unsetDefaultTemplates(template.Type); err != nil {
			return nil, fmt.Errorf("failed to unset existing default templates: %w", err)
		}
	}
	
	// Apply updates
	template.ApplyUpdates(req)
	
	// Save template
	if err := s.db.Save(&template).Error; err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}
	
	return &template, nil
}

// DeleteTemplate soft deletes a template
func (s *TemplateService) DeleteTemplate(id uuid.UUID) error {
	var template models.NotificationTemplate
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		return fmt.Errorf("template not found: %w", err)
	}
	
	// Check if template is being used by any notifications
	var notificationCount int64
	err := s.db.Model(&models.Notification{}).Where("template_id = ?", id).Count(&notificationCount).Error
	if err != nil {
		return fmt.Errorf("failed to check template usage: %w", err)
	}
	
	if notificationCount > 0 {
		return fmt.Errorf("cannot delete template: it is being used by %d notifications", notificationCount)
	}
	
	// Soft delete template
	if err := s.db.Delete(&template).Error; err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}
	
	return nil
}

// GetDefaultTemplate retrieves the default template for a given type
func (s *TemplateService) GetDefaultTemplate(templateType models.NotificationType) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	err := s.db.Where("type = ? AND is_default = true AND is_active = true", templateType).First(&template).Error
	if err != nil {
		return nil, fmt.Errorf("default template not found for type %s: %w", templateType, err)
	}
	return &template, nil
}

// CloneTemplate creates a copy of an existing template
func (s *TemplateService) CloneTemplate(id uuid.UUID, newName string, createdByID uuid.UUID) (*models.NotificationTemplate, error) {
	// Get original template
	originalTemplate, err := s.GetTemplate(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get original template: %w", err)
	}
	
	// Check if new name already exists
	var existingTemplate models.NotificationTemplate
	err = s.db.Where("name = ? AND deleted_at IS NULL", newName).First(&existingTemplate).Error
	if err == nil {
		return nil, fmt.Errorf("template with name '%s' already exists", newName)
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing template: %w", err)
	}
	
	// Create new template based on original
	newTemplate := &models.NotificationTemplate{
		Name:        newName,
		Type:        originalTemplate.Type,
		Subject:     originalTemplate.Subject,
		Body:        originalTemplate.Body,
		HTMLBody:    originalTemplate.HTMLBody,
		Description: fmt.Sprintf("Copy of %s", originalTemplate.Description),
		Variables:   originalTemplate.Variables,
		Metadata:    originalTemplate.Metadata,
		IsActive:    true,
		IsDefault:   false, // Cloned templates are never default
		Version:     1,
		CreatedByID: createdByID,
	}
	
	// Create template in database
	if err := s.db.Create(newTemplate).Error; err != nil {
		return nil, fmt.Errorf("failed to create cloned template: %w", err)
	}
	
	return newTemplate, nil
}

// GetTemplatesByType retrieves all active templates for a given type
func (s *TemplateService) GetTemplatesByType(templateType models.NotificationType) ([]models.NotificationTemplate, error) {
	var templates []models.NotificationTemplate
	err := s.db.Where("type = ? AND is_active = true", templateType).
		Order("is_default DESC, name ASC").
		Find(&templates).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve templates by type: %w", err)
	}
	
	return templates, nil
}

// ValidateTemplate validates template content and variables
func (s *TemplateService) ValidateTemplate(template *models.NotificationTemplate) error {
	if !template.IsValid() {
		return fmt.Errorf("template validation failed: invalid template data")
	}
	
	// Additional validation can be added here
	// For example, checking if all variables in the template are defined
	
	return nil
}

// GetTemplateStats returns template usage statistics
func (s *TemplateService) GetTemplateStats() (map[string]interface{}, error) {
	var stats struct {
		TotalTemplates   int64 `json:"total_templates"`
		ActiveTemplates  int64 `json:"active_templates"`
		EmailTemplates   int64 `json:"email_templates"`
		SMSTemplates     int64 `json:"sms_templates"`
		InAppTemplates   int64 `json:"in_app_templates"`
		PushTemplates    int64 `json:"push_templates"`
	}
	
	// Get total count
	if err := s.db.Model(&models.NotificationTemplate{}).Count(&stats.TotalTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	
	// Get active count
	if err := s.db.Model(&models.NotificationTemplate{}).Where("is_active = true").Count(&stats.ActiveTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to get active count: %w", err)
	}
	
	// Get counts by type
	s.db.Model(&models.NotificationTemplate{}).Where("type = ? AND is_active = true", models.NotificationTypeEmail).Count(&stats.EmailTemplates)
	s.db.Model(&models.NotificationTemplate{}).Where("type = ? AND is_active = true", models.NotificationTypeSMS).Count(&stats.SMSTemplates)
	s.db.Model(&models.NotificationTemplate{}).Where("type = ? AND is_active = true", models.NotificationTypeInApp).Count(&stats.InAppTemplates)
	s.db.Model(&models.NotificationTemplate{}).Where("type = ? AND is_active = true", models.NotificationTypePush).Count(&stats.PushTemplates)
	
	result := map[string]interface{}{
		"total_templates":  stats.TotalTemplates,
		"active_templates": stats.ActiveTemplates,
		"email_templates":  stats.EmailTemplates,
		"sms_templates":    stats.SMSTemplates,
		"in_app_templates": stats.InAppTemplates,
		"push_templates":   stats.PushTemplates,
	}
	
	return result, nil
}

// unsetDefaultTemplates removes default flag from all templates of a given type
func (s *TemplateService) unsetDefaultTemplates(templateType models.NotificationType) error {
	return s.db.Model(&models.NotificationTemplate{}).
		Where("type = ? AND is_default = true", templateType).
		Update("is_default", false).Error
}

// ProcessTemplateContent processes template content with provided data
func (s *TemplateService) ProcessTemplateContent(content string, data map[string]interface{}) string {
	result := content
	
	if data != nil {
		for key, value := range data {
			placeholder := fmt.Sprintf("{{%s}}", key)
			replacement := fmt.Sprintf("%v", value)
			
			// Simple string replacement for template variables
			for i := 0; i < len(result); i++ {
				if i+len(placeholder) <= len(result) && result[i:i+len(placeholder)] == placeholder {
					result = result[:i] + replacement + result[i+len(placeholder):]
					i += len(replacement) - 1
				}
			}
		}
	}
	
	return result
}
