package services

import (
	"fmt"
	"time"

	"admin-service/internal/models"
	"admin-service/internal/repository"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
)

// AuditService handles audit log business logic
type AuditService interface {
	GetAuditLogs(req *models.AuditLogListRequest) (*models.AuditLogListResponse, error)
	GetAuditLog(id uuid.UUID) (*models.AuditLogResponse, error)
	CreateAuditLog(req *models.AuditLogCreateRequest) error
	GetAuditStats(startDate, endDate *time.Time) (*models.AuditLogStats, error)
	GetRecentActivity(limit int) ([]*models.AuditLogResponse, error)
	CleanupOldLogs(retentionDays int) (int64, error)
}

type auditService struct {
	auditRepo repository.AuditRepository
}

// NewAuditService creates a new audit service
func NewAuditService(auditRepo repository.AuditRepository) AuditService {
	return &auditService{
		auditRepo: auditRepo,
	}
}

// GetAuditLogs retrieves audit logs with pagination and filtering
func (s *auditService) GetAuditLogs(req *models.AuditLogListRequest) (*models.AuditLogListResponse, error) {
	// Set default pagination if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	auditLogs, total, err := s.auditRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs: %w", err)
	}

	// Convert to response format
	auditLogResponses := make([]*models.AuditLogResponse, len(auditLogs))
	for i, auditLog := range auditLogs {
		auditLogResponses[i] = auditLog.ToResponse()
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &models.AuditLogListResponse{
		AuditLogs:  auditLogResponses,
		Pagination: pagination,
	}, nil
}

// GetAuditLog retrieves an audit log by ID
func (s *auditService) GetAuditLog(id uuid.UUID) (*models.AuditLogResponse, error) {
	auditLog, err := s.auditRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit log: %w", err)
	}

	return auditLog.ToResponse(), nil
}

// CreateAuditLog creates a new audit log entry
func (s *auditService) CreateAuditLog(req *models.AuditLogCreateRequest) error {
	// Validate action
	if !req.Action.IsValid() {
		return fmt.Errorf("invalid audit action: %s", req.Action)
	}

	auditLog := &models.AuditLog{
		UserID:     req.UserID,
		Action:     req.Action,
		Resource:   req.Resource,
		ResourceID: req.ResourceID,
		Details:    req.Details,
		IPAddress:  req.IPAddress,
		UserAgent:  req.UserAgent,
		Timestamp:  time.Now(),
	}

	if err := s.auditRepo.Create(auditLog); err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}

	return nil
}

// GetAuditStats retrieves audit log statistics
func (s *auditService) GetAuditStats(startDate, endDate *time.Time) (*models.AuditLogStats, error) {
	stats, err := s.auditRepo.GetStats(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit stats: %w", err)
	}

	return stats, nil
}

// GetRecentActivity retrieves recent audit log activity
func (s *auditService) GetRecentActivity(limit int) ([]*models.AuditLogResponse, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	auditLogs, err := s.auditRepo.GetRecentActivity(limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activity: %w", err)
	}

	// Convert to response format
	responses := make([]*models.AuditLogResponse, len(auditLogs))
	for i, auditLog := range auditLogs {
		responses[i] = auditLog.ToResponse()
	}

	return responses, nil
}

// CleanupOldLogs removes audit logs older than the specified retention period
func (s *auditService) CleanupOldLogs(retentionDays int) (int64, error) {
	if retentionDays <= 0 {
		return 0, fmt.Errorf("retention days must be positive")
	}

	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	deletedCount, err := s.auditRepo.DeleteOldLogs(cutoffDate)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup old logs: %w", err)
	}

	return deletedCount, nil
}
