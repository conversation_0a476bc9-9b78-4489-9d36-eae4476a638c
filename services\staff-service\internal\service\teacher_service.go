package service

import (
	"fmt"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/repository"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
)

// TeacherService handles teacher business logic
type TeacherService interface {
	GetAll(req *models.TeacherListRequest) (*models.TeacherListResponse, error)
	GetByID(id uuid.UUID) (*models.TeacherResponse, error)
	GetByTeacherID(teacherID string) (*models.TeacherResponse, error)
	GetByUserID(userID uuid.UUID) (*models.TeacherResponse, error)
	Create(req *models.TeacherCreateRequest, createdBy uuid.UUID) (*models.TeacherResponse, error)
	Update(id uuid.UUID, req *models.TeacherUpdateRequest) (*models.TeacherResponse, error)
	Delete(id uuid.UUID) error
	GetStats() (*models.TeacherStats, error)
	GetAvailableTeachers() ([]*models.TeacherResponse, error)
	GetTeacherWorkload(teacherID uuid.UUID) (*repository.TeacherWorkload, error)
	UpdatePerformanceMetrics(teacherID uuid.UUID) error
	GetTopRatedTeachers(limit int) ([]*models.TeacherResponse, error)
	SuspendTeacher(id uuid.UUID, reason string, suspendedBy uuid.UUID) (*models.TeacherResponse, error)
	ReactivateTeacher(id uuid.UUID, reactivatedBy uuid.UUID) (*models.TeacherResponse, error)
	UpdateRating(id uuid.UUID, rating float64) (*models.TeacherResponse, error)
}

type teacherService struct {
	teacherRepo repository.TeacherRepository
	courseRepo  repository.CourseRepository
}

// NewTeacherService creates a new teacher service
func NewTeacherService(
	teacherRepo repository.TeacherRepository,
	courseRepo repository.CourseRepository,
) TeacherService {
	return &teacherService{
		teacherRepo: teacherRepo,
		courseRepo:  courseRepo,
	}
}

// GetAll retrieves all teachers with pagination and filtering
func (s *teacherService) GetAll(req *models.TeacherListRequest) (*models.TeacherListResponse, error) {
	teachers, total, err := s.teacherRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get teachers: %w", err)
	}

	teacherResponses := make([]*models.TeacherResponse, len(teachers))
	for i, teacher := range teachers {
		teacherResponses[i] = teacher.ToResponse()
	}

	pagination := &sharedModels.PaginationResponse{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Total:      total,
		TotalPages: int((total + int64(req.PageSize) - 1) / int64(req.PageSize)),
		HasNext:    req.Page < int((total+int64(req.PageSize)-1)/int64(req.PageSize)),
		HasPrev:    req.Page > 1,
	}

	return &models.TeacherListResponse{
		Teachers:   teacherResponses,
		Pagination: pagination,
	}, nil
}

// GetByID retrieves a teacher by ID
func (s *teacherService) GetByID(id uuid.UUID) (*models.TeacherResponse, error) {
	teacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	return teacher.ToResponse(), nil
}

// GetByTeacherID retrieves a teacher by teacher ID
func (s *teacherService) GetByTeacherID(teacherID string) (*models.TeacherResponse, error) {
	teacher, err := s.teacherRepo.GetByTeacherID(teacherID)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	return teacher.ToResponse(), nil
}

// GetByUserID retrieves a teacher by user ID
func (s *teacherService) GetByUserID(userID uuid.UUID) (*models.TeacherResponse, error) {
	teacher, err := s.teacherRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	return teacher.ToResponse(), nil
}

// Create creates a new teacher
func (s *teacherService) Create(req *models.TeacherCreateRequest, createdBy uuid.UUID) (*models.TeacherResponse, error) {
	// Check if email already exists
	if existingTeacher, err := s.teacherRepo.GetByEmail(req.Email); err == nil && existingTeacher != nil {
		return nil, fmt.Errorf("teacher with email %s already exists", req.Email)
	}

	// Check if user ID already exists
	if req.UserID != nil {
		if existingTeacher, err := s.teacherRepo.GetByUserID(*req.UserID); err == nil && existingTeacher != nil {
			return nil, fmt.Errorf("teacher with user ID already exists")
		}
	}

	teacher := &models.Teacher{
		LinkedUserID:          req.UserID,
		FirstName:             req.FirstName,
		LastName:              req.LastName,
		Email:                 req.Email,
		Phone:                 req.Phone,
		DateOfBirth:           req.DateOfBirth,
		Address:               req.Address,
		City:                  req.City,
		Country:               req.Country,
		Status:                models.TeacherStatusActive, // Default to active
		HireDate:              req.HireDate,
		Salary:                req.Salary,
		Currency:              req.Currency,
		Qualifications:        req.Qualifications,
		Specializations:       req.Specializations,
		Experience:            req.Experience,
		Bio:                   req.Bio,
		EmergencyContactName:  req.EmergencyContactName,
		EmergencyContactPhone: req.EmergencyContactPhone,
		EmergencyContactEmail: req.EmergencyContactEmail,
		CreatedByUserID:       createdBy,
	}

	if err := s.teacherRepo.Create(teacher); err != nil {
		return nil, fmt.Errorf("failed to create teacher: %w", err)
	}

	// Reload with associations
	createdTeacher, err := s.teacherRepo.GetByID(teacher.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created teacher: %w", err)
	}

	return createdTeacher.ToResponse(), nil
}

// Update updates a teacher
func (s *teacherService) Update(id uuid.UUID, req *models.TeacherUpdateRequest) (*models.TeacherResponse, error) {
	// Check if teacher exists
	existingTeacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	// Validate status if provided
	if req.Status != nil && !req.Status.IsValid() {
		return nil, fmt.Errorf("invalid teacher status: %s", *req.Status)
	}

	// Check email uniqueness if email is being updated
	if req.Email != nil && *req.Email != existingTeacher.Email {
		if existingEmailTeacher, err := s.teacherRepo.GetByEmail(*req.Email); err == nil && existingEmailTeacher != nil {
			return nil, fmt.Errorf("teacher with email %s already exists", *req.Email)
		}
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.FirstName != nil {
		updates["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		updates["last_name"] = *req.LastName
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.DateOfBirth != nil {
		updates["date_of_birth"] = *req.DateOfBirth
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.City != nil {
		updates["city"] = *req.City
	}
	if req.Country != nil {
		updates["country"] = *req.Country
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Specializations != nil {
		updates["specializations"] = *req.Specializations
	}
	if req.Qualifications != nil {
		updates["qualifications"] = *req.Qualifications
	}
	if req.Experience != nil {
		updates["experience"] = *req.Experience
	}
	if req.Salary != nil {
		updates["salary"] = *req.Salary
	}
	if req.EmergencyContactName != nil {
		updates["emergency_contact_name"] = *req.EmergencyContactName
	}
	if req.EmergencyContactPhone != nil {
		updates["emergency_contact_phone"] = *req.EmergencyContactPhone
	}
	if req.EmergencyContactEmail != nil {
		updates["emergency_contact_email"] = *req.EmergencyContactEmail
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}

	updates["updated_at"] = time.Now()

	if err := s.teacherRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update teacher: %w", err)
	}

	// Get updated teacher
	updatedTeacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated teacher: %w", err)
	}

	return updatedTeacher.ToResponse(), nil
}

// Delete deletes a teacher
func (s *teacherService) Delete(id uuid.UUID) error {
	// Check if teacher exists
	if _, err := s.teacherRepo.GetByID(id); err != nil {
		return fmt.Errorf("failed to get teacher: %w", err)
	}

	// Check if teacher has active courses
	courses, err := s.courseRepo.GetByInstructor(id)
	if err != nil {
		return fmt.Errorf("failed to check teacher courses: %w", err)
	}

	for _, course := range courses {
		if course.Status == models.CourseStatusActive {
			return fmt.Errorf("cannot delete teacher with active courses")
		}
	}

	if err := s.teacherRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete teacher: %w", err)
	}

	return nil
}

// GetStats retrieves teacher statistics
func (s *teacherService) GetStats() (*models.TeacherStats, error) {
	stats, err := s.teacherRepo.GetStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher stats: %w", err)
	}

	return stats, nil
}

// GetAvailableTeachers retrieves teachers available for course assignment
func (s *teacherService) GetAvailableTeachers() ([]*models.TeacherResponse, error) {
	teachers, err := s.teacherRepo.GetAvailableTeachers()
	if err != nil {
		return nil, fmt.Errorf("failed to get available teachers: %w", err)
	}

	responses := make([]*models.TeacherResponse, len(teachers))
	for i, teacher := range teachers {
		responses[i] = teacher.ToResponse()
	}

	return responses, nil
}

// GetTeacherWorkload calculates a teacher's current workload
func (s *teacherService) GetTeacherWorkload(teacherID uuid.UUID) (*repository.TeacherWorkload, error) {
	workload, err := s.teacherRepo.GetTeacherWorkload(teacherID)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher workload: %w", err)
	}

	return workload, nil
}

// UpdatePerformanceMetrics updates teacher's performance metrics
func (s *teacherService) UpdatePerformanceMetrics(teacherID uuid.UUID) error {
	if err := s.teacherRepo.UpdatePerformanceMetrics(teacherID); err != nil {
		return fmt.Errorf("failed to update teacher performance metrics: %w", err)
	}

	return nil
}

// GetTopRatedTeachers retrieves top-rated teachers
func (s *teacherService) GetTopRatedTeachers(limit int) ([]*models.TeacherResponse, error) {
	teachers, err := s.teacherRepo.GetTopRatedTeachers(limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get top rated teachers: %w", err)
	}

	responses := make([]*models.TeacherResponse, len(teachers))
	for i, teacher := range teachers {
		responses[i] = teacher.ToResponse()
	}

	return responses, nil
}

// SuspendTeacher suspends a teacher
func (s *teacherService) SuspendTeacher(id uuid.UUID, reason string, suspendedBy uuid.UUID) (*models.TeacherResponse, error) {
	// Check if teacher exists
	teacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	if teacher.Status == models.TeacherStatusSuspended {
		return nil, fmt.Errorf("teacher is already suspended")
	}

	updates := map[string]interface{}{
		"status":     models.TeacherStatusSuspended,
		"updated_at": time.Now(),
	}

	if reason != "" {
		existingNotes := teacher.Notes
		suspensionNote := fmt.Sprintf("\n\nSuspended on %s: %s", time.Now().Format("2006-01-02"), reason)
		if existingNotes != "" {
			updates["notes"] = existingNotes + suspensionNote
		} else {
			updates["notes"] = suspensionNote
		}
	}

	if err := s.teacherRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to suspend teacher: %w", err)
	}

	// Get updated teacher
	updatedTeacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated teacher: %w", err)
	}

	return updatedTeacher.ToResponse(), nil
}

// ReactivateTeacher reactivates a suspended teacher
func (s *teacherService) ReactivateTeacher(id uuid.UUID, reactivatedBy uuid.UUID) (*models.TeacherResponse, error) {
	// Check if teacher exists
	teacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	if teacher.Status != models.TeacherStatusSuspended {
		return nil, fmt.Errorf("teacher is not suspended")
	}

	updates := map[string]interface{}{
		"status":     models.TeacherStatusActive,
		"updated_at": time.Now(),
	}

	existingNotes := teacher.Notes
	reactivationNote := fmt.Sprintf("\n\nReactivated on %s", time.Now().Format("2006-01-02"))
	if existingNotes != "" {
		updates["notes"] = existingNotes + reactivationNote
	} else {
		updates["notes"] = reactivationNote
	}

	if err := s.teacherRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to reactivate teacher: %w", err)
	}

	// Get updated teacher
	updatedTeacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated teacher: %w", err)
	}

	return updatedTeacher.ToResponse(), nil
}

// UpdateRating updates a teacher's rating
func (s *teacherService) UpdateRating(id uuid.UUID, rating float64) (*models.TeacherResponse, error) {
	// Validate rating range
	if rating < 0 || rating > 5 {
		return nil, fmt.Errorf("rating must be between 0 and 5")
	}

	// Check if teacher exists
	if _, err := s.teacherRepo.GetByID(id); err != nil {
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}

	updates := map[string]interface{}{
		"rating":     rating,
		"updated_at": time.Now(),
	}

	if err := s.teacherRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update teacher rating: %w", err)
	}

	// Get updated teacher
	updatedTeacher, err := s.teacherRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated teacher: %w", err)
	}

	return updatedTeacher.ToResponse(), nil
}
