-- Create notification_queue table
CREATE TABLE IF NOT EXISTS notification_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    priority INTEGER NOT NULL DEFAULT 5,
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Retry information
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    last_error TEXT,
    
    -- Processing information
    processor_id VARCHAR(255),
    locked_at TIMESTAMP WITH TIME ZONE,
    lock_expiry TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notification_queue_status ON notification_queue(status);
CREATE INDEX IF NOT EXISTS idx_notification_queue_priority ON notification_queue(priority);
CREATE INDEX IF NOT EXISTS idx_notification_queue_scheduled_at ON notification_queue(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_notification_queue_next_retry_at ON notification_queue(next_retry_at);
CREATE INDEX IF NOT EXISTS idx_notification_queue_processor_id ON notification_queue(processor_id);
CREATE INDEX IF NOT EXISTS idx_notification_queue_lock_expiry ON notification_queue(lock_expiry);
CREATE INDEX IF NOT EXISTS idx_notification_queue_deleted_at ON notification_queue(deleted_at);

-- Create composite index for queue processing
CREATE INDEX IF NOT EXISTS idx_notification_queue_processing 
    ON notification_queue(status, priority, scheduled_at) 
    WHERE deleted_at IS NULL;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_notification_queue_updated_at 
    BEFORE UPDATE ON notification_queue 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add foreign key constraint
ALTER TABLE notification_queue 
ADD CONSTRAINT fk_notification_queue_notification_id 
FOREIGN KEY (notification_id) REFERENCES notifications(id) 
ON DELETE CASCADE;
