# Notification Service Makefile

# Variables
BINARY_NAME=notification-service
DOCKER_IMAGE=notification-service
DOCKER_TAG=latest
GO_VERSION=1.21

# Default target
.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development
.PHONY: run
run: ## Run the application locally
	go run main.go

.PHONY: build
build: ## Build the application
	go build -o bin/$(BINARY_NAME) main.go

.PHONY: clean
clean: ## Clean build artifacts
	rm -rf bin/
	go clean

# Dependencies
.PHONY: deps
deps: ## Download dependencies
	go mod download
	go mod tidy

.PHONY: deps-update
deps-update: ## Update dependencies
	go get -u ./...
	go mod tidy

# Testing
.PHONY: test
test: ## Run unit tests
	go test -v ./internal/services/...

.PHONY: test-integration
test-integration: ## Run integration tests
	go test -v ./tests/integration/...

.PHONY: test-all
test-all: ## Run all tests
	go test -v ./...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: test-race
test-race: ## Run tests with race detection
	go test -race -v ./...

.PHONY: benchmark
benchmark: ## Run benchmarks
	go test -bench=. -benchmem ./...

# Code Quality
.PHONY: lint
lint: ## Run linter
	golangci-lint run

.PHONY: fmt
fmt: ## Format code
	go fmt ./...

.PHONY: vet
vet: ## Run go vet
	go vet ./...

.PHONY: check
check: fmt vet lint ## Run all code quality checks

# Database
.PHONY: migrate
migrate: ## Run database migrations
	@echo "Migrations are run automatically on startup"

.PHONY: migrate-create
migrate-create: ## Create a new migration file
	@read -p "Enter migration name: " name; \
	timestamp=$$(date +%Y%m%d%H%M%S); \
	touch migrations/$${timestamp}_$${name}.sql; \
	echo "Created migration: migrations/$${timestamp}_$${name}.sql"

# Docker
.PHONY: docker-build
docker-build: ## Build Docker image
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run: ## Run Docker container
	docker run -p 8084:8084 --env-file .env $(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-push
docker-push: ## Push Docker image
	docker push $(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-clean
docker-clean: ## Clean Docker images
	docker rmi $(DOCKER_IMAGE):$(DOCKER_TAG) || true
	docker system prune -f

# Development Environment
.PHONY: dev-setup
dev-setup: ## Set up development environment
	@echo "Setting up development environment..."
	go mod download
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from .env.example"; fi
	@echo "Development environment setup complete!"
	@echo "Please update .env file with your configuration"

.PHONY: dev-start
dev-start: ## Start development services
	@echo "Starting notification service in development mode..."
	@if [ ! -f .env ]; then echo "Error: .env file not found. Run 'make dev-setup' first."; exit 1; fi
	go run main.go

# Production
.PHONY: build-prod
build-prod: ## Build for production
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o bin/$(BINARY_NAME) main.go

.PHONY: deploy
deploy: ## Deploy to production (placeholder)
	@echo "Deployment should be handled by CI/CD pipeline"
	@echo "This is a placeholder for deployment commands"

# Monitoring and Debugging
.PHONY: logs
logs: ## Show application logs (for Docker)
	docker logs -f notification-service || echo "Container not running"

.PHONY: health
health: ## Check service health
	@curl -f http://localhost:8084/health || echo "Service not responding"

.PHONY: metrics
metrics: ## Show service metrics
	@curl -s http://localhost:8084/api/v1/analytics/dashboard | jq . || echo "Service not responding or jq not installed"

# Documentation
.PHONY: docs
docs: ## Generate documentation
	@echo "Generating API documentation..."
	@echo "Documentation available in README.md"

.PHONY: api-docs
api-docs: ## Generate API documentation
	@echo "API documentation should be generated from OpenAPI spec"
	@echo "See README.md for API endpoint documentation"

# Utilities
.PHONY: version
version: ## Show version information
	@echo "Notification Service"
	@echo "Go version: $(shell go version)"
	@echo "Git commit: $(shell git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
	@echo "Build date: $(shell date)"

.PHONY: env-check
env-check: ## Check environment variables
	@echo "Checking environment configuration..."
	@go run -ldflags="-s -w" main.go --check-config || echo "Configuration check failed"

# Load Testing
.PHONY: load-test
load-test: ## Run load tests (requires hey or similar tool)
	@echo "Running basic load test..."
	@if command -v hey >/dev/null 2>&1; then \
		hey -n 100 -c 10 http://localhost:8084/health; \
	else \
		echo "hey tool not found. Install with: go install github.com/rakyll/hey@latest"; \
	fi

# Security
.PHONY: security-scan
security-scan: ## Run security scan
	@if command -v gosec >/dev/null 2>&1; then \
		gosec ./...; \
	else \
		echo "gosec not found. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Git Hooks
.PHONY: install-hooks
install-hooks: ## Install git hooks
	@echo "Installing git hooks..."
	@echo '#!/bin/sh\nmake check' > .git/hooks/pre-commit
	@chmod +x .git/hooks/pre-commit
	@echo "Git hooks installed"

# All-in-one commands
.PHONY: ci
ci: deps check test-all ## Run CI pipeline locally

.PHONY: release
release: clean deps check test-all build-prod ## Prepare release build

.PHONY: dev
dev: dev-setup dev-start ## Quick development setup and start
