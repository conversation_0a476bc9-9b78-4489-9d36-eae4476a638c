package services

import (
	"notification-service/internal/config"
	"notification-service/internal/models"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// NotificationServiceTestSuite defines the test suite for NotificationService
type NotificationServiceTestSuite struct {
	suite.Suite
	db      *gorm.DB
	service *NotificationService
	config  *config.Config
}

// SetupSuite sets up the test suite
func (suite *NotificationServiceTestSuite) SetupSuite() {
	// Create in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)
	
	// Run migrations
	err = db.AutoMigrate(
		&models.Notification{},
		&models.NotificationTemplate{},
		&models.NotificationPreference{},
		&models.DeliveryLog{},
		&models.NotificationQueue{},
	)
	suite.Require().NoError(err)
	
	// Create test config
	cfg := &config.Config{
		MaxRetries:           3,
		RetryDelaySeconds:    60,
		BatchSize:            100,
		QueueProcessInterval: 30,
	}
	
	suite.db = db
	suite.config = cfg
	suite.service = NewNotificationService(db, cfg)
}

// TearDownSuite cleans up after the test suite
func (suite *NotificationServiceTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// SetupTest sets up each test
func (suite *NotificationServiceTestSuite) SetupTest() {
	// Clean up tables before each test
	suite.db.Exec("DELETE FROM notifications")
	suite.db.Exec("DELETE FROM notification_templates")
	suite.db.Exec("DELETE FROM notification_preferences")
	suite.db.Exec("DELETE FROM delivery_logs")
	suite.db.Exec("DELETE FROM notification_queue")
}

// TestCreateNotification tests notification creation
func (suite *NotificationServiceTestSuite) TestCreateNotification() {
	req := &models.CreateNotificationRequest{
		Type:      models.NotificationTypeEmail,
		Recipient: "<EMAIL>",
		Subject:   "Test Subject",
		Message:   "Test Message",
		Source:    "test",
	}
	
	notification, err := suite.service.CreateNotification(req)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), notification)
	assert.Equal(suite.T(), req.Type, notification.Type)
	assert.Equal(suite.T(), req.Recipient, notification.Recipient)
	assert.Equal(suite.T(), req.Subject, notification.Subject)
	assert.Equal(suite.T(), req.Message, notification.Message)
	assert.Equal(suite.T(), models.NotificationStatusPending, notification.Status)
	
	// Verify notification was saved to database
	var dbNotification models.Notification
	err = suite.db.First(&dbNotification, "id = ?", notification.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), notification.ID, dbNotification.ID)
}

// TestCreateNotificationWithInvalidData tests notification creation with invalid data
func (suite *NotificationServiceTestSuite) TestCreateNotificationWithInvalidData() {
	req := &models.CreateNotificationRequest{
		Type:      models.NotificationTypeEmail,
		Recipient: "", // Invalid: empty recipient
		Subject:   "Test Subject",
		Message:   "Test Message",
	}
	
	notification, err := suite.service.CreateNotification(req)
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), notification)
	assert.Contains(suite.T(), err.Error(), "invalid notification data")
}

// TestGetNotification tests notification retrieval
func (suite *NotificationServiceTestSuite) TestGetNotification() {
	// Create a test notification
	notification := &models.Notification{
		Type:      models.NotificationTypeEmail,
		Recipient: "<EMAIL>",
		Subject:   "Test Subject",
		Message:   "Test Message",
		Status:    models.NotificationStatusPending,
	}
	err := suite.db.Create(notification).Error
	suite.Require().NoError(err)
	
	// Retrieve the notification
	retrieved, err := suite.service.GetNotification(notification.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), notification.ID, retrieved.ID)
	assert.Equal(suite.T(), notification.Type, retrieved.Type)
	assert.Equal(suite.T(), notification.Recipient, retrieved.Recipient)
}

// TestGetNotificationNotFound tests notification retrieval with non-existent ID
func (suite *NotificationServiceTestSuite) TestGetNotificationNotFound() {
	nonExistentID := uuid.New()
	
	notification, err := suite.service.GetNotification(nonExistentID)
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), notification)
	assert.Contains(suite.T(), err.Error(), "notification not found")
}

// TestGetNotifications tests notification listing with filters
func (suite *NotificationServiceTestSuite) TestGetNotifications() {
	// Create test notifications
	notifications := []models.Notification{
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Subject:   "Test Subject 1",
			Message:   "Test Message 1",
			Status:    models.NotificationStatusPending,
		},
		{
			Type:      models.NotificationTypeSMS,
			Recipient: "+1234567890",
			Message:   "Test SMS Message",
			Status:    models.NotificationStatusSent,
		},
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Subject:   "Test Subject 2",
			Message:   "Test Message 2",
			Status:    models.NotificationStatusDelivered,
		},
	}
	
	for i := range notifications {
		err := suite.db.Create(&notifications[i]).Error
		suite.Require().NoError(err)
	}
	
	// Test without filters
	result, total, err := suite.service.GetNotifications(map[string]interface{}{}, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), total)
	assert.Len(suite.T(), result, 3)
	
	// Test with type filter
	filters := map[string]interface{}{
		"type": models.NotificationTypeEmail,
	}
	result, total, err = suite.service.GetNotifications(filters, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), total)
	assert.Len(suite.T(), result, 2)
	
	// Test with status filter
	filters = map[string]interface{}{
		"status": models.NotificationStatusPending,
	}
	result, total, err = suite.service.GetNotifications(filters, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), total)
	assert.Len(suite.T(), result, 1)
}

// TestUpdateNotificationStatus tests notification status updates
func (suite *NotificationServiceTestSuite) TestUpdateNotificationStatus() {
	// Create a test notification
	notification := &models.Notification{
		Type:      models.NotificationTypeEmail,
		Recipient: "<EMAIL>",
		Subject:   "Test Subject",
		Message:   "Test Message",
		Status:    models.NotificationStatusPending,
	}
	err := suite.db.Create(notification).Error
	suite.Require().NoError(err)
	
	// Update status to sent
	err = suite.service.UpdateNotificationStatus(notification.ID, models.NotificationStatusSent)
	assert.NoError(suite.T(), err)
	
	// Verify status was updated
	var updated models.Notification
	err = suite.db.First(&updated, "id = ?", notification.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.NotificationStatusSent, updated.Status)
	assert.NotNil(suite.T(), updated.SentAt)
	
	// Update status to delivered
	err = suite.service.UpdateNotificationStatus(notification.ID, models.NotificationStatusDelivered)
	assert.NoError(suite.T(), err)
	
	// Verify status was updated
	err = suite.db.First(&updated, "id = ?", notification.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.NotificationStatusDelivered, updated.Status)
	assert.NotNil(suite.T(), updated.DeliveredAt)
}

// TestMarkNotificationAsFailed tests marking notification as failed
func (suite *NotificationServiceTestSuite) TestMarkNotificationAsFailed() {
	// Create a test notification
	notification := &models.Notification{
		Type:       models.NotificationTypeEmail,
		Recipient:  "<EMAIL>",
		Subject:    "Test Subject",
		Message:    "Test Message",
		Status:     models.NotificationStatusPending,
		MaxRetries: 3,
	}
	err := suite.db.Create(notification).Error
	suite.Require().NoError(err)
	
	// Mark as failed
	errorMsg := "SMTP connection failed"
	err = suite.service.MarkNotificationAsFailed(notification.ID, errorMsg)
	assert.NoError(suite.T(), err)
	
	// Verify notification was updated
	var updated models.Notification
	err = suite.db.First(&updated, "id = ?", notification.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.NotificationStatusFailed, updated.Status)
	assert.Equal(suite.T(), errorMsg, updated.LastError)
	assert.Equal(suite.T(), 1, updated.RetryCount)
	assert.NotNil(suite.T(), updated.NextRetryAt)
}

// TestGetNotificationStats tests notification statistics
func (suite *NotificationServiceTestSuite) TestGetNotificationStats() {
	// Create test notifications with different statuses
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	
	notifications := []models.Notification{
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Message:   "Test Message 1",
			Status:    models.NotificationStatusDelivered,
			CreatedAt: now,
		},
		{
			Type:      models.NotificationTypeSMS,
			Recipient: "+1234567890",
			Message:   "Test SMS Message",
			Status:    models.NotificationStatusSent,
			CreatedAt: now,
		},
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Message:   "Test Message 2",
			Status:    models.NotificationStatusFailed,
			CreatedAt: now,
		},
		{
			Type:      models.NotificationTypeEmail,
			Recipient: "<EMAIL>",
			Message:   "Test Message 3",
			Status:    models.NotificationStatusDelivered,
			CreatedAt: yesterday, // Outside date range
		},
	}
	
	for i := range notifications {
		err := suite.db.Create(&notifications[i]).Error
		suite.Require().NoError(err)
	}
	
	// Get stats for today only
	startDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endDate := now
	
	stats, err := suite.service.GetNotificationStats(startDate, endDate)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), stats)
	
	// Verify stats
	assert.Equal(suite.T(), int64(3), stats["total_notifications"])
	assert.Equal(suite.T(), int64(1), stats["delivered_notifications"])
	assert.Equal(suite.T(), int64(1), stats["sent_notifications"])
	assert.Equal(suite.T(), int64(1), stats["failed_notifications"])
	assert.Equal(suite.T(), int64(2), stats["email_notifications"])
	assert.Equal(suite.T(), int64(1), stats["sms_notifications"])
	
	// Verify rates
	deliveryRate, ok := stats["delivery_rate"].(float64)
	assert.True(suite.T(), ok)
	assert.InDelta(suite.T(), 33.33, deliveryRate, 0.1) // 1/3 * 100
	
	failureRate, ok := stats["failure_rate"].(float64)
	assert.True(suite.T(), ok)
	assert.InDelta(suite.T(), 33.33, failureRate, 0.1) // 1/3 * 100
}

// TestDeleteNotification tests notification deletion
func (suite *NotificationServiceTestSuite) TestDeleteNotification() {
	// Create a test notification
	notification := &models.Notification{
		Type:      models.NotificationTypeEmail,
		Recipient: "<EMAIL>",
		Subject:   "Test Subject",
		Message:   "Test Message",
		Status:    models.NotificationStatusPending,
	}
	err := suite.db.Create(notification).Error
	suite.Require().NoError(err)
	
	// Delete the notification
	err = suite.service.DeleteNotification(notification.ID)
	assert.NoError(suite.T(), err)
	
	// Verify notification was soft deleted
	var deleted models.Notification
	err = suite.db.Unscoped().First(&deleted, "id = ?", notification.ID).Error
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), deleted.DeletedAt)
	
	// Verify notification is not found in normal queries
	err = suite.db.First(&deleted, "id = ?", notification.ID).Error
	assert.Error(suite.T(), err)
}

// TestSuite runs the notification service test suite
func TestNotificationServiceSuite(t *testing.T) {
	suite.Run(t, new(NotificationServiceTestSuite))
}
