import { apiService } from './api';
import type {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  ConfirmResetPasswordRequest,
  User,
} from '@/types/auth';

class AuthService {
  private readonly API_PREFIX = '/api/v1/auth';

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiService.post<LoginResponse>(
        `${this.API_PREFIX}/login`,
        credentials
      );

      // Store tokens
      if (response.access_token) {
        localStorage.setItem('auth_token', response.access_token);
        apiService.setAuthToken(response.access_token);
      }
      if (response.refresh_token) {
        localStorage.setItem('refresh_token', response.refresh_token);
      }

      return response;
    } catch (error: any) {
      console.error('Login error:', error);

      // If backend is not available or returns 500, provide helpful message
      if (error.response?.status === 500) {
        throw new Error('Server error: The backend service may be starting up or the database may not be initialized. Please try again in a few moments.');
      }

      if (error.response?.status === 404) {
        throw new Error('Authentication service not found. Please check if the backend services are running.');
      }

      if (!error.response) {
        // In development, if backend is not available, use mock authentication
        if (import.meta.env.DEV && credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
          console.warn('Backend not available, using mock authentication for development');

          const mockResponse: LoginResponse = {
            user: {
              id: 'mock-admin-id',
              email: '<EMAIL>',
              username: 'admin',
              first_name: 'Admin',
              last_name: 'User',
              full_name: 'Admin User',
              phone: '+998901234567',
              role: 'ADMIN',
              status: 'ACTIVE',
              email_verified: true,
              phone_verified: true,
              two_factor_enabled: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            access_token: 'mock-jwt-token-' + Date.now(),
            refresh_token: 'mock-refresh-token-' + Date.now(),
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          };

          // Store tokens
          localStorage.setItem('auth_token', mockResponse.access_token);
          localStorage.setItem('refresh_token', mockResponse.refresh_token);

          return mockResponse;
        }

        throw new Error('Network error: Unable to connect to the backend services. Please check your internet connection and try again.');
      }

      // Enhanced error handling
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          error.message ||
                          'Invalid credentials or server unavailable';
      throw new Error(errorMessage);
    }
  }

  async logout(): Promise<void> {
    try {
      await apiService.post(`${this.API_PREFIX}/logout`);
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local storage
      this.clearTokens();
    }
  }

  async refreshToken(): Promise<LoginResponse> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const request: RefreshTokenRequest = {
      refresh_token: refreshToken,
    };

    const response = await apiService.post<LoginResponse>(
      `${this.API_PREFIX}/refresh`,
      request
    );

    // Update stored tokens
    if (response.access_token) {
      localStorage.setItem('auth_token', response.access_token);
    }
    if (response.refresh_token) {
      localStorage.setItem('refresh_token', response.refresh_token);
    }

    return response;
  }

  async getCurrentUser(): Promise<User> {
    const token = this.getToken();

    // Handle mock authentication in development
    if (import.meta.env.DEV && token?.startsWith('mock-jwt-token-')) {
      return {
        id: 'mock-admin-id',
        email: '<EMAIL>',
        username: 'admin',
        first_name: 'Admin',
        last_name: 'User',
        full_name: 'Admin User',
        phone: '+998901234567',
        role: 'ADMIN',
        status: 'ACTIVE',
        email_verified: true,
        phone_verified: true,
        two_factor_enabled: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }

    return apiService.get<User>(`${this.API_PREFIX}/me`);
  }

  async changePassword(request: ChangePasswordRequest): Promise<void> {
    return apiService.post(`${this.API_PREFIX}/change-password`, request);
  }

  async resetPassword(request: ResetPasswordRequest): Promise<void> {
    return apiService.post(`${this.API_PREFIX}/reset-password`, request);
  }

  async confirmResetPassword(request: ConfirmResetPasswordRequest): Promise<void> {
    return apiService.post(`${this.API_PREFIX}/confirm-reset-password`, request);
  }

  // Token management
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
    apiService.setAuthToken(token);
  }

  clearTokens(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    apiService.clearAuthToken();
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    // Handle mock tokens in development
    if (import.meta.env.DEV && token.startsWith('mock-jwt-token-')) {
      return true;
    }

    try {
      // Basic JWT validation (check if it's not expired)
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.warn('Invalid JWT format');
        return false;
      }

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Date.now() / 1000;

      // Check if token has expiry and is not expired
      if (payload.exp && payload.exp <= currentTime) {
        console.warn('Token expired');
        return false;
      }

      return true;
    } catch (error) {
      console.warn('Invalid token format:', error);
      return false;
    }
  }

  getUserFromToken(): Partial<User> | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.sub,
        role: payload.role,
        email: payload.email,
      };
    } catch (error) {
      console.warn('Failed to parse token:', error);
      return null;
    }
  }

  // Role-based access control helpers
  hasRole(requiredRole: string): boolean {
    const user = this.getUserFromToken();
    return user?.role === requiredRole;
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.getUserFromToken();
    return user?.role ? roles.includes(user.role) : false;
  }

  canAccessAdminService(): boolean {
    return this.hasAnyRole(['ADMIN', 'CASHIER']);
  }

  canAccessStaffService(): boolean {
    return this.hasAnyRole(['RECEPTION', 'TEACHER', 'MANAGER', 'ACADEMIC_MANAGER']);
  }

  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default authService;
