import { apiService } from './api';
import type {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  ConfirmResetPasswordRequest,
  User,
} from '@/types/auth';

class AuthService {
  private readonly API_PREFIX = '/api/v1/auth';

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // Temporary mock authentication for development
    const mockUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        user: {
          id: 'mock-admin-id',
          email: '<EMAIL>',
          username: 'admin',
          first_name: 'Admin',
          last_name: 'User',
          full_name: 'Admin User',
          phone: '+998901234567',
          role: 'ADMIN' as const,
          status: 'ACTIVE' as const,
          email_verified: true,
          phone_verified: true,
          two_factor_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      },
      {
        email: '<EMAIL>',
        password: 'staff123',
        user: {
          id: 'mock-staff-id',
          email: '<EMAIL>',
          username: 'staff',
          first_name: 'Staff',
          last_name: 'User',
          full_name: 'Staff User',
          phone: '+998901234568',
          role: 'RECEPTION' as const,
          status: 'ACTIVE' as const,
          email_verified: true,
          phone_verified: true,
          two_factor_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      }
    ];

    const mockUser = mockUsers.find(u =>
      u.email === credentials.email && u.password === credentials.password
    );

    if (mockUser) {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours from now

      const mockResponse: LoginResponse = {
        access_token: 'mock-jwt-token-' + Date.now(),
        refresh_token: 'mock-refresh-token-' + Date.now(),
        expires_at: expiresAt.toISOString(),
        user: mockUser.user
      };

      // Store tokens
      localStorage.setItem('auth_token', mockResponse.access_token);
      localStorage.setItem('refresh_token', mockResponse.refresh_token);

      return mockResponse;
    }

    // For other credentials, try the real API
    try {
      const response = await apiService.post<LoginResponse>(
        `${this.API_PREFIX}/login`,
        credentials
      );

      // Store tokens
      if (response.access_token) {
        localStorage.setItem('auth_token', response.access_token);
      }
      if (response.refresh_token) {
        localStorage.setItem('refresh_token', response.refresh_token);
      }

      return response;
    } catch (error) {
      // If API fails, throw a user-friendly error
      throw new Error('Invalid credentials or server unavailable');
    }
  }

  async logout(): Promise<void> {
    try {
      await apiService.post(`${this.API_PREFIX}/logout`);
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local storage
      this.clearTokens();
    }
  }

  async refreshToken(): Promise<LoginResponse> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const request: RefreshTokenRequest = {
      refresh_token: refreshToken,
    };

    const response = await apiService.post<LoginResponse>(
      `${this.API_PREFIX}/refresh`,
      request
    );

    // Update stored tokens
    if (response.access_token) {
      localStorage.setItem('auth_token', response.access_token);
    }
    if (response.refresh_token) {
      localStorage.setItem('refresh_token', response.refresh_token);
    }

    return response;
  }

  async getCurrentUser(): Promise<User> {
    return apiService.get<User>(`${this.API_PREFIX}/me`);
  }

  async changePassword(request: ChangePasswordRequest): Promise<void> {
    return apiService.post(`${this.API_PREFIX}/change-password`, request);
  }

  async resetPassword(request: ResetPasswordRequest): Promise<void> {
    return apiService.post(`${this.API_PREFIX}/reset-password`, request);
  }

  async confirmResetPassword(request: ConfirmResetPasswordRequest): Promise<void> {
    return apiService.post(`${this.API_PREFIX}/confirm-reset-password`, request);
  }

  // Token management
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
    apiService.setAuthToken(token);
  }

  clearTokens(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    apiService.clearAuthToken();
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      // Basic JWT validation (check if it's not expired)
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      
      return payload.exp > currentTime;
    } catch (error) {
      console.warn('Invalid token format:', error);
      return false;
    }
  }

  getUserFromToken(): Partial<User> | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.sub,
        role: payload.role,
        email: payload.email,
      };
    } catch (error) {
      console.warn('Failed to parse token:', error);
      return null;
    }
  }

  // Role-based access control helpers
  hasRole(requiredRole: string): boolean {
    const user = this.getUserFromToken();
    return user?.role === requiredRole;
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.getUserFromToken();
    return user?.role ? roles.includes(user.role) : false;
  }

  canAccessAdminService(): boolean {
    return this.hasAnyRole(['ADMIN', 'CASHIER']);
  }

  canAccessStaffService(): boolean {
    return this.hasAnyRole(['RECEPTION', 'TEACHER', 'MANAGER', 'ACADEMIC_MANAGER']);
  }

  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default authService;
