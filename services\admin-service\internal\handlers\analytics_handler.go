package handlers

import (
	"time"

	"admin-service/internal/models"
	"admin-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/shared/utils"
)

// AnalyticsHandler handles analytics-related HTTP requests
type Analytics<PERSON><PERSON><PERSON> struct {
	analyticsService services.AnalyticsService
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService services.AnalyticsService) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
	}
}

// GetDashboard handles GET /analytics/dashboard
func (h *AnalyticsHandler) GetDashboard(c *gin.Context) {
	analytics, err := h.analyticsService.GetDashboardAnalytics()
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, analytics, "Dashboard analytics retrieved successfully")
}

// GetFinancialReport handles GET /analytics/financial
func (h *AnalyticsHandler) GetFinancialReport(c *gin.Context) {
	var req models.AnalyticsRequest

	// Parse query parameters
	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			// Set to end of day
			endOfDay := date.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			req.EndDate = &endOfDay
		}
	}

	if branch := c.Query("branch"); branch != "" {
		req.Branch = &branch
	}

	if groupBy := c.Query("group_by"); groupBy != "" {
		req.GroupBy = &groupBy
	}

	report, err := h.analyticsService.GetFinancialReport(&req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, report, "Financial report retrieved successfully")
}

// GetUserAnalytics handles GET /analytics/users
func (h *AnalyticsHandler) GetUserAnalytics(c *gin.Context) {
	var req models.AnalyticsRequest

	// Parse query parameters
	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			// Set to end of day
			endOfDay := date.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			req.EndDate = &endOfDay
		}
	}

	if groupBy := c.Query("group_by"); groupBy != "" {
		req.GroupBy = &groupBy
	}

	analytics, err := h.analyticsService.GetUserAnalytics(&req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, analytics, "User analytics retrieved successfully")
}

// GetSystemMetrics handles GET /analytics/system
func (h *AnalyticsHandler) GetSystemMetrics(c *gin.Context) {
	metrics, err := h.analyticsService.GetSystemMetrics()
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, metrics, "System metrics retrieved successfully")
}
