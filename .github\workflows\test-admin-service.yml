name: Test Admin Service

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  test-admin-service:
    runs-on: ubuntu-latest
    
    services:
      postgres-auth:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: crm_user
          POSTGRES_PASSWORD: crm_password
          POSTGRES_DB: crm_auth
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      postgres-admin:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: crm_user
          POSTGRES_PASSWORD: crm_password
          POSTGRES_DB: crm_admin
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Auth Service
      run: |
        cd services/auth-service
        docker build -t auth-service .

    - name: Build Admin Service
      run: |
        docker build -f services/admin-service/Dockerfile -t admin-service .

    - name: Start Auth Service
      run: |
        docker run -d \
          --name auth-service \
          --network host \
          -e PORT=8081 \
          -e DB_HOST=localhost \
          -e DB_PORT=5432 \
          -e DB_USER=crm_user \
          -e DB_PASSWORD=crm_password \
          -e DB_NAME=crm_auth \
          -e DB_SSL_MODE=disable \
          -e JWT_SECRET=test-secret \
          -e REDIS_URL=redis://localhost:6379 \
          auth-service

    - name: Start Admin Service
      run: |
        docker run -d \
          --name admin-service \
          --network host \
          -e PORT=8082 \
          -e DB_HOST=localhost \
          -e DB_PORT=5433 \
          -e DB_USER=crm_user \
          -e DB_PASSWORD=crm_password \
          -e DB_NAME_ADMIN=crm_admin \
          -e DB_SSL_MODE=disable \
          -e JWT_SECRET=test-secret \
          -e REDIS_URL=redis://localhost:6379 \
          -e AUTH_SERVICE_URL=http://localhost:8081 \
          admin-service

    - name: Wait for services to be ready
      run: |
        echo "Waiting for services to start..."
        sleep 30
        
        # Wait for Auth Service
        for i in {1..30}; do
          if curl -s http://localhost:8081/health | grep -q "healthy"; then
            echo "Auth Service is ready"
            break
          fi
          echo "Waiting for Auth Service... ($i/30)"
          sleep 5
        done
        
        # Wait for Admin Service
        for i in {1..30}; do
          if curl -s http://localhost:8082/health | grep -q "healthy"; then
            echo "Admin Service is ready"
            break
          fi
          echo "Waiting for Admin Service... ($i/30)"
          sleep 5
        done

    - name: Test Health Endpoints
      run: |
        echo "Testing Auth Service health..."
        curl -f http://localhost:8081/health
        
        echo "Testing Admin Service health..."
        curl -f http://localhost:8082/health

    - name: Test Authentication Flow
      run: |
        echo "Creating admin user..."
        curl -X POST \
          -H "Content-Type: application/json" \
          -d '{
            "email": "<EMAIL>",
            "username": "admin",
            "password": "Admin123!@#",
            "first_name": "Admin",
            "last_name": "User",
            "phone": "+998901234567",
            "role": "ADMIN"
          }' \
          http://localhost:8081/api/v1/auth/register
        
        echo "Logging in..."
        TOKEN_RESPONSE=$(curl -X POST \
          -H "Content-Type: application/json" \
          -d '{
            "email": "<EMAIL>",
            "password": "Admin123!@#"
          }' \
          http://localhost:8081/api/v1/auth/login)
        
        echo "Token response: $TOKEN_RESPONSE"
        
        # Extract token (basic extraction)
        TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        
        if [ -z "$TOKEN" ]; then
          echo "Failed to extract token"
          exit 1
        fi
        
        echo "Token extracted successfully"
        echo "TOKEN=$TOKEN" >> $GITHUB_ENV

    - name: Test Admin Service APIs
      run: |
        echo "Testing Users API..."
        curl -f -H "Authorization: Bearer $TOKEN" \
          http://localhost:8082/api/v1/users
        
        echo "Testing Analytics API..."
        curl -f -H "Authorization: Bearer $TOKEN" \
          http://localhost:8082/api/v1/analytics/dashboard
        
        echo "Testing Audit Logs API..."
        curl -f -H "Authorization: Bearer $TOKEN" \
          http://localhost:8082/api/v1/audit-logs

    - name: Test User CRUD Operations
      run: |
        echo "Creating test user..."
        CREATE_RESPONSE=$(curl -X POST \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -d '{
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "TestUser123!",
            "first_name": "Test",
            "last_name": "User",
            "phone": "+998901234568",
            "role": "CASHIER"
          }' \
          http://localhost:8082/api/v1/users)
        
        echo "Create response: $CREATE_RESPONSE"
        
        # Extract user ID (basic extraction)
        USER_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        
        if [ -n "$USER_ID" ]; then
          echo "User created with ID: $USER_ID"
          
          echo "Updating user..."
          curl -f -X PUT \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
              "first_name": "Updated Test",
              "last_name": "Updated User"
            }' \
            "http://localhost:8082/api/v1/users/$USER_ID"
          
          echo "Getting user..."
          curl -f -H "Authorization: Bearer $TOKEN" \
            "http://localhost:8082/api/v1/users/$USER_ID"
          
          echo "Deleting user..."
          curl -f -X DELETE \
            -H "Authorization: Bearer $TOKEN" \
            "http://localhost:8082/api/v1/users/$USER_ID"
        else
          echo "Failed to extract user ID"
        fi

    - name: Show Container Logs
      if: failure()
      run: |
        echo "Auth Service logs:"
        docker logs auth-service
        
        echo "Admin Service logs:"
        docker logs admin-service

    - name: Cleanup
      if: always()
      run: |
        docker stop auth-service admin-service || true
        docker rm auth-service admin-service || true
