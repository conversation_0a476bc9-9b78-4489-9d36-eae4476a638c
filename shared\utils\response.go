package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/shared/models"
)

// ResponseHelper provides helper functions for HTTP responses
type ResponseHelper struct{}

// NewResponseHelper creates a new response helper
func NewResponseHelper() *ResponseHelper {
	return &ResponseHelper{}
}

// Success sends a successful response
func (r *ResponseHelper) Success(c *gin.Context, data interface{}, message string) {
	response := models.SuccessResponse(data, message)
	c.JSON(http.StatusOK, response)
}

// Created sends a created response
func (r *ResponseHelper) Created(c *gin.Context, data interface{}, message string) {
	response := models.SuccessResponse(data, message)
	c.JSON(http.StatusCreated, response)
}

// NoContent sends a no content response
func (r *ResponseHelper) NoContent(c *gin.Context) {
	c.Status(http.StatusNoContent)
}

// BadRequest sends a bad request error response
func (r *ResponseHelper) BadRequest(c *gin.Context, message string) {
	response := models.ErrorResponse(message)
	c.J<PERSON>(http.StatusBadRequest, response)
}

// Unauthorized sends an unauthorized error response
func (r *ResponseHelper) Unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "Unauthorized access"
	}
	response := models.ErrorResponse(message)
	c.JSON(http.StatusUnauthorized, response)
}

// Forbidden sends a forbidden error response
func (r *ResponseHelper) Forbidden(c *gin.Context, message string) {
	if message == "" {
		message = "Access forbidden"
	}
	response := models.ErrorResponse(message)
	c.JSON(http.StatusForbidden, response)
}

// NotFound sends a not found error response
func (r *ResponseHelper) NotFound(c *gin.Context, message string) {
	if message == "" {
		message = "Resource not found"
	}
	response := models.ErrorResponse(message)
	c.JSON(http.StatusNotFound, response)
}

// Conflict sends a conflict error response
func (r *ResponseHelper) Conflict(c *gin.Context, message string) {
	response := models.ErrorResponse(message)
	c.JSON(http.StatusConflict, response)
}

// UnprocessableEntity sends an unprocessable entity error response
func (r *ResponseHelper) UnprocessableEntity(c *gin.Context, message string) {
	response := models.ErrorResponse(message)
	c.JSON(http.StatusUnprocessableEntity, response)
}

// InternalServerError sends an internal server error response
func (r *ResponseHelper) InternalServerError(c *gin.Context, message string) {
	if message == "" {
		message = "Internal server error"
	}
	response := models.ErrorResponse(message)
	c.JSON(http.StatusInternalServerError, response)
}

// ValidationError sends a validation error response
func (r *ResponseHelper) ValidationError(c *gin.Context, errors ValidationErrors) {
	response := &models.APIResponse{
		Success: false,
		Error:   "Validation failed",
		Data:    errors,
	}
	c.JSON(http.StatusBadRequest, response)
}

// Paginated sends a paginated response
func (r *ResponseHelper) Paginated(c *gin.Context, data interface{}, pagination *models.PaginationResponse, message string) {
	response := models.PaginatedResponse(data, pagination, message)
	c.JSON(http.StatusOK, response)
}

// Global response helper instance
var Response = NewResponseHelper()

// Helper functions for common responses

// SuccessResponse sends a successful response
func SuccessResponse(c *gin.Context, data interface{}, message string) {
	Response.Success(c, data, message)
}

// CreatedResponse sends a created response
func CreatedResponse(c *gin.Context, data interface{}, message string) {
	Response.Created(c, data, message)
}

// NoContentResponse sends a no content response
func NoContentResponse(c *gin.Context) {
	Response.NoContent(c)
}

// BadRequestResponse sends a bad request error response
func BadRequestResponse(c *gin.Context, message string) {
	Response.BadRequest(c, message)
}

// UnauthorizedResponse sends an unauthorized error response
func UnauthorizedResponse(c *gin.Context, message string) {
	Response.Unauthorized(c, message)
}

// ForbiddenResponse sends a forbidden error response
func ForbiddenResponse(c *gin.Context, message string) {
	Response.Forbidden(c, message)
}

// NotFoundResponse sends a not found error response
func NotFoundResponse(c *gin.Context, message string) {
	Response.NotFound(c, message)
}

// ConflictResponse sends a conflict error response
func ConflictResponse(c *gin.Context, message string) {
	Response.Conflict(c, message)
}

// UnprocessableEntityResponse sends an unprocessable entity error response
func UnprocessableEntityResponse(c *gin.Context, message string) {
	Response.UnprocessableEntity(c, message)
}

// InternalServerErrorResponse sends an internal server error response
func InternalServerErrorResponse(c *gin.Context, message string) {
	Response.InternalServerError(c, message)
}

// ValidationErrorResponse sends a validation error response
func ValidationErrorResponse(c *gin.Context, errors ValidationErrors) {
	Response.ValidationError(c, errors)
}

// PaginatedResponse sends a paginated response
func PaginatedResponse(c *gin.Context, data interface{}, pagination *models.PaginationResponse, message string) {
	Response.Paginated(c, data, pagination, message)
}

// HandleError handles different types of errors and sends appropriate responses
func HandleError(c *gin.Context, err error) {
	switch e := err.(type) {
	case ValidationErrors:
		ValidationErrorResponse(c, e)
	case ValidationError:
		ValidationErrorResponse(c, ValidationErrors{e})
	default:
		switch err {
		case ErrInvalidEmail, ErrInvalidPhone, ErrInvalidUUID, ErrInvalidUsername:
			BadRequestResponse(c, err.Error())
		case ErrInvalidToken, ErrExpiredToken, ErrTokenNotFound:
			UnauthorizedResponse(c, err.Error())
		case ErrInvalidPassword, ErrWeakPassword:
			BadRequestResponse(c, err.Error())
		default:
			InternalServerErrorResponse(c, "An unexpected error occurred")
		}
	}
}

// HealthResponse sends a health check response
func HealthResponse(c *gin.Context, status string, checks map[string]interface{}) {
	data := gin.H{
		"status": status,
		"checks": checks,
	}
	
	if status == "healthy" {
		SuccessResponse(c, data, "Service is healthy")
	} else {
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse("Service is unhealthy"))
	}
}

// MetricsResponse sends a metrics response
func MetricsResponse(c *gin.Context, metrics map[string]interface{}) {
	SuccessResponse(c, metrics, "Metrics retrieved successfully")
}

// FileUploadResponse sends a file upload response
func FileUploadResponse(c *gin.Context, fileInfo map[string]interface{}) {
	CreatedResponse(c, fileInfo, "File uploaded successfully")
}

// BulkOperationResponse sends a bulk operation response
func BulkOperationResponse(c *gin.Context, results map[string]interface{}) {
	SuccessResponse(c, results, "Bulk operation completed")
}

// ExportResponse sends an export response
func ExportResponse(c *gin.Context, exportInfo map[string]interface{}) {
	SuccessResponse(c, exportInfo, "Export completed successfully")
}

// ImportResponse sends an import response
func ImportResponse(c *gin.Context, importInfo map[string]interface{}) {
	SuccessResponse(c, importInfo, "Import completed successfully")
}

// SearchResponse sends a search response
func SearchResponse(c *gin.Context, results interface{}, pagination *models.PaginationResponse) {
	PaginatedResponse(c, results, pagination, "Search completed successfully")
}

// LoginResponse sends a login response
func LoginResponse(c *gin.Context, loginData *models.LoginResponse) {
	SuccessResponse(c, loginData, "Login successful")
}

// LogoutResponse sends a logout response
func LogoutResponse(c *gin.Context) {
	SuccessResponse(c, nil, "Logout successful")
}

// RefreshTokenResponse sends a refresh token response
func RefreshTokenResponse(c *gin.Context, tokenData interface{}) {
	SuccessResponse(c, tokenData, "Token refreshed successfully")
}

// PasswordChangeResponse sends a password change response
func PasswordChangeResponse(c *gin.Context) {
	SuccessResponse(c, nil, "Password changed successfully")
}

// EmailVerificationResponse sends an email verification response
func EmailVerificationResponse(c *gin.Context) {
	SuccessResponse(c, nil, "Email verified successfully")
}

// PasswordResetResponse sends a password reset response
func PasswordResetResponse(c *gin.Context) {
	SuccessResponse(c, nil, "Password reset email sent")
}

// ProfileUpdateResponse sends a profile update response
func ProfileUpdateResponse(c *gin.Context, userData interface{}) {
	SuccessResponse(c, userData, "Profile updated successfully")
}

// NotificationResponse sends a notification response
func NotificationResponse(c *gin.Context, notificationData interface{}) {
	SuccessResponse(c, notificationData, "Notification sent successfully")
}

// PaymentResponse sends a payment response
func PaymentResponse(c *gin.Context, paymentData interface{}) {
	SuccessResponse(c, paymentData, "Payment processed successfully")
}

// ReportResponse sends a report response
func ReportResponse(c *gin.Context, reportData interface{}) {
	SuccessResponse(c, reportData, "Report generated successfully")
}
