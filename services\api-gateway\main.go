package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/crm-microservices/api-gateway/internal/config"
	"github.com/crm-microservices/api-gateway/internal/handlers"
	"github.com/crm-microservices/api-gateway/internal/middleware"
	"github.com/crm-microservices/api-gateway/internal/proxy"
	"github.com/crm-microservices/api-gateway/internal/registry"
	"github.com/crm-microservices/shared/utils"
	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}

	// Set Gin mode
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize JWT manager for token validation
	jwtManager := utils.NewJWTManager(
		cfg.JWT.Secret,
		cfg.JWT.AccessTokenExpiry,
		cfg.JWT.RefreshTokenExpiry,
		cfg.JWT.Issuer,
		cfg.JWT.Audience,
	)

	// Initialize service registry
	serviceRegistry := registry.NewServiceRegistry()

	// Register services
	registerServices(serviceRegistry, cfg)

	// Initialize proxy
	serviceProxy := proxy.NewServiceProxy(serviceRegistry)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(jwtManager)
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(cfg.Security.RateLimitRequests, cfg.Security.RateLimitWindow)
	loggingMiddleware := middleware.NewLoggingMiddleware()
	corsMiddleware := middleware.NewCORSMiddleware(cfg.GetCORSOrigins())

	// Initialize handlers
	healthHandler := handlers.NewHealthHandler(serviceRegistry)
	proxyHandler := handlers.NewProxyHandler(serviceProxy, authMiddleware)

	// Setup router
	router := setupRouter(cfg, healthHandler, proxyHandler, authMiddleware, rateLimitMiddleware, loggingMiddleware, corsMiddleware)

	// Create HTTP server
	server := &http.Server{
		Addr:         cfg.GetServerAddr(),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting API Gateway on %s", cfg.GetServerAddr())
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down API Gateway...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("API Gateway stopped")
}

func registerServices(serviceRegistry *registry.ServiceRegistry, cfg *config.Config) {
	log.Println("Registering services...")

	// Register auth service
	if cfg.Services.AuthServiceURL != "" {
		authService := &registry.ServiceInfo{
			Name:     "auth-service",
			URL:      cfg.Services.AuthServiceURL,
			Health:   cfg.Services.AuthServiceURL + "/health",
			Priority: 1,
			Timeout:  30 * time.Second,
		}
		serviceRegistry.RegisterService("auth", authService)
		log.Printf("Registered auth service: URL=%s, Health=%s", authService.URL, authService.Health)
	} else {
		log.Println("WARNING: AUTH_SERVICE_URL not configured")
	}

	// Register admin service (when implemented)
	if cfg.Services.AdminServiceURL != "" {
		adminService := &registry.ServiceInfo{
			Name:     "admin-service",
			URL:      cfg.Services.AdminServiceURL,
			Health:   cfg.Services.AdminServiceURL + "/health",
			Priority: 1,
			Timeout:  30 * time.Second,
		}
		serviceRegistry.RegisterService("admin", adminService)
		log.Printf("Registered admin service: URL=%s, Health=%s", adminService.URL, adminService.Health)
	} else {
		log.Println("INFO: ADMIN_SERVICE_URL not configured")
	}

	// Register staff service (when implemented)
	if cfg.Services.StaffServiceURL != "" {
		staffService := &registry.ServiceInfo{
			Name:     "staff-service",
			URL:      cfg.Services.StaffServiceURL,
			Health:   cfg.Services.StaffServiceURL + "/health",
			Priority: 1,
			Timeout:  30 * time.Second,
		}
		serviceRegistry.RegisterService("staff", staffService)
		log.Printf("Registered staff service: URL=%s, Health=%s", staffService.URL, staffService.Health)
	} else {
		log.Println("INFO: STAFF_SERVICE_URL not configured")
	}

	// Register payment service (when implemented)
	if cfg.Services.PaymentServiceURL != "" {
		paymentService := &registry.ServiceInfo{
			Name:     "payment-service",
			URL:      cfg.Services.PaymentServiceURL,
			Health:   cfg.Services.PaymentServiceURL + "/health",
			Priority: 1,
			Timeout:  30 * time.Second,
		}
		serviceRegistry.RegisterService("payment", paymentService)
		log.Printf("Registered payment service: URL=%s, Health=%s", paymentService.URL, paymentService.Health)
	} else {
		log.Println("INFO: PAYMENT_SERVICE_URL not configured")
	}

	// Register notification service (when implemented)
	if cfg.Services.NotificationServiceURL != "" {
		notificationService := &registry.ServiceInfo{
			Name:     "notification-service",
			URL:      cfg.Services.NotificationServiceURL,
			Health:   cfg.Services.NotificationServiceURL + "/health",
			Priority: 1,
			Timeout:  30 * time.Second,
		}
		serviceRegistry.RegisterService("notification", notificationService)
		log.Printf("Registered notification service: URL=%s, Health=%s", notificationService.URL, notificationService.Health)
	} else {
		log.Println("INFO: NOTIFICATION_SERVICE_URL not configured")
	}

	log.Printf("Service registration complete. Total services registered: %d", len(serviceRegistry.GetAllServices()))

	// Debug: Log actual environment variables being used
	log.Printf("DEBUG: Service URLs from environment:")
	log.Printf("  AUTH_SERVICE_URL=%s", os.Getenv("AUTH_SERVICE_URL"))
	log.Printf("  ADMIN_SERVICE_URL=%s", os.Getenv("ADMIN_SERVICE_URL"))
	log.Printf("  STAFF_SERVICE_URL=%s", os.Getenv("STAFF_SERVICE_URL"))
	log.Printf("  PAYMENT_SERVICE_URL=%s", os.Getenv("PAYMENT_SERVICE_URL"))
	log.Printf("  CONFIG_VERSION=%s", os.Getenv("CONFIG_VERSION"))
}

func setupRouter(
	cfg *config.Config,
	healthHandler *handlers.HealthHandler,
	proxyHandler *handlers.ProxyHandler,
	authMiddleware *middleware.AuthMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
	loggingMiddleware *middleware.LoggingMiddleware,
	corsMiddleware *middleware.CORSMiddleware,
) *gin.Engine {
	router := gin.New()

	// Global middleware
	router.Use(gin.Recovery())
	router.Use(loggingMiddleware.Logger())
	router.Use(corsMiddleware.Handler())
	router.Use(rateLimitMiddleware.Handler())

	// Health check endpoints (no auth required)
	router.GET("/health", healthHandler.HealthCheck)
	router.GET("/ready", healthHandler.ReadinessCheck)
	router.GET("/live", healthHandler.LivenessCheck)
	router.GET("/version", healthHandler.VersionCheck)

	// API routes
	api := router.Group("/api/v1")
	{
		// Auth service routes (public endpoints)
		auth := api.Group("/auth")
		{
			auth.Any("/*path", proxyHandler.ProxyToAuth)
		}

		// Protected routes (require authentication)
		protected := api.Group("")
		protected.Use(authMiddleware.RequireAuth())
		{
			// User management routes
			users := protected.Group("/users")
			{
				users.Any("/*path", proxyHandler.ProxyToUsers)
			}

			// Admin routes (admin/cashier only)
			admin := protected.Group("/admin")
			admin.Use(authMiddleware.RequireAdminAccess())
			{
				admin.Any("/*path", proxyHandler.ProxyToAdmin)
			}

			// Staff routes (staff roles only)
			staff := protected.Group("/staff")
			staff.Use(authMiddleware.RequireStaffAccess())
			{
				staff.Any("/*path", proxyHandler.ProxyToStaff)
			}

			// Payment routes
			payment := protected.Group("/payment")
			{
				payment.Any("/*path", proxyHandler.ProxyToPayment)
			}

			// Notification routes
			notifications := protected.Group("/notifications")
			{
				notifications.Any("/*path", proxyHandler.ProxyToNotification)
			}
		}
	}

	return router
}
