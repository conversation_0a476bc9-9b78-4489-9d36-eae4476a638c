export interface Course {
  id: string;
  name: string;
  description: string;
  category: string;
  instructor_id: string;
  instructor_name?: string;
  duration_weeks: number;
  price: number;
  max_students: number;
  current_students: number;
  status: CourseStatus;
  start_date: string;
  end_date: string;
  schedule: CourseSchedule[];
  materials: CourseMaterial[];
  created_at: string;
  updated_at: string;
}

export type CourseStatus = 'DRAFT' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';

export interface CourseSchedule {
  day_of_week: number; // 0-6 (Sunday-Saturday)
  start_time: string;
  end_time: string;
  room?: string;
}

export interface CourseMaterial {
  id: string;
  name: string;
  type: 'PDF' | 'VIDEO' | 'LINK' | 'OTHER';
  url: string;
  size?: number;
  uploaded_at: string;
}

export interface CourseCreateRequest {
  name: string;
  description: string;
  category: string;
  instructor_id: string;
  duration_weeks: number;
  price: number;
  max_students: number;
  start_date: string;
  end_date: string;
  schedule: CourseSchedule[];
}

export interface CourseUpdateRequest {
  name?: string;
  description?: string;
  category?: string;
  instructor_id?: string;
  duration_weeks?: number;
  price?: number;
  max_students?: number;
  start_date?: string;
  end_date?: string;
  schedule?: CourseSchedule[];
  status?: CourseStatus;
}

export interface CourseFilters {
  search?: string;
  status?: CourseStatus;
  category?: string;
  instructor_id?: string;
  start_date_from?: string;
  start_date_to?: string;
}

export interface CourseStats {
  total_courses: number;
  active_courses: number;
  completed_courses: number;
  draft_courses: number;
  cancelled_courses: number;
  total_students_enrolled: number;
  average_course_rating: number;
  courses_by_category: Record<string, number>;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
