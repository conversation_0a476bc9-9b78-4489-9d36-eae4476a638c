# CRM Admin Service API Tests
# Use this file with REST Client extension in VS Code
# or any HTTP client that supports .http files

### Variables
@baseUrl = http://localhost:8082
@authUrl = http://localhost:8081
@contentType = application/json

### 1. Test Auth Service Health
GET {{authUrl}}/health

### 2. Test Admin Service Health
GET {{baseUrl}}/health

### 3. Test unauthenticated access (should fail)
GET {{baseUrl}}/api/v1/users

### 4. Register Admin User
POST {{authUrl}}/api/v1/auth/register
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "username": "admin",
  "password": "Admin123!@#",
  "first_name": "Admin",
  "last_name": "User",
  "phone": "+998901234567",
  "role": "ADMIN"
}

### 5. Login to get token
POST {{authUrl}}/api/v1/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "Admin123!@#"
}

### 6. Get Users (replace YOUR_TOKEN with actual token from login response)
GET {{baseUrl}}/api/v1/users
Authorization: Bearer YOUR_TOKEN

### 7. Get Analytics Dashboard
GET {{baseUrl}}/api/v1/analytics/dashboard
Authorization: Bearer YOUR_TOKEN

### 8. Get Audit Logs
GET {{baseUrl}}/api/v1/audit-logs
Authorization: Bearer YOUR_TOKEN

### 9. Create New User
POST {{baseUrl}}/api/v1/users
Authorization: Bearer YOUR_TOKEN
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "username": "testuser",
  "password": "TestUser123!",
  "first_name": "Test",
  "last_name": "User",
  "phone": "+998901234568",
  "role": "CASHIER"
}

### 10. Update User (replace USER_ID with actual user ID)
PUT {{baseUrl}}/api/v1/users/USER_ID
Authorization: Bearer YOUR_TOKEN
Content-Type: {{contentType}}

{
  "first_name": "Updated Test",
  "last_name": "Updated User"
}

### 11. Get User by ID (replace USER_ID with actual user ID)
GET {{baseUrl}}/api/v1/users/USER_ID
Authorization: Bearer YOUR_TOKEN

### 12. Delete User (replace USER_ID with actual user ID)
DELETE {{baseUrl}}/api/v1/users/USER_ID
Authorization: Bearer YOUR_TOKEN

### 13. Get Financial Report
GET {{baseUrl}}/api/v1/analytics/financial?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer YOUR_TOKEN

### 14. Get User Analytics
GET {{baseUrl}}/api/v1/analytics/users
Authorization: Bearer YOUR_TOKEN

### 15. Get Audit Log Statistics
GET {{baseUrl}}/api/v1/audit-logs/stats
Authorization: Bearer YOUR_TOKEN

### 16. Get Recent Activity
GET {{baseUrl}}/api/v1/audit-logs/recent?limit=10
Authorization: Bearer YOUR_TOKEN
