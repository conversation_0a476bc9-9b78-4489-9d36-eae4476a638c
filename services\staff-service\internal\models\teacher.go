package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// TeacherStatus represents the status of a teacher
type TeacherStatus string

const (
	TeacherStatusActive    TeacherStatus = "ACTIVE"
	TeacherStatusInactive  TeacherStatus = "INACTIVE"
	TeacherStatusSuspended TeacherStatus = "SUSPENDED"
	TeacherStatusOnLeave   TeacherStatus = "ON_LEAVE"
)

// IsValid checks if the teacher status is valid
func (ts TeacherStatus) IsValid() bool {
	switch ts {
	case TeacherStatusActive, TeacherStatusInactive, TeacherStatusSuspended, TeacherStatusOnLeave:
		return true
	}
	return false
}

// Teacher represents a teacher in the system
type Teacher struct {
	models.BaseModel
	TeacherID        string        `json:"teacher_id" gorm:"uniqueIndex;not null;size:20"` // Auto-generated teacher ID
	LinkedUserID     *uuid.UUID    `json:"user_id" gorm:"column:user_id;uniqueIndex"` // Link to User table if teacher has system access (no FK constraint - different service)
	User             *models.User  `json:"user,omitempty" gorm:"-"` // Excluded from database, populated by service layer
	
	// Personal information
	FirstName        string        `json:"first_name" gorm:"not null;size:100"`
	LastName         string        `json:"last_name" gorm:"not null;size:100"`
	Email            string        `json:"email" gorm:"not null;size:255;index"`
	Phone            string        `json:"phone" gorm:"size:20;index"`
	DateOfBirth      *time.Time    `json:"date_of_birth"`
	Address          string        `json:"address" gorm:"size:500"`
	City             string        `json:"city" gorm:"size:100"`
	Country          string        `json:"country" gorm:"size:100"`
	
	// Professional information
	Status           TeacherStatus `json:"status" gorm:"not null;default:'ACTIVE';index"`
	HireDate         time.Time     `json:"hire_date" gorm:"not null;index"`
	TerminationDate  *time.Time    `json:"termination_date"`
	Salary           *float64      `json:"salary"`
	Currency         string        `json:"currency" gorm:"default:'USD';size:3"`
	
	// Qualifications and expertise
	Qualifications   string        `json:"qualifications" gorm:"type:text"`
	Specializations  string        `json:"specializations" gorm:"type:text"` // JSON array of specialization areas
	Experience       int           `json:"experience"` // Years of experience
	Bio              string        `json:"bio" gorm:"type:text"`
	
	// Emergency contact
	EmergencyContactName  string   `json:"emergency_contact_name" gorm:"size:200"`
	EmergencyContactPhone string   `json:"emergency_contact_phone" gorm:"size:20"`
	EmergencyContactEmail string   `json:"emergency_contact_email" gorm:"size:255"`
	
	// Performance metrics
	Rating           *float64      `json:"rating"` // 1-5 scale
	TotalStudents    int           `json:"total_students" gorm:"default:0"`
	TotalCourses     int           `json:"total_courses" gorm:"default:0"`
	
	// Relationships
	Courses          []Course      `json:"courses,omitempty" gorm:"foreignKey:InstructorID"`
	Schedules        []Schedule    `json:"schedules,omitempty" gorm:"foreignKey:TeacherID"`
	
	// Metadata (no foreign key constraint - references auth service)
	CreatedByUserID  uuid.UUID     `json:"created_by_id" gorm:"column:created_by_id;not null"`
	CreatedBy        *models.User  `json:"created_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
	Notes            string        `json:"notes" gorm:"type:text"`
}

// GetFullName returns the full name of the teacher
func (t *Teacher) GetFullName() string {
	return t.FirstName + " " + t.LastName
}

// IsActive checks if the teacher is active
func (t *Teacher) IsActive() bool {
	return t.Status == TeacherStatusActive
}

// CanTeach checks if the teacher can be assigned to courses
func (t *Teacher) CanTeach() bool {
	return t.Status == TeacherStatusActive
}

// TeacherCreateRequest represents a request to create a teacher
type TeacherCreateRequest struct {
	UserID                *uuid.UUID `json:"user_id"` // Optional - link to existing user
	FirstName             string     `json:"first_name" binding:"required,min=2,max=100"`
	LastName              string     `json:"last_name" binding:"required,min=2,max=100"`
	Email                 string     `json:"email" binding:"required,email,max=255"`
	Phone                 string     `json:"phone" binding:"max=20"`
	DateOfBirth           *time.Time `json:"date_of_birth"`
	Address               string     `json:"address" binding:"max=500"`
	City                  string     `json:"city" binding:"max=100"`
	Country               string     `json:"country" binding:"max=100"`
	HireDate              time.Time  `json:"hire_date" binding:"required"`
	Salary                *float64   `json:"salary" binding:"omitempty,min=0"`
	Currency              string     `json:"currency" binding:"omitempty,len=3"`
	Qualifications        string     `json:"qualifications"`
	Specializations       string     `json:"specializations"`
	Experience            int        `json:"experience" binding:"min=0"`
	Bio                   string     `json:"bio"`
	EmergencyContactName  string     `json:"emergency_contact_name" binding:"max=200"`
	EmergencyContactPhone string     `json:"emergency_contact_phone" binding:"max=20"`
	EmergencyContactEmail string     `json:"emergency_contact_email" binding:"omitempty,email,max=255"`
	Notes                 string     `json:"notes"`
}

// TeacherUpdateRequest represents a request to update a teacher
type TeacherUpdateRequest struct {
	FirstName             *string        `json:"first_name" binding:"omitempty,min=2,max=100"`
	LastName              *string        `json:"last_name" binding:"omitempty,min=2,max=100"`
	Email                 *string        `json:"email" binding:"omitempty,email,max=255"`
	Phone                 *string        `json:"phone" binding:"omitempty,max=20"`
	DateOfBirth           *time.Time     `json:"date_of_birth"`
	Address               *string        `json:"address" binding:"omitempty,max=500"`
	City                  *string        `json:"city" binding:"omitempty,max=100"`
	Country               *string        `json:"country" binding:"omitempty,max=100"`
	Status                *TeacherStatus `json:"status"`
	TerminationDate       *time.Time     `json:"termination_date"`
	Salary                *float64       `json:"salary" binding:"omitempty,min=0"`
	Currency              *string        `json:"currency" binding:"omitempty,len=3"`
	Qualifications        *string        `json:"qualifications"`
	Specializations       *string        `json:"specializations"`
	Experience            *int           `json:"experience" binding:"omitempty,min=0"`
	Bio                   *string        `json:"bio"`
	EmergencyContactName  *string        `json:"emergency_contact_name" binding:"omitempty,max=200"`
	EmergencyContactPhone *string        `json:"emergency_contact_phone" binding:"omitempty,max=20"`
	EmergencyContactEmail *string        `json:"emergency_contact_email" binding:"omitempty,email,max=255"`
	Rating                *float64       `json:"rating" binding:"omitempty,min=1,max=5"`
	Notes                 *string        `json:"notes"`
}

// TeacherResponse represents a teacher response
type TeacherResponse struct {
	ID                    uuid.UUID     `json:"id"`
	TeacherID             string        `json:"teacher_id"`
	UserID                *uuid.UUID    `json:"user_id"`
	FirstName             string        `json:"first_name"`
	LastName              string        `json:"last_name"`
	FullName              string        `json:"full_name"`
	Email                 string        `json:"email"`
	Phone                 string        `json:"phone"`
	DateOfBirth           *time.Time    `json:"date_of_birth"`
	Address               string        `json:"address"`
	City                  string        `json:"city"`
	Country               string        `json:"country"`
	Status                TeacherStatus `json:"status"`
	HireDate              time.Time     `json:"hire_date"`
	TerminationDate       *time.Time    `json:"termination_date"`
	Salary                *float64      `json:"salary"`
	Currency              string        `json:"currency"`
	Qualifications        string        `json:"qualifications"`
	Specializations       string        `json:"specializations"`
	Experience            int           `json:"experience"`
	Bio                   string        `json:"bio"`
	EmergencyContactName  string        `json:"emergency_contact_name"`
	EmergencyContactPhone string        `json:"emergency_contact_phone"`
	EmergencyContactEmail string        `json:"emergency_contact_email"`
	Rating                *float64      `json:"rating"`
	TotalStudents         int           `json:"total_students"`
	TotalCourses          int           `json:"total_courses"`
	CreatedByID           uuid.UUID     `json:"created_by_id"`
	CreatedByName         string        `json:"created_by_name"`
	Notes                 string        `json:"notes"`
	CreatedAt             time.Time     `json:"created_at"`
	UpdatedAt             time.Time     `json:"updated_at"`
}

// ToResponse converts a Teacher to TeacherResponse
func (t *Teacher) ToResponse() *TeacherResponse {
	response := &TeacherResponse{
		ID:                    t.ID,
		TeacherID:             t.TeacherID,
		UserID:                t.LinkedUserID,
		FirstName:             t.FirstName,
		LastName:              t.LastName,
		FullName:              t.GetFullName(),
		Email:                 t.Email,
		Phone:                 t.Phone,
		DateOfBirth:           t.DateOfBirth,
		Address:               t.Address,
		City:                  t.City,
		Country:               t.Country,
		Status:                t.Status,
		HireDate:              t.HireDate,
		TerminationDate:       t.TerminationDate,
		Salary:                t.Salary,
		Currency:              t.Currency,
		Qualifications:        t.Qualifications,
		Specializations:       t.Specializations,
		Experience:            t.Experience,
		Bio:                   t.Bio,
		EmergencyContactName:  t.EmergencyContactName,
		EmergencyContactPhone: t.EmergencyContactPhone,
		EmergencyContactEmail: t.EmergencyContactEmail,
		Rating:                t.Rating,
		TotalStudents:         t.TotalStudents,
		TotalCourses:          t.TotalCourses,
		CreatedByID:           t.CreatedByUserID,
		Notes:                 t.Notes,
		CreatedAt:             t.CreatedAt,
		UpdatedAt:             t.UpdatedAt,
	}

	// Include created by user information if available
	if t.CreatedBy != nil {
		response.CreatedByName = t.CreatedBy.GetFullName()
	}

	return response
}

// TeacherListRequest represents a request to list teachers
type TeacherListRequest struct {
	models.PaginationRequest
	Status      *TeacherStatus `json:"status" form:"status"`
	CreatedByID *uuid.UUID     `json:"created_by_id" form:"created_by_id"`
	Search      string         `json:"search" form:"search"`
	StartDate   *time.Time     `json:"start_date" form:"start_date"`
	EndDate     *time.Time     `json:"end_date" form:"end_date"`
	Available   *bool          `json:"available" form:"available"` // Available for new assignments
}

// TeacherListResponse represents a response for teacher list
type TeacherListResponse struct {
	Teachers   []*TeacherResponse         `json:"teachers"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// TeacherSuspensionRequest represents a request to suspend a teacher
type TeacherSuspensionRequest struct {
	SuspensionDate   *time.Time `json:"suspension_date"`
	SuspensionReason string     `json:"suspension_reason" binding:"required"`
	SuspensionEnd    *time.Time `json:"suspension_end"`
	Notes            string     `json:"notes"`
}

// TeacherRatingUpdateRequest represents a request to update teacher rating
type TeacherRatingUpdateRequest struct {
	Rating float64 `json:"rating" binding:"required,min=1,max=5"`
	Notes  string  `json:"notes"`
}

// TeacherStats represents teacher statistics
type TeacherStats struct {
	TotalTeachers       int64                     `json:"total_teachers"`
	TeachersByStatus    map[TeacherStatus]int64   `json:"teachers_by_status"`
	AverageRating       *float64                  `json:"average_rating"`
	AverageSalary       *float64                  `json:"average_salary"`
	AverageExperience   *float64                  `json:"average_experience"`
	TopRatedTeachers    []*TeacherResponse        `json:"top_rated_teachers"`
	NewTeachersToday    int64                     `json:"new_teachers_today"`
	NewTeachersThisWeek int64                     `json:"new_teachers_this_week"`
}
