package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// DayOfWeek represents days of the week
type DayOfWeek string

const (
	Monday    DayOfWeek = "MONDAY"
	Tuesday   DayOfWeek = "TUESDAY"
	Wednesday DayOfWeek = "WEDNESDAY"
	Thursday  DayOfWeek = "THURSDAY"
	Friday    DayOfWeek = "FRIDAY"
	Saturday  DayOfWeek = "SATURDAY"
	Sunday    DayOfWeek = "SUNDAY"
)

// IsValid checks if the day of week is valid
func (d DayOfWeek) IsValid() bool {
	switch d {
	case Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday:
		return true
	}
	return false
}

// ScheduleStatus represents the status of a schedule
type ScheduleStatus string

const (
	ScheduleStatusActive    ScheduleStatus = "ACTIVE"
	ScheduleStatusCancelled ScheduleStatus = "CANCELLED"
	ScheduleStatusCompleted ScheduleStatus = "COMPLETED"
	ScheduleStatusPostponed ScheduleStatus = "POSTPONED"
)

// IsValid checks if the schedule status is valid
func (ss ScheduleStatus) IsValid() bool {
	switch ss {
	case ScheduleStatusActive, ScheduleStatusCancelled, ScheduleStatusCompleted, ScheduleStatusPostponed:
		return true
	}
	return false
}

// Schedule represents a class schedule
type Schedule struct {
	models.BaseModel
	CourseID         uuid.UUID      `json:"course_id" gorm:"not null;index"`
	Course           *Course        `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	TeacherID        uuid.UUID      `json:"teacher_id" gorm:"not null;index"`
	Teacher          *Teacher       `json:"teacher,omitempty" gorm:"foreignKey:TeacherID"`
	
	// Schedule details
	Title            string         `json:"title" gorm:"not null;size:200"`
	Description      string         `json:"description" gorm:"type:text"`
	DayOfWeek        DayOfWeek      `json:"day_of_week" gorm:"not null;index"`
	StartTime        time.Time      `json:"start_time" gorm:"not null;index"`
	EndTime          time.Time      `json:"end_time" gorm:"not null;index"`
	Duration         int            `json:"duration" gorm:"not null"` // Duration in minutes
	
	// Location and resources
	Room             string         `json:"room" gorm:"size:100"`
	Building         string         `json:"building" gorm:"size:100"`
	Location         string         `json:"location" gorm:"size:200"`
	MaxCapacity      int            `json:"max_capacity" gorm:"default:30"`
	
	// Schedule metadata
	Status           ScheduleStatus `json:"status" gorm:"not null;default:'ACTIVE';index"`
	IsRecurring      bool           `json:"is_recurring" gorm:"default:true"`
	RecurrenceEnd    *time.Time     `json:"recurrence_end"`
	
	// Special dates (for one-time schedules or exceptions)
	SpecificDate     *time.Time     `json:"specific_date" gorm:"index"`
	
	// Notes and requirements
	Notes            string         `json:"notes" gorm:"type:text"`
	Requirements     string         `json:"requirements" gorm:"type:text"`
	
	// Relationships
	Attendances      []Attendance   `json:"attendances,omitempty" gorm:"foreignKey:ScheduleID"`
	
	// Metadata (no foreign key constraint - references auth service)
	CreatedByUserID  uuid.UUID      `json:"created_by_id" gorm:"column:created_by_id;not null"`
	CreatedBy        *models.User   `json:"created_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
}

// IsActive checks if the schedule is active
func (s *Schedule) IsActive() bool {
	return s.Status == ScheduleStatusActive
}

// GetDurationHours returns duration in hours
func (s *Schedule) GetDurationHours() float64 {
	return float64(s.Duration) / 60.0
}

// HasConflict checks if this schedule conflicts with another (basic check)
func (s *Schedule) HasConflict(other *Schedule) bool {
	// Same day and overlapping times
	if s.DayOfWeek == other.DayOfWeek {
		return s.StartTime.Before(other.EndTime) && s.EndTime.After(other.StartTime)
	}
	return false
}

// ScheduleCreateRequest represents a request to create a schedule
type ScheduleCreateRequest struct {
	CourseID      uuid.UUID `json:"course_id" binding:"required"`
	TeacherID     uuid.UUID `json:"teacher_id" binding:"required"`
	Title         string    `json:"title" binding:"required,min=2,max=200"`
	Description   string    `json:"description"`
	DayOfWeek     DayOfWeek `json:"day_of_week" binding:"required"`
	StartTime     time.Time `json:"start_time" binding:"required"`
	EndTime       time.Time `json:"end_time" binding:"required"`
	Room          string    `json:"room" binding:"max=100"`
	Building      string    `json:"building" binding:"max=100"`
	Location      string    `json:"location" binding:"max=200"`
	MaxCapacity   int       `json:"max_capacity" binding:"min=1,max=100"`
	IsRecurring   bool      `json:"is_recurring"`
	RecurrenceEnd *time.Time `json:"recurrence_end"`
	SpecificDate  *time.Time `json:"specific_date"`
	Notes         string    `json:"notes"`
	Requirements  string    `json:"requirements"`
}

// ScheduleUpdateRequest represents a request to update a schedule
type ScheduleUpdateRequest struct {
	Title         *string         `json:"title" binding:"omitempty,min=2,max=200"`
	Description   *string         `json:"description"`
	DayOfWeek     *DayOfWeek      `json:"day_of_week"`
	StartTime     *time.Time      `json:"start_time"`
	EndTime       *time.Time      `json:"end_time"`
	Room          *string         `json:"room" binding:"omitempty,max=100"`
	Building      *string         `json:"building" binding:"omitempty,max=100"`
	Location      *string         `json:"location" binding:"omitempty,max=200"`
	MaxCapacity   *int            `json:"max_capacity" binding:"omitempty,min=1,max=100"`
	Status        *ScheduleStatus `json:"status"`
	IsRecurring   *bool           `json:"is_recurring"`
	RecurrenceEnd *time.Time      `json:"recurrence_end"`
	SpecificDate  *time.Time      `json:"specific_date"`
	Notes         *string         `json:"notes"`
	Requirements  *string         `json:"requirements"`
}

// ScheduleResponse represents a schedule response
type ScheduleResponse struct {
	ID            uuid.UUID      `json:"id"`
	CourseID      uuid.UUID      `json:"course_id"`
	CourseName    string         `json:"course_name"`
	CourseCode    string         `json:"course_code"`
	TeacherID     uuid.UUID      `json:"teacher_id"`
	TeacherName   string         `json:"teacher_name"`
	Title         string         `json:"title"`
	Description   string         `json:"description"`
	DayOfWeek     DayOfWeek      `json:"day_of_week"`
	StartTime     time.Time      `json:"start_time"`
	EndTime       time.Time      `json:"end_time"`
	Duration      int            `json:"duration"`
	DurationHours float64        `json:"duration_hours"`
	Room          string         `json:"room"`
	Building      string         `json:"building"`
	Location      string         `json:"location"`
	MaxCapacity   int            `json:"max_capacity"`
	Status        ScheduleStatus `json:"status"`
	IsRecurring   bool           `json:"is_recurring"`
	RecurrenceEnd *time.Time     `json:"recurrence_end"`
	SpecificDate  *time.Time     `json:"specific_date"`
	Notes         string         `json:"notes"`
	Requirements  string         `json:"requirements"`
	CreatedByID   uuid.UUID      `json:"created_by_id"`
	CreatedByName string         `json:"created_by_name"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}

// ToResponse converts a Schedule to ScheduleResponse
func (s *Schedule) ToResponse() *ScheduleResponse {
	response := &ScheduleResponse{
		ID:            s.ID,
		CourseID:      s.CourseID,
		TeacherID:     s.TeacherID,
		Title:         s.Title,
		Description:   s.Description,
		DayOfWeek:     s.DayOfWeek,
		StartTime:     s.StartTime,
		EndTime:       s.EndTime,
		Duration:      s.Duration,
		DurationHours: s.GetDurationHours(),
		Room:          s.Room,
		Building:      s.Building,
		Location:      s.Location,
		MaxCapacity:   s.MaxCapacity,
		Status:        s.Status,
		IsRecurring:   s.IsRecurring,
		RecurrenceEnd: s.RecurrenceEnd,
		SpecificDate:  s.SpecificDate,
		Notes:         s.Notes,
		Requirements:  s.Requirements,
		CreatedByID:   s.CreatedByUserID,
		CreatedAt:     s.CreatedAt,
		UpdatedAt:     s.UpdatedAt,
	}

	// Include course information if available
	if s.Course != nil {
		response.CourseName = s.Course.Name
		response.CourseCode = s.Course.CourseCode
	}

	// Include teacher information if available
	if s.Teacher != nil {
		response.TeacherName = s.Teacher.GetFullName()
	}

	// Include created by user information if available
	if s.CreatedBy != nil {
		response.CreatedByName = s.CreatedBy.GetFullName()
	}

	return response
}

// ScheduleListRequest represents a request to list schedules
type ScheduleListRequest struct {
	models.PaginationRequest
	CourseID     *uuid.UUID      `json:"course_id" form:"course_id"`
	TeacherID    *uuid.UUID      `json:"teacher_id" form:"teacher_id"`
	DayOfWeek    *DayOfWeek      `json:"day_of_week" form:"day_of_week"`
	Status       *ScheduleStatus `json:"status" form:"status"`
	Room         *string         `json:"room" form:"room"`
	Building     *string         `json:"building" form:"building"`
	CreatedByID  *uuid.UUID      `json:"created_by_id" form:"created_by_id"`
	Search       string          `json:"search" form:"search"`
	StartDate    *time.Time      `json:"start_date" form:"start_date"`
	EndDate      *time.Time      `json:"end_date" form:"end_date"`
	IsRecurring  *bool           `json:"is_recurring" form:"is_recurring"`
}

// ScheduleListResponse represents a response for schedule list
type ScheduleListResponse struct {
	Schedules  []*ScheduleResponse        `json:"schedules"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// ScheduleConflictRequest represents a request to check schedule conflicts
type ScheduleConflictRequest struct {
	TeacherID    *uuid.UUID `json:"teacher_id" form:"teacher_id"`
	Room         *string    `json:"room" form:"room"`
	DayOfWeek    DayOfWeek  `json:"day_of_week" form:"day_of_week" binding:"required"`
	StartTime    time.Time  `json:"start_time" form:"start_time" binding:"required"`
	EndTime      time.Time  `json:"end_time" form:"end_time" binding:"required"`
	ExcludeID    *uuid.UUID `json:"exclude_id" form:"exclude_id"` // Exclude this schedule ID from conflict check
}

// ScheduleConflictResponse represents conflicts found
type ScheduleConflictResponse struct {
	HasConflicts bool                `json:"has_conflicts"`
	Conflicts    []*ScheduleResponse `json:"conflicts"`
	Message      string              `json:"message"`
}

// ScheduleStats represents schedule statistics
type ScheduleStats struct {
	TotalSchedules       int64                     `json:"total_schedules"`
	SchedulesByStatus    map[ScheduleStatus]int64  `json:"schedules_by_status"`
	SchedulesByDay       map[DayOfWeek]int64       `json:"schedules_by_day"`
	AverageClassDuration float64                   `json:"average_class_duration"`
	TotalTeachingHours   float64                   `json:"total_teaching_hours"`
	RoomUtilization      map[string]int64          `json:"room_utilization"`
}
