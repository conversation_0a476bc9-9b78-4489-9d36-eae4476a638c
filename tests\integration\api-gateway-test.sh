#!/bin/bash

# 🌐 API Gateway Integration Test Suite
# Tests API Gateway routing, authentication, and service integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
GATEWAY_URL="http://localhost:8080"
AUTH_SERVICE_URL="http://localhost:8081"
ADMIN_SERVICE_URL="http://localhost:8082"
STAFF_SERVICE_URL="http://localhost:8083"
PAYMENT_SERVICE_URL="http://localhost:8084"
NOTIFICATION_SERVICE_URL="http://localhost:8085"

# Test data
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="GatewayAdmin123!@#"
STAFF_EMAIL="<EMAIL>"
STAFF_PASSWORD="GatewayStaff123!@#"

# Global variables
ADMIN_TOKEN=""
STAFF_TOKEN=""

# Utility functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "step")
            echo -e "\n${BLUE}🔄 $message${NC}"
            ;;
    esac
}

# Wait for service to be ready
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    print_status "info" "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url/health" > /dev/null 2>&1; then
            print_status "success" "$service_name is ready"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_status "error" "$service_name failed to start within timeout"
    return 1
}

# Test API endpoint through gateway
test_gateway_endpoint() {
    local name=$1
    local method=$2
    local path=$3
    local headers=$4
    local data=$5
    local expected_status=${6:-200}
    
    print_status "info" "Testing: $name"
    
    local url="$GATEWAY_URL$path"
    local response
    local status_code
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" -H "$headers" "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -H "$headers" -d "$data" "$url")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "success" "$name - Status: $status_code"
        echo "$response_body"
        return 0
    else
        print_status "error" "$name - Expected: $expected_status, Got: $status_code"
        echo "Response: $response_body"
        return 1
    fi
}

# Extract JSON value
extract_json_value() {
    local json=$1
    local key=$2
    echo "$json" | grep -o "\"$key\":\"[^\"]*\"" | cut -d'"' -f4
}

# Test gateway health endpoints
test_gateway_health() {
    print_status "step" "Testing API Gateway health endpoints"
    
    # Test basic health check
    test_gateway_endpoint "Gateway Health Check" "GET" "/health" "" "" "200"
    
    # Test readiness check
    test_gateway_endpoint "Gateway Readiness Check" "GET" "/ready" "" "" "200"
    
    # Test liveness check
    test_gateway_endpoint "Gateway Liveness Check" "GET" "/live" "" "" "200"
    
    # Test version check
    test_gateway_endpoint "Gateway Version Check" "GET" "/version" "" "" "200"
}

# Test authentication through gateway
test_gateway_authentication() {
    print_status "step" "Testing authentication through API Gateway"
    
    # Create admin user through gateway
    local admin_data='{
        "email": "'$ADMIN_EMAIL'",
        "username": "gateway-admin",
        "password": "'$ADMIN_PASSWORD'",
        "first_name": "Gateway",
        "last_name": "Admin",
        "phone": "+998901234567",
        "role": "ADMIN"
    }'
    
    test_gateway_endpoint "Create Admin User via Gateway" "POST" "/api/v1/auth/register" "" "$admin_data" "201"
    
    # Login through gateway
    local login_data='{
        "email": "'$ADMIN_EMAIL'",
        "password": "'$ADMIN_PASSWORD'"
    }'
    
    local login_response
    login_response=$(test_gateway_endpoint "Admin Login via Gateway" "POST" "/api/v1/auth/login" "" "$login_data" "200")
    ADMIN_TOKEN=$(extract_json_value "$login_response" "access_token")
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_status "error" "Failed to extract admin token from gateway"
        return 1
    fi
    
    print_status "success" "Admin token obtained through gateway"
    
    # Test token validation through gateway
    test_gateway_endpoint "Token Validation via Gateway" "GET" "/api/v1/auth/validate" "Authorization: Bearer $ADMIN_TOKEN" "" "200"
}

# Test service routing through gateway
test_service_routing() {
    print_status "step" "Testing service routing through API Gateway"
    
    local auth_header="Authorization: Bearer $ADMIN_TOKEN"
    
    # Test admin service routing
    test_gateway_endpoint "Admin Service via Gateway - Users List" "GET" "/api/v1/users" "$auth_header" "" "200"
    test_gateway_endpoint "Admin Service via Gateway - Analytics" "GET" "/api/v1/analytics/dashboard" "$auth_header" "" "200"
    
    # Test staff service routing (if available)
    test_gateway_endpoint "Staff Service via Gateway - Students List" "GET" "/api/v1/students" "$auth_header" "" "200" || print_status "warning" "Staff service may not be available"
    
    # Test payment service routing (if available)
    test_gateway_endpoint "Payment Service via Gateway - Payment Methods" "GET" "/api/v1/payment-methods" "$auth_header" "" "200" || print_status "warning" "Payment service may not be available"
    
    # Test notification service routing (if available)
    test_gateway_endpoint "Notification Service via Gateway - Templates" "GET" "/api/v1/templates" "$auth_header" "" "200" || print_status "warning" "Notification service may not be available"
}

# Test rate limiting
test_rate_limiting() {
    print_status "step" "Testing rate limiting through API Gateway"
    
    # Make rapid requests to test rate limiting
    local rate_limit_exceeded=false
    
    for i in {1..20}; do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/health")
        
        if [ "$status_code" = "429" ]; then
            rate_limit_exceeded=true
            break
        fi
        
        sleep 0.1
    done
    
    if [ "$rate_limit_exceeded" = true ]; then
        print_status "success" "Rate limiting is working (got 429 status)"
    else
        print_status "warning" "Rate limiting may not be configured or limit is very high"
    fi
}

# Test CORS headers
test_cors_configuration() {
    print_status "step" "Testing CORS configuration"
    
    # Test CORS preflight request
    local cors_response
    cors_response=$(curl -s -I -X OPTIONS \
        -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: POST" \
        -H "Access-Control-Request-Headers: Content-Type,Authorization" \
        "$GATEWAY_URL/api/v1/auth/login")
    
    if echo "$cors_response" | grep -i "access-control-allow-origin" > /dev/null; then
        print_status "success" "CORS headers are present"
    else
        print_status "warning" "CORS headers not found"
    fi
}

# Test error handling
test_error_handling() {
    print_status "step" "Testing error handling through API Gateway"
    
    # Test 404 for non-existent endpoint
    test_gateway_endpoint "Non-existent Endpoint" "GET" "/api/v1/non-existent" "" "" "404"
    
    # Test 401 for unauthorized access
    test_gateway_endpoint "Unauthorized Access" "GET" "/api/v1/users" "" "" "401"
    
    # Test 403 for insufficient permissions (if applicable)
    # This would require a user with limited permissions
}

# Test service failover
test_service_failover() {
    print_status "step" "Testing service failover behavior"
    
    # Test what happens when a service is unavailable
    # This is more of a manual test as we can't easily stop services in this script
    print_status "info" "Service failover testing requires manual service shutdown"
    print_status "info" "To test: Stop a service and verify gateway handles it gracefully"
}

# Test load balancing (if multiple instances)
test_load_balancing() {
    print_status "step" "Testing load balancing (if configured)"
    
    # Make multiple requests and check if they're distributed
    # This is a basic test - real load balancing would require multiple service instances
    local auth_header="Authorization: Bearer $ADMIN_TOKEN"
    
    for i in {1..5}; do
        test_gateway_endpoint "Load Balance Test $i" "GET" "/api/v1/auth/validate" "$auth_header" "" "200" > /dev/null
    done
    
    print_status "success" "Load balancing test completed (basic)"
}

# Test gateway metrics
test_gateway_metrics() {
    print_status "step" "Testing gateway metrics endpoint"
    
    # Test metrics endpoint (if available)
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$GATEWAY_URL/metrics")
    
    if [ "$status_code" = "200" ]; then
        print_status "success" "Metrics endpoint is available"
    else
        print_status "info" "Metrics endpoint not available (status: $status_code)"
    fi
}

# Main test execution
main() {
    print_status "step" "Starting API Gateway Integration Tests"
    
    # Wait for all services to be ready
    print_status "step" "Checking service availability"
    wait_for_service "API Gateway" "$GATEWAY_URL" || exit 1
    wait_for_service "Auth Service" "$AUTH_SERVICE_URL" || exit 1
    wait_for_service "Admin Service" "$ADMIN_SERVICE_URL" || exit 1
    
    # Run test suites
    test_gateway_health
    test_gateway_authentication
    test_service_routing
    test_rate_limiting
    test_cors_configuration
    test_error_handling
    test_service_failover
    test_load_balancing
    test_gateway_metrics
    
    print_status "step" "API Gateway Integration Tests Summary"
    print_status "success" "All API Gateway integration tests completed!"
    
    echo ""
    echo "📊 Test Summary:"
    echo "   ✅ Gateway Health: Tested"
    echo "   ✅ Authentication: Tested"
    echo "   ✅ Service Routing: Tested"
    echo "   ✅ Rate Limiting: Tested"
    echo "   ✅ CORS Configuration: Tested"
    echo "   ✅ Error Handling: Tested"
    echo "   ✅ Load Balancing: Basic Test"
    echo "   ✅ Metrics: Checked"
    echo ""
    echo "🎉 API Gateway is properly integrated and functional!"
}

# Run main function
main "$@"
