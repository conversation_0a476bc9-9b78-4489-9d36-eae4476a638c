package repository

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/crm-microservices/shared/models"
)

// UserRepository handles user data operations
type UserRepository interface {
	GetAll(req *models.UserListRequest) ([]*models.User, int64, error)
	GetByID(id uuid.UUID) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	GetByUsername(username string) (*models.User, error)
	Create(user *models.User) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetUserStats() (*UserStats, error)
	GetRecentRegistrations(limit int) ([]*models.User, error)
	GetMostActiveUsers(limit int) ([]*UserActivitySummary, error)
}

type userRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// GetAll retrieves users with pagination and filtering
func (r *userRepository) GetAll(req *models.UserListRequest) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := r.db.Model(&models.User{})

	// Apply filters
	if req.Role != nil {
		query = query.Where("role = ?", *req.Role)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where(
			"LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ? OR LOWER(username) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

// GetByID retrieves a user by ID
func (r *userRepository) GetByID(id uuid.UUID) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// GetByEmail retrieves a user by email
func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, "email = ?", email).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// GetByUsername retrieves a user by username
func (r *userRepository) GetByUsername(username string) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// Create creates a new user
func (r *userRepository) Create(user *models.User) error {
	if err := r.db.Create(user).Error; err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	return nil
}

// Update updates a user
func (r *userRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.User{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	return nil
}

// Delete soft deletes a user
func (r *userRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.User{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	return nil
}

// UserStats represents user statistics
type UserStats struct {
	TotalUsers       int64                    `json:"total_users"`
	ActiveUsers      int64                    `json:"active_users"`
	InactiveUsers    int64                    `json:"inactive_users"`
	SuspendedUsers   int64                    `json:"suspended_users"`
	UsersByRole      map[models.UserRole]int64 `json:"users_by_role"`
	NewUsersToday    int64                    `json:"new_users_today"`
	NewUsersThisWeek int64                    `json:"new_users_this_week"`
}

// UserActivitySummary represents user activity summary
type UserActivitySummary struct {
	UserID      uuid.UUID       `json:"user_id"`
	UserName    string          `json:"user_name"`
	UserEmail   string          `json:"user_email"`
	Role        models.UserRole `json:"role"`
	LoginCount  int64           `json:"login_count"`
	LastLoginAt *string         `json:"last_login_at"`
}

// GetUserStats retrieves user statistics
func (r *userRepository) GetUserStats() (*UserStats, error) {
	stats := &UserStats{
		UsersByRole: make(map[models.UserRole]int64),
	}

	// Total users
	if err := r.db.Model(&models.User{}).Count(&stats.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}

	// Users by status
	if err := r.db.Model(&models.User{}).Where("status = ?", models.StatusActive).Count(&stats.ActiveUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}

	if err := r.db.Model(&models.User{}).Where("status = ?", models.StatusInactive).Count(&stats.InactiveUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count inactive users: %w", err)
	}

	if err := r.db.Model(&models.User{}).Where("status = ?", models.StatusSuspended).Count(&stats.SuspendedUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count suspended users: %w", err)
	}

	// Users by role
	roles := []models.UserRole{
		models.RoleAdmin, models.RoleCashier, models.RoleReception,
		models.RoleTeacher, models.RoleManager, models.RoleAcademicManager,
	}

	for _, role := range roles {
		var count int64
		if err := r.db.Model(&models.User{}).Where("role = ?", role).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count users by role %s: %w", role, err)
		}
		stats.UsersByRole[role] = count
	}

	// New users today
	if err := r.db.Model(&models.User{}).Where("DATE(created_at) = CURRENT_DATE").Count(&stats.NewUsersToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count new users today: %w", err)
	}

	// New users this week
	if err := r.db.Model(&models.User{}).Where("created_at >= DATE_TRUNC('week', CURRENT_DATE)").Count(&stats.NewUsersThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count new users this week: %w", err)
	}

	return stats, nil
}

// GetRecentRegistrations retrieves recent user registrations
func (r *userRepository) GetRecentRegistrations(limit int) ([]*models.User, error) {
	var users []*models.User
	if err := r.db.Order("created_at DESC").Limit(limit).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent registrations: %w", err)
	}
	return users, nil
}

// GetMostActiveUsers retrieves most active users based on login activity
func (r *userRepository) GetMostActiveUsers(limit int) ([]*UserActivitySummary, error) {
	var results []*UserActivitySummary

	query := `
		SELECT 
			u.id as user_id,
			CONCAT(u.first_name, ' ', u.last_name) as user_name,
			u.email as user_email,
			u.role,
			COUNT(s.id) as login_count,
			MAX(s.last_used_at) as last_login_at
		FROM users u
		LEFT JOIN sessions s ON u.id = s.user_id
		WHERE u.deleted_at IS NULL
		GROUP BY u.id, u.first_name, u.last_name, u.email, u.role
		ORDER BY login_count DESC, last_login_at DESC
		LIMIT ?
	`

	if err := r.db.Raw(query, limit).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get most active users: %w", err)
	}

	return results, nil
}
