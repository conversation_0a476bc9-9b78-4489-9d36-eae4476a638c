package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// DeliveryStatus represents the status of a delivery attempt
type DeliveryStatus string

const (
	DeliveryStatusPending   DeliveryStatus = "PENDING"
	DeliveryStatusSent      DeliveryStatus = "SENT"
	DeliveryStatusDelivered DeliveryStatus = "DELIVERED"
	DeliveryStatusFailed    DeliveryStatus = "FAILED"
	DeliveryStatusBounced   DeliveryStatus = "BOUNCED"
	DeliveryStatusOpened    DeliveryStatus = "OPENED"
	DeliveryStatusClicked   DeliveryStatus = "CLICKED"
)

// DeliveryLog represents a delivery attempt log
type DeliveryLog struct {
	ID             uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	NotificationID uuid.UUID      `json:"notification_id" gorm:"type:uuid;not null;index"`
	Status         DeliveryStatus `json:"status" gorm:"not null;index"`
	
	// Delivery details
	Provider       string `json:"provider" gorm:"not null"` // SMTP, Twilio, etc.
	ProviderID     string `json:"provider_id"`              // External provider message ID
	Recipient      string `json:"recipient" gorm:"not null"`
	
	// Timing
	AttemptedAt    time.Time  `json:"attempted_at" gorm:"not null"`
	DeliveredAt    *time.Time `json:"delivered_at"`
	OpenedAt       *time.Time `json:"opened_at"`
	ClickedAt      *time.Time `json:"clicked_at"`
	
	// Response details
	ResponseCode   int    `json:"response_code"`
	ResponseBody   string `json:"response_body" gorm:"type:text"`
	ErrorMessage   string `json:"error_message" gorm:"type:text"`
	
	// Tracking
	UserAgent      string `json:"user_agent" gorm:"type:text"`
	IPAddress      string `json:"ip_address"`
	ClickedURL     string `json:"clicked_url" gorm:"type:text"`
	
	// Metadata
	Metadata       map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Audit fields
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	
	// Relationships
	Notification   Notification `json:"notification,omitempty" gorm:"foreignKey:NotificationID"`
}

// BeforeCreate sets the ID if not provided
func (d *DeliveryLog) BeforeCreate(tx *gorm.DB) error {
	if d.ID == uuid.Nil {
		d.ID = uuid.New()
	}
	if d.AttemptedAt.IsZero() {
		d.AttemptedAt = time.Now()
	}
	return nil
}

// MarkAsDelivered marks the delivery as delivered
func (d *DeliveryLog) MarkAsDelivered(providerID string) {
	d.Status = DeliveryStatusDelivered
	d.ProviderID = providerID
	now := time.Now()
	d.DeliveredAt = &now
}

// MarkAsFailed marks the delivery as failed
func (d *DeliveryLog) MarkAsFailed(errorMsg string, responseCode int, responseBody string) {
	d.Status = DeliveryStatusFailed
	d.ErrorMessage = errorMsg
	d.ResponseCode = responseCode
	d.ResponseBody = responseBody
}

// MarkAsOpened marks the delivery as opened
func (d *DeliveryLog) MarkAsOpened(userAgent, ipAddress string) {
	d.Status = DeliveryStatusOpened
	now := time.Now()
	d.OpenedAt = &now
	d.UserAgent = userAgent
	d.IPAddress = ipAddress
}

// MarkAsClicked marks the delivery as clicked
func (d *DeliveryLog) MarkAsClicked(clickedURL, userAgent, ipAddress string) {
	d.Status = DeliveryStatusClicked
	now := time.Now()
	d.ClickedAt = &now
	d.ClickedURL = clickedURL
	d.UserAgent = userAgent
	d.IPAddress = ipAddress
}

// CreateDeliveryLogRequest represents a request to create a delivery log
type CreateDeliveryLogRequest struct {
	NotificationID uuid.UUID              `json:"notification_id" binding:"required"`
	Status         DeliveryStatus         `json:"status" binding:"required"`
	Provider       string                 `json:"provider" binding:"required"`
	ProviderID     string                 `json:"provider_id"`
	Recipient      string                 `json:"recipient" binding:"required"`
	ResponseCode   int                    `json:"response_code"`
	ResponseBody   string                 `json:"response_body"`
	ErrorMessage   string                 `json:"error_message"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// ToDeliveryLog converts the request to a delivery log model
func (r *CreateDeliveryLogRequest) ToDeliveryLog() *DeliveryLog {
	return &DeliveryLog{
		NotificationID: r.NotificationID,
		Status:         r.Status,
		Provider:       r.Provider,
		ProviderID:     r.ProviderID,
		Recipient:      r.Recipient,
		ResponseCode:   r.ResponseCode,
		ResponseBody:   r.ResponseBody,
		ErrorMessage:   r.ErrorMessage,
		Metadata:       r.Metadata,
		AttemptedAt:    time.Now(),
	}
}

// DeliveryStats represents delivery statistics
type DeliveryStats struct {
	TotalDeliveries    int64   `json:"total_deliveries"`
	SuccessfulDeliveries int64 `json:"successful_deliveries"`
	FailedDeliveries   int64   `json:"failed_deliveries"`
	PendingDeliveries  int64   `json:"pending_deliveries"`
	OpenRate           float64 `json:"open_rate"`
	ClickRate          float64 `json:"click_rate"`
	BounceRate         float64 `json:"bounce_rate"`
	DeliveryRate       float64 `json:"delivery_rate"`
}

// CalculateStats calculates delivery statistics
func CalculateStats(logs []DeliveryLog) DeliveryStats {
	stats := DeliveryStats{}
	
	if len(logs) == 0 {
		return stats
	}
	
	stats.TotalDeliveries = int64(len(logs))
	
	var successful, failed, pending, opened, clicked, bounced int64
	
	for _, log := range logs {
		switch log.Status {
		case DeliveryStatusDelivered:
			successful++
		case DeliveryStatusFailed:
			failed++
		case DeliveryStatusPending:
			pending++
		case DeliveryStatusBounced:
			bounced++
		case DeliveryStatusOpened:
			opened++
			successful++ // Opened implies delivered
		case DeliveryStatusClicked:
			clicked++
			opened++     // Clicked implies opened
			successful++ // Clicked implies delivered
		}
	}
	
	stats.SuccessfulDeliveries = successful
	stats.FailedDeliveries = failed
	stats.PendingDeliveries = pending
	
	if stats.TotalDeliveries > 0 {
		stats.DeliveryRate = float64(successful) / float64(stats.TotalDeliveries) * 100
		stats.BounceRate = float64(bounced) / float64(stats.TotalDeliveries) * 100
		
		if successful > 0 {
			stats.OpenRate = float64(opened) / float64(successful) * 100
			stats.ClickRate = float64(clicked) / float64(successful) * 100
		}
	}
	
	return stats
}
