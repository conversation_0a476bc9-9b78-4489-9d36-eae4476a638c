#!/bin/bash

# 🧪 End-to-End Integration Test Suite
# Tests complete user workflows across all microservices

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
API_GATEWAY_URL="http://localhost:8080"
AUTH_SERVICE_URL="http://localhost:8081"
ADMIN_SERVICE_URL="http://localhost:8082"
STAFF_SERVICE_URL="http://localhost:8083"
PAYMENT_SERVICE_URL="http://localhost:8084"
NOTIFICATION_SERVICE_URL="http://localhost:8085"

# Test data
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="Admin123!@#"
STAFF_EMAIL="<EMAIL>"
STAFF_PASSWORD="Staff123!@#"
STUDENT_EMAIL="<EMAIL>"

# Global variables
ADMIN_TOKEN=""
STAFF_TOKEN=""
STUDENT_ID=""
COURSE_ID=""
PAYMENT_ID=""

# Utility functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "step")
            echo -e "\n${BLUE}🔄 $message${NC}"
            ;;
    esac
}

# Wait for service to be ready
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    print_status "info" "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url/health" > /dev/null 2>&1; then
            print_status "success" "$service_name is ready"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_status "error" "$service_name failed to start within timeout"
    return 1
}

# Test API endpoint
test_api() {
    local name=$1
    local method=$2
    local url=$3
    local headers=$4
    local data=$5
    local expected_status=${6:-200}
    
    print_status "info" "Testing: $name"
    
    local response
    local status_code
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" -H "$headers" "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -H "$headers" -d "$data" "$url")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "success" "$name - Status: $status_code"
        echo "$response_body"
        return 0
    else
        print_status "error" "$name - Expected: $expected_status, Got: $status_code"
        echo "Response: $response_body"
        return 1
    fi
}

# Extract JSON value
extract_json_value() {
    local json=$1
    local key=$2
    echo "$json" | grep -o "\"$key\":\"[^\"]*\"" | cut -d'"' -f4
}

# Main test execution
main() {
    print_status "step" "Starting End-to-End Integration Tests"
    
    # Step 1: Check all services are running
    print_status "step" "Step 1: Checking service health"
    wait_for_service "Auth Service" "$AUTH_SERVICE_URL" || exit 1
    wait_for_service "Admin Service" "$ADMIN_SERVICE_URL" || exit 1
    wait_for_service "Staff Service" "$STAFF_SERVICE_URL" || exit 1
    wait_for_service "Payment Service" "$PAYMENT_SERVICE_URL" || exit 1
    wait_for_service "Notification Service" "$NOTIFICATION_SERVICE_URL" || exit 1
    
    # Step 2: Create admin user
    print_status "step" "Step 2: Creating admin user"
    local admin_data='{
        "email": "'$ADMIN_EMAIL'",
        "username": "admin",
        "password": "'$ADMIN_PASSWORD'",
        "first_name": "Admin",
        "last_name": "User",
        "phone": "+998901234567",
        "role": "ADMIN"
    }'
    
    test_api "Create Admin User" "POST" "$AUTH_SERVICE_URL/api/v1/auth/register" "" "$admin_data" "201"
    
    # Step 3: Admin login
    print_status "step" "Step 3: Admin authentication"
    local login_data='{
        "email": "'$ADMIN_EMAIL'",
        "password": "'$ADMIN_PASSWORD'"
    }'
    
    local login_response
    login_response=$(test_api "Admin Login" "POST" "$AUTH_SERVICE_URL/api/v1/auth/login" "" "$login_data" "200")
    ADMIN_TOKEN=$(extract_json_value "$login_response" "access_token")
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_status "error" "Failed to extract admin token"
        exit 1
    fi
    
    print_status "success" "Admin token obtained"
    
    # Step 4: Create staff user via admin service
    print_status "step" "Step 4: Creating staff user"
    local staff_data='{
        "email": "'$STAFF_EMAIL'",
        "username": "staff",
        "password": "'$STAFF_PASSWORD'",
        "first_name": "Staff",
        "last_name": "User",
        "phone": "+998901234568",
        "role": "RECEPTION"
    }'
    
    test_api "Create Staff User" "POST" "$ADMIN_SERVICE_URL/api/v1/users" "Authorization: Bearer $ADMIN_TOKEN" "$staff_data" "201"
    
    # Step 5: Staff login
    print_status "step" "Step 5: Staff authentication"
    local staff_login_data='{
        "email": "'$STAFF_EMAIL'",
        "password": "'$STAFF_PASSWORD'"
    }'
    
    local staff_login_response
    staff_login_response=$(test_api "Staff Login" "POST" "$AUTH_SERVICE_URL/api/v1/auth/login" "" "$staff_login_data" "200")
    STAFF_TOKEN=$(extract_json_value "$staff_login_response" "access_token")
    
    if [ -z "$STAFF_TOKEN" ]; then
        print_status "error" "Failed to extract staff token"
        exit 1
    fi
    
    print_status "success" "Staff token obtained"
    
    # Step 6: Create course via staff service
    print_status "step" "Step 6: Creating course"
    local course_data='{
        "name": "Introduction to Programming",
        "description": "Basic programming concepts",
        "duration_weeks": 12,
        "price": 500.00,
        "max_students": 20,
        "start_date": "2024-02-01T00:00:00Z"
    }'
    
    local course_response
    course_response=$(test_api "Create Course" "POST" "$STAFF_SERVICE_URL/api/v1/courses" "Authorization: Bearer $STAFF_TOKEN" "$course_data" "201")
    COURSE_ID=$(extract_json_value "$course_response" "id")
    
    print_status "success" "Course created with ID: $COURSE_ID"
    
    # Step 7: Create student via staff service
    print_status "step" "Step 7: Creating student"
    local student_data='{
        "email": "'$STUDENT_EMAIL'",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+998901234569",
        "date_of_birth": "1995-01-01",
        "address": "123 Main St, Tashkent"
    }'
    
    local student_response
    student_response=$(test_api "Create Student" "POST" "$STAFF_SERVICE_URL/api/v1/students" "Authorization: Bearer $STAFF_TOKEN" "$student_data" "201")
    STUDENT_ID=$(extract_json_value "$student_response" "id")
    
    print_status "success" "Student created with ID: $STUDENT_ID"
    
    # Step 8: Enroll student in course
    print_status "step" "Step 8: Enrolling student in course"
    local enrollment_data='{
        "student_id": "'$STUDENT_ID'",
        "course_id": "'$COURSE_ID'",
        "enrollment_date": "2024-01-15T00:00:00Z"
    }'
    
    test_api "Enroll Student" "POST" "$STAFF_SERVICE_URL/api/v1/enrollments" "Authorization: Bearer $STAFF_TOKEN" "$enrollment_data" "201"
    
    # Step 9: Process payment
    print_status "step" "Step 9: Processing payment"
    local payment_data='{
        "student_id": "'$STUDENT_ID'",
        "course_id": "'$COURSE_ID'",
        "amount": 500.00,
        "currency": "USD",
        "payment_method": "stripe",
        "description": "Course enrollment payment"
    }'
    
    local payment_response
    payment_response=$(test_api "Process Payment" "POST" "$PAYMENT_SERVICE_URL/api/v1/payments" "Authorization: Bearer $STAFF_TOKEN" "$payment_data" "201")
    PAYMENT_ID=$(extract_json_value "$payment_response" "id")
    
    print_status "success" "Payment processed with ID: $PAYMENT_ID"
    
    # Step 10: Send notification
    print_status "step" "Step 10: Sending enrollment notification"
    local notification_data='{
        "recipient_email": "'$STUDENT_EMAIL'",
        "template_name": "enrollment_confirmation",
        "data": {
            "student_name": "John Doe",
            "course_name": "Introduction to Programming",
            "start_date": "2024-02-01"
        }
    }'
    
    test_api "Send Notification" "POST" "$NOTIFICATION_SERVICE_URL/api/v1/notifications/send" "Authorization: Bearer $STAFF_TOKEN" "$notification_data" "200"
    
    # Step 11: Verify data consistency across services
    print_status "step" "Step 11: Verifying data consistency"
    
    # Check student exists in staff service
    test_api "Get Student" "GET" "$STAFF_SERVICE_URL/api/v1/students/$STUDENT_ID" "Authorization: Bearer $STAFF_TOKEN" "" "200"
    
    # Check payment exists in payment service
    test_api "Get Payment" "GET" "$PAYMENT_SERVICE_URL/api/v1/payments/$PAYMENT_ID" "Authorization: Bearer $STAFF_TOKEN" "" "200"
    
    # Check analytics in admin service
    test_api "Get Analytics" "GET" "$ADMIN_SERVICE_URL/api/v1/analytics/dashboard" "Authorization: Bearer $ADMIN_TOKEN" "" "200"
    
    print_status "step" "Step 12: Testing complete user workflow"
    print_status "success" "All end-to-end tests passed!"
    
    echo ""
    echo "📊 Test Summary:"
    echo "   ✅ Service Health Checks: Passed"
    echo "   ✅ User Authentication: Passed"
    echo "   ✅ Cross-Service Communication: Passed"
    echo "   ✅ Data Consistency: Passed"
    echo "   ✅ Complete User Workflow: Passed"
    echo ""
    echo "🎉 End-to-End Integration Tests Completed Successfully!"
}

# Cleanup function
cleanup() {
    print_status "info" "Cleaning up test data..."
    # Add cleanup logic here if needed
}

# Trap cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
