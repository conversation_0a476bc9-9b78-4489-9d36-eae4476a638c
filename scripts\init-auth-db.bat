@echo off
echo Initializing Auth Database...
echo.

REM Check if psql is available
where psql >nul 2>nul
if %errorlevel% neq 0 (
    echo PostgreSQL client (psql) not found. Please install PostgreSQL client tools.
    pause
    exit /b 1
)

REM Run the initialization script
echo Running initialization script...
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/initialize-production-database.sql

if %errorlevel% equ 0 (
    echo.
    echo ===== SUCCESS =====
    echo Auth database initialized successfully!
    echo.
    echo Default login credentials:
    echo   Email: <EMAIL>
    echo   Password: admin123
    echo.
    echo Additional test users:
    echo   <EMAIL> / admin123 (RECEPTION)
    echo   <EMAIL> / admin123 (TEACHER)
    echo   <EMAIL> / admin123 (MANAGER)
) else (
    echo.
    echo ===== FAILED =====
    echo Failed to initialize auth database.
)

echo.
pause
