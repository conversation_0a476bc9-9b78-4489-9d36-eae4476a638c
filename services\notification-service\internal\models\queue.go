package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// QueueStatus represents the status of a queued notification
type QueueStatus string

const (
	QueueStatusPending    QueueStatus = "PENDING"
	QueueStatusProcessing QueueStatus = "PROCESSING"
	QueueStatusCompleted  QueueStatus = "COMPLETED"
	QueueStatusFailed     QueueStatus = "FAILED"
	QueueStatusCancelled  QueueStatus = "CANCELLED"
)

// NotificationQueue represents a notification in the processing queue
type NotificationQueue struct {
	ID             uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	NotificationID uuid.UUID   `json:"notification_id" gorm:"type:uuid;not null;uniqueIndex"`
	Status         QueueStatus `json:"status" gorm:"not null;default:'PENDING';index"`
	Priority       int         `json:"priority" gorm:"not null;default:5;index"` // 1=highest, 10=lowest
	
	// Scheduling
	ScheduledAt    time.Time  `json:"scheduled_at" gorm:"not null;index"`
	ProcessedAt    *time.Time `json:"processed_at"`
	CompletedAt    *time.Time `json:"completed_at"`
	
	// Retry information
	RetryCount     int        `json:"retry_count" gorm:"default:0"`
	MaxRetries     int        `json:"max_retries" gorm:"default:3"`
	NextRetryAt    *time.Time `json:"next_retry_at"`
	LastError      string     `json:"last_error" gorm:"type:text"`
	
	// Processing information
	ProcessorID    string     `json:"processor_id"` // ID of the worker processing this
	LockedAt       *time.Time `json:"locked_at"`
	LockExpiry     *time.Time `json:"lock_expiry"`
	
	// Metadata
	Metadata       map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Audit fields
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	
	// Relationships
	Notification   Notification `json:"notification,omitempty" gorm:"foreignKey:NotificationID"`
}

// BeforeCreate sets the ID if not provided
func (q *NotificationQueue) BeforeCreate(tx *gorm.DB) error {
	if q.ID == uuid.Nil {
		q.ID = uuid.New()
	}
	if q.ScheduledAt.IsZero() {
		q.ScheduledAt = time.Now()
	}
	if q.Priority == 0 {
		q.Priority = 5 // Default priority
	}
	return nil
}

// IsReadyForProcessing checks if the queue item is ready for processing
func (q *NotificationQueue) IsReadyForProcessing() bool {
	now := time.Now()
	
	// Check if it's time to process
	if q.ScheduledAt.After(now) {
		return false
	}
	
	// Check if it's not already being processed
	if q.Status == QueueStatusProcessing {
		// Check if lock has expired
		if q.LockExpiry != nil && q.LockExpiry.Before(now) {
			return true // Lock expired, can be reprocessed
		}
		return false
	}
	
	// Check if it's in a processable state
	return q.Status == QueueStatusPending || (q.Status == QueueStatusFailed && q.CanRetry())
}

// CanRetry checks if the queue item can be retried
func (q *NotificationQueue) CanRetry() bool {
	return q.RetryCount < q.MaxRetries
}

// Lock locks the queue item for processing
func (q *NotificationQueue) Lock(processorID string, lockDuration time.Duration) {
	now := time.Now()
	q.Status = QueueStatusProcessing
	q.ProcessorID = processorID
	q.LockedAt = &now
	lockExpiry := now.Add(lockDuration)
	q.LockExpiry = &lockExpiry
	q.ProcessedAt = &now
}

// MarkAsCompleted marks the queue item as completed
func (q *NotificationQueue) MarkAsCompleted() {
	q.Status = QueueStatusCompleted
	now := time.Now()
	q.CompletedAt = &now
	q.ProcessorID = ""
	q.LockedAt = nil
	q.LockExpiry = nil
}

// MarkAsFailed marks the queue item as failed
func (q *NotificationQueue) MarkAsFailed(errorMsg string) {
	q.Status = QueueStatusFailed
	q.LastError = errorMsg
	q.RetryCount++
	q.ProcessorID = ""
	q.LockedAt = nil
	q.LockExpiry = nil
	
	if q.CanRetry() {
		// Calculate next retry time with exponential backoff
		retryDelay := time.Duration(q.RetryCount*q.RetryCount) * time.Minute
		nextRetry := time.Now().Add(retryDelay)
		q.NextRetryAt = &nextRetry
		q.Status = QueueStatusPending // Reset to pending for retry
	}
}

// MarkAsCancelled marks the queue item as cancelled
func (q *NotificationQueue) MarkAsCancelled() {
	q.Status = QueueStatusCancelled
	q.ProcessorID = ""
	q.LockedAt = nil
	q.LockExpiry = nil
}

// GetPriorityFromNotification determines priority based on notification properties
func GetPriorityFromNotification(notification *Notification) int {
	switch notification.Priority {
	case NotificationPriorityUrgent:
		return 1
	case NotificationPriorityHigh:
		return 2
	case NotificationPriorityNormal:
		return 5
	case NotificationPriorityLow:
		return 8
	default:
		return 5
	}
}

// CreateQueueItemRequest represents a request to create a queue item
type CreateQueueItemRequest struct {
	NotificationID uuid.UUID              `json:"notification_id" binding:"required"`
	Priority       int                    `json:"priority"`
	ScheduledAt    time.Time              `json:"scheduled_at"`
	MaxRetries     int                    `json:"max_retries"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// ToQueueItem converts the request to a queue item model
func (r *CreateQueueItemRequest) ToQueueItem() *NotificationQueue {
	queueItem := &NotificationQueue{
		NotificationID: r.NotificationID,
		Priority:       r.Priority,
		ScheduledAt:    r.ScheduledAt,
		MaxRetries:     r.MaxRetries,
		Metadata:       r.Metadata,
		Status:         QueueStatusPending,
	}
	
	if queueItem.Priority == 0 {
		queueItem.Priority = 5
	}
	
	if queueItem.ScheduledAt.IsZero() {
		queueItem.ScheduledAt = time.Now()
	}
	
	if queueItem.MaxRetries == 0 {
		queueItem.MaxRetries = 3
	}
	
	return queueItem
}

// QueueStats represents queue statistics
type QueueStats struct {
	TotalItems      int64 `json:"total_items"`
	PendingItems    int64 `json:"pending_items"`
	ProcessingItems int64 `json:"processing_items"`
	CompletedItems  int64 `json:"completed_items"`
	FailedItems     int64 `json:"failed_items"`
	CancelledItems  int64 `json:"cancelled_items"`
	AvgProcessingTime float64 `json:"avg_processing_time_seconds"`
}

// CalculateQueueStats calculates queue statistics
func CalculateQueueStats(items []NotificationQueue) QueueStats {
	stats := QueueStats{}
	
	if len(items) == 0 {
		return stats
	}
	
	stats.TotalItems = int64(len(items))
	
	var totalProcessingTime time.Duration
	var processedCount int64
	
	for _, item := range items {
		switch item.Status {
		case QueueStatusPending:
			stats.PendingItems++
		case QueueStatusProcessing:
			stats.ProcessingItems++
		case QueueStatusCompleted:
			stats.CompletedItems++
			if item.ProcessedAt != nil && item.CompletedAt != nil {
				processingTime := item.CompletedAt.Sub(*item.ProcessedAt)
				totalProcessingTime += processingTime
				processedCount++
			}
		case QueueStatusFailed:
			stats.FailedItems++
		case QueueStatusCancelled:
			stats.CancelledItems++
		}
	}
	
	if processedCount > 0 {
		stats.AvgProcessingTime = totalProcessingTime.Seconds() / float64(processedCount)
	}
	
	return stats
}
