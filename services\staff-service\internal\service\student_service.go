package service

import (
	"fmt"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/repository"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
)

// StudentService handles student business logic
type StudentService interface {
	GetAll(req *models.StudentListRequest) (*models.StudentListResponse, error)
	GetByID(id uuid.UUID) (*models.StudentResponse, error)
	GetByStudentID(studentID string) (*models.StudentResponse, error)
	Create(req *models.StudentCreateRequest, createdBy uuid.UUID) (*models.StudentResponse, error)
	Update(id uuid.UUID, req *models.StudentUpdateRequest) (*models.StudentResponse, error)
	Delete(id uuid.UUID) error
	GetStats() (*models.StudentStats, error)
	GetProgress(studentID uuid.UUID) (*models.StudentProgress, error)
	GetByCourse(courseID uuid.UUID) ([]*models.StudentResponse, error)
	UpdateGrades(studentID uuid.UUID) error
	GetActiveStudents() ([]*models.StudentResponse, error)
	SuspendStudent(id uuid.UUID, reason string, suspendedBy uuid.UUID) (*models.StudentResponse, error)
	ReactivateStudent(id uuid.UUID, reactivatedBy uuid.UUID) (*models.StudentResponse, error)
	GraduateStudent(id uuid.UUID, graduatedBy uuid.UUID) (*models.StudentResponse, error)
}

type studentService struct {
	studentRepo    repository.StudentRepository
	enrollmentRepo repository.EnrollmentRepository
	gradeRepo      repository.GradeRepository
	attendanceRepo repository.AttendanceRepository
}

// NewStudentService creates a new student service
func NewStudentService(
	studentRepo repository.StudentRepository,
	enrollmentRepo repository.EnrollmentRepository,
	gradeRepo repository.GradeRepository,
	attendanceRepo repository.AttendanceRepository,
) StudentService {
	return &studentService{
		studentRepo:    studentRepo,
		enrollmentRepo: enrollmentRepo,
		gradeRepo:      gradeRepo,
		attendanceRepo: attendanceRepo,
	}
}

// GetAll retrieves all students with pagination and filtering
func (s *studentService) GetAll(req *models.StudentListRequest) (*models.StudentListResponse, error) {
	students, total, err := s.studentRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get students: %w", err)
	}

	studentResponses := make([]*models.StudentResponse, len(students))
	for i, student := range students {
		studentResponses[i] = student.ToResponse()
	}

	pagination := &sharedModels.PaginationResponse{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Total:      total,
		TotalPages: int((total + int64(req.PageSize) - 1) / int64(req.PageSize)),
		HasNext:    req.Page < int((total+int64(req.PageSize)-1)/int64(req.PageSize)),
		HasPrev:    req.Page > 1,
	}

	return &models.StudentListResponse{
		Students:   studentResponses,
		Pagination: pagination,
	}, nil
}

// GetByID retrieves a student by ID
func (s *studentService) GetByID(id uuid.UUID) (*models.StudentResponse, error) {
	student, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	return student.ToResponse(), nil
}

// GetByStudentID retrieves a student by student ID
func (s *studentService) GetByStudentID(studentID string) (*models.StudentResponse, error) {
	student, err := s.studentRepo.GetByStudentID(studentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	return student.ToResponse(), nil
}

// Create creates a new student
func (s *studentService) Create(req *models.StudentCreateRequest, createdBy uuid.UUID) (*models.StudentResponse, error) {
	// Check if email already exists
	if existingStudent, err := s.studentRepo.GetByEmail(req.Email); err == nil && existingStudent != nil {
		return nil, fmt.Errorf("student with email %s already exists", req.Email)
	}

	// Set default values
	now := time.Now()

	student := &models.Student{
		FirstName:             req.FirstName,
		LastName:              req.LastName,
		Email:                 req.Email,
		Phone:                 req.Phone,
		DateOfBirth:           req.DateOfBirth,
		Address:               req.Address,
		City:                  req.City,
		Country:               req.Country,
		Status:                models.StudentStatusActive, // Default to active
		EnrollmentDate:        now,                        // Set to current time
		EmergencyContactName:  req.EmergencyContactName,
		EmergencyContactPhone: req.EmergencyContactPhone,
		EmergencyContactEmail: req.EmergencyContactEmail,
		Notes:                 req.Notes,
		ConvertedFromID:       req.ConvertedFromID,
		CreatedByUserID:       createdBy,
	}

	if err := s.studentRepo.Create(student); err != nil {
		return nil, fmt.Errorf("failed to create student: %w", err)
	}

	// Reload with associations
	createdStudent, err := s.studentRepo.GetByID(student.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created student: %w", err)
	}

	return createdStudent.ToResponse(), nil
}

// Update updates a student
func (s *studentService) Update(id uuid.UUID, req *models.StudentUpdateRequest) (*models.StudentResponse, error) {
	// Check if student exists
	existingStudent, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	// Validate status if provided
	if req.Status != nil && !req.Status.IsValid() {
		return nil, fmt.Errorf("invalid student status: %s", *req.Status)
	}

	// Check email uniqueness if email is being updated
	if req.Email != nil && *req.Email != existingStudent.Email {
		if existingEmailStudent, err := s.studentRepo.GetByEmail(*req.Email); err == nil && existingEmailStudent != nil {
			return nil, fmt.Errorf("student with email %s already exists", *req.Email)
		}
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.FirstName != nil {
		updates["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		updates["last_name"] = *req.LastName
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.DateOfBirth != nil {
		updates["date_of_birth"] = *req.DateOfBirth
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.City != nil {
		updates["city"] = *req.City
	}
	if req.Country != nil {
		updates["country"] = *req.Country
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.EmergencyContactName != nil {
		updates["emergency_contact_name"] = *req.EmergencyContactName
	}
	if req.EmergencyContactPhone != nil {
		updates["emergency_contact_phone"] = *req.EmergencyContactPhone
	}
	if req.EmergencyContactEmail != nil {
		updates["emergency_contact_email"] = *req.EmergencyContactEmail
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}

	updates["updated_at"] = time.Now()

	if err := s.studentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update student: %w", err)
	}

	// Get updated student
	updatedStudent, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated student: %w", err)
	}

	return updatedStudent.ToResponse(), nil
}

// Delete deletes a student
func (s *studentService) Delete(id uuid.UUID) error {
	// Check if student exists
	if _, err := s.studentRepo.GetByID(id); err != nil {
		return fmt.Errorf("failed to get student: %w", err)
	}

	// Check if student has active enrollments
	enrollments, err := s.enrollmentRepo.GetByStudent(id)
	if err != nil {
		return fmt.Errorf("failed to check student enrollments: %w", err)
	}

	for _, enrollment := range enrollments {
		if enrollment.Status == models.EnrollmentStatusActive {
			return fmt.Errorf("cannot delete student with active enrollments")
		}
	}

	if err := s.studentRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete student: %w", err)
	}

	return nil
}

// GetStats retrieves student statistics
func (s *studentService) GetStats() (*models.StudentStats, error) {
	stats, err := s.studentRepo.GetStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get student stats: %w", err)
	}

	return stats, nil
}

// GetProgress retrieves student academic progress
func (s *studentService) GetProgress(studentID uuid.UUID) (*models.StudentProgress, error) {
	progress, err := s.studentRepo.GetProgress(studentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get student progress: %w", err)
	}

	return progress, nil
}

// GetByCourse retrieves students enrolled in a specific course
func (s *studentService) GetByCourse(courseID uuid.UUID) ([]*models.StudentResponse, error) {
	students, err := s.studentRepo.GetByCourse(courseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get students by course: %w", err)
	}

	responses := make([]*models.StudentResponse, len(students))
	for i, student := range students {
		responses[i] = student.ToResponse()
	}

	return responses, nil
}

// UpdateGrades recalculates and updates student's overall grade and attendance
func (s *studentService) UpdateGrades(studentID uuid.UUID) error {
	if err := s.studentRepo.UpdateGrades(studentID); err != nil {
		return fmt.Errorf("failed to update student grades: %w", err)
	}

	return nil
}

// GetActiveStudents retrieves all active students
func (s *studentService) GetActiveStudents() ([]*models.StudentResponse, error) {
	students, err := s.studentRepo.GetActiveStudents()
	if err != nil {
		return nil, fmt.Errorf("failed to get active students: %w", err)
	}

	responses := make([]*models.StudentResponse, len(students))
	for i, student := range students {
		responses[i] = student.ToResponse()
	}

	return responses, nil
}

// SuspendStudent suspends a student
func (s *studentService) SuspendStudent(id uuid.UUID, reason string, suspendedBy uuid.UUID) (*models.StudentResponse, error) {
	// Check if student exists
	student, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	if student.Status == models.StudentStatusSuspended {
		return nil, fmt.Errorf("student is already suspended")
	}

	updates := map[string]interface{}{
		"status":     models.StudentStatusSuspended,
		"updated_at": time.Now(),
	}

	if reason != "" {
		existingNotes := student.Notes
		suspensionNote := fmt.Sprintf("\n\nSuspended on %s: %s", time.Now().Format("2006-01-02"), reason)
		if existingNotes != "" {
			updates["notes"] = existingNotes + suspensionNote
		} else {
			updates["notes"] = suspensionNote
		}
	}

	if err := s.studentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to suspend student: %w", err)
	}

	// Get updated student
	updatedStudent, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated student: %w", err)
	}

	return updatedStudent.ToResponse(), nil
}

// ReactivateStudent reactivates a suspended student
func (s *studentService) ReactivateStudent(id uuid.UUID, reactivatedBy uuid.UUID) (*models.StudentResponse, error) {
	// Check if student exists
	student, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	if student.Status != models.StudentStatusSuspended {
		return nil, fmt.Errorf("student is not suspended")
	}

	updates := map[string]interface{}{
		"status":     models.StudentStatusActive,
		"updated_at": time.Now(),
	}

	existingNotes := student.Notes
	reactivationNote := fmt.Sprintf("\n\nReactivated on %s", time.Now().Format("2006-01-02"))
	if existingNotes != "" {
		updates["notes"] = existingNotes + reactivationNote
	} else {
		updates["notes"] = reactivationNote
	}

	if err := s.studentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to reactivate student: %w", err)
	}

	// Get updated student
	updatedStudent, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated student: %w", err)
	}

	return updatedStudent.ToResponse(), nil
}

// GraduateStudent marks a student as graduated
func (s *studentService) GraduateStudent(id uuid.UUID, graduatedBy uuid.UUID) (*models.StudentResponse, error) {
	// Check if student exists
	student, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	if student.Status == models.StudentStatusGraduated {
		return nil, fmt.Errorf("student is already graduated")
	}

	if student.Status != models.StudentStatusActive {
		return nil, fmt.Errorf("only active students can be graduated")
	}

	// Check if student has completed all required courses
	enrollments, err := s.enrollmentRepo.GetByStudent(id)
	if err != nil {
		return nil, fmt.Errorf("failed to check student enrollments: %w", err)
	}

	hasActiveEnrollments := false
	for _, enrollment := range enrollments {
		if enrollment.Status == models.EnrollmentStatusActive {
			hasActiveEnrollments = true
			break
		}
	}

	if hasActiveEnrollments {
		return nil, fmt.Errorf("student has active enrollments and cannot be graduated")
	}

	updates := map[string]interface{}{
		"status":         models.StudentStatusGraduated,
		"graduation_date": time.Now(),
		"updated_at":     time.Now(),
	}

	existingNotes := student.Notes
	graduationNote := fmt.Sprintf("\n\nGraduated on %s", time.Now().Format("2006-01-02"))
	if existingNotes != "" {
		updates["notes"] = existingNotes + graduationNote
	} else {
		updates["notes"] = graduationNote
	}

	if err := s.studentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to graduate student: %w", err)
	}

	// Get updated student
	updatedStudent, err := s.studentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated student: %w", err)
	}

	return updatedStudent.ToResponse(), nil
}
