#!/bin/bash

# 🚀 Performance and Load Testing Script
# Tests system performance under various load conditions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RESULTS_DIR="$SCRIPT_DIR/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Service endpoints
AUTH_URL="http://localhost:8081"
ADMIN_URL="http://localhost:8082"
STAFF_URL="http://localhost:8083"
PAYMENT_URL="http://localhost:8084"
NOTIFICATION_URL="http://localhost:8085"
GATEWAY_URL="http://localhost:8080"

# Test parameters
CONCURRENT_USERS=(1 5 10 20 50)
TEST_DURATION=60  # seconds
RAMP_UP_TIME=10   # seconds

# Utility functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "step")
            echo -e "\n${BLUE}🔄 $message${NC}"
            ;;
    esac
}

# Setup test environment
setup_test_environment() {
    print_status "step" "Setting up performance test environment"
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Check if services are running
    local services_ready=true
    local services=("$AUTH_URL" "$ADMIN_URL" "$STAFF_URL" "$PAYMENT_URL" "$NOTIFICATION_URL")
    
    for service_url in "${services[@]}"; do
        if curl -s -f "$service_url/health" > /dev/null 2>&1; then
            print_status "success" "Service at $service_url is ready"
        else
            print_status "error" "Service at $service_url is not responding"
            services_ready=false
        fi
    done
    
    if [ "$services_ready" = false ]; then
        print_status "error" "Some services are not ready. Please start all services first."
        exit 1
    fi
    
    # Get authentication token for authenticated tests
    get_auth_token
}

# Get authentication token
get_auth_token() {
    print_status "info" "Obtaining authentication token"
    
    # Create test user if not exists
    local user_data='{
        "email": "<EMAIL>",
        "username": "loadtest",
        "password": "LoadTest123!@#",
        "first_name": "Load",
        "last_name": "Test",
        "phone": "+998901234567",
        "role": "ADMIN"
    }'
    
    # Try to register (ignore if user already exists)
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$user_data" \
        "$AUTH_URL/api/v1/auth/register" > /dev/null 2>&1 || true
    
    # Login to get token
    local login_data='{
        "email": "<EMAIL>",
        "password": "LoadTest123!@#"
    }'
    
    local response
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$AUTH_URL/api/v1/auth/login")
    
    AUTH_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$AUTH_TOKEN" ]; then
        print_status "success" "Authentication token obtained"
    else
        print_status "error" "Failed to obtain authentication token"
        exit 1
    fi
}

# Run load test for a specific endpoint
run_load_test() {
    local test_name=$1
    local url=$2
    local method=$3
    local headers=$4
    local data=$5
    local concurrent_users=$6
    local duration=$7
    
    print_status "step" "Running load test: $test_name ($concurrent_users users, ${duration}s)"
    
    local result_file="$RESULTS_DIR/${test_name}_${concurrent_users}users_${TIMESTAMP}.txt"
    local temp_dir="/tmp/loadtest_$$"
    mkdir -p "$temp_dir"
    
    # Start concurrent requests
    for ((i=1; i<=concurrent_users; i++)); do
        (
            local start_time=$(date +%s)
            local end_time=$((start_time + duration))
            local request_count=0
            local success_count=0
            
            while [ $(date +%s) -lt $end_time ]; do
                local response_time_start=$(date +%s%3N)
                
                if [ "$method" = "GET" ]; then
                    local status_code=$(curl -s -o /dev/null -w "%{http_code}" -H "$headers" "$url")
                else
                    local status_code=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -H "$headers" -d "$data" "$url")
                fi
                
                local response_time_end=$(date +%s%3N)
                local response_time=$((response_time_end - response_time_start))
                
                ((request_count++))
                if [ "$status_code" = "200" ] || [ "$status_code" = "201" ]; then
                    ((success_count++))
                fi
                
                echo "$i,$request_count,$status_code,$response_time" >> "$temp_dir/worker_$i.csv"
                
                # Small delay to prevent overwhelming
                sleep 0.1
            done
            
            echo "Worker $i: $request_count requests, $success_count successful"
        ) &
    done
    
    # Wait for all workers to complete
    wait
    
    # Aggregate results
    local total_requests=0
    local total_success=0
    local total_response_time=0
    local min_response_time=999999
    local max_response_time=0
    
    for ((i=1; i<=concurrent_users; i++)); do
        if [ -f "$temp_dir/worker_$i.csv" ]; then
            while IFS=',' read -r worker_id request_id status_code response_time; do
                ((total_requests++))
                if [ "$status_code" = "200" ] || [ "$status_code" = "201" ]; then
                    ((total_success++))
                fi
                
                total_response_time=$((total_response_time + response_time))
                
                if [ "$response_time" -lt "$min_response_time" ]; then
                    min_response_time=$response_time
                fi
                
                if [ "$response_time" -gt "$max_response_time" ]; then
                    max_response_time=$response_time
                fi
            done < "$temp_dir/worker_$i.csv"
        fi
    done
    
    # Calculate metrics
    local success_rate=0
    local avg_response_time=0
    local requests_per_second=0
    
    if [ $total_requests -gt 0 ]; then
        success_rate=$((total_success * 100 / total_requests))
        avg_response_time=$((total_response_time / total_requests))
        requests_per_second=$((total_requests / duration))
    fi
    
    # Save results
    cat > "$result_file" << EOF
Load Test Results: $test_name
Timestamp: $(date)
Test Duration: ${duration}s
Concurrent Users: $concurrent_users

Performance Metrics:
- Total Requests: $total_requests
- Successful Requests: $total_success
- Success Rate: $success_rate%
- Requests per Second: $requests_per_second
- Average Response Time: ${avg_response_time}ms
- Min Response Time: ${min_response_time}ms
- Max Response Time: ${max_response_time}ms
EOF
    
    # Display results
    print_status "info" "Test Results:"
    echo "   Total Requests: $total_requests"
    echo "   Success Rate: $success_rate%"
    echo "   Requests/sec: $requests_per_second"
    echo "   Avg Response Time: ${avg_response_time}ms"
    
    # Cleanup
    rm -rf "$temp_dir"
    
    # Return success if success rate is above 95%
    if [ $success_rate -ge 95 ]; then
        return 0
    else
        return 1
    fi
}

# Test health endpoints
test_health_endpoints() {
    print_status "step" "Testing health endpoints performance"
    
    local services=("auth-service:$AUTH_URL" "admin-service:$ADMIN_URL" "staff-service:$STAFF_URL" "payment-service:$PAYMENT_URL" "notification-service:$NOTIFICATION_URL")
    
    for service_info in "${services[@]}"; do
        local service_name=$(echo "$service_info" | cut -d':' -f1)
        local service_url=$(echo "$service_info" | cut -d':' -f2)
        
        for users in "${CONCURRENT_USERS[@]}"; do
            run_load_test "${service_name}_health" "$service_url/health" "GET" "" "" "$users" "$TEST_DURATION"
        done
    done
}

# Test authenticated endpoints
test_authenticated_endpoints() {
    print_status "step" "Testing authenticated endpoints performance"
    
    local auth_header="Authorization: Bearer $AUTH_TOKEN"
    
    # Test admin endpoints
    for users in "${CONCURRENT_USERS[@]}"; do
        run_load_test "admin_users_list" "$ADMIN_URL/api/v1/users" "GET" "$auth_header" "" "$users" "$TEST_DURATION"
        run_load_test "admin_analytics" "$ADMIN_URL/api/v1/analytics/dashboard" "GET" "$auth_header" "" "$users" "$TEST_DURATION"
    done
    
    # Test staff endpoints
    for users in "${CONCURRENT_USERS[@]}"; do
        run_load_test "staff_students_list" "$STAFF_URL/api/v1/students" "GET" "$auth_header" "" "$users" "$TEST_DURATION"
        run_load_test "staff_courses_list" "$STAFF_URL/api/v1/courses" "GET" "$auth_header" "" "$users" "$TEST_DURATION"
    done
}

# Test database-intensive operations
test_database_operations() {
    print_status "step" "Testing database-intensive operations"
    
    local auth_header="Authorization: Bearer $AUTH_TOKEN"
    
    # Test user creation (write operations)
    local user_data='{
        "email": "test{{RANDOM}}@test.com",
        "username": "test{{RANDOM}}",
        "password": "Test123!@#",
        "first_name": "Test",
        "last_name": "User",
        "phone": "+998901234567",
        "role": "RECEPTION"
    }'
    
    for users in 1 5 10; do  # Limit concurrent writes
        run_load_test "user_creation" "$ADMIN_URL/api/v1/users" "POST" "$auth_header" "$user_data" "$users" 30
    done
}

# Generate performance report
generate_performance_report() {
    print_status "step" "Generating performance report"
    
    local report_file="$RESULTS_DIR/performance_report_${TIMESTAMP}.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Go Docker Platform - Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Go Docker Platform - Performance Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Test Duration: ${TEST_DURATION} seconds per test</p>
    </div>
    
    <div class="section">
        <h2>Test Configuration</h2>
        <ul>
            <li>Concurrent Users Tested: ${CONCURRENT_USERS[*]}</li>
            <li>Test Duration: ${TEST_DURATION} seconds</li>
            <li>Ramp-up Time: ${RAMP_UP_TIME} seconds</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Performance Metrics</h2>
        <p>Detailed results are available in individual test files in the results directory.</p>
        
        <h3>Success Criteria</h3>
        <ul>
            <li class="success">✅ Response Time: &lt; 200ms for 95% of requests</li>
            <li class="success">✅ Success Rate: &gt; 95%</li>
            <li class="success">✅ Throughput: &gt; 100 requests/second</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Test Files</h2>
        <ul>
EOF

    # List all result files
    for file in "$RESULTS_DIR"/*.txt; do
        if [ -f "$file" ]; then
            local filename=$(basename "$file")
            echo "            <li><a href=\"$filename\">$filename</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
</body>
</html>
EOF
    
    print_status "success" "Performance report generated: $report_file"
}

# Main execution
main() {
    print_status "step" "Starting Performance and Load Testing"
    
    # Setup
    setup_test_environment
    
    # Run performance tests
    test_health_endpoints
    test_authenticated_endpoints
    test_database_operations
    
    # Generate report
    generate_performance_report
    
    print_status "success" "Performance testing completed!"
    echo "📁 Results available in: $RESULTS_DIR"
}

# Run main function
main "$@"
