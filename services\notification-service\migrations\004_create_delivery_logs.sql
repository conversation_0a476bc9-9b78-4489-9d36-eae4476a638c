-- Create delivery_logs table
CREATE TABLE IF NOT EXISTS delivery_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL,
    
    -- Delivery details
    provider VARCHAR(100) NOT NULL,
    provider_id VARCHAR(255),
    recipient VARCHAR(255) NOT NULL,
    
    -- Timing
    attempted_at TIMESTAMP WITH TIME ZONE NOT NULL,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    
    -- Response details
    response_code INTEGER,
    response_body TEXT,
    error_message TEXT,
    
    -- Tracking
    user_agent TEXT,
    ip_address VARCHAR(45),
    clicked_url TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX IF NOT EXISTS idx_delivery_logs_notification_id ON delivery_logs(notification_id);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_status ON delivery_logs(status);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_provider ON delivery_logs(provider);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_recipient ON delivery_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_attempted_at ON delivery_logs(attempted_at);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_delivered_at ON delivery_logs(delivered_at);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_deleted_at ON delivery_logs(deleted_at);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_delivery_logs_updated_at 
    BEFORE UPDATE ON delivery_logs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add foreign key constraint
ALTER TABLE delivery_logs 
ADD CONSTRAINT fk_delivery_logs_notification_id 
FOREIGN KEY (notification_id) REFERENCES notifications(id) 
ON DELETE CASCADE;
