# Admin Service

The Admin Service provides administrative functionality for the CRM microservices platform. It handles user management, analytics, audit logging, and financial reporting for users with ADMIN and CASHIER roles.

## Features

### User Management
- Complete CRUD operations for all user types
- Role-based access control
- User search and filtering
- User activity tracking
- Bulk user operations

### Analytics & Reporting
- Dashboard analytics with KPIs
- Financial reporting and analytics
- User analytics and statistics
- System metrics and performance data
- Custom report generation

### Audit Logging
- Comprehensive audit trail for all system activities
- Audit log search and filtering
- Audit statistics and reporting
- Configurable retention policies
- Real-time activity monitoring

### System Administration
- System configuration management
- Health monitoring and metrics
- Performance optimization tools
- Data export and import capabilities

## API Endpoints

### User Management
- `GET /api/v1/users` - List users with pagination and filtering
- `GET /api/v1/users/:id` - Get user by ID
- `POST /api/v1/users` - Create new user
- `PUT /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user

### Analytics
- `GET /api/v1/analytics/dashboard` - Get dashboard analytics
- `GET /api/v1/analytics/financial` - Get financial reports
- `GET /api/v1/analytics/users` - Get user analytics
- `GET /api/v1/analytics/system` - Get system metrics

### Audit Logs
- `GET /api/v1/audit-logs` - List audit logs with filtering
- `GET /api/v1/audit-logs/:id` - Get audit log by ID
- `GET /api/v1/audit-logs/stats` - Get audit statistics
- `GET /api/v1/audit-logs/recent` - Get recent activity

### Health Checks
- `GET /health` - Service health check
- `GET /ready` - Readiness check
- `GET /live` - Liveness check

## Configuration

The service uses environment variables for configuration:

```env
PORT=8082
ENVIRONMENT=development
DATABASE_URL=postgresql://user:password@host:port/dbname
JWT_SECRET=your-jwt-secret
REDIS_URL=redis://localhost:6379
AUTH_SERVICE_URL=http://localhost:8081
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100
```

## Authentication & Authorization

- All API endpoints (except health checks) require JWT authentication
- Only users with ADMIN or CASHIER roles can access this service
- Role-based permissions are enforced at the middleware level
- Audit logging tracks all user activities

## Database Schema

The service uses the following main tables:
- `users` - User information (shared with auth service)
- `audit_logs` - Audit trail entries
- `sessions` - User sessions (shared with auth service)

## Development

### Prerequisites
- Go 1.21+
- PostgreSQL 13+
- Redis 6+

### Running Locally
```bash
# Install dependencies
go mod download

# Run the service
go run main.go
```

### Running with Docker
```bash
# Build image
docker build -t admin-service .

# Run container
docker run -p 8082:8082 admin-service
```

### Testing
```bash
# Run unit tests
go test ./...

# Run tests with coverage
go test -cover ./...
```

## Monitoring

The service provides comprehensive monitoring capabilities:

### Health Checks
- `/health` - Overall service health
- `/ready` - Service readiness
- `/live` - Service liveness

### Metrics
- Request/response metrics
- Database connection metrics
- Business metrics (user counts, activity, etc.)
- System performance metrics

### Logging
- Structured JSON logging
- Request/response logging
- Error logging with stack traces
- Audit logging for all activities

## Security

### Authentication
- JWT token validation
- Role-based access control
- Session management

### Data Protection
- Password hashing with bcrypt
- Sensitive data exclusion from logs
- SQL injection prevention with parameterized queries

### Audit Trail
- Complete audit logging for all operations
- IP address and user agent tracking
- Tamper-evident audit records

## Performance

### Optimization
- Database query optimization
- Connection pooling
- Efficient pagination
- Caching for frequently accessed data

### Scalability
- Stateless design for horizontal scaling
- Database connection pooling
- Efficient memory usage
- Configurable resource limits

## Error Handling

The service implements comprehensive error handling:
- Structured error responses
- Proper HTTP status codes
- Error logging and monitoring
- Graceful degradation

## Dependencies

### Core Dependencies
- `gin-gonic/gin` - HTTP web framework
- `gorm.io/gorm` - ORM for database operations
- `google/uuid` - UUID generation
- `golang.org/x/crypto` - Password hashing

### Database
- PostgreSQL with GORM
- Connection pooling
- Migration support

### Shared Libraries
- `shared/models` - Common data models
- `shared/middleware` - Authentication middleware
- `shared/utils` - Utility functions
- `shared/database` - Database connection utilities
