package services

import (
	"fmt"
	"log"
	"notification-service/internal/config"
	"notification-service/internal/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// QueueService handles notification queue operations
type QueueService struct {
	db     *gorm.DB
	config *config.Config
}

// NewQueueService creates a new queue service
func NewQueueService(db *gorm.DB, config *config.Config) *QueueService {
	return &QueueService{
		db:     db,
		config: config,
	}
}

// ProcessQueue processes pending notifications in the queue
func (s *QueueService) ProcessQueue(emailService *EmailService, smsService *SMSService) error {
	// Get ready notifications from queue
	var queueItems []models.NotificationQueue
	err := s.db.Preload("Notification").
		Where("status = ? AND scheduled_at <= ?", models.QueueStatusPending, time.Now()).
		Or("status = ? AND next_retry_at <= ?", models.QueueStatusFailed, time.Now()).
		Order("priority ASC, scheduled_at ASC").
		Limit(s.config.BatchSize).
		Find(&queueItems).Error
	
	if err != nil {
		return fmt.Errorf("failed to fetch queue items: %w", err)
	}
	
	log.Printf("Processing %d notifications from queue", len(queueItems))
	
	for _, item := range queueItems {
		if err := s.processQueueItem(&item, emailService, smsService); err != nil {
			log.Printf("Failed to process queue item %s: %v", item.ID, err)
		}
	}
	
	return nil
}

// processQueueItem processes a single queue item
func (s *QueueService) processQueueItem(item *models.NotificationQueue, emailService *EmailService, smsService *SMSService) error {
	// Check if item is ready for processing
	if !item.IsReadyForProcessing() {
		return nil
	}
	
	// Lock the item for processing
	processorID := uuid.New().String()
	item.Lock(processorID, 5*time.Minute) // 5 minute lock
	
	if err := s.db.Save(item).Error; err != nil {
		return fmt.Errorf("failed to lock queue item: %w", err)
	}
	
	// Process the notification
	err := s.processNotification(&item.Notification, emailService, smsService)
	
	if err != nil {
		// Mark as failed
		item.MarkAsFailed(err.Error())
		log.Printf("Notification processing failed: %v", err)
	} else {
		// Mark as completed
		item.MarkAsCompleted()
		log.Printf("Notification processed successfully: %s", item.NotificationID)
	}
	
	// Update queue item
	if err := s.db.Save(item).Error; err != nil {
		log.Printf("Failed to update queue item status: %v", err)
	}
	
	return nil
}

// processNotification processes a single notification based on its type
func (s *QueueService) processNotification(notification *models.Notification, emailService *EmailService, smsService *SMSService) error {
	switch notification.Type {
	case models.NotificationTypeEmail:
		return s.processEmailNotification(notification, emailService)
	case models.NotificationTypeSMS:
		return s.processSMSNotification(notification, smsService)
	case models.NotificationTypeInApp:
		return s.processInAppNotification(notification)
	case models.NotificationTypePush:
		return s.processPushNotification(notification)
	default:
		return fmt.Errorf("unsupported notification type: %s", notification.Type)
	}
}

// processEmailNotification processes an email notification
func (s *QueueService) processEmailNotification(notification *models.Notification, emailService *EmailService) error {
	// Create delivery log
	deliveryLog := &models.DeliveryLog{
		NotificationID: notification.ID,
		Status:         models.DeliveryStatusPending,
		Provider:       "SMTP",
		Recipient:      notification.Recipient,
		AttemptedAt:    time.Now(),
	}
	
	// Send email
	err := emailService.SendEmail(notification.Recipient, notification.Subject, notification.Message, notification.HTMLContent)
	
	if err != nil {
		deliveryLog.MarkAsFailed(err.Error(), 0, "")
		notification.MarkAsFailed(err.Error())
	} else {
		deliveryLog.MarkAsDelivered("")
		notification.MarkAsSent()
	}
	
	// Save delivery log
	if err := s.db.Create(deliveryLog).Error; err != nil {
		log.Printf("Failed to create delivery log: %v", err)
	}
	
	// Update notification
	if err := s.db.Save(notification).Error; err != nil {
		log.Printf("Failed to update notification: %v", err)
	}
	
	return err
}

// processSMSNotification processes an SMS notification
func (s *QueueService) processSMSNotification(notification *models.Notification, smsService *SMSService) error {
	// Create delivery log
	deliveryLog := &models.DeliveryLog{
		NotificationID: notification.ID,
		Status:         models.DeliveryStatusPending,
		Provider:       "Twilio",
		Recipient:      notification.Recipient,
		AttemptedAt:    time.Now(),
	}
	
	// Send SMS
	err := smsService.SendSMS(notification.Recipient, notification.Message)
	
	if err != nil {
		deliveryLog.MarkAsFailed(err.Error(), 0, "")
		notification.MarkAsFailed(err.Error())
	} else {
		deliveryLog.MarkAsDelivered("")
		notification.MarkAsSent()
	}
	
	// Save delivery log
	if err := s.db.Create(deliveryLog).Error; err != nil {
		log.Printf("Failed to create delivery log: %v", err)
	}
	
	// Update notification
	if err := s.db.Save(notification).Error; err != nil {
		log.Printf("Failed to update notification: %v", err)
	}
	
	return err
}

// processInAppNotification processes an in-app notification
func (s *QueueService) processInAppNotification(notification *models.Notification) error {
	// For in-app notifications, we just mark them as sent
	// The actual delivery happens when the user's app polls for notifications
	
	// Create delivery log
	deliveryLog := &models.DeliveryLog{
		NotificationID: notification.ID,
		Status:         models.DeliveryStatusSent,
		Provider:       "IN_APP",
		Recipient:      notification.Recipient,
		AttemptedAt:    time.Now(),
	}
	deliveryLog.MarkAsDelivered("")
	
	// Mark notification as sent
	notification.MarkAsSent()
	
	// Save delivery log
	if err := s.db.Create(deliveryLog).Error; err != nil {
		log.Printf("Failed to create delivery log: %v", err)
	}
	
	// Update notification
	if err := s.db.Save(notification).Error; err != nil {
		log.Printf("Failed to update notification: %v", err)
	}
	
	return nil
}

// processPushNotification processes a push notification
func (s *QueueService) processPushNotification(notification *models.Notification) error {
	// TODO: Implement push notification processing
	// For now, we'll just mark it as sent
	
	// Create delivery log
	deliveryLog := &models.DeliveryLog{
		NotificationID: notification.ID,
		Status:         models.DeliveryStatusSent,
		Provider:       "PUSH",
		Recipient:      notification.Recipient,
		AttemptedAt:    time.Now(),
	}
	deliveryLog.MarkAsDelivered("")
	
	// Mark notification as sent
	notification.MarkAsSent()
	
	// Save delivery log
	if err := s.db.Create(deliveryLog).Error; err != nil {
		log.Printf("Failed to create delivery log: %v", err)
	}
	
	// Update notification
	if err := s.db.Save(notification).Error; err != nil {
		log.Printf("Failed to update notification: %v", err)
	}
	
	return nil
}

// GetQueueStats returns queue statistics
func (s *QueueService) GetQueueStats() (*models.QueueStats, error) {
	var items []models.NotificationQueue
	err := s.db.Find(&items).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch queue items: %w", err)
	}
	
	stats := models.CalculateQueueStats(items)
	return &stats, nil
}

// CleanupExpiredLocks cleans up expired locks in the queue
func (s *QueueService) CleanupExpiredLocks() error {
	now := time.Now()
	err := s.db.Model(&models.NotificationQueue{}).
		Where("status = ? AND lock_expiry < ?", models.QueueStatusProcessing, now).
		Updates(map[string]interface{}{
			"status":      models.QueueStatusPending,
			"processor_id": "",
			"locked_at":   nil,
			"lock_expiry": nil,
		}).Error
	
	if err != nil {
		return fmt.Errorf("failed to cleanup expired locks: %w", err)
	}
	
	return nil
}

// RetryFailedNotifications retries failed notifications that are ready for retry
func (s *QueueService) RetryFailedNotifications() error {
	now := time.Now()
	err := s.db.Model(&models.NotificationQueue{}).
		Where("status = ? AND next_retry_at <= ? AND retry_count < max_retries", models.QueueStatusFailed, now).
		Updates(map[string]interface{}{
			"status":       models.QueueStatusPending,
			"next_retry_at": nil,
		}).Error
	
	if err != nil {
		return fmt.Errorf("failed to retry failed notifications: %w", err)
	}
	
	return nil
}

// StartQueueProcessor starts the queue processor in a goroutine
func (s *QueueService) StartQueueProcessor(emailService *EmailService, smsService *SMSService) {
	ticker := time.NewTicker(time.Duration(s.config.QueueProcessInterval) * time.Second)
	
	go func() {
		for range ticker.C {
			// Process queue
			if err := s.ProcessQueue(emailService, smsService); err != nil {
				log.Printf("Queue processing error: %v", err)
			}
			
			// Cleanup expired locks
			if err := s.CleanupExpiredLocks(); err != nil {
				log.Printf("Lock cleanup error: %v", err)
			}
			
			// Retry failed notifications
			if err := s.RetryFailedNotifications(); err != nil {
				log.Printf("Retry failed notifications error: %v", err)
			}
		}
	}()
	
	log.Printf("Queue processor started with %d second interval", s.config.QueueProcessInterval)
}
