# 🌐 API Gateway Service

The API Gateway serves as the single entry point for all client requests in the CRM microservices platform, providing routing, authentication, rate limiting, and cross-cutting concerns.

## 🚀 Features

### Core Gateway Functionality
- **Request Routing**: Intelligent routing to downstream services
- **Service Discovery**: Dynamic service registration and health checking
- **Load Balancing**: Distribute requests across healthy service instances
- **Circuit Breaker**: Fail-fast pattern for unhealthy services

### Security & Authentication
- **JWT Token Validation**: Centralized authentication using shared JWT manager
- **Role-based Routing**: Route requests based on user roles and permissions
- **CORS Handling**: Cross-origin resource sharing configuration
- **Rate Limiting**: Protect services from abuse and overload

### Observability & Monitoring
- **Request Logging**: Structured logging with correlation IDs
- **Health Checks**: Monitor gateway and downstream service health
- **Metrics Collection**: Request counts, response times, error rates
- **Distributed Tracing**: Request correlation across services

## 🏗️ Architecture

### Directory Structure
```
api-gateway/
├── main.go                    # Application entry point
├── Dockerfile                 # Container configuration
├── go.mod                     # Go module definition
├── internal/
│   ├── config/               # Configuration management
│   ├── handlers/             # HTTP request handlers
│   ├── middleware/           # Gateway middleware
│   ├── proxy/                # Service proxy logic
│   └── registry/             # Service discovery
└── README.md                 # This file
```

### Components
- **Service Registry**: Manages downstream service discovery and health
- **Proxy Engine**: Routes and forwards requests to appropriate services
- **Middleware Stack**: Authentication, rate limiting, logging, CORS
- **Health Monitor**: Continuous health checking of services

## 🔧 Configuration

### Environment Variables
```bash
# API Gateway
API_GATEWAY_PORT=8080
ENVIRONMENT=development
DEBUG=true

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Service URLs
AUTH_SERVICE_URL=http://localhost:8081
ADMIN_SERVICE_URL=http://localhost:8082
STAFF_SERVICE_URL=http://localhost:8083
PAYMENT_SERVICE_URL=http://localhost:8084
NOTIFICATION_SERVICE_URL=http://localhost:8085

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=1m

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

## 📡 API Routes

### Gateway Health Endpoints
```
GET    /health                         # Gateway health check
GET    /ready                          # Readiness probe
GET    /live                           # Liveness probe
GET    /version                        # Version information
GET    /metrics                        # Gateway metrics
GET    /services                       # Registered services info
```

### Proxied Service Routes
```
# Authentication Service (Public)
POST   /api/v1/auth/login              # User login
POST   /api/v1/auth/register           # User registration
POST   /api/v1/auth/refresh            # Token refresh
GET    /api/v1/auth/validate           # Token validation

# User Management (Authenticated)
GET    /api/v1/users/profile           # Get user profile
PUT    /api/v1/users/profile           # Update profile

# Admin Service (Admin/Cashier only)
GET    /api/v1/admin/*                 # Admin endpoints
POST   /api/v1/admin/*                 # Admin operations

# Staff Service (Staff roles only)
GET    /api/v1/staff/*                 # Staff endpoints
POST   /api/v1/staff/*                 # Staff operations

# Payment Service (Authenticated)
GET    /api/v1/payment/*               # Payment endpoints
POST   /api/v1/payment/*               # Payment operations

# Notification Service (Authenticated)
GET    /api/v1/notification/*          # Notification endpoints
POST   /api/v1/notification/*          # Notification operations
```

### Proxy Utility Endpoints
```
GET    /proxy/health/{service}         # Service health check
GET    /proxy/version/{service}        # Service version
GET    /proxy/metrics/{service}        # Service metrics
GET    /proxy/stats                    # Proxy statistics
```

## 🛡️ Security Features

### Authentication Flow
1. **Token Extraction**: Extract JWT from Authorization header
2. **Token Validation**: Validate token signature and expiration
3. **User Context**: Set user information in request context
4. **Header Forwarding**: Forward user info to downstream services

### Role-based Access Control
- **Public Routes**: `/auth/*` endpoints (no authentication required)
- **Authenticated Routes**: Require valid JWT token
- **Admin Routes**: Require ADMIN or CASHIER role
- **Staff Routes**: Require staff-level roles (RECEPTION, TEACHER, MANAGER, etc.)

### Rate Limiting
- **IP-based Limiting**: Limit requests per IP address
- **User-based Limiting**: Limit requests per authenticated user
- **Service-based Limiting**: Different limits for different services

## 🚀 Getting Started

### Prerequisites
- Go 1.21+
- Running Auth Service (required)
- Other services (optional, will show as unhealthy)

### Local Development
1. **Start Auth Service**:
   ```bash
   cd ../auth-service
   go run main.go
   ```

2. **Start API Gateway**:
   ```bash
   cd services/api-gateway
   go mod download
   go run main.go
   ```

3. **Test Gateway**:
   ```bash
   curl http://localhost:8080/health
   ```

### Docker Development
```bash
# Build and run
docker-compose up api-gateway

# Check health
curl http://localhost:8080/health
```

## 🧪 Testing

### Health Checks
```bash
# Gateway health
curl http://localhost:8080/health

# Service readiness
curl http://localhost:8080/ready

# Service discovery
curl http://localhost:8080/services
```

### Authentication Flow
```bash
# Register user (via gateway)
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","username":"testuser","password":"TestPassword123!","first_name":"Test","last_name":"User","phone":"+998901234567","role":"RECEPTION"}'

# Login (via gateway)
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123!"}'

# Access protected endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/v1/users/profile
```

### Rate Limiting Test
```bash
# Test rate limiting
for i in {1..110}; do
  curl -s -o /dev/null -w "%{http_code}\n" http://localhost:8080/health
done
```

## 📊 Monitoring

### Health Monitoring
- **Gateway Health**: Overall gateway status
- **Service Health**: Downstream service availability
- **Registry Stats**: Service discovery statistics

### Request Metrics
- **Request Count**: Total requests per endpoint
- **Response Times**: Average response times
- **Error Rates**: HTTP error status codes
- **Rate Limit Hits**: Rate limiting statistics

### Logging
- **Structured Logs**: JSON-formatted request/response logs
- **Correlation IDs**: Track requests across services
- **User Context**: Include user information in logs
- **Error Tracking**: Detailed error logging

## 🔄 Service Discovery

### Service Registration
Services are registered with:
- **Name**: Unique service identifier
- **URL**: Service base URL
- **Health Endpoint**: Health check URL
- **Priority**: Service priority for load balancing
- **Timeout**: Request timeout configuration

### Health Checking
- **Periodic Checks**: Every 30 seconds
- **Automatic Recovery**: Services marked healthy when recovered
- **Circuit Breaking**: Stop routing to unhealthy services

## 🚀 Deployment

### Docker
```bash
docker build -t api-gateway .
docker run -p 8080:8080 api-gateway
```

### Environment Configuration
- **Development**: Debug logging, relaxed CORS
- **Production**: Optimized settings, strict security

## 🤝 Contributing

1. Follow Go coding standards
2. Add tests for new features
3. Update documentation
4. Ensure security best practices

## 📝 License

This project is part of the CRM Microservices platform.
