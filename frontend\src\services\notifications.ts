import { apiService } from './api';
import type {
  Notification,
  NotificationCreateRequest,
  NotificationFilters,
  NotificationStats,
  PaginatedResponse
} from '@/types/notification';

class NotificationService {
  private readonly API_PREFIX = '/api/v1/notification';

  // Get all notifications with pagination and filters
  async getNotifications(params?: {
    page?: number;
    limit?: number;
    filters?: NotificationFilters;
  }): Promise<PaginatedResponse<Notification>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.filters?.search) queryParams.append('search', params.filters.search);
    if (params?.filters?.type) queryParams.append('type', params.filters.type);
    if (params?.filters?.status) queryParams.append('status', params.filters.status);
    if (params?.filters?.recipient_type) queryParams.append('recipient_type', params.filters.recipient_type);
    if (params?.filters?.date_from) queryParams.append('date_from', params.filters.date_from);
    if (params?.filters?.date_to) queryParams.append('date_to', params.filters.date_to);

    const url = queryParams.toString() ? `${this.API_PREFIX}/notifications?${queryParams}` : `${this.API_PREFIX}/notifications`;
    return apiService.get<PaginatedResponse<Notification>>(url);
  }

  // Get notification by ID
  async getNotification(id: string): Promise<Notification> {
    return apiService.get<Notification>(`${this.API_PREFIX}/notifications/${id}`);
  }

  // Create new notification
  async createNotification(data: NotificationCreateRequest): Promise<Notification> {
    return apiService.post<Notification>(`${this.API_PREFIX}/notifications`, data);
  }

  // Send notification immediately
  async sendNotification(data: NotificationCreateRequest): Promise<{ notification_id: string; sent_count: number }> {
    return apiService.post<{ notification_id: string; sent_count: number }>(`${this.API_PREFIX}/send`, data);
  }

  // Schedule notification
  async scheduleNotification(data: NotificationCreateRequest & { scheduled_at: string }): Promise<Notification> {
    return apiService.post<Notification>(`${this.API_PREFIX}/schedule`, data);
  }

  // Cancel scheduled notification
  async cancelNotification(id: string): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/notifications/${id}/cancel`);
  }

  // Get notification statistics
  async getNotificationStats(): Promise<NotificationStats> {
    return apiService.get<NotificationStats>(`${this.API_PREFIX}/stats`);
  }

  // Notification templates
  async getTemplates(): Promise<any[]> {
    return apiService.get<any[]>(`${this.API_PREFIX}/templates`);
  }

  async createTemplate(data: {
    name: string;
    type: string;
    subject?: string;
    content: string;
    variables?: string[];
  }): Promise<any> {
    return apiService.post<any>(`${this.API_PREFIX}/templates`, data);
  }

  async updateTemplate(id: string, data: any): Promise<any> {
    return apiService.put<any>(`${this.API_PREFIX}/templates/${id}`, data);
  }

  async deleteTemplate(id: string): Promise<void> {
    return apiService.delete<void>(`${this.API_PREFIX}/templates/${id}`);
  }

  // Notification delivery status
  async getDeliveryStatus(id: string): Promise<{
    total_recipients: number;
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
  }> {
    return apiService.get<any>(`${this.API_PREFIX}/notifications/${id}/delivery-status`);
  }

  // Bulk notifications
  async sendBulkNotification(data: {
    type: string;
    subject?: string;
    content: string;
    recipients: Array<{
      type: 'email' | 'sms' | 'push';
      address: string;
      variables?: Record<string, any>;
    }>;
    scheduled_at?: string;
  }): Promise<{ notification_id: string; queued_count: number }> {
    return apiService.post<any>(`${this.API_PREFIX}/bulk-send`, data);
  }

  // Notification preferences
  async getUserPreferences(userId: string): Promise<any> {
    return apiService.get<any>(`${this.API_PREFIX}/users/${userId}/preferences`);
  }

  async updateUserPreferences(userId: string, preferences: any): Promise<any> {
    return apiService.put<any>(`${this.API_PREFIX}/users/${userId}/preferences`, preferences);
  }

  // Notification analytics
  async getAnalytics(params?: {
    date_from?: string;
    date_to?: string;
    group_by?: 'day' | 'week' | 'month';
    breakdown_by?: 'type' | 'status' | 'channel';
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.date_from) queryParams.append('date_from', params.date_from);
    if (params?.date_to) queryParams.append('date_to', params.date_to);
    if (params?.group_by) queryParams.append('group_by', params.group_by);
    if (params?.breakdown_by) queryParams.append('breakdown_by', params.breakdown_by);

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/analytics?${queryParams}` 
      : `${this.API_PREFIX}/analytics`;
    
    return apiService.get<any>(url);
  }

  // Quick notification methods
  async sendEmailToStudent(studentId: string, subject: string, content: string): Promise<void> {
    return this.sendNotification({
      type: 'EMAIL',
      subject,
      content,
      recipient_type: 'SPECIFIC_USERS',
      recipients: [{
        type: 'student',
        id: studentId
      }]
    }).then(() => {});
  }

  async sendSMSToStudent(studentId: string, content: string): Promise<void> {
    return this.sendNotification({
      type: 'SMS',
      content,
      recipient_type: 'SPECIFIC_USERS',
      recipients: [{
        type: 'student',
        id: studentId
      }]
    }).then(() => {});
  }
}

// Create and export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
