# 🔐 Authentication Service

The Authentication Service is a core microservice in the CRM platform that handles user authentication, authorization, and session management.

## 🚀 Features

### Authentication
- **JWT Token Management**: Access and refresh token generation/validation
- **Secure Password Hashing**: Argon2id algorithm for password security
- **Session Management**: Track and manage user sessions
- **Multi-factor Authentication**: Ready for 2FA implementation

### User Management
- **User Registration**: Create new user accounts with validation
- **Profile Management**: Update user profiles and preferences
- **Role-based Access Control**: Support for multiple user roles
- **Account Status Management**: Activate/deactivate user accounts

### Security Features
- **Password Complexity Validation**: Enforce strong password policies
- **Rate Limiting**: Protect against brute force attacks
- **Input Validation**: Comprehensive request validation
- **Audit Logging**: Track authentication events

## 🏗️ Architecture

### Directory Structure
```
auth-service/
├── main.go                    # Application entry point
├── Dockerfile                 # Container configuration
├── go.mod                     # Go module definition
├── internal/
│   ├── config/               # Configuration management
│   ├── handlers/             # HTTP request handlers
│   ├── services/             # Business logic
│   └── repository/           # Data access layer
└── README.md                 # This file
```

### Dependencies
- **Gin**: HTTP web framework
- **GORM**: ORM for database operations
- **JWT**: Token-based authentication
- **Argon2**: Password hashing
- **PostgreSQL**: Primary database
- **Redis**: Session storage and caching

## 🔧 Configuration

### Environment Variables
```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=crm_user
DB_PASSWORD=crm_password
DB_NAME_AUTH=crm_auth

# JWT
JWT_SECRET=your-secret-key
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Service
AUTH_SERVICE_PORT=8081
ENVIRONMENT=development
DEBUG=true
```

## 📡 API Endpoints

### Authentication Endpoints
```
POST   /api/v1/auth/login              # User login
POST   /api/v1/auth/register           # User registration
POST   /api/v1/auth/refresh            # Refresh access token
POST   /api/v1/auth/logout             # User logout
GET    /api/v1/auth/validate           # Validate token
POST   /api/v1/auth/forgot-password    # Request password reset
POST   /api/v1/auth/reset-password     # Reset password
GET    /api/v1/auth/status             # Get auth status
POST   /api/v1/auth/change-password    # Change password
```

### User Management Endpoints
```
GET    /api/v1/users/profile           # Get user profile
PUT    /api/v1/users/profile           # Update profile
POST   /api/v1/users/verify-email      # Verify email
POST   /api/v1/users/resend-verification # Resend verification
```

### Admin Endpoints (Admin/Cashier only)
```
GET    /api/v1/admin/users             # List users
GET    /api/v1/admin/users/:id         # Get user by ID
POST   /api/v1/admin/users             # Create user
PUT    /api/v1/admin/users/:id         # Update user
DELETE /api/v1/admin/users/:id         # Delete user
POST   /api/v1/admin/users/:id/activate   # Activate user
POST   /api/v1/admin/users/:id/deactivate # Deactivate user
```

### Health Check Endpoints
```
GET    /health                         # Service health
GET    /ready                          # Readiness check
GET    /live                           # Liveness check
GET    /metrics                        # Service metrics
GET    /version                        # Version info
```

## 🔐 User Roles

The service supports the following user roles:

- **ADMIN**: Full system access
- **CASHIER**: Financial operations access
- **RECEPTION**: Front desk operations
- **TEACHER**: Teaching and student management
- **MANAGER**: Management operations
- **ACADEMIC_MANAGER**: Academic oversight

## 🚀 Getting Started

### Prerequisites
- Go 1.21+
- PostgreSQL 15+
- Redis 7+
- Docker (optional)

### Local Development
1. **Clone and setup**:
   ```bash
   cd services/auth-service
   cp ../../.env.example ../../.env
   # Edit .env with your configuration
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Run the service**:
   ```bash
   go run main.go
   ```

### Docker Development
1. **Start with Docker Compose**:
   ```bash
   cd ../../
   docker-compose up -d postgres-auth redis
   docker-compose up auth-service
   ```

2. **Check health**:
   ```bash
   curl http://localhost:8081/health
   ```

## 🧪 Testing

### Unit Tests
```bash
go test ./...
```

### Integration Tests
```bash
go test -tags=integration ./...
```

### API Testing
```bash
# Register a user
curl -X POST http://localhost:8081/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "TestPassword123!",
    "first_name": "Test",
    "last_name": "User",
    "phone": "+998901234567",
    "role": "RECEPTION"
  }'

# Login
curl -X POST http://localhost:8081/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'
```

## 📊 Monitoring

### Health Checks
- **Health**: `/health` - Overall service health
- **Readiness**: `/ready` - Service readiness for traffic
- **Liveness**: `/live` - Service liveness probe

### Metrics
- Database connection pool stats
- Active session count
- Request/response metrics
- Error rates

## 🔒 Security

### Password Security
- Argon2id hashing algorithm
- Configurable complexity requirements
- Protection against common passwords

### Token Security
- Short-lived access tokens (15 minutes)
- Long-lived refresh tokens (7 days)
- Secure token validation
- Session invalidation on logout

### Input Validation
- Email format validation
- Phone number normalization
- Username format checking
- SQL injection prevention

## 🚀 Deployment

### Docker
```bash
docker build -t auth-service .
docker run -p 8081:8081 auth-service
```

### Environment-specific Configuration
- Development: Debug logging, auto-migration
- Production: Optimized settings, manual migration

## 🤝 Contributing

1. Follow Go coding standards
2. Add tests for new features
3. Update documentation
4. Ensure security best practices

## 📝 License

This project is part of the CRM Microservices platform.
