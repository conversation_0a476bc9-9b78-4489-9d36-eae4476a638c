# 🗄️ Database Setup Guide - Go Docker Platform

Comprehensive guide for database setup, schema management, and migrations for the CRM microservices system.

## 📋 Overview

The CRM system uses a **database-per-service** pattern with dedicated Neon PostgreSQL databases for each microservice. This approach ensures data isolation, service independence, and optimal performance.

## 🏗️ Database Architecture

### Database Distribution
| Service | Database | Endpoint | Purpose |
|---------|----------|----------|---------|
| Auth Service | ep-wandering-fire | Authentication & Users | User accounts, roles, sessions |
| Admin Service | ep-red-heart | Administrative Data | Analytics, audit logs, system config |
| Staff Service | ep-shy-fire | Operational Data | Leads, students, courses, enrollments |
| Payment Service | ep-floral-rice | Financial Data | Payments, transactions, billing |
| Notification Service | ep-tight-fog | Communication Data | Messages, templates, delivery logs |

### Connection Details
```bash
# Auth Database
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Admin Database  
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Staff Database
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Payment Database
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Notification Database
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

## 🔧 Database Setup

### Prerequisites
- Neon PostgreSQL account
- PostgreSQL client (psql)
- Database initialization scripts

### Step 1: Create Neon Databases

1. **Login to Neon Console**: https://console.neon.tech
2. **Create Project**: "CRM Microservices"
3. **Create Databases**: One for each service
4. **Configure Settings**:
   - **Compute**: Shared (for development) or Dedicated (for production)
   - **Region**: East US 2 (for optimal performance)
   - **Auto-scaling**: Enabled
   - **Connection Pooling**: Enabled

### Step 2: Initialize Database Schemas

Run the initialization scripts for each database:

```bash
# Initialize Auth Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-auth-database.sql

# Initialize Admin Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-admin-database.sql

# Initialize Staff Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-staff-database.sql

# Initialize Payment Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-payment-database.sql

# Initialize Notification Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f scripts/init-notification-database.sql
```

## 📊 Database Schemas

### Auth Service Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role VARCHAR(50) NOT NULL CHECK (role IN ('ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Sessions Table
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);
```

### Admin Service Database Schema

#### Analytics Table
```sql
CREATE TABLE analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2) NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Staff Service Database Schema

#### Leads Table
```sql
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    status VARCHAR(50) DEFAULT 'NEW' CHECK (status IN ('NEW', 'CONTACTED', 'QUALIFIED', 'CONVERTED', 'LOST')),
    source VARCHAR(50) CHECK (source IN ('WEBSITE', 'PHONE_CALL', 'REFERRAL', 'SOCIAL_MEDIA', 'WALK_IN')),
    course_interest VARCHAR(100),
    notes TEXT,
    assigned_to UUID,
    converted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Students Table
```sql
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    date_of_birth DATE,
    address TEXT,
    emergency_contact VARCHAR(20),
    status VARCHAR(50) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'GRADUATED', 'DROPPED')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Courses Table
```sql
CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    level VARCHAR(50) CHECK (level IN ('BEGINNER', 'ELEMENTARY', 'INTERMEDIATE', 'UPPER_INTERMEDIATE', 'ADVANCED')),
    duration_weeks INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'UZS',
    max_students INTEGER DEFAULT 15,
    schedule VARCHAR(200),
    teacher_id UUID,
    status VARCHAR(50) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'COMPLETED')),
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Enrollments Table
```sql
CREATE TABLE enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    enrollment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    start_date DATE NOT NULL,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED')),
    payment_plan VARCHAR(50) CHECK (payment_plan IN ('FULL', 'MONTHLY', 'WEEKLY')),
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Payment Service Database Schema

#### Payments Table
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    enrollment_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'UZS',
    gateway VARCHAR(50) NOT NULL CHECK (gateway IN ('UZCARD', 'HUMO', 'PAYME', 'CLICK', 'CASH')),
    payment_method VARCHAR(50) CHECK (payment_method IN ('CARD', 'CASH', 'BANK_TRANSFER')),
    status VARCHAR(50) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'REFUNDED')),
    transaction_id VARCHAR(255),
    gateway_response JSONB,
    description TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Refunds Table
```sql
CREATE TABLE refunds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    reason TEXT,
    status VARCHAR(50) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED')),
    gateway_response JSONB,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Notification Service Database Schema

#### Notifications Table
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('EMAIL', 'SMS', 'PUSH')),
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(500),
    message TEXT NOT NULL,
    template_id UUID,
    status VARCHAR(50) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Templates Table
```sql
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('EMAIL', 'SMS')),
    subject VARCHAR(500),
    body TEXT NOT NULL,
    variables JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 Migration Strategy

### Database-First Approach
The system uses a **database-first approach** where:

1. **Tables are pre-created** using initialization scripts
2. **Auto-migration is disabled** in production (`AUTO_MIGRATE=false`)
3. **Schema changes** are managed through versioned SQL scripts
4. **Data integrity** is maintained through proper constraints

### Migration Process

#### 1. Development Environment
```bash
# Enable auto-migration for development
export AUTO_MIGRATE=true

# GORM will automatically create/update tables
go run main.go
```

#### 2. Production Environment
```bash
# Disable auto-migration for production
export AUTO_MIGRATE=false

# Use manual migration scripts
psql "connection_string" -f migrations/001_add_new_column.sql
```

### Migration Scripts Structure
```
scripts/
├── init-auth-database.sql      # Initial auth schema
├── init-admin-database.sql     # Initial admin schema
├── init-staff-database.sql     # Initial staff schema
├── init-payment-database.sql   # Initial payment schema
├── init-notification-database.sql # Initial notification schema
└── migrations/
    ├── 001_add_user_preferences.sql
    ├── 002_add_payment_gateway_config.sql
    └── 003_add_notification_templates.sql
```

## 🔐 Security Configuration

### Connection Security
- **SSL Mode**: Required for all connections
- **Channel Binding**: Required for enhanced security
- **Connection Pooling**: Enabled with secure connection reuse
- **Timeout Settings**: Configured to prevent hanging connections

### Data Security
- **Encryption at Rest**: Provided by Neon PostgreSQL
- **Encryption in Transit**: TLS 1.2+ for all connections
- **Password Hashing**: bcrypt with cost factor 12
- **Sensitive Data**: Stored in encrypted format

### Access Control
- **Database Users**: Separate users for each service
- **Permissions**: Minimal required permissions per service
- **Network Security**: IP whitelisting and VPC configuration
- **Audit Logging**: All database operations logged

## 📊 Performance Optimization

### Indexing Strategy
```sql
-- Auth Service Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);

-- Staff Service Indexes
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_assigned_to ON leads(assigned_to);
CREATE INDEX idx_students_status ON students(status);
CREATE INDEX idx_enrollments_student_id ON enrollments(student_id);
CREATE INDEX idx_enrollments_course_id ON enrollments(course_id);

-- Payment Service Indexes
CREATE INDEX idx_payments_student_id ON payments(student_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);
```

### Connection Pooling
```go
// Database configuration
config := &gorm.Config{
    ConnPool: &sql.DB{
        MaxOpenConns:    25,
        MaxIdleConns:    5,
        ConnMaxLifetime: 5 * time.Minute,
    },
}
```

### Query Optimization
- **Prepared Statements**: Used for all queries
- **Batch Operations**: For bulk inserts/updates
- **Pagination**: Implemented for large result sets
- **Caching**: Redis caching for frequently accessed data

## 🔍 Monitoring & Maintenance

### Health Checks
```sql
-- Database health check query
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables;
```

### Performance Monitoring
```sql
-- Query performance monitoring
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;
```

### Backup Strategy
- **Automated Backups**: Daily backups by Neon
- **Point-in-Time Recovery**: Available for last 7 days
- **Cross-Region Replication**: For disaster recovery
- **Backup Testing**: Regular restore testing

## 🆘 Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Test database connectivity
psql "connection_string" -c "SELECT 1;"

# Check connection pool status
SELECT * FROM pg_stat_activity WHERE state = 'active';
```

#### Performance Issues
```sql
-- Find slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC;

-- Check table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

#### Data Integrity Issues
```sql
-- Check for orphaned records
SELECT COUNT(*) FROM enrollments e 
LEFT JOIN students s ON e.student_id = s.id 
WHERE s.id IS NULL;

-- Verify foreign key constraints
SELECT conname, conrelid::regclass, confrelid::regclass 
FROM pg_constraint 
WHERE contype = 'f';
```

### Recovery Procedures

#### Database Restore
```bash
# Restore from backup
pg_restore -h hostname -U username -d database_name backup_file.dump

# Verify restore
psql "connection_string" -c "SELECT COUNT(*) FROM users;"
```

#### Data Migration
```bash
# Export data
pg_dump "source_connection" --data-only --table=table_name > data.sql

# Import data
psql "target_connection" -f data.sql
```

---

**Database setup complete! 🎉**

Your CRM system now has a robust, scalable database architecture with proper security, performance optimization, and monitoring capabilities.
