package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// RateLimitMiddleware provides rate limiting functionality
type RateLimitMiddleware struct {
	limiters map[string]*rate.Limiter
	mutex    sync.RWMutex
	rate     int
	window   time.Duration
}

// NewRateLimitMiddleware creates a new rate limit middleware
func NewRateLimitMiddleware(requestsPerWindow int, window time.Duration) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		limiters: make(map[string]*rate.Limiter),
		rate:     requestsPerWindow,
		window:   window,
	}
}

// Handler returns the rate limiting middleware handler
func (rlm *RateLimitMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get client identifier (IP address)
		clientID := c.ClientIP()

		// Get or create limiter for this client
		limiter := rlm.getLimiter(clientID)

		// Check if request is allowed
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error":   "Rate limit exceeded. Please try again later.",
				"retry_after": rlm.window.Seconds(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// getLimiter gets or creates a rate limiter for a client
func (rlm *RateLimitMiddleware) getLimiter(clientID string) *rate.Limiter {
	rlm.mutex.RLock()
	limiter, exists := rlm.limiters[clientID]
	rlm.mutex.RUnlock()

	if !exists {
		rlm.mutex.Lock()
		// Double-check after acquiring write lock
		limiter, exists = rlm.limiters[clientID]
		if !exists {
			// Create new limiter
			// Convert requests per window to requests per second
			rps := rate.Limit(float64(rlm.rate) / rlm.window.Seconds())
			limiter = rate.NewLimiter(rps, rlm.rate)
			rlm.limiters[clientID] = limiter
		}
		rlm.mutex.Unlock()
	}

	return limiter
}

// CleanupOldLimiters removes inactive limiters to prevent memory leaks
func (rlm *RateLimitMiddleware) CleanupOldLimiters() {
	ticker := time.NewTicker(5 * time.Minute) // Cleanup every 5 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rlm.mutex.Lock()
			// In a real implementation, you would track last access time
			// and remove limiters that haven't been used recently
			// For simplicity, we'll just clear all limiters periodically
			if len(rlm.limiters) > 1000 { // Arbitrary threshold
				rlm.limiters = make(map[string]*rate.Limiter)
			}
			rlm.mutex.Unlock()
		}
	}
}

// UserBasedRateLimitMiddleware provides user-based rate limiting
type UserBasedRateLimitMiddleware struct {
	limiters map[string]*rate.Limiter
	mutex    sync.RWMutex
	rate     int
	window   time.Duration
}

// NewUserBasedRateLimitMiddleware creates a new user-based rate limit middleware
func NewUserBasedRateLimitMiddleware(requestsPerWindow int, window time.Duration) *UserBasedRateLimitMiddleware {
	return &UserBasedRateLimitMiddleware{
		limiters: make(map[string]*rate.Limiter),
		rate:     requestsPerWindow,
		window:   window,
	}
}

// Handler returns the user-based rate limiting middleware handler
func (urlm *UserBasedRateLimitMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user identifier from context (set by auth middleware)
		userID, exists := c.Get("user_id")
		if !exists {
			// Fall back to IP-based limiting for unauthenticated requests
			userID = c.ClientIP()
		}

		clientID := userID.(string)

		// Get or create limiter for this user
		limiter := urlm.getLimiter(clientID)

		// Check if request is allowed
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error":   "Rate limit exceeded for user. Please try again later.",
				"retry_after": urlm.window.Seconds(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// getLimiter gets or creates a rate limiter for a user
func (urlm *UserBasedRateLimitMiddleware) getLimiter(userID string) *rate.Limiter {
	urlm.mutex.RLock()
	limiter, exists := urlm.limiters[userID]
	urlm.mutex.RUnlock()

	if !exists {
		urlm.mutex.Lock()
		// Double-check after acquiring write lock
		limiter, exists = urlm.limiters[userID]
		if !exists {
			// Create new limiter
			rps := rate.Limit(float64(urlm.rate) / urlm.window.Seconds())
			limiter = rate.NewLimiter(rps, urlm.rate)
			urlm.limiters[userID] = limiter
		}
		urlm.mutex.Unlock()
	}

	return limiter
}

// ServiceBasedRateLimitMiddleware provides service-based rate limiting
type ServiceBasedRateLimitMiddleware struct {
	limiters map[string]*rate.Limiter
	mutex    sync.RWMutex
	configs  map[string]ServiceRateConfig
}

// ServiceRateConfig holds rate limiting configuration for a specific service
type ServiceRateConfig struct {
	RequestsPerWindow int
	Window           time.Duration
}

// NewServiceBasedRateLimitMiddleware creates a new service-based rate limit middleware
func NewServiceBasedRateLimitMiddleware(configs map[string]ServiceRateConfig) *ServiceBasedRateLimitMiddleware {
	return &ServiceBasedRateLimitMiddleware{
		limiters: make(map[string]*rate.Limiter),
		configs:  configs,
	}
}

// Handler returns the service-based rate limiting middleware handler
func (srlm *ServiceBasedRateLimitMiddleware) HandlerForService(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		config, exists := srlm.configs[serviceName]
		if !exists {
			// No rate limiting configured for this service
			c.Next()
			return
		}

		// Get client identifier
		clientID := c.ClientIP()
		if userID, exists := c.Get("user_id"); exists {
			clientID = userID.(string)
		}

		// Create unique key for service + client
		key := serviceName + ":" + clientID

		// Get or create limiter
		limiter := srlm.getLimiter(key, config)

		// Check if request is allowed
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error":   "Rate limit exceeded for service. Please try again later.",
				"service": serviceName,
				"retry_after": config.Window.Seconds(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// getLimiter gets or creates a rate limiter for a service + client combination
func (srlm *ServiceBasedRateLimitMiddleware) getLimiter(key string, config ServiceRateConfig) *rate.Limiter {
	srlm.mutex.RLock()
	limiter, exists := srlm.limiters[key]
	srlm.mutex.RUnlock()

	if !exists {
		srlm.mutex.Lock()
		// Double-check after acquiring write lock
		limiter, exists = srlm.limiters[key]
		if !exists {
			// Create new limiter
			rps := rate.Limit(float64(config.RequestsPerWindow) / config.Window.Seconds())
			limiter = rate.NewLimiter(rps, config.RequestsPerWindow)
			srlm.limiters[key] = limiter
		}
		srlm.mutex.Unlock()
	}

	return limiter
}

// GetRateLimitStats returns statistics about rate limiting
func (rlm *RateLimitMiddleware) GetRateLimitStats() map[string]interface{} {
	rlm.mutex.RLock()
	defer rlm.mutex.RUnlock()

	return map[string]interface{}{
		"active_limiters": len(rlm.limiters),
		"rate_per_window": rlm.rate,
		"window_duration": rlm.window.String(),
	}
}
