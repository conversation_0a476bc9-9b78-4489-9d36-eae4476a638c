package services

import (
	"fmt"

	"github.com/google/uuid"
	"github.com/crm-microservices/auth-service/internal/repository"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// UserService handles user management business logic
type UserService struct {
	userRepo        *repository.UserRepository
	passwordManager *utils.PasswordManager
}

// NewUserService creates a new user service
func NewUserService(userRepo *repository.UserRepository, passwordManager *utils.PasswordManager) *UserService {
	return &UserService{
		userRepo:        userRepo,
		passwordManager: passwordManager,
	}
}

// GetProfile retrieves user profile by ID
func (s *UserService) GetProfile(userID uuid.UUID) (*models.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	return user.ToResponse(), nil
}

// UpdateProfile updates user profile
func (s *UserService) UpdateProfile(userID uuid.UUID, req *models.UserUpdateRequest) (*models.UserResponse, error) {
	// Get existing user
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	// Validate input
	validator := utils.NewValidator()

	if req.Email != nil {
		validator.ValidateEmail("email", *req.Email)
		// Check if email is already taken by another user
		if *req.Email != user.Email {
			if exists, err := s.userRepo.ExistsByEmail(*req.Email); err != nil {
				return nil, fmt.Errorf("failed to check email existence: %w", err)
			} else if exists {
				validator.AddError("email", "email already exists", *req.Email)
			}
		}
	}

	if req.Username != nil {
		validator.ValidateUsername("username", *req.Username)
		// Check if username is already taken by another user
		if *req.Username != user.Username {
			if exists, err := s.userRepo.ExistsByUsername(*req.Username); err != nil {
				return nil, fmt.Errorf("failed to check username existence: %w", err)
			} else if exists {
				validator.AddError("username", "username already exists", *req.Username)
			}
		}
	}

	if req.Phone != nil {
		validator.ValidatePhone("phone", *req.Phone)
		// Check if phone is already taken by another user
		if *req.Phone != user.Phone {
			if exists, err := s.userRepo.ExistsByPhone(*req.Phone); err != nil {
				return nil, fmt.Errorf("failed to check phone existence: %w", err)
			} else if exists {
				validator.AddError("phone", "phone number already exists", *req.Phone)
			}
		}
	}

	if validator.HasErrors() {
		return nil, validator.GetErrors()
	}

	// Update user fields
	if req.Email != nil {
		user.Email = utils.NormalizeEmail(*req.Email)
		user.EmailVerified = false // Reset email verification
		user.EmailVerifiedAt = nil
	}

	if req.Username != nil {
		user.Username = *req.Username
	}

	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}

	if req.LastName != nil {
		user.LastName = *req.LastName
	}

	if req.Phone != nil {
		user.Phone = utils.NormalizePhone(*req.Phone)
		user.PhoneVerified = false // Reset phone verification
		user.PhoneVerifiedAt = nil
	}

	if req.DateOfBirth != nil {
		user.DateOfBirth = req.DateOfBirth
	}

	if req.Address != nil {
		user.Address = *req.Address
	}

	if req.City != nil {
		user.City = *req.City
	}

	if req.Country != nil {
		user.Country = *req.Country
	}

	// Save updated user
	if err := s.userRepo.Update(user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user.ToResponse(), nil
}

// CreateUser creates a new user (admin only)
func (s *UserService) CreateUser(req *models.UserCreateRequest) (*models.UserResponse, error) {
	// Validate input
	validator := utils.NewValidator()
	validator.ValidateRequired("email", req.Email)
	validator.ValidateEmail("email", req.Email)
	validator.ValidateRequired("username", req.Username)
	validator.ValidateUsername("username", req.Username)
	validator.ValidateRequired("password", req.Password)
	validator.ValidateRequired("first_name", req.FirstName)
	validator.ValidateRequired("last_name", req.LastName)
	validator.ValidateRequired("phone", req.Phone)
	validator.ValidatePhone("phone", req.Phone)

	if validator.HasErrors() {
		return nil, validator.GetErrors()
	}

	// Validate password strength
	if err := utils.ValidatePasswordComplexity(req.Password); err != nil {
		validator.AddError("password", err.Error(), req.Password)
		return nil, validator.GetErrors()
	}

	// Check if user already exists
	if exists, err := s.userRepo.ExistsByEmail(req.Email); err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	} else if exists {
		validator.AddError("email", "email already exists", req.Email)
		return nil, validator.GetErrors()
	}

	if exists, err := s.userRepo.ExistsByUsername(req.Username); err != nil {
		return nil, fmt.Errorf("failed to check username existence: %w", err)
	} else if exists {
		validator.AddError("username", "username already exists", req.Username)
		return nil, validator.GetErrors()
	}

	if exists, err := s.userRepo.ExistsByPhone(req.Phone); err != nil {
		return nil, fmt.Errorf("failed to check phone existence: %w", err)
	} else if exists {
		validator.AddError("phone", "phone number already exists", req.Phone)
		return nil, validator.GetErrors()
	}

	// Hash password
	hashedPassword, err := s.passwordManager.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &models.User{
		Email:       utils.NormalizeEmail(req.Email),
		Username:    req.Username,
		Password:    hashedPassword,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		Phone:       utils.NormalizePhone(req.Phone),
		Role:        req.Role,
		Status:      models.StatusActive,
		DateOfBirth: req.DateOfBirth,
		Address:     req.Address,
		City:        req.City,
		Country:     req.Country,
	}

	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user.ToResponse(), nil
}

// UpdateUser updates a user (admin only)
func (s *UserService) UpdateUser(userID uuid.UUID, req *models.UserUpdateRequest) (*models.UserResponse, error) {
	// Get existing user
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	// Validate input
	validator := utils.NewValidator()

	if req.Email != nil {
		validator.ValidateEmail("email", *req.Email)
		if *req.Email != user.Email {
			if exists, err := s.userRepo.ExistsByEmail(*req.Email); err != nil {
				return nil, fmt.Errorf("failed to check email existence: %w", err)
			} else if exists {
				validator.AddError("email", "email already exists", *req.Email)
			}
		}
	}

	if req.Username != nil {
		validator.ValidateUsername("username", *req.Username)
		if *req.Username != user.Username {
			if exists, err := s.userRepo.ExistsByUsername(*req.Username); err != nil {
				return nil, fmt.Errorf("failed to check username existence: %w", err)
			} else if exists {
				validator.AddError("username", "username already exists", *req.Username)
			}
		}
	}

	if req.Phone != nil {
		validator.ValidatePhone("phone", *req.Phone)
		if *req.Phone != user.Phone {
			if exists, err := s.userRepo.ExistsByPhone(*req.Phone); err != nil {
				return nil, fmt.Errorf("failed to check phone existence: %w", err)
			} else if exists {
				validator.AddError("phone", "phone number already exists", *req.Phone)
			}
		}
	}

	if validator.HasErrors() {
		return nil, validator.GetErrors()
	}

	// Update user fields (same as UpdateProfile but includes role and status)
	if req.Email != nil {
		user.Email = utils.NormalizeEmail(*req.Email)
		user.EmailVerified = false
		user.EmailVerifiedAt = nil
	}

	if req.Username != nil {
		user.Username = *req.Username
	}

	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}

	if req.LastName != nil {
		user.LastName = *req.LastName
	}

	if req.Phone != nil {
		user.Phone = utils.NormalizePhone(*req.Phone)
		user.PhoneVerified = false
		user.PhoneVerifiedAt = nil
	}

	if req.Role != nil {
		user.Role = *req.Role
	}

	if req.Status != nil {
		user.Status = *req.Status
	}

	if req.DateOfBirth != nil {
		user.DateOfBirth = req.DateOfBirth
	}

	if req.Address != nil {
		user.Address = *req.Address
	}

	if req.City != nil {
		user.City = *req.City
	}

	if req.Country != nil {
		user.Country = *req.Country
	}

	// Save updated user
	if err := s.userRepo.Update(user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user.ToResponse(), nil
}

// GetUser retrieves a user by ID (admin only)
func (s *UserService) GetUser(userID uuid.UUID) (*models.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	return user.ToResponse(), nil
}

// ListUsers retrieves users with pagination and filtering (admin only)
func (s *UserService) ListUsers(req *models.UserListRequest) (*models.UserListResponse, error) {
	users, total, err := s.userRepo.List(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Convert to response format
	userResponses := make([]*models.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = user.ToResponse()
	}

	// Create pagination response
	pagination := models.NewPaginationResponse(req.Page, req.PageSize, total)

	return &models.UserListResponse{
		Users:      userResponses,
		Pagination: pagination,
	}, nil
}

// DeleteUser soft deletes a user (admin only)
func (s *UserService) DeleteUser(userID uuid.UUID) error {
	// Check if user exists
	_, err := s.userRepo.GetByID(userID)
	if err != nil {
		return ErrUserNotFound
	}

	// Soft delete user
	if err := s.userRepo.Delete(userID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// ActivateUser activates a user account (admin only)
func (s *UserService) ActivateUser(userID uuid.UUID) error {
	if err := s.userRepo.UpdateStatus(userID, models.StatusActive); err != nil {
		return fmt.Errorf("failed to activate user: %w", err)
	}
	return nil
}

// DeactivateUser deactivates a user account (admin only)
func (s *UserService) DeactivateUser(userID uuid.UUID) error {
	if err := s.userRepo.UpdateStatus(userID, models.StatusInactive); err != nil {
		return fmt.Errorf("failed to deactivate user: %w", err)
	}
	return nil
}

// VerifyEmail marks user's email as verified
func (s *UserService) VerifyEmail(userID uuid.UUID) error {
	if err := s.userRepo.VerifyEmail(userID); err != nil {
		return fmt.Errorf("failed to verify email: %w", err)
	}
	return nil
}

// VerifyPhone marks user's phone as verified
func (s *UserService) VerifyPhone(userID uuid.UUID) error {
	if err := s.userRepo.VerifyPhone(userID); err != nil {
		return fmt.Errorf("failed to verify phone: %w", err)
	}
	return nil
}
