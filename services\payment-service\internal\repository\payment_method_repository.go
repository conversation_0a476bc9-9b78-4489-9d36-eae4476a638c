package repository

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
	"payment-service/internal/models"
	sharedModels "github.com/crm-microservices/shared/models"
)

// PaymentMethodRepository handles payment method data operations
type PaymentMethodRepository interface {
	GetAll(req *PaymentMethodListRequest) ([]*models.PaymentMethod, int64, error)
	GetByID(id uuid.UUID) (*models.PaymentMethod, error)
	GetByUserID(userID uuid.UUID) ([]*models.PaymentMethod, error)
	GetDefaultByUserID(userID uuid.UUID) (*models.PaymentMethod, error)
	GetByGatewayMethodID(gatewayMethodID string) (*models.PaymentMethod, error)
	Create(paymentMethod *models.PaymentMethod) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	SetAsDefault(userID, paymentMethodID uuid.UUID) error
	DeactivateByUserID(userID uuid.UUID) error
}

type paymentMethodRepository struct {
	db *gorm.DB
}

// NewPaymentMethodRepository creates a new payment method repository
func NewPaymentMethodRepository(db *gorm.DB) PaymentMethodRepository {
	return &paymentMethodRepository{db: db}
}

// PaymentMethodListRequest represents request parameters for listing payment methods
type PaymentMethodListRequest struct {
	sharedModels.PaginationRequest
	UserID      *uuid.UUID                     `form:"user_id"`
	Type        *sharedModels.PaymentMethod    `form:"type"`
	GatewayType *string                        `form:"gateway_type"`
	IsActive    *bool                          `form:"is_active"`
	IsDefault   *bool                          `form:"is_default"`
}

// GetAll retrieves payment methods with pagination and filtering
func (r *paymentMethodRepository) GetAll(req *PaymentMethodListRequest) ([]*models.PaymentMethod, int64, error) {
	var paymentMethods []*models.PaymentMethod
	var total int64

	query := r.db.Model(&models.PaymentMethod{})

	// Apply filters
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}

	if req.GatewayType != nil {
		query = query.Where("gateway_type = ?", *req.GatewayType)
	}

	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	if req.IsDefault != nil {
		query = query.Where("is_default = ?", *req.IsDefault)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()
	
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&paymentMethods).Error; err != nil {
		return nil, 0, err
	}

	return paymentMethods, total, nil
}

// GetByID retrieves a payment method by ID
func (r *paymentMethodRepository) GetByID(id uuid.UUID) (*models.PaymentMethod, error) {
	var paymentMethod models.PaymentMethod
	if err := r.db.First(&paymentMethod, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &paymentMethod, nil
}

// GetByUserID retrieves payment methods by user ID
func (r *paymentMethodRepository) GetByUserID(userID uuid.UUID) ([]*models.PaymentMethod, error) {
	var paymentMethods []*models.PaymentMethod
	if err := r.db.Where("user_id = ? AND is_active = ?", userID, true).Order("is_default DESC, created_at DESC").Find(&paymentMethods).Error; err != nil {
		return nil, err
	}
	return paymentMethods, nil
}

// GetDefaultByUserID retrieves the default payment method for a user
func (r *paymentMethodRepository) GetDefaultByUserID(userID uuid.UUID) (*models.PaymentMethod, error) {
	var paymentMethod models.PaymentMethod
	if err := r.db.Where("user_id = ? AND is_default = ? AND is_active = ?", userID, true, true).First(&paymentMethod).Error; err != nil {
		return nil, err
	}
	return &paymentMethod, nil
}

// GetByGatewayMethodID retrieves a payment method by gateway method ID
func (r *paymentMethodRepository) GetByGatewayMethodID(gatewayMethodID string) (*models.PaymentMethod, error) {
	var paymentMethod models.PaymentMethod
	if err := r.db.First(&paymentMethod, "gateway_method_id = ?", gatewayMethodID).Error; err != nil {
		return nil, err
	}
	return &paymentMethod, nil
}

// Create creates a new payment method
func (r *paymentMethodRepository) Create(paymentMethod *models.PaymentMethod) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// If this is set as default, unset other defaults for the user
		if paymentMethod.IsDefault {
			if err := tx.Model(&models.PaymentMethod{}).
				Where("user_id = ? AND id != ?", paymentMethod.UserID, paymentMethod.ID).
				Update("is_default", false).Error; err != nil {
				return err
			}
		}

		return tx.Create(paymentMethod).Error
	})
}

// Update updates a payment method
func (r *paymentMethodRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Get the payment method to check if we're setting it as default
		var paymentMethod models.PaymentMethod
		if err := tx.First(&paymentMethod, "id = ?", id).Error; err != nil {
			return err
		}

		// If setting as default, unset other defaults for the user
		if isDefault, ok := updates["is_default"]; ok && isDefault.(bool) {
			if err := tx.Model(&models.PaymentMethod{}).
				Where("user_id = ? AND id != ?", paymentMethod.UserID, id).
				Update("is_default", false).Error; err != nil {
				return err
			}
		}

		return tx.Model(&models.PaymentMethod{}).Where("id = ?", id).Updates(updates).Error
	})
}

// Delete soft deletes a payment method
func (r *paymentMethodRepository) Delete(id uuid.UUID) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Get the payment method to check if it's default
		var paymentMethod models.PaymentMethod
		if err := tx.First(&paymentMethod, "id = ?", id).Error; err != nil {
			return err
		}

		// Delete the payment method
		if err := tx.Delete(&models.PaymentMethod{}, "id = ?", id).Error; err != nil {
			return err
		}

		// If this was the default, set another one as default if available
		if paymentMethod.IsDefault {
			var newDefault models.PaymentMethod
			if err := tx.Where("user_id = ? AND is_active = ?", paymentMethod.UserID, true).
				Order("created_at DESC").First(&newDefault).Error; err == nil {
				tx.Model(&newDefault).Update("is_default", true)
			}
		}

		return nil
	})
}

// SetAsDefault sets a payment method as the default for a user
func (r *paymentMethodRepository) SetAsDefault(userID, paymentMethodID uuid.UUID) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Unset all defaults for the user
		if err := tx.Model(&models.PaymentMethod{}).
			Where("user_id = ?", userID).
			Update("is_default", false).Error; err != nil {
			return err
		}

		// Set the specified payment method as default
		return tx.Model(&models.PaymentMethod{}).
			Where("id = ? AND user_id = ?", paymentMethodID, userID).
			Update("is_default", true).Error
	})
}

// DeactivateByUserID deactivates all payment methods for a user
func (r *paymentMethodRepository) DeactivateByUserID(userID uuid.UUID) error {
	return r.db.Model(&models.PaymentMethod{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"is_active":  false,
			"is_default": false,
		}).Error
}
