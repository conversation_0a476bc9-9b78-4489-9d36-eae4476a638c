-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id VARCHAR(50) UNIQUE NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    enrollment_date DATE NOT NULL,
    graduation_date DATE,
    overall_grade DECIMAL(5,2),
    attendance_rate DECIMAL(5,2),
    emergency_contact VARCHAR(255),
    emergency_phone VARCHAR(20),
    notes TEXT,
    created_by_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Create indexes for students table
CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_students_status ON students(status);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_date ON students(enrollment_date);
CREATE INDEX IF NOT EXISTS idx_students_created_at ON students(created_at);
CREATE INDEX IF NOT EXISTS idx_students_deleted_at ON students(deleted_at);

-- Add constraints
ALTER TABLE students ADD CONSTRAINT chk_students_status 
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'GRADUATED', 'DROPPED_OUT'));

ALTER TABLE students ADD CONSTRAINT chk_students_overall_grade 
    CHECK (overall_grade >= 0 AND overall_grade <= 100);

ALTER TABLE students ADD CONSTRAINT chk_students_attendance_rate 
    CHECK (attendance_rate >= 0 AND attendance_rate <= 100);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate student ID
CREATE OR REPLACE FUNCTION generate_student_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.student_id IS NULL OR NEW.student_id = '' THEN
        NEW.student_id := 'STU' || TO_CHAR(CURRENT_DATE, 'YYYY') || LPAD(nextval('student_id_seq')::text, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for student ID generation
CREATE SEQUENCE IF NOT EXISTS student_id_seq START 1;

-- Create trigger for student ID generation
CREATE TRIGGER generate_student_id_trigger BEFORE INSERT ON students
    FOR EACH ROW EXECUTE FUNCTION generate_student_id();
