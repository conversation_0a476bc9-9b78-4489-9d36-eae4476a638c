package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"payment-service/internal/services"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// FinancialHandler handles financial reporting and analytics HTTP requests
type FinancialHandler struct {
	financialService services.FinancialService
}

// NewFinancialHandler creates a new financial handler
func NewFinancialHandler(financialService services.FinancialService) *FinancialHandler {
	return &FinancialHandler{
		financialService: financialService,
	}
}

// GenerateFinancialReport handles POST /reports/generate
func (h *FinancialHandler) GenerateFinancialReport(c *gin.Context) {
	var req services.FinancialReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.financialService.GenerateFinancialReport(&req)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Report, "Financial report generated successfully"))
}

// GetPaymentAnalytics handles GET /analytics/payments
func (h *FinancialHandler) GetPaymentAnalytics(c *gin.Context) {
	var req services.AnalyticsRequest

	// Parse query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("start_date and end_date are required"))
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
		return
	}

	req.StartDate = startDate
	req.EndDate = endDate

	if gatewayType := c.Query("gateway_type"); gatewayType != "" {
		req.GatewayType = &gatewayType
	}

	if currency := c.Query("currency"); currency != "" {
		req.Currency = &currency
	}

	response, err := h.financialService.GetPaymentAnalytics(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Analytics, "Payment analytics retrieved successfully"))
}

// GetRevenueMetrics handles GET /analytics/revenue
func (h *FinancialHandler) GetRevenueMetrics(c *gin.Context) {
	var req services.RevenueMetricsRequest

	// Parse query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	period := c.Query("period")

	if startDateStr == "" || endDateStr == "" || period == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("start_date, end_date, and period are required"))
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
		return
	}

	req.StartDate = startDate
	req.EndDate = endDate
	req.Period = period

	if gatewayType := c.Query("gateway_type"); gatewayType != "" {
		req.GatewayType = &gatewayType
	}

	response, err := h.financialService.GetRevenueMetrics(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Metrics, "Revenue metrics retrieved successfully"))
}

// GetGatewayPerformance handles GET /analytics/gateways
func (h *FinancialHandler) GetGatewayPerformance(c *gin.Context) {
	var req services.GatewayPerformanceRequest

	// Parse query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("start_date and end_date are required"))
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
		return
	}

	req.StartDate = startDate
	req.EndDate = endDate

	response, err := h.financialService.GetGatewayPerformance(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Performance, "Gateway performance retrieved successfully"))
}

// GetPaymentTrends handles GET /analytics/trends
func (h *FinancialHandler) GetPaymentTrends(c *gin.Context) {
	var req services.PaymentTrendsRequest

	// Parse query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	granularity := c.Query("granularity")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("start_date and end_date are required"))
		return
	}

	if granularity == "" {
		granularity = "daily" // default
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
		return
	}

	req.StartDate = startDate
	req.EndDate = endDate
	req.Granularity = granularity

	response, err := h.financialService.GetPaymentTrends(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Trends, "Payment trends retrieved successfully"))
}

// ExportFinancialData handles POST /reports/export
func (h *FinancialHandler) ExportFinancialData(c *gin.Context) {
	var req services.ExportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.financialService.ExportFinancialData(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response, "Financial data export initiated successfully"))
}

// ReconcilePayments handles POST /reconciliation
func (h *FinancialHandler) ReconcilePayments(c *gin.Context) {
	var req services.ReconciliationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.financialService.ReconcilePayments(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(response.Record, "Payment reconciliation completed successfully"))
}

// GetFinancialDashboard handles GET /dashboard
func (h *FinancialHandler) GetFinancialDashboard(c *gin.Context) {
	// Parse optional date range
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to last 30 days
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	// Get analytics data
	analyticsReq := &services.AnalyticsRequest{
		StartDate: startDate,
		EndDate:   endDate,
	}

	analytics, err := h.financialService.GetPaymentAnalytics(analyticsReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	// Get gateway performance
	gatewayReq := &services.GatewayPerformanceRequest{
		StartDate: startDate,
		EndDate:   endDate,
	}

	gatewayPerformance, err := h.financialService.GetGatewayPerformance(gatewayReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	// Get payment trends (last 7 days)
	trendsReq := &services.PaymentTrendsRequest{
		StartDate:   time.Now().AddDate(0, 0, -7),
		EndDate:     time.Now(),
		Granularity: "daily",
	}

	trends, err := h.financialService.GetPaymentTrends(trendsReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	// Compile dashboard data
	dashboard := map[string]interface{}{
		"period": map[string]interface{}{
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
		},
		"analytics":           analytics.Analytics,
		"gateway_performance": gatewayPerformance.Performance,
		"trends":             trends.Trends,
		"summary": map[string]interface{}{
			"total_revenue":    analytics.Analytics.TotalRevenue,
			"payment_count":    analytics.Analytics.PaymentCount,
			"success_rate":     analytics.Analytics.SuccessRate,
			"average_payment":  analytics.Analytics.AveragePayment,
		},
	}

	c.JSON(http.StatusOK, models.SuccessResponse(dashboard, "Financial dashboard data retrieved successfully"))
}

// GetRevenueComparison handles GET /analytics/revenue/comparison
func (h *FinancialHandler) GetRevenueComparison(c *gin.Context) {
	// Parse query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	compareWith := c.Query("compare_with") // previous_period, previous_year

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("start_date and end_date are required"))
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
		return
	}

	// Calculate comparison period
	var compareStartDate, compareEndDate time.Time
	duration := endDate.Sub(startDate)

	switch compareWith {
	case "previous_period":
		compareEndDate = startDate
		compareStartDate = compareEndDate.Add(-duration)
	case "previous_year":
		compareStartDate = startDate.AddDate(-1, 0, 0)
		compareEndDate = endDate.AddDate(-1, 0, 0)
	default:
		compareWith = "previous_period"
		compareEndDate = startDate
		compareStartDate = compareEndDate.Add(-duration)
	}

	// Get analytics for current period
	currentReq := &services.AnalyticsRequest{
		StartDate: startDate,
		EndDate:   endDate,
	}

	currentAnalytics, err := h.financialService.GetPaymentAnalytics(currentReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	// Get analytics for comparison period
	compareReq := &services.AnalyticsRequest{
		StartDate: compareStartDate,
		EndDate:   compareEndDate,
	}

	compareAnalytics, err := h.financialService.GetPaymentAnalytics(compareReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(err.Error()))
		return
	}

	// Calculate growth rates
	revenueGrowth := 0.0
	paymentGrowth := 0.0
	successRateChange := 0.0

	if compareAnalytics.Analytics.TotalRevenue > 0 {
		revenueGrowth = ((currentAnalytics.Analytics.TotalRevenue - compareAnalytics.Analytics.TotalRevenue) / compareAnalytics.Analytics.TotalRevenue) * 100
	}

	if compareAnalytics.Analytics.PaymentCount > 0 {
		paymentGrowth = ((float64(currentAnalytics.Analytics.PaymentCount) - float64(compareAnalytics.Analytics.PaymentCount)) / float64(compareAnalytics.Analytics.PaymentCount)) * 100
	}

	successRateChange = currentAnalytics.Analytics.SuccessRate - compareAnalytics.Analytics.SuccessRate

	comparison := map[string]interface{}{
		"current_period": map[string]interface{}{
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
			"analytics":  currentAnalytics.Analytics,
		},
		"comparison_period": map[string]interface{}{
			"start_date": compareStartDate.Format("2006-01-02"),
			"end_date":   compareEndDate.Format("2006-01-02"),
			"analytics":  compareAnalytics.Analytics,
		},
		"growth": map[string]interface{}{
			"revenue_growth":       revenueGrowth,
			"payment_growth":       paymentGrowth,
			"success_rate_change":  successRateChange,
			"comparison_type":      compareWith,
		},
	}

	c.JSON(http.StatusOK, models.SuccessResponse(comparison, "Revenue comparison retrieved successfully"))
}
