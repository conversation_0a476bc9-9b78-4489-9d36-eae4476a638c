export interface Notification {
  id: string;
  type: NotificationType;
  subject?: string;
  content: string;
  status: NotificationStatus;
  recipient_type: RecipientType;
  recipients: NotificationRecipient[];
  scheduled_at?: string;
  sent_at?: string;
  delivery_stats: DeliveryStats;
  template_id?: string;
  template_name?: string;
  created_by: string;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

export type NotificationType = 'EMAIL' | 'SMS' | 'PUSH' | 'IN_APP';

export type NotificationStatus = 
  | 'DRAFT' 
  | 'SCHEDULED' 
  | 'SENDING' 
  | 'SENT' 
  | 'FAILED' 
  | 'CANCELLED';

export type RecipientType = 
  | 'ALL_STUDENTS' 
  | 'ALL_STAFF' 
  | 'SPECIFIC_USERS' 
  | 'COURSE_STUDENTS' 
  | 'ROLE_BASED' 
  | 'CUSTOM';

export interface NotificationRecipient {
  type: 'email' | 'sms' | 'push';
  address: string;
  user_id?: string;
  user_name?: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed';
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
  variables?: Record<string, any>;
}

export interface DeliveryStats {
  total_recipients: number;
  sent: number;
  delivered: number;
  failed: number;
  pending: number;
}

export interface NotificationCreateRequest {
  type: NotificationType;
  subject?: string;
  content: string;
  recipient_type: RecipientType;
  recipients: Array<{
    type: 'student' | 'staff' | 'user' | 'email' | 'phone';
    id?: string;
    address?: string;
    variables?: Record<string, any>;
  }>;
  scheduled_at?: string;
  template_id?: string;
}

export interface NotificationFilters {
  search?: string;
  type?: NotificationType;
  status?: NotificationStatus;
  recipient_type?: RecipientType;
  date_from?: string;
  date_to?: string;
  created_by?: string;
}

export interface NotificationStats {
  total_notifications: number;
  sent_notifications: number;
  scheduled_notifications: number;
  failed_notifications: number;
  notifications_by_type: Record<NotificationType, number>;
  notifications_by_status: Record<NotificationStatus, number>;
  total_recipients_reached: number;
  average_delivery_rate: number;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  subject?: string;
  content: string;
  variables: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
