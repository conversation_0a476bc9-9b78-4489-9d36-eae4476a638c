package handlers

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/shared/database"
	"github.com/crm-microservices/shared/utils"
)

// HealthHandler handles health check requests
type HealthHandler struct {
	dbManager *database.ConnectionManager
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(dbManager *database.ConnectionManager) *HealthHandler {
	return &HealthHandler{
		dbManager: dbManager,
	}
}

// HealthCheck handles health check requests
// @Summary Health check
// @Description Check service health status
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /health [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	checks := make(map[string]interface{})
	overallStatus := "healthy"

	// Check database connection
	dbHealth := h.dbManager.HealthCheck()
	checks["database"] = dbHealth
	
	if status, ok := dbHealth["status"].(string); ok && status != "healthy" {
		overallStatus = "unhealthy"
	}

	// Check service uptime (you could track this globally)
	checks["uptime"] = time.Since(time.Now().Add(-time.Hour)).String() // Placeholder

	// Check memory usage (basic check)
	checks["memory"] = map[string]interface{}{
		"status": "healthy", // You could implement actual memory checks
	}

	// Add service information
	checks["service"] = map[string]interface{}{
		"name":    "auth-service",
		"version": "1.0.0",
		"status":  "running",
	}

	// Add timestamp
	checks["timestamp"] = time.Now().UTC().Format(time.RFC3339)

	utils.HealthResponse(c, overallStatus, checks)
}

// ReadinessCheck handles readiness check requests
// @Summary Readiness check
// @Description Check if service is ready to serve requests
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /ready [get]
func (h *HealthHandler) ReadinessCheck(c *gin.Context) {
	checks := make(map[string]interface{})
	overallStatus := "ready"

	// Check database connection
	if err := h.dbManager.Ping(); err != nil {
		checks["database"] = map[string]interface{}{
			"status": "not_ready",
			"error":  err.Error(),
		}
		overallStatus = "not_ready"
	} else {
		checks["database"] = map[string]interface{}{
			"status": "ready",
		}
	}

	// Add service readiness
	checks["service"] = map[string]interface{}{
		"status": "ready",
	}

	checks["timestamp"] = time.Now().UTC().Format(time.RFC3339)

	if overallStatus == "ready" {
		utils.SuccessResponse(c, map[string]interface{}{
			"status": overallStatus,
			"checks": checks,
		}, "Service is ready")
	} else {
		c.JSON(503, map[string]interface{}{
			"success": false,
			"error":   "Service is not ready",
			"data": map[string]interface{}{
				"status": overallStatus,
				"checks": checks,
			},
		})
	}
}

// LivenessCheck handles liveness check requests
// @Summary Liveness check
// @Description Check if service is alive
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /live [get]
func (h *HealthHandler) LivenessCheck(c *gin.Context) {
	// Simple liveness check - if we can respond, we're alive
	utils.SuccessResponse(c, map[string]interface{}{
		"status":    "alive",
		"service":   "auth-service",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}, "Service is alive")
}

// MetricsCheck handles metrics requests
// @Summary Get service metrics
// @Description Get basic service metrics
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /metrics [get]
func (h *HealthHandler) MetricsCheck(c *gin.Context) {
	metrics := make(map[string]interface{})

	// Database metrics
	if dbStats := h.dbManager.GetConnectionStats(); dbStats != nil {
		metrics["database"] = dbStats
	}

	// Service metrics
	metrics["service"] = map[string]interface{}{
		"name":      "auth-service",
		"version":   "1.0.0",
		"uptime":    time.Since(time.Now().Add(-time.Hour)).String(), // Placeholder
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// You could add more metrics here:
	// - Request count
	// - Response times
	// - Error rates
	// - Active sessions
	// - etc.

	utils.MetricsResponse(c, metrics)
}

// VersionCheck handles version requests
// @Summary Get service version
// @Description Get service version information
// @Tags Health
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /version [get]
func (h *HealthHandler) VersionCheck(c *gin.Context) {
	version := map[string]interface{}{
		"service":     "auth-service",
		"version":     "1.0.0",
		"build_time":  "2024-01-01T00:00:00Z", // You could set this during build
		"git_commit":  "unknown",               // You could set this during build
		"go_version":  "1.21",
		"environment": "development", // You could get this from config
	}

	utils.SuccessResponse(c, version, "Version information retrieved")
}
