-- Create courses table
CREATE TABLE IF NOT EXISTS courses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    course_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    level VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    credits INTEGER DEFAULT 0,
    duration INTEGER, -- in hours
    max_students INTEGER DEFAULT 30,
    current_students INTEGER DEFAULT 0,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    start_date DATE,
    end_date DATE,
    instructor_id UUID,
    prerequisites TEXT,
    objectives TEXT,
    materials TEXT,
    created_by_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_courses_instructor FOREIGN <PERSON>EY (instructor_id) REFERENCES teachers(id) ON DELETE SET NULL
);

-- Create indexes for courses table
CREATE INDEX IF NOT EXISTS idx_courses_course_code ON courses(course_code);
CREATE INDEX IF NOT EXISTS idx_courses_status ON courses(status);
CREATE INDEX IF NOT EXISTS idx_courses_level ON courses(level);
CREATE INDEX IF NOT EXISTS idx_courses_instructor_id ON courses(instructor_id);
CREATE INDEX IF NOT EXISTS idx_courses_start_date ON courses(start_date);
CREATE INDEX IF NOT EXISTS idx_courses_price ON courses(price);
CREATE INDEX IF NOT EXISTS idx_courses_created_at ON courses(created_at);
CREATE INDEX IF NOT EXISTS idx_courses_deleted_at ON courses(deleted_at);

-- Add constraints
ALTER TABLE courses ADD CONSTRAINT chk_courses_level 
    CHECK (level IN ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'));

ALTER TABLE courses ADD CONSTRAINT chk_courses_status 
    CHECK (status IN ('DRAFT', 'ACTIVE', 'INACTIVE', 'COMPLETED', 'CANCELLED'));

ALTER TABLE courses ADD CONSTRAINT chk_courses_credits 
    CHECK (credits >= 0);

ALTER TABLE courses ADD CONSTRAINT chk_courses_duration 
    CHECK (duration > 0);

ALTER TABLE courses ADD CONSTRAINT chk_courses_max_students 
    CHECK (max_students > 0);

ALTER TABLE courses ADD CONSTRAINT chk_courses_current_students 
    CHECK (current_students >= 0 AND current_students <= max_students);

ALTER TABLE courses ADD CONSTRAINT chk_courses_price 
    CHECK (price >= 0);

ALTER TABLE courses ADD CONSTRAINT chk_courses_dates 
    CHECK (end_date IS NULL OR start_date IS NULL OR end_date >= start_date);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
