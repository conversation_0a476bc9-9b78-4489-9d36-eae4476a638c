package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// EnrollmentRepository handles enrollment data operations
type EnrollmentRepository interface {
	GetAll(req *models.EnrollmentListRequest) ([]*models.Enrollment, int64, error)
	GetByID(id uuid.UUID) (*models.Enrollment, error)
	GetByStudentAndCourse(studentID, courseID uuid.UUID) (*models.Enrollment, error)
	Create(enrollment *models.Enrollment) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.EnrollmentStats, error)
	GetByStudent(studentID uuid.UUID) ([]*models.Enrollment, error)
	GetByCourse(courseID uuid.UUID) ([]*models.Enrollment, error)
	GetActiveEnrollments() ([]*models.Enrollment, error)
	GetEnrollmentsByPaymentStatus(status string) ([]*models.Enrollment, error)
	GetOverduePayments() ([]*models.Enrollment, error)
	BulkUpdatePaymentStatus(enrollmentIDs []uuid.UUID, status string, updatedBy uuid.UUID) error
	GetRevenueByPeriod(startDate, endDate time.Time) (float64, error)
	ValidateEnrollment(studentID, courseID uuid.UUID) error
}

type enrollmentRepository struct {
	db *gorm.DB
}

// NewEnrollmentRepository creates a new enrollment repository
func NewEnrollmentRepository(db *gorm.DB) EnrollmentRepository {
	return &enrollmentRepository{db: db}
}

// GetAll retrieves enrollments with pagination and filtering
func (r *enrollmentRepository) GetAll(req *models.EnrollmentListRequest) ([]*models.Enrollment, int64, error) {
	var enrollments []*models.Enrollment
	var total int64

	query := r.db.Model(&models.Enrollment{}).Preload("Student").Preload("Course").Preload("EnrolledBy")

	// Apply filters
	if req.StudentID != nil {
		query = query.Where("student_id = ?", *req.StudentID)
	}

	if req.CourseID != nil {
		query = query.Where("course_id = ?", *req.CourseID)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.PaymentStatus != nil {
		query = query.Where("payment_status = ?", *req.PaymentStatus)
	}

	if req.EnrolledByID != nil {
		query = query.Where("enrolled_by_id = ?", *req.EnrolledByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Joins("LEFT JOIN students ON students.id = enrollments.student_id").
			Joins("LEFT JOIN courses ON courses.id = enrollments.course_id").
			Where(
				"LOWER(students.first_name) LIKE ? OR LOWER(students.last_name) LIKE ? OR LOWER(courses.name) LIKE ? OR LOWER(courses.course_code) LIKE ?",
				searchTerm, searchTerm, searchTerm, searchTerm,
			)
	}

	if req.StartDate != nil {
		query = query.Where("enrollment_date >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("enrollment_date <= ?", *req.EndDate)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count enrollments: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("enrollment_date DESC").Find(&enrollments).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get enrollments: %w", err)
	}

	return enrollments, total, nil
}

// GetByID retrieves an enrollment by ID
func (r *enrollmentRepository) GetByID(id uuid.UUID) (*models.Enrollment, error) {
	var enrollment models.Enrollment
	if err := r.db.Preload("Student").Preload("Course").Preload("EnrolledBy").
		First(&enrollment, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("enrollment not found")
		}
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}
	return &enrollment, nil
}

// GetByStudentAndCourse retrieves an enrollment by student and course
func (r *enrollmentRepository) GetByStudentAndCourse(studentID, courseID uuid.UUID) (*models.Enrollment, error) {
	var enrollment models.Enrollment
	if err := r.db.Preload("Student").Preload("Course").Preload("EnrolledBy").
		Where("student_id = ? AND course_id = ?", studentID, courseID).
		First(&enrollment).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("enrollment not found")
		}
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}
	return &enrollment, nil
}

// Create creates a new enrollment
func (r *enrollmentRepository) Create(enrollment *models.Enrollment) error {
	if err := r.db.Create(enrollment).Error; err != nil {
		return fmt.Errorf("failed to create enrollment: %w", err)
	}
	return nil
}

// Update updates an enrollment
func (r *enrollmentRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Enrollment{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update enrollment: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("enrollment not found")
	}
	return nil
}

// Delete soft deletes an enrollment
func (r *enrollmentRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Enrollment{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete enrollment: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("enrollment not found")
	}
	return nil
}

// GetStats retrieves enrollment statistics
func (r *enrollmentRepository) GetStats() (*models.EnrollmentStats, error) {
	stats := &models.EnrollmentStats{
		EnrollmentsByStatus:  make(map[models.EnrollmentStatus]int64),
		EnrollmentsByPayment: make(map[string]int64),
	}

	// Total enrollments
	if err := r.db.Model(&models.Enrollment{}).Count(&stats.TotalEnrollments).Error; err != nil {
		return nil, fmt.Errorf("failed to count total enrollments: %w", err)
	}

	// Enrollments by status
	statuses := []models.EnrollmentStatus{
		models.EnrollmentStatusActive, models.EnrollmentStatusCompleted,
		models.EnrollmentStatusDropped, models.EnrollmentStatusSuspended,
		models.EnrollmentStatusPending,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Enrollment{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count enrollments by status %s: %w", status, err)
		}
		stats.EnrollmentsByStatus[status] = count
	}

	// Enrollments by payment status
	paymentStatuses := []string{"PENDING", "PARTIAL", "PAID", "OVERDUE"}
	for _, status := range paymentStatuses {
		var count int64
		if err := r.db.Model(&models.Enrollment{}).Where("payment_status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count enrollments by payment status %s: %w", status, err)
		}
		stats.EnrollmentsByPayment[status] = count
	}

	// Total revenue
	if err := r.db.Model(&models.Enrollment{}).Select("SUM(total_fee)").Scan(&stats.TotalRevenue).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total revenue: %w", err)
	}

	// Outstanding amount
	if err := r.db.Model(&models.Enrollment{}).Select("SUM(total_fee - paid_amount)").Scan(&stats.OutstandingAmount).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate outstanding amount: %w", err)
	}

	// Average grade
	var avgGrade float64
	if err := r.db.Model(&models.Enrollment{}).Where("final_grade IS NOT NULL").Select("AVG(final_grade)").Scan(&avgGrade).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average grade: %w", err)
	}
	if avgGrade > 0 {
		stats.AverageGrade = &avgGrade
	}

	// Average attendance
	var avgAttendance float64
	if err := r.db.Model(&models.Enrollment{}).Where("attendance_rate IS NOT NULL").Select("AVG(attendance_rate)").Scan(&avgAttendance).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average attendance: %w", err)
	}
	if avgAttendance > 0 {
		stats.AverageAttendance = &avgAttendance
	}

	// New enrollments today
	if err := r.db.Model(&models.Enrollment{}).Where("DATE(enrollment_date) = CURRENT_DATE").Count(&stats.NewEnrollmentsToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count new enrollments today: %w", err)
	}

	// New enrollments this week
	if err := r.db.Model(&models.Enrollment{}).Where("enrollment_date >= DATE_TRUNC('week', CURRENT_DATE)").Count(&stats.NewEnrollmentsThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count new enrollments this week: %w", err)
	}

	// Completion rate
	completedCount := stats.EnrollmentsByStatus[models.EnrollmentStatusCompleted]
	if stats.TotalEnrollments > 0 {
		stats.CompletionRate = float64(completedCount) / float64(stats.TotalEnrollments) * 100
	}

	// Dropout rate
	droppedCount := stats.EnrollmentsByStatus[models.EnrollmentStatusDropped]
	if stats.TotalEnrollments > 0 {
		stats.DropoutRate = float64(droppedCount) / float64(stats.TotalEnrollments) * 100
	}

	return stats, nil
}

// GetByStudent retrieves enrollments for a specific student
func (r *enrollmentRepository) GetByStudent(studentID uuid.UUID) ([]*models.Enrollment, error) {
	var enrollments []*models.Enrollment
	if err := r.db.Preload("Course").Preload("EnrolledBy").
		Where("student_id = ?", studentID).
		Order("enrollment_date DESC").Find(&enrollments).Error; err != nil {
		return nil, fmt.Errorf("failed to get enrollments by student: %w", err)
	}
	return enrollments, nil
}

// GetByCourse retrieves enrollments for a specific course
func (r *enrollmentRepository) GetByCourse(courseID uuid.UUID) ([]*models.Enrollment, error) {
	var enrollments []*models.Enrollment
	if err := r.db.Preload("Student").Preload("EnrolledBy").
		Where("course_id = ?", courseID).
		Order("enrollment_date DESC").Find(&enrollments).Error; err != nil {
		return nil, fmt.Errorf("failed to get enrollments by course: %w", err)
	}
	return enrollments, nil
}

// GetActiveEnrollments retrieves all active enrollments
func (r *enrollmentRepository) GetActiveEnrollments() ([]*models.Enrollment, error) {
	var enrollments []*models.Enrollment
	if err := r.db.Preload("Student").Preload("Course").Preload("EnrolledBy").
		Where("status = ?", models.EnrollmentStatusActive).
		Order("enrollment_date DESC").Find(&enrollments).Error; err != nil {
		return nil, fmt.Errorf("failed to get active enrollments: %w", err)
	}
	return enrollments, nil
}

// GetEnrollmentsByPaymentStatus retrieves enrollments by payment status
func (r *enrollmentRepository) GetEnrollmentsByPaymentStatus(status string) ([]*models.Enrollment, error) {
	var enrollments []*models.Enrollment
	if err := r.db.Preload("Student").Preload("Course").Preload("EnrolledBy").
		Where("payment_status = ?", status).
		Order("enrollment_date DESC").Find(&enrollments).Error; err != nil {
		return nil, fmt.Errorf("failed to get enrollments by payment status: %w", err)
	}
	return enrollments, nil
}

// GetOverduePayments retrieves enrollments with overdue payments
func (r *enrollmentRepository) GetOverduePayments() ([]*models.Enrollment, error) {
	var enrollments []*models.Enrollment
	if err := r.db.Preload("Student").Preload("Course").Preload("EnrolledBy").
		Where("payment_status IN ? AND total_fee > paid_amount", []string{"PENDING", "PARTIAL", "OVERDUE"}).
		Order("enrollment_date ASC").Find(&enrollments).Error; err != nil {
		return nil, fmt.Errorf("failed to get overdue payments: %w", err)
	}
	return enrollments, nil
}

// BulkUpdatePaymentStatus updates payment status for multiple enrollments
func (r *enrollmentRepository) BulkUpdatePaymentStatus(enrollmentIDs []uuid.UUID, status string, updatedBy uuid.UUID) error {
	updates := map[string]interface{}{
		"payment_status": status,
		"updated_at":     time.Now(),
	}

	result := r.db.Model(&models.Enrollment{}).Where("id IN ?", enrollmentIDs).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to bulk update payment status: %w", result.Error)
	}

	return nil
}

// GetRevenueByPeriod calculates revenue for a specific period
func (r *enrollmentRepository) GetRevenueByPeriod(startDate, endDate time.Time) (float64, error) {
	var revenue float64
	if err := r.db.Model(&models.Enrollment{}).
		Where("enrollment_date >= ? AND enrollment_date <= ?", startDate, endDate).
		Select("SUM(paid_amount)").Scan(&revenue).Error; err != nil {
		return 0, fmt.Errorf("failed to calculate revenue: %w", err)
	}
	return revenue, nil
}

// ValidateEnrollment checks if a student can enroll in a course
func (r *enrollmentRepository) ValidateEnrollment(studentID, courseID uuid.UUID) error {
	// Check if student is already enrolled in the course
	var existingEnrollment models.Enrollment
	if err := r.db.Where("student_id = ? AND course_id = ? AND status IN ?", 
		studentID, courseID, []models.EnrollmentStatus{models.EnrollmentStatusActive, models.EnrollmentStatusPending}).
		First(&existingEnrollment).Error; err == nil {
		return fmt.Errorf("student is already enrolled in this course")
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing enrollment: %w", err)
	}

	// Check course capacity
	var enrollmentCount int64
	if err := r.db.Model(&models.Enrollment{}).
		Where("course_id = ? AND status IN ?", courseID, 
			[]models.EnrollmentStatus{models.EnrollmentStatusActive, models.EnrollmentStatusPending}).
		Count(&enrollmentCount).Error; err != nil {
		return fmt.Errorf("failed to count course enrollments: %w", err)
	}

	var course models.Course
	if err := r.db.First(&course, "id = ?", courseID).Error; err != nil {
		return fmt.Errorf("failed to get course: %w", err)
	}

	if enrollmentCount >= int64(course.MaxStudents) {
		return fmt.Errorf("course is at maximum capacity")
	}

	return nil
}
