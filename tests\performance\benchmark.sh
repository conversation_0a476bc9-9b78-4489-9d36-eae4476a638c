#!/bin/bash

# 📊 Benchmark Testing Script for Go Docker Platform
# Measures baseline performance metrics for all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RESULTS_DIR="$SCRIPT_DIR/benchmark-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Service endpoints
AUTH_URL="http://localhost:8081"
ADMIN_URL="http://localhost:8082"
STAFF_URL="http://localhost:8083"
PAYMENT_URL="http://localhost:8084"
NOTIFICATION_URL="http://localhost:8085"
GATEWAY_URL="http://localhost:8080"

# Benchmark parameters
WARMUP_REQUESTS=100
BENCHMARK_REQUESTS=1000
CONCURRENT_USERS=10

# Performance targets
TARGET_RESPONSE_TIME_MS=200
TARGET_THROUGHPUT_RPS=100
TARGET_SUCCESS_RATE=99.5

# Utility functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "step")
            echo -e "\n${BLUE}🔄 $message${NC}"
            ;;
    esac
}

# Setup benchmark environment
setup_benchmark_environment() {
    print_status "step" "Setting up benchmark environment"
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Check if services are running
    local services_ready=true
    local services=("$AUTH_URL" "$ADMIN_URL" "$STAFF_URL" "$PAYMENT_URL" "$NOTIFICATION_URL")
    
    for service_url in "${services[@]}"; do
        if curl -s -f "$service_url/health" > /dev/null 2>&1; then
            print_status "success" "Service at $service_url is ready"
        else
            print_status "error" "Service at $service_url is not responding"
            services_ready=false
        fi
    done
    
    if [ "$services_ready" = false ]; then
        print_status "error" "Some services are not ready. Please start all services first."
        exit 1
    fi
    
    # Get authentication token
    get_auth_token
    
    # Warm up services
    warmup_services
}

# Get authentication token
get_auth_token() {
    print_status "info" "Obtaining authentication token for benchmarking"
    
    # Create benchmark user
    local user_data='{
        "email": "<EMAIL>",
        "username": "benchmark",
        "password": "Benchmark123!@#",
        "first_name": "Benchmark",
        "last_name": "Test",
        "phone": "+998901234567",
        "role": "ADMIN"
    }'
    
    # Try to register (ignore if user already exists)
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$user_data" \
        "$AUTH_URL/api/v1/auth/register" > /dev/null 2>&1 || true
    
    # Login to get token
    local login_data='{
        "email": "<EMAIL>",
        "password": "Benchmark123!@#"
    }'
    
    local response
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$AUTH_URL/api/v1/auth/login")
    
    AUTH_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$AUTH_TOKEN" ]; then
        print_status "success" "Authentication token obtained for benchmarking"
    else
        print_status "error" "Failed to obtain authentication token"
        exit 1
    fi
}

# Warm up services
warmup_services() {
    print_status "info" "Warming up services with $WARMUP_REQUESTS requests"
    
    local services=("$AUTH_URL" "$ADMIN_URL" "$STAFF_URL" "$PAYMENT_URL" "$NOTIFICATION_URL")
    
    for service_url in "${services[@]}"; do
        for ((i=1; i<=WARMUP_REQUESTS; i++)); do
            curl -s -o /dev/null "$service_url/health" &
        done
    done
    
    wait
    print_status "success" "Service warmup completed"
}

# Run benchmark for a specific endpoint
run_endpoint_benchmark() {
    local test_name=$1
    local url=$2
    local method=$3
    local headers=$4
    local data=$5
    
    print_status "step" "Benchmarking: $test_name"
    
    local result_file="$RESULTS_DIR/${test_name}_benchmark_${TIMESTAMP}.txt"
    local temp_dir="/tmp/benchmark_$$"
    mkdir -p "$temp_dir"
    
    # Record start time
    local start_time=$(date +%s%3N)
    
    # Run concurrent requests
    for ((i=1; i<=CONCURRENT_USERS; i++)); do
        (
            local requests_per_user=$((BENCHMARK_REQUESTS / CONCURRENT_USERS))
            
            for ((j=1; j<=requests_per_user; j++)); do
                local request_start=$(date +%s%3N)
                
                if [ "$method" = "GET" ]; then
                    local status_code=$(curl -s -o /dev/null -w "%{http_code}" -H "$headers" "$url")
                else
                    local status_code=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -H "$headers" -d "$data" "$url")
                fi
                
                local request_end=$(date +%s%3N)
                local response_time=$((request_end - request_start))
                
                echo "$i,$j,$status_code,$response_time" >> "$temp_dir/worker_$i.csv"
            done
        ) &
    done
    
    # Wait for all workers to complete
    wait
    
    # Record end time
    local end_time=$(date +%s%3N)
    local total_time=$((end_time - start_time))
    
    # Aggregate results
    local total_requests=0
    local successful_requests=0
    local total_response_time=0
    local min_response_time=999999
    local max_response_time=0
    local response_times=()
    
    for ((i=1; i<=CONCURRENT_USERS; i++)); do
        if [ -f "$temp_dir/worker_$i.csv" ]; then
            while IFS=',' read -r worker_id request_id status_code response_time; do
                ((total_requests++))
                
                if [ "$status_code" = "200" ] || [ "$status_code" = "201" ]; then
                    ((successful_requests++))
                fi
                
                total_response_time=$((total_response_time + response_time))
                response_times+=($response_time)
                
                if [ "$response_time" -lt "$min_response_time" ]; then
                    min_response_time=$response_time
                fi
                
                if [ "$response_time" -gt "$max_response_time" ]; then
                    max_response_time=$response_time
                fi
            done < "$temp_dir/worker_$i.csv"
        fi
    done
    
    # Calculate metrics
    local success_rate=0
    local avg_response_time=0
    local throughput=0
    
    if [ $total_requests -gt 0 ]; then
        success_rate=$(echo "scale=2; $successful_requests * 100 / $total_requests" | bc)
        avg_response_time=$((total_response_time / total_requests))
        throughput=$(echo "scale=2; $total_requests * 1000 / $total_time" | bc)
    fi
    
    # Calculate percentiles
    IFS=$'\n' sorted_times=($(sort -n <<<"${response_times[*]}"))
    local p50_index=$(( ${#sorted_times[@]} * 50 / 100 ))
    local p95_index=$(( ${#sorted_times[@]} * 95 / 100 ))
    local p99_index=$(( ${#sorted_times[@]} * 99 / 100 ))
    
    local p50_response_time=${sorted_times[$p50_index]}
    local p95_response_time=${sorted_times[$p95_index]}
    local p99_response_time=${sorted_times[$p99_index]}
    
    # Save results
    cat > "$result_file" << EOF
Benchmark Results: $test_name
Timestamp: $(date)
URL: $url
Method: $method

Test Configuration:
- Total Requests: $total_requests
- Concurrent Users: $CONCURRENT_USERS
- Test Duration: ${total_time}ms

Performance Metrics:
- Success Rate: $success_rate%
- Throughput: $throughput requests/second
- Average Response Time: ${avg_response_time}ms
- Min Response Time: ${min_response_time}ms
- Max Response Time: ${max_response_time}ms
- 50th Percentile: ${p50_response_time}ms
- 95th Percentile: ${p95_response_time}ms
- 99th Percentile: ${p99_response_time}ms

Performance Targets:
- Target Response Time: ${TARGET_RESPONSE_TIME_MS}ms
- Target Throughput: ${TARGET_THROUGHPUT_RPS} RPS
- Target Success Rate: ${TARGET_SUCCESS_RATE}%

Results:
EOF

    # Check against targets
    if (( $(echo "$success_rate >= $TARGET_SUCCESS_RATE" | bc -l) )); then
        echo "✅ Success Rate: PASS ($success_rate% >= $TARGET_SUCCESS_RATE%)" >> "$result_file"
    else
        echo "❌ Success Rate: FAIL ($success_rate% < $TARGET_SUCCESS_RATE%)" >> "$result_file"
    fi
    
    if (( $(echo "$avg_response_time <= $TARGET_RESPONSE_TIME_MS" | bc -l) )); then
        echo "✅ Response Time: PASS (${avg_response_time}ms <= ${TARGET_RESPONSE_TIME_MS}ms)" >> "$result_file"
    else
        echo "❌ Response Time: FAIL (${avg_response_time}ms > ${TARGET_RESPONSE_TIME_MS}ms)" >> "$result_file"
    fi
    
    if (( $(echo "$throughput >= $TARGET_THROUGHPUT_RPS" | bc -l) )); then
        echo "✅ Throughput: PASS ($throughput RPS >= $TARGET_THROUGHPUT_RPS RPS)" >> "$result_file"
    else
        echo "❌ Throughput: FAIL ($throughput RPS < $TARGET_THROUGHPUT_RPS RPS)" >> "$result_file"
    fi
    
    # Display summary
    print_status "info" "Benchmark Results for $test_name:"
    echo "   Success Rate: $success_rate%"
    echo "   Throughput: $throughput RPS"
    echo "   Avg Response Time: ${avg_response_time}ms"
    echo "   95th Percentile: ${p95_response_time}ms"
    
    # Cleanup
    rm -rf "$temp_dir"
}

# Run all benchmarks
run_all_benchmarks() {
    print_status "step" "Running comprehensive benchmarks"
    
    local auth_header="Authorization: Bearer $AUTH_TOKEN"
    
    # Health endpoint benchmarks
    run_endpoint_benchmark "auth_health" "$AUTH_URL/health" "GET" "" ""
    run_endpoint_benchmark "admin_health" "$ADMIN_URL/health" "GET" "" ""
    run_endpoint_benchmark "staff_health" "$STAFF_URL/health" "GET" "" ""
    run_endpoint_benchmark "payment_health" "$PAYMENT_URL/health" "GET" "" ""
    run_endpoint_benchmark "notification_health" "$NOTIFICATION_URL/health" "GET" "" ""
    
    # Authentication benchmarks
    local login_data='{"email": "<EMAIL>", "password": "Benchmark123!@#"}'
    run_endpoint_benchmark "auth_login" "$AUTH_URL/api/v1/auth/login" "POST" "" "$login_data"
    run_endpoint_benchmark "auth_validate" "$AUTH_URL/api/v1/auth/validate" "GET" "$auth_header" ""
    
    # Admin service benchmarks
    run_endpoint_benchmark "admin_users_list" "$ADMIN_URL/api/v1/users" "GET" "$auth_header" ""
    run_endpoint_benchmark "admin_analytics" "$ADMIN_URL/api/v1/analytics/dashboard" "GET" "$auth_header" ""
    
    # Staff service benchmarks
    run_endpoint_benchmark "staff_students_list" "$STAFF_URL/api/v1/students" "GET" "$auth_header" ""
    run_endpoint_benchmark "staff_courses_list" "$STAFF_URL/api/v1/courses" "GET" "$auth_header" ""
    
    # Payment service benchmarks
    run_endpoint_benchmark "payment_methods_list" "$PAYMENT_URL/api/v1/payment-methods" "GET" "$auth_header" ""
    run_endpoint_benchmark "payment_transactions_list" "$PAYMENT_URL/api/v1/transactions" "GET" "$auth_header" ""
}

# Generate benchmark report
generate_benchmark_report() {
    print_status "step" "Generating benchmark report"
    
    local report_file="$RESULTS_DIR/benchmark_report_${TIMESTAMP}.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Go Docker Platform - Benchmark Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Go Docker Platform - Benchmark Report</h1>
        <p>Generated on: $(date)</p>
        <p>Total Requests per Test: $BENCHMARK_REQUESTS</p>
        <p>Concurrent Users: $CONCURRENT_USERS</p>
    </div>
    
    <div class="section">
        <h2>Performance Targets</h2>
        <ul>
            <li>Response Time: &lt; ${TARGET_RESPONSE_TIME_MS}ms</li>
            <li>Throughput: &gt; ${TARGET_THROUGHPUT_RPS} RPS</li>
            <li>Success Rate: &gt; ${TARGET_SUCCESS_RATE}%</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Benchmark Results</h2>
        <p>Detailed results are available in individual benchmark files.</p>
        
        <h3>Test Files</h3>
        <ul>
EOF

    # List all result files
    for file in "$RESULTS_DIR"/*_benchmark_${TIMESTAMP}.txt; do
        if [ -f "$file" ]; then
            local filename=$(basename "$file")
            echo "            <li><a href=\"$filename\">$filename</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
</body>
</html>
EOF
    
    print_status "success" "Benchmark report generated: $report_file"
}

# Main execution
main() {
    print_status "step" "Starting Benchmark Testing for Go Docker Platform"
    
    # Setup
    setup_benchmark_environment
    
    # Run benchmarks
    run_all_benchmarks
    
    # Generate report
    generate_benchmark_report
    
    print_status "success" "Benchmark testing completed!"
    echo "📁 Results available in: $RESULTS_DIR"
}

# Run main function
main "$@"
