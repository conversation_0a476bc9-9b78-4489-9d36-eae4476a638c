package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// LoggingMiddleware provides structured logging for requests
type LoggingMiddleware struct {
	skipPaths []string
}

// NewLoggingMiddleware creates a new logging middleware
func NewLoggingMiddleware() *LoggingMiddleware {
	return &LoggingMiddleware{
		skipPaths: []string{"/health", "/ready", "/live"}, // Skip health check endpoints
	}
}

// <PERSON>gger returns the logging middleware handler
func (lm *LoggingMiddleware) Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// Skip logging for certain paths
		for _, path := range lm.skipPaths {
			if param.Path == path {
				return ""
			}
		}

		// Get user information if available
		userID := param.Keys["user_id"]
		userRole := param.Keys["user_role"]
		correlationID := param.Keys["correlation_id"]

		// Format log entry
		return fmt.Sprintf(`{"time":"%s","method":"%s","path":"%s","status":%d,"latency":"%s","ip":"%s","user_agent":"%s","user_id":"%v","user_role":"%v","correlation_id":"%v","error":"%s"}%s`,
			param.TimeStamp.Format(time.RFC3339),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			param.Request.UserAgent(),
			userID,
			userRole,
			correlationID,
			param.ErrorMessage,
			"\n",
		)
	})
}

// SetSkipPaths sets paths to skip logging
func (lm *LoggingMiddleware) SetSkipPaths(paths []string) {
	lm.skipPaths = paths
}

// RequestLoggingMiddleware provides detailed request/response logging
type RequestLoggingMiddleware struct {
	logBody bool
}

// NewRequestLoggingMiddleware creates a new request logging middleware
func NewRequestLoggingMiddleware(logBody bool) *RequestLoggingMiddleware {
	return &RequestLoggingMiddleware{
		logBody: logBody,
	}
}

// Handler returns the request logging middleware handler
func (rlm *RequestLoggingMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Get correlation ID
		correlationID := c.GetHeader("X-Correlation-ID")
		if correlationID == "" {
			correlationID = generateCorrelationID()
			c.Header("X-Correlation-ID", correlationID)
		}

		// Store correlation ID in context for logging
		c.Set("correlation_id", correlationID)

		// Log request
		fmt.Printf(`{"level":"info","time":"%s","msg":"request_started","method":"%s","path":"%s","ip":"%s","correlation_id":"%s"}%s`,
			start.Format(time.RFC3339),
			c.Request.Method,
			c.Request.URL.Path,
			c.ClientIP(),
			correlationID,
			"\n",
		)

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get user information if available
		userID, _ := c.Get("user_id")
		userRole, _ := c.Get("user_role")

		// Log response
		fmt.Printf(`{"level":"info","time":"%s","msg":"request_completed","method":"%s","path":"%s","status":%d,"latency":"%s","ip":"%s","user_id":"%v","user_role":"%v","correlation_id":"%s"}%s`,
			time.Now().Format(time.RFC3339),
			c.Request.Method,
			c.Request.URL.Path,
			c.Writer.Status(),
			latency,
			c.ClientIP(),
			userID,
			userRole,
			correlationID,
			"\n",
		)
	}
}

// generateCorrelationID generates a correlation ID for request tracing
func generateCorrelationID() string {
	return fmt.Sprintf("gw-%d", time.Now().UnixNano())
}

// ErrorLoggingMiddleware provides error logging
type ErrorLoggingMiddleware struct{}

// NewErrorLoggingMiddleware creates a new error logging middleware
func NewErrorLoggingMiddleware() *ErrorLoggingMiddleware {
	return &ErrorLoggingMiddleware{}
}

// Handler returns the error logging middleware handler
func (elm *ErrorLoggingMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Log any errors that occurred
		if len(c.Errors) > 0 {
			correlationID, _ := c.Get("correlation_id")
			userID, _ := c.Get("user_id")

			for _, err := range c.Errors {
				fmt.Printf(`{"level":"error","time":"%s","msg":"request_error","method":"%s","path":"%s","error":"%s","user_id":"%v","correlation_id":"%v"}%s`,
					time.Now().Format(time.RFC3339),
					c.Request.Method,
					c.Request.URL.Path,
					err.Error(),
					userID,
					correlationID,
					"\n",
				)
			}
		}
	}
}

// MetricsMiddleware provides basic metrics collection
type MetricsMiddleware struct {
	requestCount   map[string]int
	responseTime   map[string]time.Duration
	statusCodes    map[int]int
}

// NewMetricsMiddleware creates a new metrics middleware
func NewMetricsMiddleware() *MetricsMiddleware {
	return &MetricsMiddleware{
		requestCount: make(map[string]int),
		responseTime: make(map[string]time.Duration),
		statusCodes:  make(map[int]int),
	}
}

// Handler returns the metrics middleware handler
func (mm *MetricsMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		// Record metrics
		path := c.Request.URL.Path
		method := c.Request.Method
		status := c.Writer.Status()
		latency := time.Since(start)

		// Update counters
		key := fmt.Sprintf("%s %s", method, path)
		mm.requestCount[key]++
		mm.responseTime[key] = latency
		mm.statusCodes[status]++
	}
}

// GetMetrics returns collected metrics
func (mm *MetricsMiddleware) GetMetrics() map[string]interface{} {
	return map[string]interface{}{
		"request_count":  mm.requestCount,
		"response_times": mm.responseTime,
		"status_codes":   mm.statusCodes,
	}
}
