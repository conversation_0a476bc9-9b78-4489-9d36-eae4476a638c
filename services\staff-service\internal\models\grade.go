package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// GradeType represents the type of grade/assessment
type GradeType string

const (
	GradeTypeAssignment GradeType = "ASSIGNMENT"
	GradeTypeQuiz       GradeType = "QUIZ"
	GradeTypeExam       GradeType = "EXAM"
	GradeTypeMidterm    GradeType = "MIDTERM"
	GradeTypeFinal      GradeType = "FINAL"
	GradeTypeProject    GradeType = "PROJECT"
	GradeTypeParticipation GradeType = "PARTICIPATION"
	GradeTypeOther      GradeType = "OTHER"
)

// IsValid checks if the grade type is valid
func (gt GradeType) IsValid() bool {
	switch gt {
	case GradeTypeAssignment, GradeTypeQuiz, GradeTypeExam, GradeTypeMidterm,
		 GradeTypeFinal, GradeTypeProject, GradeTypeParticipation, GradeTypeOther:
		return true
	}
	return false
}

// Grade represents a student's grade for an assessment
type Grade struct {
	models.BaseModel
	StudentID        uuid.UUID `json:"student_id" gorm:"not null;index"`
	Student          *Student  `json:"student,omitempty" gorm:"foreignKey:StudentID"`
	CourseID         uuid.UUID `json:"course_id" gorm:"not null;index"`
	Course           *Course   `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	
	// Assessment details
	AssessmentName   string    `json:"assessment_name" gorm:"not null;size:200"`
	AssessmentType   GradeType `json:"assessment_type" gorm:"not null;index"`
	Description      string    `json:"description" gorm:"type:text"`
	
	// Grade information
	Score            float64   `json:"score" gorm:"not null"` // Actual score achieved
	MaxScore         float64   `json:"max_score" gorm:"not null"` // Maximum possible score
	Percentage       float64   `json:"percentage" gorm:"not null"` // Score as percentage (0-100)
	LetterGrade      string    `json:"letter_grade" gorm:"size:5"` // A, B, C, D, F, etc.
	GradePoints      *float64  `json:"grade_points"` // GPA points (4.0 scale)
	
	// Weight and importance
	Weight           float64   `json:"weight" gorm:"not null;default:1.0"` // Weight in final grade calculation
	IsExtraCredit    bool      `json:"is_extra_credit" gorm:"default:false"`
	
	// Dates
	AssessmentDate   time.Time `json:"assessment_date" gorm:"not null;index"`
	DueDate          *time.Time `json:"due_date"`
	SubmittedAt      *time.Time `json:"submitted_at"`
	GradedAt         time.Time `json:"graded_at" gorm:"autoCreateTime"`
	
	// Feedback and comments
	Comments         string    `json:"comments" gorm:"type:text"`
	PrivateNotes     string    `json:"private_notes" gorm:"type:text"` // Internal notes, not visible to student
	
	// Metadata (no foreign key constraint - references auth service)
	GradedByUserID   uuid.UUID    `json:"graded_by_id" gorm:"column:graded_by_id;not null"`
	GradedBy         *models.User `json:"graded_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
}

// GetLetterGrade calculates letter grade based on percentage
func (g *Grade) GetLetterGrade() string {
	if g.LetterGrade != "" {
		return g.LetterGrade
	}
	
	// Standard grading scale
	switch {
	case g.Percentage >= 90:
		return "A"
	case g.Percentage >= 80:
		return "B"
	case g.Percentage >= 70:
		return "C"
	case g.Percentage >= 60:
		return "D"
	default:
		return "F"
	}
}

// GetGradePoints calculates GPA points based on letter grade
func (g *Grade) GetGradePoints() float64 {
	if g.GradePoints != nil {
		return *g.GradePoints
	}
	
	// Standard 4.0 scale
	switch g.GetLetterGrade() {
	case "A":
		return 4.0
	case "B":
		return 3.0
	case "C":
		return 2.0
	case "D":
		return 1.0
	default:
		return 0.0
	}
}

// IsLate checks if the assessment was submitted late
func (g *Grade) IsLate() bool {
	return g.DueDate != nil && g.SubmittedAt != nil && g.SubmittedAt.After(*g.DueDate)
}

// GradeCreateRequest represents a request to create a grade
type GradeCreateRequest struct {
	StudentID        uuid.UUID  `json:"student_id" binding:"required"`
	CourseID         uuid.UUID  `json:"course_id" binding:"required"`
	AssessmentName   string     `json:"assessment_name" binding:"required,min=2,max=200"`
	AssessmentType   GradeType  `json:"assessment_type" binding:"required"`
	Description      string     `json:"description"`
	Score            float64    `json:"score" binding:"required,min=0"`
	MaxScore         float64    `json:"max_score" binding:"required,min=0"`
	LetterGrade      string     `json:"letter_grade" binding:"omitempty,max=5"`
	GradePoints      *float64   `json:"grade_points" binding:"omitempty,min=0,max=4"`
	Weight           float64    `json:"weight" binding:"min=0"`
	IsExtraCredit    bool       `json:"is_extra_credit"`
	AssessmentDate   time.Time  `json:"assessment_date" binding:"required"`
	DueDate          *time.Time `json:"due_date"`
	SubmittedAt      *time.Time `json:"submitted_at"`
	Comments         string     `json:"comments"`
	PrivateNotes     string     `json:"private_notes"`
}

// GradeUpdateRequest represents a request to update a grade
type GradeUpdateRequest struct {
	AssessmentName   *string    `json:"assessment_name" binding:"omitempty,min=2,max=200"`
	AssessmentType   *GradeType `json:"assessment_type"`
	Description      *string    `json:"description"`
	Score            *float64   `json:"score" binding:"omitempty,min=0"`
	MaxScore         *float64   `json:"max_score" binding:"omitempty,min=0"`
	LetterGrade      *string    `json:"letter_grade" binding:"omitempty,max=5"`
	GradePoints      *float64   `json:"grade_points" binding:"omitempty,min=0,max=4"`
	Weight           *float64   `json:"weight" binding:"omitempty,min=0"`
	IsExtraCredit    *bool      `json:"is_extra_credit"`
	AssessmentDate   *time.Time `json:"assessment_date"`
	DueDate          *time.Time `json:"due_date"`
	SubmittedAt      *time.Time `json:"submitted_at"`
	Comments         *string    `json:"comments"`
	PrivateNotes     *string    `json:"private_notes"`
}

// GradeBulkCreateRequest represents a request to create multiple grades
type GradeBulkCreateRequest struct {
	CourseID       uuid.UUID           `json:"course_id" binding:"required"`
	AssessmentName string              `json:"assessment_name" binding:"required,min=2,max=200"`
	AssessmentType GradeType           `json:"assessment_type" binding:"required"`
	Description    string              `json:"description"`
	MaxScore       float64             `json:"max_score" binding:"required,min=0"`
	Weight         float64             `json:"weight" binding:"min=0"`
	AssessmentDate time.Time           `json:"assessment_date" binding:"required"`
	DueDate        *time.Time          `json:"due_date"`
	Grades         []GradeRecordRequest `json:"grades" binding:"required,min=1"`
}

// GradeRecordRequest represents individual grade record in bulk request
type GradeRecordRequest struct {
	StudentID     uuid.UUID  `json:"student_id" binding:"required"`
	Score         float64    `json:"score" binding:"required,min=0"`
	LetterGrade   string     `json:"letter_grade" binding:"omitempty,max=5"`
	SubmittedAt   *time.Time `json:"submitted_at"`
	Comments      string     `json:"comments"`
	PrivateNotes  string     `json:"private_notes"`
}

// GradeResponse represents a grade response
type GradeResponse struct {
	ID             uuid.UUID `json:"id"`
	StudentID      uuid.UUID `json:"student_id"`
	StudentName    string    `json:"student_name"`
	StudentEmail   string    `json:"student_email"`
	CourseID       uuid.UUID `json:"course_id"`
	CourseName     string    `json:"course_name"`
	CourseCode     string    `json:"course_code"`
	AssessmentName string    `json:"assessment_name"`
	AssessmentType GradeType `json:"assessment_type"`
	Description    string    `json:"description"`
	Score          float64   `json:"score"`
	MaxScore       float64   `json:"max_score"`
	Percentage     float64   `json:"percentage"`
	LetterGrade    string    `json:"letter_grade"`
	GradePoints    float64   `json:"grade_points"`
	Weight         float64   `json:"weight"`
	IsExtraCredit  bool      `json:"is_extra_credit"`
	IsLate         bool      `json:"is_late"`
	AssessmentDate time.Time `json:"assessment_date"`
	DueDate        *time.Time `json:"due_date"`
	SubmittedAt    *time.Time `json:"submitted_at"`
	GradedAt       time.Time `json:"graded_at"`
	Comments       string    `json:"comments"`
	PrivateNotes   string    `json:"private_notes"`
	GradedByID     uuid.UUID `json:"graded_by_id"`
	GradedByName   string    `json:"graded_by_name"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ToResponse converts a Grade to GradeResponse
func (g *Grade) ToResponse() *GradeResponse {
	response := &GradeResponse{
		ID:             g.ID,
		StudentID:      g.StudentID,
		CourseID:       g.CourseID,
		AssessmentName: g.AssessmentName,
		AssessmentType: g.AssessmentType,
		Description:    g.Description,
		Score:          g.Score,
		MaxScore:       g.MaxScore,
		Percentage:     g.Percentage,
		LetterGrade:    g.GetLetterGrade(),
		GradePoints:    g.GetGradePoints(),
		Weight:         g.Weight,
		IsExtraCredit:  g.IsExtraCredit,
		IsLate:         g.IsLate(),
		AssessmentDate: g.AssessmentDate,
		DueDate:        g.DueDate,
		SubmittedAt:    g.SubmittedAt,
		GradedAt:       g.GradedAt,
		Comments:       g.Comments,
		PrivateNotes:   g.PrivateNotes,
		GradedByID:     g.GradedByUserID,
		CreatedAt:      g.CreatedAt,
		UpdatedAt:      g.UpdatedAt,
	}

	// Include student information if available
	if g.Student != nil {
		response.StudentName = g.Student.GetFullName()
		response.StudentEmail = g.Student.Email
	}

	// Include course information if available
	if g.Course != nil {
		response.CourseName = g.Course.Name
		response.CourseCode = g.Course.CourseCode
	}

	// Include graded by user information if available
	if g.GradedBy != nil {
		response.GradedByName = g.GradedBy.GetFullName()
	}

	return response
}

// GradeListRequest represents a request to list grades
type GradeListRequest struct {
	models.PaginationRequest
	StudentID      *uuid.UUID `json:"student_id" form:"student_id"`
	CourseID       *uuid.UUID `json:"course_id" form:"course_id"`
	AssessmentType *GradeType `json:"assessment_type" form:"assessment_type"`
	GradedByID     *uuid.UUID `json:"graded_by_id" form:"graded_by_id"`
	StartDate      *time.Time `json:"start_date" form:"start_date"`
	EndDate        *time.Time `json:"end_date" form:"end_date"`
	Search         string     `json:"search" form:"search"`
	MinScore       *float64   `json:"min_score" form:"min_score"`
	MaxScore       *float64   `json:"max_score" form:"max_score"`
}

// GradeListResponse represents a response for grade list
type GradeListResponse struct {
	Grades     []*GradeResponse           `json:"grades"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// GradeStats represents grade statistics
type GradeStats struct {
	TotalGrades        int64                 `json:"total_grades"`
	GradesByType       map[GradeType]int64   `json:"grades_by_type"`
	GradesByLetter     map[string]int64      `json:"grades_by_letter"`
	AverageScore       float64               `json:"average_score"`
	AveragePercentage  float64               `json:"average_percentage"`
	AverageGPA         float64               `json:"average_gpa"`
	HighestScore       float64               `json:"highest_score"`
	LowestScore        float64               `json:"lowest_score"`
	PassingRate        float64               `json:"passing_rate"`
}

// StudentGradeSummary represents grade summary for a student
type StudentGradeSummary struct {
	StudentID         uuid.UUID `json:"student_id"`
	StudentName       string    `json:"student_name"`
	CourseID          uuid.UUID `json:"course_id"`
	CourseName        string    `json:"course_name"`
	TotalAssessments  int64     `json:"total_assessments"`
	AverageScore      float64   `json:"average_score"`
	AveragePercentage float64   `json:"average_percentage"`
	CurrentGPA        float64   `json:"current_gpa"`
	LetterGrade       string    `json:"letter_grade"`
	LastAssessment    *time.Time `json:"last_assessment"`
}
