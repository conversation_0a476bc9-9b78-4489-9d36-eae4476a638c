# 🚀 CRM Frontend - React Application

Professional React frontend for the Go Docker Platform CRM system, built with modern technologies and best practices.

## ✨ Features

- **🔐 Authentication & Authorization** - JWT-based auth with role-based access control
- **📱 Responsive Design** - Mobile-first design with Tailwind CSS
- **⚡ Performance Optimized** - Code splitting, lazy loading, and optimized bundles
- **🎨 Modern UI/UX** - Clean, professional interface with smooth animations
- **🔄 Real-time Updates** - React Query for efficient data fetching and caching
- **🛡️ Type Safety** - Full TypeScript implementation
- **🧪 Testing Ready** - Vitest setup for unit and integration tests
- **📊 Analytics Ready** - Built-in support for analytics and monitoring

## 🛠️ Technology Stack

### Core Technologies
- **React 18** - Latest React with concurrent features
- **TypeScript** - Full type safety and better developer experience
- **Vite** - Fast build tool and development server
- **React Router v6** - Modern routing with data loading
- **Zustand** - Lightweight state management
- **React Query** - Server state management and caching

### UI & Styling
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **Heroicons** - Beautiful hand-crafted SVG icons
- **Framer Motion** - Production-ready motion library
- **React Hot Toast** - Beautiful notifications

### Development Tools
- **ESLint** - Code linting and formatting
- **Prettier** - Code formatting
- **Vitest** - Fast unit testing framework
- **React Hook Form** - Performant forms with easy validation

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm 9+

### Installation

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Start development server
npm run dev
```

The application will be available at `http://localhost:3000`

### Default Login Credentials
- **Email**: <EMAIL>
- **Password**: admin123

## 📁 Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── auth/         # Authentication components
│   │   ├── layout/       # Layout components (Header, Sidebar)
│   │   └── ui/           # Generic UI components
│   ├── pages/            # Page components
│   │   ├── auth/         # Authentication pages
│   │   ├── users/        # User management pages
│   │   ├── students/     # Student management pages
│   │   ├── leads/        # Lead management pages
│   │   ├── courses/      # Course management pages
│   │   ├── payments/     # Payment management pages
│   │   └── notifications/ # Notification pages
│   ├── services/         # API services and HTTP client
│   ├── store/            # Zustand stores
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│   ├── hooks/            # Custom React hooks
│   └── assets/           # Images, fonts, etc.
├── index.html            # HTML template
├── package.json          # Dependencies and scripts
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
└── vite.config.ts        # Vite configuration
```

## 🔐 Authentication & Authorization

The frontend implements comprehensive authentication and authorization:

### Authentication Flow
1. **Login** - JWT token-based authentication
2. **Token Storage** - Secure token storage in localStorage
3. **Auto Refresh** - Automatic token refresh before expiration
4. **Route Protection** - Protected routes with authentication checks

### Role-Based Access Control
- **ADMIN** - Full system access
- **CASHIER** - User and payment management
- **RECEPTION** - Student and lead management
- **TEACHER** - Course and student management
- **MANAGER** - Comprehensive staff operations
- **ACADEMIC_MANAGER** - Academic operations and oversight

### Protected Routes
Routes are automatically protected based on user roles:
```typescript
<ProtectedRoute requiredRoles={['ADMIN', 'CASHIER']}>
  <UsersPage />
</ProtectedRoute>
```

## 🎨 UI Components

### Design System
- **Consistent Colors** - Primary, secondary, success, warning, error palettes
- **Typography** - Inter font family with consistent sizing
- **Spacing** - Tailwind's spacing scale for consistency
- **Components** - Reusable button, input, card, and table components

### Responsive Design
- **Mobile First** - Designed for mobile devices first
- **Breakpoints** - sm (640px), md (768px), lg (1024px), xl (1280px)
- **Flexible Layouts** - CSS Grid and Flexbox for responsive layouts

## 📊 State Management

### Authentication State (Zustand)
```typescript
const { user, isAuthenticated, login, logout } = useAuth()
```

### Server State (React Query)
```typescript
const { data, isLoading, error } = useQuery('users', fetchUsers)
```

### Form State (React Hook Form)
```typescript
const { register, handleSubmit, formState: { errors } } = useForm()
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint errors
npm run type-check      # TypeScript type checking

# Testing
npm run test            # Run tests
npm run test:ui         # Run tests with UI
npm run test:coverage   # Run tests with coverage
```

### Environment Variables

```bash
# API Configuration
VITE_API_URL=https://crm-api-gateway.onrender.com
VITE_API_VERSION=v1

# Application
VITE_APP_NAME=CRM Dashboard
VITE_APP_VERSION=1.0.0

# Features
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true
```

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Render Deployment
The application is configured for deployment on Render platform:

1. **Build Command**: `npm run build`
2. **Start Command**: `npm run preview`
3. **Environment Variables**: Configure in Render dashboard

### Performance Optimizations
- **Code Splitting** - Automatic route-based code splitting
- **Tree Shaking** - Remove unused code
- **Asset Optimization** - Optimized images and fonts
- **Caching** - Efficient caching strategies

## 🧪 Testing

### Testing Strategy
- **Unit Tests** - Component and utility function tests
- **Integration Tests** - API integration and user flow tests
- **E2E Tests** - Full application workflow tests

### Running Tests
```bash
npm run test            # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Generate coverage report
```

## 📱 Mobile Compatibility

### React Native Preparation
The codebase is structured to facilitate future React Native development:

- **Shared Types** - TypeScript types can be reused
- **API Services** - HTTP client can be adapted for React Native
- **State Management** - Zustand works seamlessly with React Native
- **Component Structure** - Logical separation for easy adaptation

### Mobile Features
- **Touch Optimized** - Touch-friendly interface elements
- **Responsive Navigation** - Mobile-first navigation patterns
- **Performance** - Optimized for mobile performance

## 🔍 Monitoring & Analytics

### Error Tracking
- **Error Boundaries** - React error boundaries for graceful error handling
- **API Error Handling** - Comprehensive API error handling and user feedback

### Performance Monitoring
- **Bundle Analysis** - Webpack bundle analyzer for optimization
- **Core Web Vitals** - Performance metrics tracking
- **User Experience** - Loading states and smooth transitions

## 🤝 Contributing

### Development Workflow
1. **Fork** the repository
2. **Create** a feature branch
3. **Implement** changes with tests
4. **Run** linting and tests
5. **Submit** a pull request

### Code Standards
- **TypeScript** - Strict type checking enabled
- **ESLint** - Consistent code style
- **Prettier** - Automatic code formatting
- **Conventional Commits** - Standardized commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## 🆘 Support

- **Documentation** - Check the inline code documentation
- **Issues** - Report bugs via GitHub Issues
- **API Documentation** - See [API_DOCUMENTATION.md](../docs/API_DOCUMENTATION.md)
- **Backend Repository** - [Go Docker Platform](https://github.com/MrFarrukhT/Go-Docker-Platform)

---

**Built with ❤️ using React, TypeScript, and modern web technologies**
