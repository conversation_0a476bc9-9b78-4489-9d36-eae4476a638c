package registry

import (
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"
)

// ServiceInfo holds information about a registered service
type ServiceInfo struct {
	Name      string        `json:"name"`
	URL       string        `json:"url"`
	Health    string        `json:"health"`
	Priority  int           `json:"priority"`
	Timeout   time.Duration `json:"timeout"`
	Healthy   bool          `json:"healthy"`
	LastCheck time.Time     `json:"last_check"`
}

// ServiceRegistry manages service discovery and health checking
type ServiceRegistry struct {
	services map[string]*ServiceInfo
	mutex    sync.RWMutex
	client   *http.Client
}

// NewServiceRegistry creates a new service registry
func NewServiceRegistry() *ServiceRegistry {
	registry := &ServiceRegistry{
		services: make(map[string]*ServiceInfo),
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}

	// Start health checking goroutine
	go registry.startHealthChecking()

	return registry
}

// RegisterService registers a new service
func (sr *ServiceRegistry) RegisterService(name string, info *ServiceInfo) {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	info.Healthy = true // Assume healthy initially
	info.LastCheck = time.Now()
	sr.services[name] = info
}

// UnregisterService removes a service from the registry
func (sr *ServiceRegistry) UnregisterService(name string) {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	delete(sr.services, name)
}

// GetService returns service information by name
func (sr *ServiceRegistry) GetService(name string) (*ServiceInfo, error) {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	service, exists := sr.services[name]
	if !exists {
		return nil, fmt.Errorf("service '%s' not found", name)
	}

	if !service.Healthy {
		return nil, fmt.Errorf("service '%s' is unhealthy", name)
	}

	return service, nil
}

// GetAllServices returns all registered services
func (sr *ServiceRegistry) GetAllServices() map[string]*ServiceInfo {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	// Create a copy to avoid race conditions
	services := make(map[string]*ServiceInfo)
	for name, info := range sr.services {
		services[name] = &ServiceInfo{
			Name:      info.Name,
			URL:       info.URL,
			Health:    info.Health,
			Priority:  info.Priority,
			Timeout:   info.Timeout,
			Healthy:   info.Healthy,
			LastCheck: info.LastCheck,
		}
	}

	return services
}

// GetHealthyServices returns only healthy services
func (sr *ServiceRegistry) GetHealthyServices() map[string]*ServiceInfo {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	healthyServices := make(map[string]*ServiceInfo)
	for name, info := range sr.services {
		if info.Healthy {
			healthyServices[name] = &ServiceInfo{
				Name:      info.Name,
				URL:       info.URL,
				Health:    info.Health,
				Priority:  info.Priority,
				Timeout:   info.Timeout,
				Healthy:   info.Healthy,
				LastCheck: info.LastCheck,
			}
		}
	}

	return healthyServices
}

// CheckServiceHealth checks the health of a specific service
func (sr *ServiceRegistry) CheckServiceHealth(name string) error {
	sr.mutex.RLock()
	service, exists := sr.services[name]
	if !exists {
		sr.mutex.RUnlock()
		return fmt.Errorf("service '%s' not found", name)
	}

	healthURL := service.Health
	timeout := service.Timeout
	sr.mutex.RUnlock()

	// Create a client with the service-specific timeout
	client := &http.Client{Timeout: timeout}

	log.Printf("Checking health for service '%s' at URL: %s", name, healthURL)

	resp, err := client.Get(healthURL)
	if err != nil {
		log.Printf("Health check failed for service '%s': %v", name, err)
		sr.updateServiceHealth(name, false)
		return fmt.Errorf("health check failed for service '%s': %w", name, err)
	}
	defer resp.Body.Close()

	healthy := resp.StatusCode == http.StatusOK
	sr.updateServiceHealth(name, healthy)

	if healthy {
		log.Printf("Service '%s' is healthy (status: %d)", name, resp.StatusCode)
	} else {
		log.Printf("Service '%s' is unhealthy (status: %d)", name, resp.StatusCode)
		return fmt.Errorf("service '%s' returned unhealthy status: %d", name, resp.StatusCode)
	}

	return nil
}

// updateServiceHealth updates the health status of a service
func (sr *ServiceRegistry) updateServiceHealth(name string, healthy bool) {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	if service, exists := sr.services[name]; exists {
		service.Healthy = healthy
		service.LastCheck = time.Now()
	}
}

// startHealthChecking starts the background health checking process
func (sr *ServiceRegistry) startHealthChecking() {
	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sr.performHealthChecks()
		}
	}
}

// performHealthChecks performs health checks on all registered services
func (sr *ServiceRegistry) performHealthChecks() {
	sr.mutex.RLock()
	services := make(map[string]*ServiceInfo)
	for name, info := range sr.services {
		services[name] = info
	}
	sr.mutex.RUnlock()

	for name := range services {
		go func(serviceName string) {
			if err := sr.CheckServiceHealth(serviceName); err != nil {
				// Log the error (in a real implementation, use proper logging)
				fmt.Printf("Health check failed for service %s: %v\n", serviceName, err)
			}
		}(name)
	}
}

// GetServiceStats returns statistics about the service registry
func (sr *ServiceRegistry) GetServiceStats() map[string]interface{} {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	totalServices := len(sr.services)
	healthyServices := 0
	unhealthyServices := 0

	for _, service := range sr.services {
		if service.Healthy {
			healthyServices++
		} else {
			unhealthyServices++
		}
	}

	return map[string]interface{}{
		"total_services":     totalServices,
		"healthy_services":   healthyServices,
		"unhealthy_services": unhealthyServices,
		"last_updated":       time.Now().UTC().Format(time.RFC3339),
	}
}

// IsServiceHealthy checks if a specific service is healthy
func (sr *ServiceRegistry) IsServiceHealthy(name string) bool {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	if service, exists := sr.services[name]; exists {
		return service.Healthy
	}
	return false
}

// GetServiceURL returns the URL of a service if it's healthy
func (sr *ServiceRegistry) GetServiceURL(name string) (string, error) {
	service, err := sr.GetService(name)
	if err != nil {
		return "", err
	}
	return service.URL, nil
}
