import { useState, useEffect } from 'react'
import { PlusIcon, MagnifyingGlassIcon, FunnelIcon, PencilIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { User, UserRole, UserStatus, UserCreateRequest, UserUpdateRequest } from '@/types/auth'
import { useAuth } from '@/store/auth'

// Mock data for development - will be replaced with API calls
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    username: 'admin',
    first_name: 'Admin',
    last_name: 'User',
    full_name: 'Admin User',
    phone: '+998901234567',
    role: 'ADMIN',
    status: 'ACTIVE',
    email_verified: true,
    phone_verified: true,
    two_factor_enabled: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    last_login_at: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    username: 'staff',
    first_name: 'Staff',
    last_name: 'Member',
    full_name: 'Staff Member',
    phone: '+998901234568',
    role: 'RECEPTION',
    status: 'ACTIVE',
    email_verified: true,
    phone_verified: false,
    two_factor_enabled: true,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    last_login_at: '2024-01-14T15:45:00Z'
  },
  {
    id: '3',
    email: '<EMAIL>',
    username: 'teacher1',
    first_name: 'John',
    last_name: 'Teacher',
    full_name: 'John Teacher',
    phone: '+998901234569',
    role: 'TEACHER',
    status: 'ACTIVE',
    email_verified: true,
    phone_verified: true,
    two_factor_enabled: false,
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z',
    last_login_at: '2024-01-13T09:15:00Z'
  },
  {
    id: '4',
    email: '<EMAIL>',
    username: 'manager1',
    first_name: 'Sarah',
    last_name: 'Manager',
    full_name: 'Sarah Manager',
    phone: '+998901234570',
    role: 'MANAGER',
    status: 'INACTIVE',
    email_verified: true,
    phone_verified: true,
    two_factor_enabled: true,
    created_at: '2024-01-04T00:00:00Z',
    updated_at: '2024-01-04T00:00:00Z',
    last_login_at: '2024-01-10T14:20:00Z'
  }
]

interface UserFilters {
  search: string
  role: UserRole | 'ALL'
  status: UserStatus | 'ALL'
}

const UsersPage = () => {
  const { user: currentUser } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    role: 'ALL',
    status: 'ALL'
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)

  // Load users on component mount
  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setUsers(mockUsers)
    } catch (error) {
      console.error('Failed to load users:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.full_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      user.email.toLowerCase().includes(filters.search.toLowerCase()) ||
      user.username.toLowerCase().includes(filters.search.toLowerCase())

    const matchesRole = filters.role === 'ALL' || user.role === filters.role
    const matchesStatus = filters.status === 'ALL' || user.status === filters.status

    return matchesSearch && matchesRole && matchesStatus
  })

  const handleCreateUser = async (userData: UserCreateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newUser: User = {
        id: Date.now().toString(),
        ...userData,
        full_name: `${userData.first_name} ${userData.last_name}`,
        status: 'ACTIVE',
        email_verified: false,
        phone_verified: false,
        two_factor_enabled: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      setUsers(prev => [...prev, newUser])
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create user:', error)
    }
  }

  const handleUpdateUser = async (userId: string, userData: UserUpdateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setUsers(prev => prev.map(user =>
        user.id === userId
          ? {
              ...user,
              ...userData,
              full_name: userData.first_name && userData.last_name
                ? `${userData.first_name} ${userData.last_name}`
                : user.full_name,
              updated_at: new Date().toISOString()
            }
          : user
      ))
      setShowEditModal(false)
      setSelectedUser(null)
    } catch (error) {
      console.error('Failed to update user:', error)
    }
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setUsers(prev => prev.filter(user => user.id !== userId))
    } catch (error) {
      console.error('Failed to delete user:', error)
    }
  }

  const getRoleBadgeColor = (role: UserRole) => {
    const colors = {
      ADMIN: 'bg-purple-100 text-purple-800',
      CASHIER: 'bg-green-100 text-green-800',
      RECEPTION: 'bg-blue-100 text-blue-800',
      TEACHER: 'bg-yellow-100 text-yellow-800',
      MANAGER: 'bg-red-100 text-red-800',
      ACADEMIC_MANAGER: 'bg-indigo-100 text-indigo-800'
    }
    return colors[role] || 'bg-gray-100 text-gray-800'
  }

  const getStatusBadgeColor = (status: UserStatus) => {
    const colors = {
      ACTIVE: 'bg-green-100 text-green-800',
      INACTIVE: 'bg-gray-100 text-gray-800',
      SUSPENDED: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600 mt-1">
            Manage system users, roles, and permissions
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <select
            value={filters.role}
            onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value as UserRole | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Roles</option>
            <option value="ADMIN">Admin</option>
            <option value="CASHIER">Cashier</option>
            <option value="RECEPTION">Reception</option>
            <option value="TEACHER">Teacher</option>
            <option value="MANAGER">Manager</option>
            <option value="ACADEMIC_MANAGER">Academic Manager</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as UserStatus | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="SUSPENDED">Suspended</option>
          </select>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4" />
            {filteredUsers.length} of {users.length} users
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-head">User</th>
                <th className="table-head">Role</th>
                <th className="table-head">Status</th>
                <th className="table-head">Last Login</th>
                <th className="table-head">Created</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="table-cell text-center py-8 text-gray-500">
                    No users found matching your criteria
                  </td>
                </tr>
              ) : (
                filteredUsers.map((user) => (
                  <tr key={user.id} className="table-row">
                    <td className="table-cell">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-primary-600 font-medium text-sm">
                            {user.first_name[0]}{user.last_name[0]}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{user.full_name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="text-xs text-gray-400">@{user.username}</div>
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <Badge className={getRoleBadgeColor(user.role)}>
                        {user.role.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <Badge className={getStatusBadgeColor(user.status)}>
                        {user.status}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {user.last_login_at ? formatDate(user.last_login_at) : 'Never'}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {formatDate(user.created_at)}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(user)
                            setShowViewModal(true)
                          }}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(user)
                            setShowEditModal(true)
                          }}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        {currentUser?.id !== user.id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <UserCreateModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateUser}
        />
      )}

      {showEditModal && selectedUser && (
        <UserEditModal
          user={selectedUser}
          onClose={() => {
            setShowEditModal(false)
            setSelectedUser(null)
          }}
          onSubmit={(userData) => handleUpdateUser(selectedUser.id, userData)}
        />
      )}

      {showViewModal && selectedUser && (
        <UserViewModal
          user={selectedUser}
          onClose={() => {
            setShowViewModal(false)
            setSelectedUser(null)
          }}
        />
      )}
    </div>
  )
}

// Modal Components (will be implemented separately)
const UserCreateModal = ({ onClose, onSubmit }: {
  onClose: () => void
  onSubmit: (data: UserCreateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">Create New User</h2>
        <p className="text-gray-600 mb-4">User creation form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Create User</Button>
        </div>
      </div>
    </div>
  )
}

const UserEditModal = ({ user, onClose, onSubmit }: {
  user: User
  onClose: () => void
  onSubmit: (data: UserUpdateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">Edit User: {user.full_name}</h2>
        <p className="text-gray-600 mb-4">User editing form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Save Changes</Button>
        </div>
      </div>
    </div>
  )
}

const UserViewModal = ({ user, onClose }: {
  user: User
  onClose: () => void
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">User Details: {user.full_name}</h2>
        <div className="space-y-2 text-sm">
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>Username:</strong> {user.username}</p>
          <p><strong>Phone:</strong> {user.phone}</p>
          <p><strong>Role:</strong> {user.role}</p>
          <p><strong>Status:</strong> {user.status}</p>
          <p><strong>Created:</strong> {new Date(user.created_at).toLocaleDateString()}</p>
        </div>
        <div className="mt-4">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}

export default UsersPage
