# 💳 Payment Service

A comprehensive payment processing microservice built with Go, supporting multiple payment gateways including Stripe, PayPal, and local Uzbekistan payment systems (UzCard, Humo, Payme, Click).

## 🚀 Features

### Payment Processing
- **Multiple Payment Gateways**: Stripe, PayPal, UzCard, Humo, Payme, Click
- **Payment Methods**: Credit/Debit Cards, Digital Wallets, Local Payment Systems
- **Multi-Currency Support**: USD, EUR, UZS
- **Real-time Payment Processing**: Instant payment confirmation and status updates
- **Payment Retry Logic**: Automatic retry for failed transactions with configurable limits

### Transaction Management
- **Transaction Tracking**: Complete audit trail for all payment transactions
- **Status Management**: Pending, Processing, Completed, Failed, Cancelled states
- **Gateway Integration**: Seamless integration with multiple payment providers
- **Fee Calculation**: Automatic gateway fee calculation and tracking
- **Reconciliation**: Payment reconciliation with gateway reports

### Financial Reporting
- **Revenue Analytics**: Comprehensive revenue tracking and analytics
- **Payment Reports**: Detailed payment and transaction reports
- **Gateway Performance**: Performance metrics for each payment gateway
- **Financial Dashboards**: Real-time financial dashboards and KPIs
- **Export Functionality**: Export reports in various formats

### Security & Compliance
- **PCI DSS Compliance**: Secure handling of payment card data
- **Data Encryption**: Encryption of sensitive payment information
- **Audit Logging**: Complete audit trail for all payment activities
- **Fraud Detection**: Basic fraud detection and prevention measures
- **Webhook Security**: Secure webhook handling with signature verification

## 🏗️ Architecture

### Service Components
- **Payment Handler**: REST API endpoints for payment operations
- **Transaction Handler**: Transaction management and tracking
- **Gateway Handler**: Payment gateway integrations and webhooks
- **Repository Layer**: Data access layer with GORM
- **Service Layer**: Business logic and payment processing
- **Gateway Integrations**: Individual gateway implementations

### Database Schema
- **Payments**: Core payment records with status tracking
- **Transactions**: Individual transaction records for each payment attempt
- **Payment Methods**: Stored payment methods for users
- **Refunds**: Refund records linked to original payments
- **Financial Reports**: Generated financial reports and analytics
- **Reconciliation Records**: Gateway reconciliation data

## 📊 API Endpoints

### Payment Management
```
GET    /api/v1/payments              # List payments with filtering
GET    /api/v1/payments/:id          # Get payment details
POST   /api/v1/payments              # Create new payment
PUT    /api/v1/payments/:id          # Update payment
DELETE /api/v1/payments/:id          # Delete payment
POST   /api/v1/payments/:id/process  # Process payment
POST   /api/v1/payments/:id/refund   # Refund payment
```

### Transaction Management
```
GET    /api/v1/transactions          # List transactions
GET    /api/v1/transactions/:id      # Get transaction details
POST   /api/v1/transactions          # Create transaction
PUT    /api/v1/transactions/:id      # Update transaction
POST   /api/v1/transactions/:id/retry # Retry failed transaction
```

### Gateway Operations
```
GET    /api/v1/gateways              # List supported gateways
GET    /api/v1/gateways/:type        # Get gateway information
POST   /api/v1/gateways/:type/test   # Test gateway connection
POST   /api/v1/gateways/stripe/webhook    # Stripe webhook
POST   /api/v1/gateways/paypal/webhook    # PayPal webhook
POST   /api/v1/gateways/uzcard/webhook    # UzCard webhook
POST   /api/v1/gateways/humo/webhook      # Humo webhook
POST   /api/v1/gateways/payme/webhook     # Payme webhook
POST   /api/v1/gateways/click/webhook     # Click webhook
```

### Financial Reporting
```
GET    /api/v1/reports/financial     # Financial reports
GET    /api/v1/reports/revenue       # Revenue reports
GET    /api/v1/reports/transactions  # Transaction reports
GET    /api/v1/payments/stats        # Payment statistics
GET    /api/v1/transactions/stats    # Transaction statistics
```

### Health & Monitoring
```
GET    /health                       # Service health check
GET    /ready                        # Service readiness check
GET    /live                         # Service liveness check
```

## 🔧 Configuration

### Environment Variables

```env
# Service Configuration
PORT=8083
ENVIRONMENT=development
DATABASE_URL=postgresql://user:password@host:port/dbname
JWT_SECRET=your-jwt-secret

# Gateway Configuration - Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Gateway Configuration - PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_WEBHOOK_ID=your-webhook-id
PAYPAL_MODE=sandbox

# Gateway Configuration - UzCard
UZCARD_MERCHANT_ID=your-merchant-id
UZCARD_SECRET_KEY=your-secret-key

# Gateway Configuration - Humo
HUMO_MERCHANT_ID=your-merchant-id
HUMO_SECRET_KEY=your-secret-key

# Gateway Configuration - Payme
PAYME_MERCHANT_ID=your-merchant-id
PAYME_SECRET_KEY=your-secret-key

# Gateway Configuration - Click
CLICK_MERCHANT_ID=your-merchant-id
CLICK_SECRET_KEY=your-secret-key
CLICK_SERVICE_ID=your-service-id
CLICK_MERCHANT_USER_ID=your-user-id

# Payment Configuration
DEFAULT_CURRENCY=USD
MAX_PAYMENT_AMOUNT=100000.00
PAYMENT_TIMEOUT_MINUTES=30
REFUND_TIMEOUT_DAYS=30
```

## 🚀 Getting Started

### Prerequisites
- Go 1.21+
- PostgreSQL 13+
- Redis 6+ (optional)

### Installation

1. **Clone and navigate to the service**:
```bash
cd services/payment-service
```

2. **Install dependencies**:
```bash
go mod download
```

3. **Set up environment variables**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Run database migrations**:
```bash
# Migrations are automatically applied on service startup
```

5. **Start the service**:
```bash
go run main.go
```

### Docker Deployment

```bash
# Build the image
docker build -t payment-service .

# Run the container
docker run -p 8083:8083 \
  -e DATABASE_URL="your-database-url" \
  -e STRIPE_SECRET_KEY="your-stripe-key" \
  payment-service
```

## 🧪 Testing

### Unit Tests
```bash
go test ./...
```

### Integration Tests
```bash
go test -tags=integration ./...
```

### API Testing
```bash
# Test payment creation
curl -X POST http://localhost:8083/api/v1/payments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "amount": 100.00,
    "currency": "USD",
    "method": "CARD",
    "gateway_type": "stripe",
    "description": "Test payment"
  }'
```

## 📈 Monitoring

### Health Checks
- **Health**: `/health` - Overall service health
- **Readiness**: `/ready` - Service readiness for traffic
- **Liveness**: `/live` - Service liveness check

### Metrics
- Payment success/failure rates
- Transaction processing times
- Gateway performance metrics
- Revenue and financial KPIs
- Error rates and response times

### Logging
- Structured JSON logging
- Payment processing audit trails
- Gateway interaction logs
- Error and exception tracking

## 🔒 Security

### Payment Security
- PCI DSS compliance guidelines
- Secure token handling
- Encrypted sensitive data storage
- Secure webhook verification

### API Security
- JWT authentication required
- Role-based access control
- Rate limiting protection
- Input validation and sanitization

## 🤝 Integration

### With Other Services
- **Admin Service**: User management and authentication
- **Staff Service**: Student and course management
- **Notification Service**: Payment notifications

### Gateway Integration
- Webhook handling for real-time updates
- Automatic reconciliation processes
- Error handling and retry mechanisms
- Multi-gateway failover support

## 📚 Documentation

- [API Documentation](../docs/API_DOCUMENTATION.md)
- [Gateway Integration Guide](../docs/GATEWAY_INTEGRATION.md)
- [Deployment Guide](../docs/DEPLOYMENT_GUIDE.md)
- [Security Guidelines](../docs/SECURITY_GUIDELINES.md)
