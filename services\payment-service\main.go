package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"payment-service/internal/config"
	"payment-service/internal/handlers"
	"payment-service/internal/repository"
	"payment-service/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/shared/database"
	"github.com/crm-microservices/shared/middleware"
	"github.com/crm-microservices/shared/utils"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	dbManager, err := createDatabaseConnection(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer dbManager.Close()

	db := dbManager.GetDB()

	// Verify database connection and tables exist (tables are pre-created)
	if err := verifyDatabaseTables(dbManager); err != nil {
		log.Fatalf("Failed to verify database tables: %v", err)
	}

	// Initialize repositories
	paymentRepo := repository.NewPaymentRepository(db)
	transactionRepo := repository.NewTransactionRepository(db)
	// paymentMethodRepo := repository.NewPaymentMethodRepository(db) // Unused for now

	// Initialize services
	paymentService := services.NewPaymentService(paymentRepo, transactionRepo)
	transactionService := services.NewTransactionService(transactionRepo)
	gatewayService := services.NewGatewayService(paymentRepo, transactionRepo)
	financialService := services.NewFinancialService(paymentRepo, transactionRepo)

	// Initialize handlers
	paymentHandler := handlers.NewPaymentHandler(paymentService)
	transactionHandler := handlers.NewTransactionHandler(transactionService)
	gatewayHandler := handlers.NewGatewayHandler(gatewayService)
	financialHandler := handlers.NewFinancialHandler(financialService)
	healthHandler := handlers.NewHealthHandler(db)

	// Setup Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	// Initialize JWT manager and auth middleware
	jwtManager := utils.NewJWTManager(
		cfg.JWTSecret,
		15*time.Minute,  // access token TTL
		7*24*time.Hour,  // refresh token TTL (7 days)
		"crm-payment-service",
		"crm-system",
	)
	authMiddleware := middleware.NewAuthMiddleware(jwtManager)

	// Health check endpoint (no auth required)
	router.GET("/health", healthHandler.HealthCheck)

	// API routes with authentication
	api := router.Group("/api/v1")
	api.Use(authMiddleware.RequireAuth())

	// Payment routes
	payments := api.Group("/payments")
	{
		payments.GET("", paymentHandler.GetPayments)
		payments.GET("/:id", paymentHandler.GetPayment)
		payments.POST("", paymentHandler.CreatePayment)
		payments.PUT("/:id", paymentHandler.UpdatePayment)
		payments.POST("/:id/refund", paymentHandler.RefundPayment)
	}

	// Transaction routes
	transactions := api.Group("/transactions")
	{
		transactions.GET("", transactionHandler.GetTransactions)
		transactions.GET("/:id", transactionHandler.GetTransaction)
		transactions.POST("/:id/retry", transactionHandler.RetryTransaction)
	}

	// Gateway routes
	gateways := api.Group("/gateways")
	{
		gateways.POST("/stripe/webhook", gatewayHandler.StripeWebhook)
		gateways.POST("/paypal/webhook", gatewayHandler.PayPalWebhook)
	}

	// Financial reporting routes
	reports := api.Group("/reports")
	{
		reports.GET("/financial", paymentHandler.GetFinancialReport)
		reports.GET("/revenue", paymentHandler.GetRevenueReport)
		reports.GET("/transactions", transactionHandler.GetTransactionReport)
		reports.POST("/generate", financialHandler.GenerateFinancialReport)
		reports.POST("/export", financialHandler.ExportFinancialData)
	}

	// Analytics routes
	analytics := api.Group("/analytics")
	{
		analytics.GET("/payments", financialHandler.GetPaymentAnalytics)
		analytics.GET("/revenue", financialHandler.GetRevenueMetrics)
		analytics.GET("/revenue/comparison", financialHandler.GetRevenueComparison)
		analytics.GET("/gateways", financialHandler.GetGatewayPerformance)
		analytics.GET("/trends", financialHandler.GetPaymentTrends)
	}

	// Dashboard routes
	dashboard := api.Group("/dashboard")
	{
		dashboard.GET("", financialHandler.GetFinancialDashboard)
	}

	// Reconciliation routes
	reconciliation := api.Group("/reconciliation")
	{
		reconciliation.POST("", financialHandler.ReconcilePayments)
	}

	// Start server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%s", cfg.Port),
		Handler: router,
	}

	// Graceful shutdown
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	log.Printf("Payment Service started on port %s", cfg.Port)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// The context is used to inform the server it has 5 seconds to finish
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exiting")
}

// corsMiddleware provides CORS support
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// createDatabaseConnection creates a database connection from URL
func createDatabaseConnection(databaseURL string) (*database.ConnectionManager, error) {
	// Always prioritize DATABASE_URL if provided
	if databaseURL != "" {
		log.Printf("Using DATABASE_URL for connection: %s", databaseURL)
		dbManager := database.NewConnectionManagerFromURL(databaseURL)
		if err := dbManager.Connect(); err != nil {
			log.Printf("Failed to connect with DATABASE_URL: %v", err)
			return nil, fmt.Errorf("failed to connect with DATABASE_URL: %w", err)
		}
		log.Printf("Successfully connected using DATABASE_URL")
		return dbManager, nil
	}

	// Only use individual environment variables if DATABASE_URL is not set
	log.Printf("DATABASE_URL not provided, using individual environment variables for database connection")
	host := getEnv("DB_HOST", "")
	port := getEnv("DB_PORT", "5432")
	user := getEnv("DB_USER", "")
	password := getEnv("DB_PASSWORD", "")
	dbname := getEnv("DB_NAME_PAYMENT", "")
	sslmode := getEnv("DB_SSL_MODE", "require")

	// If any individual env vars are empty, return error to prevent fallback to wrong database
	if host == "" || user == "" || password == "" || dbname == "" {
		return nil, fmt.Errorf("DATABASE_URL not provided and individual database environment variables are incomplete (host=%s, user=%s, password=***, dbname=%s)", host, user, dbname)
	}

	dbConfig := &database.Config{
		Host:     host,
		Port:     port,
		User:     user,
		Password: password,
		DBName:   dbname,
		SSLMode:  sslmode,
		TimeZone: "UTC",
	}

	log.Printf("Connecting to database: host=%s, port=%s, user=%s, dbname=%s, sslmode=%s",
		host, port, user, dbname, sslmode)

	dbManager := database.NewConnectionManager(dbConfig)
	if err := dbManager.Connect(); err != nil {
		log.Printf("Failed to connect with individual config: %v", err)
		return nil, fmt.Errorf("failed to connect with individual config: %w", err)
	}

	return dbManager, nil
}

// verifyDatabaseTables verifies that required tables exist
func verifyDatabaseTables(dbManager *database.ConnectionManager) error {
	db := dbManager.GetDB()

	// Verify that required tables exist (tables are pre-created)
	tables := []string{"payments", "transactions", "refunds"}
	for _, table := range tables {
		err := db.Exec("SELECT 1 FROM " + table + " LIMIT 1").Error
		if err != nil {
			return fmt.Errorf("failed to connect to database or table %s doesn't exist: %w", table, err)
		}
	}

	log.Println("Database connection verified - using pre-created tables")
	return nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
