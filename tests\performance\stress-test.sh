#!/bin/bash

# 🔥 Stress Testing Script for Go Docker Platform
# Tests system behavior under extreme load conditions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RESULTS_DIR="$SCRIPT_DIR/stress-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Service endpoints
AUTH_URL="http://localhost:8081"
ADMIN_URL="http://localhost:8082"
STAFF_URL="http://localhost:8083"
PAYMENT_URL="http://localhost:8084"
NOTIFICATION_URL="http://localhost:8085"
GATEWAY_URL="http://localhost:8080"

# Stress test parameters
MAX_CONCURRENT_USERS=200
STRESS_DURATION=300  # 5 minutes
RAMP_UP_TIME=60     # 1 minute
MEMORY_LIMIT_MB=1024
CPU_LIMIT_PERCENT=80

# Utility functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "step")
            echo -e "\n${BLUE}🔄 $message${NC}"
            ;;
    esac
}

# Setup stress test environment
setup_stress_environment() {
    print_status "step" "Setting up stress test environment"
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Check if services are running
    local services_ready=true
    local services=("$AUTH_URL" "$ADMIN_URL" "$STAFF_URL" "$PAYMENT_URL" "$NOTIFICATION_URL")
    
    for service_url in "${services[@]}"; do
        if curl -s -f "$service_url/health" > /dev/null 2>&1; then
            print_status "success" "Service at $service_url is ready"
        else
            print_status "error" "Service at $service_url is not responding"
            services_ready=false
        fi
    done
    
    if [ "$services_ready" = false ]; then
        print_status "error" "Some services are not ready. Please start all services first."
        exit 1
    fi
    
    # Get authentication token
    get_auth_token
    
    # Start system monitoring
    start_system_monitoring
}

# Get authentication token
get_auth_token() {
    print_status "info" "Obtaining authentication token for stress testing"
    
    # Create stress test user
    local user_data='{
        "email": "<EMAIL>",
        "username": "stresstest",
        "password": "StressTest123!@#",
        "first_name": "Stress",
        "last_name": "Test",
        "phone": "+998901234567",
        "role": "ADMIN"
    }'
    
    # Try to register (ignore if user already exists)
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$user_data" \
        "$AUTH_URL/api/v1/auth/register" > /dev/null 2>&1 || true
    
    # Login to get token
    local login_data='{
        "email": "<EMAIL>",
        "password": "StressTest123!@#"
    }'
    
    local response
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$AUTH_URL/api/v1/auth/login")
    
    AUTH_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$AUTH_TOKEN" ]; then
        print_status "success" "Authentication token obtained for stress testing"
    else
        print_status "error" "Failed to obtain authentication token"
        exit 1
    fi
}

# Start system monitoring
start_system_monitoring() {
    print_status "info" "Starting system monitoring"
    
    # Monitor CPU and memory usage
    (
        while true; do
            local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
            local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
            local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
            
            echo "$timestamp,CPU,$cpu_usage" >> "$RESULTS_DIR/system_metrics_${TIMESTAMP}.csv"
            echo "$timestamp,Memory,$memory_usage" >> "$RESULTS_DIR/system_metrics_${TIMESTAMP}.csv"
            
            sleep 5
        done
    ) &
    MONITOR_PID=$!
    
    print_status "success" "System monitoring started (PID: $MONITOR_PID)"
}

# Stop system monitoring
stop_system_monitoring() {
    if [ -n "$MONITOR_PID" ]; then
        kill $MONITOR_PID 2>/dev/null || true
        print_status "info" "System monitoring stopped"
    fi
}

# Run memory stress test
run_memory_stress_test() {
    print_status "step" "Running memory stress test"
    
    local test_name="memory_stress"
    local result_file="$RESULTS_DIR/${test_name}_${TIMESTAMP}.txt"
    
    # Create memory-intensive requests
    local concurrent_users=50
    local duration=120  # 2 minutes
    
    print_status "info" "Testing memory usage with $concurrent_users concurrent users for ${duration}s"
    
    # Start memory stress
    for ((i=1; i<=concurrent_users; i++)); do
        (
            local start_time=$(date +%s)
            local end_time=$((start_time + duration))
            local request_count=0
            local success_count=0
            
            while [ $(date +%s) -lt $end_time ]; do
                # Make memory-intensive requests (large data operations)
                local large_data='{"data": "'$(head -c 10000 /dev/zero | tr '\0' 'A')'"}'
                
                local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
                    -X POST \
                    -H "Content-Type: application/json" \
                    -H "Authorization: Bearer $AUTH_TOKEN" \
                    -d "$large_data" \
                    "$ADMIN_URL/api/v1/analytics/process-data" 2>/dev/null || echo "000")
                
                ((request_count++))
                if [ "$status_code" = "200" ] || [ "$status_code" = "201" ]; then
                    ((success_count++))
                fi
                
                # Small delay to prevent overwhelming
                sleep 0.5
            done
            
            echo "Worker $i: $request_count requests, $success_count successful" >> "$result_file"
        ) &
    done
    
    # Wait for all workers to complete
    wait
    
    # Analyze memory usage
    local max_memory=$(awk -F',' '/Memory/ {if($3>max) max=$3} END {print max}' "$RESULTS_DIR/system_metrics_${TIMESTAMP}.csv")
    
    echo "Memory Stress Test Results:" >> "$result_file"
    echo "Peak Memory Usage: ${max_memory}%" >> "$result_file"
    echo "Memory Limit: ${MEMORY_LIMIT_MB}MB" >> "$result_file"
    
    if (( $(echo "$max_memory > 90" | bc -l) )); then
        print_status "warning" "High memory usage detected: ${max_memory}%"
    else
        print_status "success" "Memory usage within acceptable limits: ${max_memory}%"
    fi
}

# Run CPU stress test
run_cpu_stress_test() {
    print_status "step" "Running CPU stress test"
    
    local test_name="cpu_stress"
    local result_file="$RESULTS_DIR/${test_name}_${TIMESTAMP}.txt"
    
    # Create CPU-intensive requests
    local concurrent_users=100
    local duration=120  # 2 minutes
    
    print_status "info" "Testing CPU usage with $concurrent_users concurrent users for ${duration}s"
    
    # Start CPU stress
    for ((i=1; i<=concurrent_users; i++)); do
        (
            local start_time=$(date +%s)
            local end_time=$((start_time + duration))
            local request_count=0
            local success_count=0
            
            while [ $(date +%s) -lt $end_time ]; do
                # Make CPU-intensive requests (complex calculations)
                local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
                    -H "Authorization: Bearer $AUTH_TOKEN" \
                    "$ADMIN_URL/api/v1/analytics/complex-calculation?iterations=1000" 2>/dev/null || echo "000")
                
                ((request_count++))
                if [ "$status_code" = "200" ] || [ "$status_code" = "201" ]; then
                    ((success_count++))
                fi
                
                # No delay for CPU stress
            done
            
            echo "Worker $i: $request_count requests, $success_count successful" >> "$result_file"
        ) &
    done
    
    # Wait for all workers to complete
    wait
    
    # Analyze CPU usage
    local max_cpu=$(awk -F',' '/CPU/ {if($3>max) max=$3} END {print max}' "$RESULTS_DIR/system_metrics_${TIMESTAMP}.csv")
    
    echo "CPU Stress Test Results:" >> "$result_file"
    echo "Peak CPU Usage: ${max_cpu}%" >> "$result_file"
    echo "CPU Limit: ${CPU_LIMIT_PERCENT}%" >> "$result_file"
    
    if (( $(echo "$max_cpu > $CPU_LIMIT_PERCENT" | bc -l) )); then
        print_status "warning" "High CPU usage detected: ${max_cpu}%"
    else
        print_status "success" "CPU usage within acceptable limits: ${max_cpu}%"
    fi
}

# Run connection stress test
run_connection_stress_test() {
    print_status "step" "Running connection stress test"
    
    local test_name="connection_stress"
    local result_file="$RESULTS_DIR/${test_name}_${TIMESTAMP}.txt"
    
    # Test maximum concurrent connections
    local max_connections=500
    local duration=60  # 1 minute
    
    print_status "info" "Testing maximum connections: $max_connections for ${duration}s"
    
    # Start connection stress
    for ((i=1; i<=max_connections; i++)); do
        (
            # Keep connection open for the duration
            timeout $duration curl -s \
                -H "Authorization: Bearer $AUTH_TOKEN" \
                "$AUTH_URL/api/v1/auth/validate" > /dev/null 2>&1
            
            echo "Connection $i completed"
        ) &
        
        # Small delay between connection attempts
        sleep 0.01
    done
    
    # Wait for all connections to complete
    wait
    
    echo "Connection Stress Test Results:" >> "$result_file"
    echo "Attempted Connections: $max_connections" >> "$result_file"
    echo "Duration: ${duration}s" >> "$result_file"
    
    print_status "success" "Connection stress test completed"
}

# Run database stress test
run_database_stress_test() {
    print_status "step" "Running database stress test"
    
    local test_name="database_stress"
    local result_file="$RESULTS_DIR/${test_name}_${TIMESTAMP}.txt"
    
    # Test database with heavy read/write operations
    local concurrent_users=30
    local duration=180  # 3 minutes
    
    print_status "info" "Testing database with $concurrent_users concurrent users for ${duration}s"
    
    # Start database stress
    for ((i=1; i<=concurrent_users; i++)); do
        (
            local start_time=$(date +%s)
            local end_time=$((start_time + duration))
            local read_count=0
            local write_count=0
            local error_count=0
            
            while [ $(date +%s) -lt $end_time ]; do
                # Alternate between read and write operations
                if [ $((RANDOM % 2)) -eq 0 ]; then
                    # Read operation
                    local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
                        -H "Authorization: Bearer $AUTH_TOKEN" \
                        "$ADMIN_URL/api/v1/users?page=1&limit=100" 2>/dev/null || echo "000")
                    
                    if [ "$status_code" = "200" ]; then
                        ((read_count++))
                    else
                        ((error_count++))
                    fi
                else
                    # Write operation (create test data)
                    local test_data='{
                        "email": "stress'$i'_'$(date +%s)'@test.com",
                        "username": "stress'$i'_'$(date +%s)'",
                        "password": "Test123!@#",
                        "first_name": "Stress",
                        "last_name": "Test",
                        "phone": "+998901234567",
                        "role": "RECEPTION"
                    }'
                    
                    local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
                        -X POST \
                        -H "Content-Type: application/json" \
                        -H "Authorization: Bearer $AUTH_TOKEN" \
                        -d "$test_data" \
                        "$ADMIN_URL/api/v1/users" 2>/dev/null || echo "000")
                    
                    if [ "$status_code" = "201" ]; then
                        ((write_count++))
                    else
                        ((error_count++))
                    fi
                fi
                
                # Small delay
                sleep 0.2
            done
            
            echo "Worker $i: $read_count reads, $write_count writes, $error_count errors" >> "$result_file"
        ) &
    done
    
    # Wait for all workers to complete
    wait
    
    # Calculate totals
    local total_reads=$(awk '{sum+=$3} END {print sum}' "$result_file")
    local total_writes=$(awk '{sum+=$5} END {print sum}' "$result_file")
    local total_errors=$(awk '{sum+=$7} END {print sum}' "$result_file")
    
    echo "" >> "$result_file"
    echo "Database Stress Test Summary:" >> "$result_file"
    echo "Total Reads: $total_reads" >> "$result_file"
    echo "Total Writes: $total_writes" >> "$result_file"
    echo "Total Errors: $total_errors" >> "$result_file"
    echo "Error Rate: $(echo "scale=2; $total_errors * 100 / ($total_reads + $total_writes + $total_errors)" | bc)%" >> "$result_file"
    
    print_status "success" "Database stress test completed"
}

# Generate stress test report
generate_stress_report() {
    print_status "step" "Generating stress test report"
    
    local report_file="$RESULTS_DIR/stress_test_report_${TIMESTAMP}.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Go Docker Platform - Stress Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Go Docker Platform - Stress Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Test Duration: Various (see individual tests)</p>
        <p>Maximum Concurrent Users: $MAX_CONCURRENT_USERS</p>
    </div>
    
    <div class="section">
        <h2>Test Configuration</h2>
        <ul>
            <li>Memory Limit: ${MEMORY_LIMIT_MB}MB</li>
            <li>CPU Limit: ${CPU_LIMIT_PERCENT}%</li>
            <li>Stress Duration: ${STRESS_DURATION} seconds</li>
            <li>Ramp-up Time: ${RAMP_UP_TIME} seconds</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Stress Test Results</h2>
        <p>Detailed results are available in individual test files in the stress-results directory.</p>
        
        <h3>Test Files</h3>
        <ul>
EOF

    # List all result files
    for file in "$RESULTS_DIR"/*_${TIMESTAMP}.txt; do
        if [ -f "$file" ]; then
            local filename=$(basename "$file")
            echo "            <li><a href=\"$filename\">$filename</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
    
    <div class="section">
        <h2>System Metrics</h2>
        <p>System monitoring data: <a href="system_metrics_${TIMESTAMP}.csv">system_metrics_${TIMESTAMP}.csv</a></p>
    </div>
</body>
</html>
EOF
    
    print_status "success" "Stress test report generated: $report_file"
}

# Cleanup function
cleanup() {
    print_status "info" "Cleaning up stress test environment"
    stop_system_monitoring
}

# Main execution
main() {
    print_status "step" "Starting Stress Testing for Go Docker Platform"
    
    # Setup
    setup_stress_environment
    
    # Run stress tests
    run_memory_stress_test
    sleep 30  # Cool down between tests
    
    run_cpu_stress_test
    sleep 30
    
    run_connection_stress_test
    sleep 30
    
    run_database_stress_test
    
    # Generate report
    generate_stress_report
    
    print_status "success" "Stress testing completed!"
    echo "📁 Results available in: $RESULTS_DIR"
}

# Trap cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
