package tests

import (
	"testing"
	"time"

	"admin-service/internal/models"
	"admin-service/internal/repository"
	"admin-service/internal/services"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	sharedModels "github.com/crm-microservices/shared/models"
)

// MockUserRepository is a mock implementation of UserRepository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetAll(req *sharedModels.UserListRequest) ([]*sharedModels.User, int64, error) {
	args := m.Called(req)
	return args.Get(0).([]*sharedModels.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) GetByID(id uuid.UUID) (*sharedModels.User, error) {
	args := m.Called(id)
	return args.Get(0).(*sharedModels.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(email string) (*sharedModels.User, error) {
	args := m.Called(email)
	return args.Get(0).(*sharedModels.User), args.Error(1)
}

func (m *MockUserRepository) GetByUsername(username string) (*sharedModels.User, error) {
	args := m.Called(username)
	return args.Get(0).(*sharedModels.User), args.Error(1)
}

func (m *MockUserRepository) Create(user *sharedModels.User) error {
	args := m.Called(user)
	return args.Error(0)
}

func (m *MockUserRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	args := m.Called(id, updates)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(id uuid.UUID) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserStats() (*repository.UserStats, error) {
	args := m.Called()
	return args.Get(0).(*repository.UserStats), args.Error(1)
}

func (m *MockUserRepository) GetRecentRegistrations(limit int) ([]*sharedModels.User, error) {
	args := m.Called(limit)
	return args.Get(0).([]*sharedModels.User), args.Error(1)
}

func (m *MockUserRepository) GetMostActiveUsers(limit int) ([]*repository.UserActivitySummary, error) {
	args := m.Called(limit)
	return args.Get(0).([]*repository.UserActivitySummary), args.Error(1)
}

// MockAuditRepository is a mock implementation of AuditRepository
type MockAuditRepository struct {
	mock.Mock
}

func (m *MockAuditRepository) Create(auditLog *models.AuditLog) error {
	args := m.Called(auditLog)
	return args.Error(0)
}

func (m *MockAuditRepository) GetAll(req *models.AuditLogListRequest) ([]*models.AuditLog, int64, error) {
	args := m.Called(req)
	return args.Get(0).([]*models.AuditLog), args.Get(1).(int64), args.Error(2)
}

func (m *MockAuditRepository) GetByID(id uuid.UUID) (*models.AuditLog, error) {
	args := m.Called(id)
	return args.Get(0).(*models.AuditLog), args.Error(1)
}

func (m *MockAuditRepository) GetStats(startDate, endDate *time.Time) (*models.AuditLogStats, error) {
	args := m.Called(startDate, endDate)
	return args.Get(0).(*models.AuditLogStats), args.Error(1)
}

func (m *MockAuditRepository) GetRecentActivity(limit int) ([]*models.AuditLog, error) {
	args := m.Called(limit)
	return args.Get(0).([]*models.AuditLog), args.Error(1)
}

func (m *MockAuditRepository) DeleteOldLogs(olderThan time.Time) (int64, error) {
	args := m.Called(olderThan)
	return args.Get(0).(int64), args.Error(1)
}

// TestUserService_CreateUser tests the CreateUser method
func TestUserService_CreateUser(t *testing.T) {
	// Setup
	mockUserRepo := new(MockUserRepository)
	mockAuditRepo := new(MockAuditRepository)
	userService := services.NewUserService(mockUserRepo, mockAuditRepo)

	// Test data
	userID := uuid.New()
	req := &sharedModels.UserCreateRequest{
		Email:     "<EMAIL>",
		Username:  "testuser",
		Password:  "password123",
		FirstName: "Test",
		LastName:  "User",
		Phone:     "+************",
		Role:      sharedModels.RoleAdmin,
	}

	// Mock expectations
	mockUserRepo.On("GetByEmail", req.Email).Return((*sharedModels.User)(nil), assert.AnError)
	mockUserRepo.On("GetByUsername", req.Username).Return((*sharedModels.User)(nil), assert.AnError)
	mockUserRepo.On("Create", mock.AnythingOfType("*models.User")).Return(nil)
	mockAuditRepo.On("Create", mock.AnythingOfType("*models.AuditLog")).Return(nil)

	// Execute
	result, err := userService.CreateUser(req, userID, "127.0.0.1", "test-agent")

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, req.Email, result.Email)
	assert.Equal(t, req.Username, result.Username)
	assert.Equal(t, req.FirstName, result.FirstName)
	assert.Equal(t, req.LastName, result.LastName)
	assert.Equal(t, req.Role, result.Role)

	// Verify mocks
	mockUserRepo.AssertExpectations(t)
	mockAuditRepo.AssertExpectations(t)
}

// TestUserService_GetUsers tests the GetUsers method
func TestUserService_GetUsers(t *testing.T) {
	// Setup
	mockUserRepo := new(MockUserRepository)
	mockAuditRepo := new(MockAuditRepository)
	userService := services.NewUserService(mockUserRepo, mockAuditRepo)

	// Test data
	users := []*sharedModels.User{
		{
			BaseModel: sharedModels.BaseModel{ID: uuid.New()},
			Email:     "<EMAIL>",
			Username:  "user1",
			FirstName: "User",
			LastName:  "One",
			Role:      sharedModels.RoleAdmin,
		},
		{
			BaseModel: sharedModels.BaseModel{ID: uuid.New()},
			Email:     "<EMAIL>",
			Username:  "user2",
			FirstName: "User",
			LastName:  "Two",
			Role:      sharedModels.RoleCashier,
		},
	}

	req := &sharedModels.UserListRequest{
		PaginationRequest: sharedModels.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	}

	// Mock expectations
	mockUserRepo.On("GetAll", req).Return(users, int64(2), nil)

	// Execute
	result, err := userService.GetUsers(req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Users, 2)
	assert.Equal(t, int64(2), result.Pagination.Total)

	// Verify mocks
	mockUserRepo.AssertExpectations(t)
}
