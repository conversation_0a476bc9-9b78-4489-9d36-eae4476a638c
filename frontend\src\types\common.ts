// Common types used across the application

export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REFUNDED';
export type PaymentMethod = 'CASH' | 'CARD' | 'UZCARD' | 'HUMO' | 'PAYME' | 'CLICK';
export type TransactionStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
export type TransactionType = 'PAYMENT' | 'REFUND' | 'FEE';

export type LeadStatus = 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'CONVERTED' | 'LOST';
export type StudentStatus = 'ACTIVE' | 'INACTIVE' | 'GRADUATED' | 'DROPPED';
export type CourseStatus = 'ACTIVE' | 'INACTIVE' | 'COMPLETED' | 'CANCELLED';

export type NotificationType = 'EMAIL' | 'SMS' | 'IN_APP';
export type NotificationStatus = 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';

export type AuditAction = 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'LOGIN' | 'LOGOUT';

// Base model interface
export interface BaseModel {
  id: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// Student interface
export interface Student extends BaseModel {
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  phone: string;
  date_of_birth?: string;
  address?: string;
  city?: string;
  country?: string;
  status: StudentStatus;
  enrollment_date: string;
  graduation_date?: string;
  notes?: string;
  emergency_contact?: string;
  emergency_phone?: string;
}

// Lead interface
export interface Lead extends BaseModel {
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  phone: string;
  status: LeadStatus;
  source?: string;
  notes?: string;
  assigned_to?: string;
  contacted_at?: string;
  converted_at?: string;
  course_interest?: string;
}

// Course interface
export interface Course extends BaseModel {
  name: string;
  description?: string;
  code: string;
  status: CourseStatus;
  start_date: string;
  end_date?: string;
  duration_weeks?: number;
  max_students?: number;
  current_students?: number;
  price: number;
  currency: string;
  teacher_id?: string;
  teacher_name?: string;
}

// Payment interface
export interface Payment extends BaseModel {
  student_id: string;
  student_name?: string;
  course_id?: string;
  course_name?: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  transaction_id?: string;
  gateway_response?: Record<string, any>;
  processed_at?: string;
  notes?: string;
}

// Transaction interface
export interface Transaction extends BaseModel {
  payment_id?: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  currency: string;
  gateway: string;
  gateway_transaction_id?: string;
  gateway_response?: Record<string, any>;
  processed_at?: string;
  notes?: string;
}

// Notification interface
export interface CRMNotification extends BaseModel {
  type: NotificationType;
  status: NotificationStatus;
  recipient: string;
  subject?: string;
  message: string;
  template?: string;
  data?: Record<string, any>;
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
}

// Dashboard stats
export interface DashboardStats {
  total_students: number;
  active_students: number;
  total_leads: number;
  new_leads: number;
  total_courses: number;
  active_courses: number;
  total_payments: number;
  pending_payments: number;
  revenue_this_month: number;
  revenue_last_month: number;
}

// Chart data types
export interface ChartDataPoint {
  name: string;
  value: number;
  label?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  label?: string;
}
