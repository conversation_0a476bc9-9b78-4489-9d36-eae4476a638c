export interface Payment {
  id: string;
  transaction_id: string;
  student_id: string;
  student_name?: string;
  course_id: string;
  course_name?: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  description?: string;
  notes?: string;
  payment_date: string;
  due_date?: string;
  processed_by?: string;
  processed_by_name?: string;
  receipt_url?: string;
  refund_amount?: number;
  refund_reason?: string;
  refund_date?: string;
  created_at: string;
  updated_at: string;
}

export type PaymentMethod = 
  | 'CASH' 
  | 'CARD' 
  | 'BANK_TRANSFER' 
  | 'ONLINE' 
  | 'MOBILE_PAYMENT' 
  | 'CHECK' 
  | 'OTHER';

export type PaymentStatus = 
  | 'PENDING' 
  | 'COMPLETED' 
  | 'FAILED' 
  | 'CANCELLED' 
  | 'REFUNDED' 
  | 'PARTIALLY_REFUNDED';

export interface PaymentCreateRequest {
  student_id: string;
  course_id: string;
  amount: number;
  currency?: string;
  method: PaymentMethod;
  description?: string;
  notes?: string;
  payment_date?: string;
  due_date?: string;
}

export interface PaymentUpdateRequest {
  amount?: number;
  method?: PaymentMethod;
  status?: PaymentStatus;
  description?: string;
  notes?: string;
  payment_date?: string;
  due_date?: string;
}

export interface PaymentFilters {
  search?: string;
  status?: PaymentStatus;
  method?: PaymentMethod;
  student_id?: string;
  course_id?: string;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
}

export interface PaymentStats {
  total_payments: number;
  total_amount: number;
  completed_payments: number;
  pending_payments: number;
  failed_payments: number;
  refunded_payments: number;
  payments_by_method: Record<PaymentMethod, number>;
  payments_by_status: Record<PaymentStatus, number>;
  monthly_revenue: number;
  average_payment_amount: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
