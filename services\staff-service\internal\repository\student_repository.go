package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// StudentRepository handles student data operations
type StudentRepository interface {
	GetAll(req *models.StudentListRequest) ([]*models.Student, int64, error)
	GetByID(id uuid.UUID) (*models.Student, error)
	GetByStudentID(studentID string) (*models.Student, error)
	GetByEmail(email string) (*models.Student, error)
	Create(student *models.Student) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.StudentStats, error)
	GetProgress(studentID uuid.UUID) (*models.StudentProgress, error)
	GetByCourse(courseID uuid.UUID) ([]*models.Student, error)
	GenerateStudentID() (string, error)
	UpdateGrades(studentID uuid.UUID) error
	GetActiveStudents() ([]*models.Student, error)
}

type studentRepository struct {
	db *gorm.DB
}

// NewStudentRepository creates a new student repository
func NewStudentRepository(db *gorm.DB) StudentRepository {
	return &studentRepository{db: db}
}

// GetAll retrieves students with pagination and filtering
func (r *studentRepository) GetAll(req *models.StudentListRequest) ([]*models.Student, int64, error) {
	var students []*models.Student
	var total int64

	query := r.db.Model(&models.Student{}).Preload("CreatedBy")

	// Apply filters
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.CreatedByID != nil {
		query = query.Where("created_by_id = ?", *req.CreatedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where(
			"LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ? OR LOWER(student_id) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	if req.StartDate != nil {
		query = query.Where("enrollment_date >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("enrollment_date <= ?", *req.EndDate)
	}

	if req.CourseID != nil {
		// Filter by enrolled course
		query = query.Joins("JOIN enrollments ON enrollments.student_id = students.id").
			Where("enrollments.course_id = ? AND enrollments.status = ?", *req.CourseID, models.EnrollmentStatusActive)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count students: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("enrollment_date DESC").Find(&students).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get students: %w", err)
	}

	return students, total, nil
}

// GetByID retrieves a student by ID
func (r *studentRepository) GetByID(id uuid.UUID) (*models.Student, error) {
	var student models.Student
	if err := r.db.Preload("CreatedBy").Preload("Enrollments").Preload("Enrollments.Course").
		First(&student, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}
	return &student, nil
}

// GetByStudentID retrieves a student by student ID
func (r *studentRepository) GetByStudentID(studentID string) (*models.Student, error) {
	var student models.Student
	if err := r.db.Preload("CreatedBy").First(&student, "student_id = ?", studentID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}
	return &student, nil
}

// GetByEmail retrieves a student by email
func (r *studentRepository) GetByEmail(email string) (*models.Student, error) {
	var student models.Student
	if err := r.db.Preload("CreatedBy").First(&student, "email = ?", email).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}
	return &student, nil
}

// Create creates a new student
func (r *studentRepository) Create(student *models.Student) error {
	// Generate student ID if not provided
	if student.StudentID == "" {
		studentID, err := r.GenerateStudentID()
		if err != nil {
			return fmt.Errorf("failed to generate student ID: %w", err)
		}
		student.StudentID = studentID
	}

	if err := r.db.Create(student).Error; err != nil {
		return fmt.Errorf("failed to create student: %w", err)
	}
	return nil
}

// Update updates a student
func (r *studentRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Student{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update student: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("student not found")
	}
	return nil
}

// Delete soft deletes a student
func (r *studentRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Student{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete student: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("student not found")
	}
	return nil
}

// GetStats retrieves student statistics
func (r *studentRepository) GetStats() (*models.StudentStats, error) {
	stats := &models.StudentStats{
		StudentsByStatus: make(map[models.StudentStatus]int64),
	}

	// Total students
	if err := r.db.Model(&models.Student{}).Count(&stats.TotalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count total students: %w", err)
	}

	// Students by status
	statuses := []models.StudentStatus{
		models.StudentStatusActive, models.StudentStatusInactive, models.StudentStatusSuspended,
		models.StudentStatusGraduated, models.StudentStatusDropped,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Student{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count students by status %s: %w", status, err)
		}
		stats.StudentsByStatus[status] = count
	}

	// New students today
	if err := r.db.Model(&models.Student{}).Where("DATE(enrollment_date) = CURRENT_DATE").Count(&stats.NewStudentsToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count new students today: %w", err)
	}

	// New students this week
	if err := r.db.Model(&models.Student{}).Where("enrollment_date >= DATE_TRUNC('week', CURRENT_DATE)").Count(&stats.NewStudentsThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count new students this week: %w", err)
	}

	// Average grade
	var avgGrade float64
	if err := r.db.Model(&models.Student{}).Where("overall_grade IS NOT NULL").Select("AVG(overall_grade)").Scan(&avgGrade).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average grade: %w", err)
	}
	if avgGrade > 0 {
		stats.AverageGrade = &avgGrade
	}

	// Average attendance
	var avgAttendance float64
	if err := r.db.Model(&models.Student{}).Where("attendance_rate IS NOT NULL").Select("AVG(attendance_rate)").Scan(&avgAttendance).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average attendance: %w", err)
	}
	if avgAttendance > 0 {
		stats.AverageAttendance = &avgAttendance
	}

	return stats, nil
}

// GetProgress retrieves student academic progress
func (r *studentRepository) GetProgress(studentID uuid.UUID) (*models.StudentProgress, error) {
	student, err := r.GetByID(studentID)
	if err != nil {
		return nil, err
	}

	progress := &models.StudentProgress{
		StudentID:   studentID,
		StudentName: student.GetFullName(),
		OverallGrade: student.OverallGrade,
		AttendanceRate: student.AttendanceRate,
	}

	// Count enrolled and completed courses
	var enrolledCount, completedCount int64
	if err := r.db.Model(&models.Enrollment{}).Where("student_id = ? AND status = ?", studentID, models.EnrollmentStatusActive).Count(&enrolledCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count enrolled courses: %w", err)
	}
	if err := r.db.Model(&models.Enrollment{}).Where("student_id = ? AND status = ?", studentID, models.EnrollmentStatusCompleted).Count(&completedCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count completed courses: %w", err)
	}

	progress.EnrolledCourses = int(enrolledCount)
	progress.CompletedCourses = int(completedCount)

	// Get recent grades (last 10)
	var grades []*models.Grade
	if err := r.db.Preload("Course").Where("student_id = ?", studentID).Order("graded_at DESC").Limit(10).Find(&grades).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent grades: %w", err)
	}
	progress.RecentGrades = grades

	return progress, nil
}

// GetByCourse retrieves students enrolled in a specific course
func (r *studentRepository) GetByCourse(courseID uuid.UUID) ([]*models.Student, error) {
	var students []*models.Student
	if err := r.db.Joins("JOIN enrollments ON enrollments.student_id = students.id").
		Where("enrollments.course_id = ? AND enrollments.status = ?", courseID, models.EnrollmentStatusActive).
		Preload("CreatedBy").Find(&students).Error; err != nil {
		return nil, fmt.Errorf("failed to get students by course: %w", err)
	}
	return students, nil
}

// GenerateStudentID generates a unique student ID
func (r *studentRepository) GenerateStudentID() (string, error) {
	year := time.Now().Year()
	
	// Get the count of students created this year
	var count int64
	if err := r.db.Model(&models.Student{}).Where("EXTRACT(YEAR FROM enrollment_date) = ?", year).Count(&count).Error; err != nil {
		return "", fmt.Errorf("failed to count students for year: %w", err)
	}

	// Generate ID in format: STU2024001, STU2024002, etc.
	studentID := fmt.Sprintf("STU%d%03d", year, count+1)

	// Check if ID already exists (unlikely but safe)
	var existingStudent models.Student
	if err := r.db.Where("student_id = ?", studentID).First(&existingStudent).Error; err == nil {
		// ID exists, try with incremented number
		for i := 1; i <= 1000; i++ {
			studentID = fmt.Sprintf("STU%d%03d", year, count+1+int64(i))
			if err := r.db.Where("student_id = ?", studentID).First(&existingStudent).Error; err == gorm.ErrRecordNotFound {
				break
			}
		}
	}

	return studentID, nil
}

// UpdateGrades recalculates and updates student's overall grade and attendance
func (r *studentRepository) UpdateGrades(studentID uuid.UUID) error {
	// Calculate overall grade from all grades
	var avgGrade float64
	if err := r.db.Model(&models.Grade{}).Where("student_id = ?", studentID).Select("AVG(percentage)").Scan(&avgGrade).Error; err != nil {
		return fmt.Errorf("failed to calculate average grade: %w", err)
	}

	// Calculate attendance rate
	var totalClasses, presentClasses int64
	if err := r.db.Model(&models.Attendance{}).Where("student_id = ?", studentID).Count(&totalClasses).Error; err != nil {
		return fmt.Errorf("failed to count total classes: %w", err)
	}

	if err := r.db.Model(&models.Attendance{}).Where("student_id = ? AND status IN ?", studentID, 
		[]models.AttendanceStatus{models.AttendanceStatusPresent, models.AttendanceStatusLate}).Count(&presentClasses).Error; err != nil {
		return fmt.Errorf("failed to count present classes: %w", err)
	}

	var attendanceRate float64
	if totalClasses > 0 {
		attendanceRate = float64(presentClasses) / float64(totalClasses) * 100
	}

	// Update student record
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if avgGrade > 0 {
		updates["overall_grade"] = avgGrade
	}

	if totalClasses > 0 {
		updates["attendance_rate"] = attendanceRate
	}

	return r.Update(studentID, updates)
}

// GetActiveStudents retrieves all active students
func (r *studentRepository) GetActiveStudents() ([]*models.Student, error) {
	var students []*models.Student
	if err := r.db.Where("status = ?", models.StudentStatusActive).Preload("CreatedBy").Find(&students).Error; err != nil {
		return nil, fmt.Errorf("failed to get active students: %w", err)
	}
	return students, nil
}
