-- Create refunds table
CREATE TABLE IF NOT EXISTS refunds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID NOT NULL,
    
    -- Refund details
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    reason TEXT,
    
    -- Gateway information
    gateway_refund_id VARCHAR(255),
    gateway_response TEXT,
    
    -- Processing information
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by_id UUID,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for refunds table
CREATE INDEX IF NOT EXISTS idx_refunds_payment_id ON refunds(payment_id);
CREATE INDEX IF NOT EXISTS idx_refunds_status ON refunds(status);
CREATE INDEX IF NOT EXISTS idx_refunds_gateway_refund_id ON refunds(gateway_refund_id);
CREATE INDEX IF NOT EXISTS idx_refunds_processed_by_id ON refunds(processed_by_id);
CREATE INDEX IF NOT EXISTS idx_refunds_created_at ON refunds(created_at);
CREATE INDEX IF NOT EXISTS idx_refunds_deleted_at ON refunds(deleted_at);
CREATE INDEX IF NOT EXISTS idx_refunds_processed_at ON refunds(processed_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_refunds_payment_status ON refunds(payment_id, status);
CREATE INDEX IF NOT EXISTS idx_refunds_status_created ON refunds(status, created_at);

-- Add foreign key constraint to payments table
ALTER TABLE refunds ADD CONSTRAINT fk_refunds_payment_id 
FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE;

-- Add check constraints for valid enum values
ALTER TABLE refunds ADD CONSTRAINT chk_refunds_status 
CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'));

ALTER TABLE refunds ADD CONSTRAINT chk_refunds_currency 
CHECK (currency IN ('USD', 'UZS', 'EUR'));

-- Add comments for documentation
COMMENT ON TABLE refunds IS 'Refund records for payments';
COMMENT ON COLUMN refunds.id IS 'Unique identifier for the refund';
COMMENT ON COLUMN refunds.payment_id IS 'Reference to the original payment';
COMMENT ON COLUMN refunds.amount IS 'Refund amount in the specified currency';
COMMENT ON COLUMN refunds.currency IS 'Currency code (USD, UZS, EUR)';
COMMENT ON COLUMN refunds.status IS 'Refund status (PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED)';
COMMENT ON COLUMN refunds.reason IS 'Reason for the refund';
COMMENT ON COLUMN refunds.gateway_refund_id IS 'Refund ID from the payment gateway';
COMMENT ON COLUMN refunds.gateway_response IS 'Raw response from the payment gateway';
COMMENT ON COLUMN refunds.processed_at IS 'When the refund was processed';
COMMENT ON COLUMN refunds.processed_by_id IS 'ID of the user who processed the refund';
COMMENT ON COLUMN refunds.metadata IS 'Additional metadata in JSON format';
