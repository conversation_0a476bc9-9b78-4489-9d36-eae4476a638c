// API response types based on backend models

export interface PaginationRequest {
  page: number;
  page_size: number;
}

export interface PaginationResponse {
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface APIResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  pagination?: PaginationResponse;
}

export interface UserListRequest extends PaginationRequest {
  role?: string;
  status?: string;
  search?: string;
}

export interface UserListResponse {
  users: User[];
  pagination: PaginationResponse;
}

// Health check response
export interface HealthResponse {
  status: string;
  timestamp: string;
  database?: string;
  version?: string;
  services?: Record<string, string>;
}

// Error types
export interface APIError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Generic list response
export interface ListResponse<T> {
  data: T[];
  pagination: PaginationResponse;
}

// Import User type from auth
import type { User } from './auth';
