name: Notification Service CI

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'services/notification-service/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'services/notification-service/**'

defaults:
  run:
    working-directory: services/notification-service

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        go-version: [1.21, 1.22]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ matrix.go-version }}-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-${{ matrix.go-version }}-
    
    - name: Download dependencies
      run: go mod download
    
    - name: Verify dependencies
      run: go mod verify
    
    - name: Run tests
      run: go test -v -race -timeout=5m ./...
    
    - name: Run tests with coverage
      run: go test -v -coverprofile=coverage.out ./...
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./services/notification-service/coverage.out
        flags: notification-service
        name: notification-service-coverage
        fail_ci_if_error: false

  lint:
    name: Lint
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21
    
    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        working-directory: services/notification-service
        args: --timeout=5m

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21
    
    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out gosec.sarif ./...'
        working-directory: services/notification-service
    
    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: services/notification-service/gosec.sarif

  build:
    name: Build
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21
    
    - name: Build application
      run: go build -v ./...
    
    - name: Build binary
      run: go build -o notification-service main.go
    
    - name: Test binary
      run: ./notification-service --version || echo "Binary built successfully"

  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: services/notification-service
        push: false
        tags: notification-service:test
        cache-from: type=gha
        cache-to: type=gha,mode=max

  integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: notification_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21
    
    - name: Download dependencies
      run: go mod download
    
    - name: Run integration tests
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/notification_test?sslmode=disable
        JWT_SECRET: test-secret
        SMTP_HOST: localhost
        SMTP_PORT: 1025
        TWILIO_ACCOUNT_SID: test-sid
        TWILIO_AUTH_TOKEN: test-token
      run: go test -v -tags=integration ./tests/integration/...

  benchmark:
    name: Benchmark
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21
    
    - name: Download dependencies
      run: go mod download
    
    - name: Run benchmarks
      run: go test -bench=. -benchmem -timeout=10m ./internal/services/... > benchmark.txt
    
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: services/notification-service/benchmark.txt
