version: '3.8'

services:
  # PostgreSQL Databases
  postgres-auth:
    image: postgres:15-alpine
    container_name: crm-postgres-auth
    environment:
      POSTGRES_DB: crm_auth
      POSTGRES_USER: crm_user
      POSTGRES_PASSWORD: crm_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_auth_data:/var/lib/postgresql/data
      - ./scripts/init-auth-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crm_user -d crm_auth"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres-admin:
    image: postgres:15-alpine
    container_name: crm-postgres-admin
    environment:
      POSTGRES_DB: crm_admin
      POSTGRES_USER: crm_user
      POSTGRES_PASSWORD: crm_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_admin_data:/var/lib/postgresql/data
      - ./scripts/init-admin-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crm_user -d crm_admin"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres-staff:
    image: postgres:15-alpine
    container_name: crm-postgres-staff
    environment:
      POSTGRES_DB: crm_staff
      POSTGRES_USER: crm_user
      POSTGRES_PASSWORD: crm_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_staff_data:/var/lib/postgresql/data
      - ./scripts/init-staff-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crm_user -d crm_staff"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres-payment:
    image: postgres:15-alpine
    container_name: crm-postgres-payment
    environment:
      POSTGRES_DB: crm_payment
      POSTGRES_USER: crm_user
      POSTGRES_PASSWORD: crm_password
    ports:
      - "5435:5432"
    volumes:
      - postgres_payment_data:/var/lib/postgresql/data
      - ./scripts/init-payment-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crm_user -d crm_payment"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis
  redis:
    image: redis:7-alpine
    container_name: crm-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - crm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Authentication Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: crm-auth-service
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DB_HOST=postgres-auth
      - DB_PORT=5432
      - DB_USER=crm_user
      - DB_PASSWORD=crm_password
      - DB_NAME_AUTH=crm_auth
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_ACCESS_EXPIRY=15m
      - JWT_REFRESH_EXPIRY=7d
      - AUTH_SERVICE_PORT=8081
      - LOG_LEVEL=debug
    ports:
      - "8081:8081"
    depends_on:
      postgres-auth:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - crm-network
    volumes:
      - ./services/auth-service:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin Service
  admin-service:
    build:
      context: .
      dockerfile: ./services/admin-service/Dockerfile
    container_name: crm-admin-service
    environment:
      - PORT=8082
      - ENVIRONMENT=development
      - DB_HOST=postgres-admin
      - DB_PORT=5432
      - DB_USER=crm_user
      - DB_PASSWORD=crm_password
      - DB_NAME_ADMIN=crm_admin
      - DB_SSL_MODE=disable
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8081
    ports:
      - "8082:8082"
    depends_on:
      postgres-admin:
        condition: service_healthy
      redis:
        condition: service_healthy
      auth-service:
        condition: service_healthy
    networks:
      - crm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Staff Service
  staff-service:
    build:
      context: .
      dockerfile: ./services/staff-service/Dockerfile
    container_name: crm-staff-service
    environment:
      - PORT=8083
      - ENVIRONMENT=development
      - DB_HOST=postgres-staff
      - DB_PORT=5432
      - DB_USER=crm_user
      - DB_PASSWORD=crm_password
      - DB_NAME_STAFF=crm_staff
      - DB_SSL_MODE=disable
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8081
      - ADMIN_SERVICE_URL=http://admin-service:8082
    ports:
      - "8083:8083"
    depends_on:
      postgres-staff:
        condition: service_healthy
      redis:
        condition: service_healthy
      auth-service:
        condition: service_healthy
    networks:
      - crm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8084/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Payment Service
  payment-service:
    build:
      context: .
      dockerfile: ./services/payment-service/Dockerfile
    container_name: crm-payment-service
    environment:
      - PORT=8083
      - ENVIRONMENT=development
      - DB_HOST=postgres-payment
      - DB_PORT=5432
      - DB_USER=crm_user
      - DB_PASSWORD=crm_password
      - DB_NAME_PAYMENT=crm_payment
      - DB_SSL_MODE=disable
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8081
      - ADMIN_SERVICE_URL=http://admin-service:8082
      - STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
      - STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
      - STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
      - PAYPAL_CLIENT_ID=your_paypal_client_id
      - PAYPAL_CLIENT_SECRET=your_paypal_client_secret
      - PAYPAL_MODE=sandbox
      - DEFAULT_CURRENCY=USD
      - MAX_PAYMENT_AMOUNT=100000.00
      - PAYMENT_TIMEOUT_MINUTES=30
      - REFUND_TIMEOUT_DAYS=30
    ports:
      - "8083:8083"
    depends_on:
      postgres-payment:
        condition: service_healthy
      redis:
        condition: service_healthy
      auth-service:
        condition: service_healthy
    networks:
      - crm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8083/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: ./services/api-gateway/Dockerfile
    container_name: crm-api-gateway
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - API_GATEWAY_PORT=8080
      - AUTH_SERVICE_URL=http://auth-service:8081
      - ADMIN_SERVICE_URL=http://admin-service:8082
      - STAFF_SERVICE_URL=http://staff-service:8083
      - PAYMENT_SERVICE_URL=http://payment-service:8084
      - NOTIFICATION_SERVICE_URL=http://notification-service:8085
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001
      - RATE_LIMIT_REQUESTS=1000
      - RATE_LIMIT_WINDOW=1h
      - LOG_LEVEL=debug
    ports:
      - "8080:8080"
    depends_on:
      auth-service:
        condition: service_healthy
      admin-service:
        condition: service_healthy
      payment-service:
        condition: service_healthy
    networks:
      - crm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Notification Service
  notification-service:
    build:
      context: .
      dockerfile: ./services/notification-service/Dockerfile
    container_name: crm-notification-service
    environment:
      - PORT=8085
      - ENVIRONMENT=development
      - DB_HOST=postgres-admin
      - DB_PORT=5432
      - DB_USER=crm_user
      - DB_PASSWORD=crm_password
      - DB_NAME_NOTIFICATION=crm_admin
      - DB_SSL_MODE=disable
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8081
      - ADMIN_SERVICE_URL=http://admin-service:8082
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=your_app_password
      - TWILIO_ACCOUNT_SID=your_twilio_account_sid
      - TWILIO_AUTH_TOKEN=your_twilio_auth_token
      - TWILIO_PHONE_NUMBER=+**********
    ports:
      - "8085:8085"
    depends_on:
      postgres-admin:
        condition: service_healthy
      redis:
        condition: service_healthy
      auth-service:
        condition: service_healthy
    networks:
      - crm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: crm-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - crm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  grafana:
    image: grafana/grafana:latest
    container_name: crm-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_SECURITY_ADMIN_USER=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - crm-network
    depends_on:
      prometheus:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Alertmanager for handling alerts
  alertmanager:
    image: prom/alertmanager:latest
    container_name: crm-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - crm-network
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: crm-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - crm-network
    restart: unless-stopped

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: crm-cadvisor
    ports:
      - "8090:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - crm-network
    restart: unless-stopped

volumes:
  postgres_auth_data:
  postgres_admin_data:
  postgres_staff_data:
  postgres_payment_data:
  redis_data:
  prometheus_data:
  grafana_data:
  alertmanager_data:

networks:
  crm-network:
    driver: bridge
