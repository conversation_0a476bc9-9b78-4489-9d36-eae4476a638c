import { apiService } from './api';
import type {
  Student,
  StudentCreateRequest,
  StudentUpdateRequest,
  StudentFilters,
  StudentStats,
  PaginatedResponse
} from '@/types/student';

class StudentService {
  private readonly API_PREFIX = '/api/v1/staff/students';

  // Get all students with pagination and filters
  async getStudents(params?: {
    page?: number;
    limit?: number;
    filters?: StudentFilters;
  }): Promise<PaginatedResponse<Student>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.filters?.search) queryParams.append('search', params.filters.search);
    if (params?.filters?.status) queryParams.append('status', params.filters.status);
    if (params?.filters?.course_id) queryParams.append('course_id', params.filters.course_id);
    if (params?.filters?.enrollment_date_from) {
      queryParams.append('enrollment_date_from', params.filters.enrollment_date_from);
    }
    if (params?.filters?.enrollment_date_to) {
      queryParams.append('enrollment_date_to', params.filters.enrollment_date_to);
    }

    const url = queryParams.toString() ? `${this.API_PREFIX}?${queryParams}` : this.API_PREFIX;
    return apiService.get<PaginatedResponse<Student>>(url);
  }

  // Get student by ID
  async getStudent(id: string): Promise<Student> {
    return apiService.get<Student>(`${this.API_PREFIX}/${id}`);
  }

  // Create new student
  async createStudent(data: StudentCreateRequest): Promise<Student> {
    return apiService.post<Student>(this.API_PREFIX, data);
  }

  // Update student
  async updateStudent(id: string, data: StudentUpdateRequest): Promise<Student> {
    return apiService.put<Student>(`${this.API_PREFIX}/${id}`, data);
  }

  // Delete student
  async deleteStudent(id: string): Promise<void> {
    return apiService.delete<void>(`${this.API_PREFIX}/${id}`);
  }

  // Get student statistics
  async getStudentStats(): Promise<StudentStats> {
    return apiService.get<StudentStats>(`${this.API_PREFIX}/stats`);
  }

  // Student enrollment management
  async enrollStudent(studentId: string, courseId: string): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/${studentId}/enroll`, {
      course_id: courseId
    });
  }

  async unenrollStudent(studentId: string, courseId: string): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/${studentId}/unenroll`, {
      course_id: courseId
    });
  }

  // Student status management
  async updateStudentStatus(id: string, status: string): Promise<Student> {
    return apiService.patch<Student>(`${this.API_PREFIX}/${id}/status`, { status });
  }

  // Bulk operations
  async bulkUpdateStudents(ids: string[], data: Partial<StudentUpdateRequest>): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/bulk-update`, {
      student_ids: ids,
      ...data
    });
  }

  async bulkDeleteStudents(ids: string[]): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/bulk-delete`, {
      student_ids: ids
    });
  }

  // Student progress and attendance
  async getStudentProgress(id: string): Promise<any> {
    return apiService.get<any>(`${this.API_PREFIX}/${id}/progress`);
  }

  async getStudentAttendance(id: string, params?: {
    course_id?: string;
    date_from?: string;
    date_to?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.course_id) queryParams.append('course_id', params.course_id);
    if (params?.date_from) queryParams.append('date_from', params.date_from);
    if (params?.date_to) queryParams.append('date_to', params.date_to);

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/${id}/attendance?${queryParams}` 
      : `${this.API_PREFIX}/${id}/attendance`;
    
    return apiService.get<any>(url);
  }
}

// Create and export singleton instance
export const studentService = new StudentService();
export default studentService;
