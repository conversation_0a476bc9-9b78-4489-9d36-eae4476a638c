package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	// Server configuration
	Port        string
	Environment string
	LogLevel    string

	// Database configuration
	DatabaseURL string

	// Auth service configuration
	AuthServiceURL string
	JWTSecret      string

	// Email configuration
	SMTPHost     string
	SMTPPort     int
	SMTPUser     string
	SMTPPassword string
	SMTPFrom     string

	// SMS configuration
	TwilioAccountSID string
	TwilioAuthToken  string
	TwilioFromNumber string

	// SendGrid configuration (alternative to SMTP)
	SendGridAPIKey string
	SendGridFrom   string

	// Notification settings
	MaxRetries           int
	RetryDelaySeconds    int
	BatchSize            int
	QueueProcessInterval int
}

func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	config := &Config{
		Port:        getEnv("PORT", "8085"),
		Environment: getEnv("ENVIRONMENT", "development"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),

		DatabaseURL: getEnv("DATABASE_URL", "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"),

		AuthServiceURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8081"),
		JWTSecret:      getEnv("JWT_SECRET", "your-secret-key"),

		SMTPHost:     getEnv("SMTP_HOST", "smtp.gmail.com"),
		SMTPPort:     getEnvAsInt("SMTP_PORT", 587),
		SMTPUser:     getEnv("SMTP_USER", ""),
		SMTPPassword: getEnv("SMTP_PASSWORD", ""),
		SMTPFrom:     getEnv("SMTP_FROM", "<EMAIL>"),

		TwilioAccountSID: getEnv("TWILIO_ACCOUNT_SID", ""),
		TwilioAuthToken:  getEnv("TWILIO_AUTH_TOKEN", ""),
		TwilioFromNumber: getEnv("TWILIO_FROM_NUMBER", ""),

		SendGridAPIKey: getEnv("SENDGRID_API_KEY", ""),
		SendGridFrom:   getEnv("SENDGRID_FROM", "<EMAIL>"),

		MaxRetries:           getEnvAsInt("MAX_RETRIES", 3),
		RetryDelaySeconds:    getEnvAsInt("RETRY_DELAY_SECONDS", 60),
		BatchSize:            getEnvAsInt("BATCH_SIZE", 100),
		QueueProcessInterval: getEnvAsInt("QUEUE_PROCESS_INTERVAL", 30),
	}

	return config, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
