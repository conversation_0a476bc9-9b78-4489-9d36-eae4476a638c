package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	Environment string
	Debug       bool
	
	// Server configuration
	Server ServerConfig
	
	// Database configuration
	Database DatabaseConfig
	
	// Redis configuration
	Redis RedisConfig
	
	// JWT configuration
	JWT JWTConfig
	
	// SMTP configuration
	SMTP SMTPConfig
	
	// SMS configuration
	SMS SMSConfig
	
	// Payment gateway configuration
	Payment PaymentConfig
	
	// Logging configuration
	Logging LoggingConfig
	
	// Security configuration
	Security SecurityConfig
	
	// Feature flags
	Features FeatureConfig
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Host         string
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	URL      string // Full database URL (takes precedence over individual fields)
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
	TimeZone string
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	Secret               string
	AccessTokenExpiry    time.Duration
	RefreshTokenExpiry   time.Duration
	Issuer               string
	Audience             string
}

// SMTPConfig holds SMTP configuration
type SMTPConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	From     string
	FromName string
}

// SMSConfig holds SMS configuration
type SMSConfig struct {
	Provider string
	APIUrl   string
	APIToken string
	From     string
}

// PaymentConfig holds payment gateway configuration
type PaymentConfig struct {
	UzCard UzCardConfig
	Humo   HumoConfig
	Payme  PaymeConfig
	Click  ClickConfig
}

// UzCardConfig holds UzCard configuration
type UzCardConfig struct {
	APIUrl     string
	APIKey     string
	MerchantID string
}

// HumoConfig holds Humo configuration
type HumoConfig struct {
	APIUrl     string
	APIKey     string
	MerchantID string
}

// PaymeConfig holds Payme configuration
type PaymeConfig struct {
	APIUrl     string
	APIKey     string
	MerchantID string
}

// ClickConfig holds Click configuration
type ClickConfig struct {
	APIUrl     string
	APIKey     string
	MerchantID string
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string
	Format string
	Output string
}

// SecurityConfig holds security configuration
type SecurityConfig struct {
	BcryptCost              int
	PasswordMinLength       int
	PasswordRequireSpecial  bool
	PasswordRequireNumber   bool
	PasswordRequireUppercase bool
	RateLimitRequests       int
	RateLimitWindow         time.Duration
}

// FeatureConfig holds feature flags
type FeatureConfig struct {
	PaymentEnabled bool
	SMSEnabled     bool
	EmailEnabled   bool
	AuditEnabled   bool
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists (ignore errors as it's optional)
	_ = godotenv.Load()

	// Debug: Print all environment variables that start with JWT or contain relevant info
	fmt.Println("DEBUG: Environment variables starting with JWT:")
	for _, env := range os.Environ() {
		if strings.HasPrefix(env, "JWT") {
			fmt.Printf("  %s\n", env)
		}
	}

	// Also check some other relevant environment variables
	fmt.Printf("DEBUG: ENVIRONMENT=%s\n", os.Getenv("ENVIRONMENT"))
	fmt.Printf("DEBUG: API_GATEWAY_PORT=%s\n", os.Getenv("API_GATEWAY_PORT"))
	fmt.Printf("DEBUG: Total environment variables: %d\n", len(os.Environ()))

	config := &Config{
		Environment: getEnv("ENVIRONMENT", "development"),
		Debug:       getEnvBool("DEBUG", true),
	}

	// Load server configuration
	config.Server = ServerConfig{
		Host:         getEnv("SERVER_HOST", "0.0.0.0"),
		Port:         getEnv("SERVER_PORT", "8080"),
		ReadTimeout:  getEnvDuration("SERVER_READ_TIMEOUT", 30*time.Second),
		WriteTimeout: getEnvDuration("SERVER_WRITE_TIMEOUT", 30*time.Second),
		IdleTimeout:  getEnvDuration("SERVER_IDLE_TIMEOUT", 60*time.Second),
	}

	// Load database configuration
	config.Database = DatabaseConfig{
		URL:      getEnv("DATABASE_URL", ""),
		Host:     getEnv("DB_HOST", "localhost"),
		Port:     getEnv("DB_PORT", "5432"),
		User:     getEnv("DB_USER", "postgres"),
		Password: getEnv("DB_PASSWORD", ""),
		DBName:   getEnv("DB_NAME", "crm"),
		SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		TimeZone: getEnv("DB_TIMEZONE", "UTC"),
	}

	// Load Redis configuration
	config.Redis = RedisConfig{
		Host:     getEnv("REDIS_HOST", "localhost"),
		Port:     getEnv("REDIS_PORT", "6379"),
		Password: getEnv("REDIS_PASSWORD", ""),
		DB:       getEnvInt("REDIS_DB", 0),
	}

	// Load JWT configuration
	jwtSecret := getEnv("JWT_SECRET", "your-secret-key")
	fmt.Printf("DEBUG: JWT_SECRET environment variable value: '%s'\n", jwtSecret)

	config.JWT = JWTConfig{
		Secret:               jwtSecret,
		AccessTokenExpiry:    getEnvDuration("JWT_ACCESS_EXPIRY", 15*time.Minute),
		RefreshTokenExpiry:   getEnvDuration("JWT_REFRESH_EXPIRY", 7*24*time.Hour),
		Issuer:               getEnv("JWT_ISSUER", "crm-microservices"),
		Audience:             getEnv("JWT_AUDIENCE", "crm-users"),
	}

	// Load SMTP configuration
	config.SMTP = SMTPConfig{
		Host:     getEnv("SMTP_HOST", ""),
		Port:     getEnvInt("SMTP_PORT", 587),
		User:     getEnv("SMTP_USER", ""),
		Password: getEnv("SMTP_PASSWORD", ""),
		From:     getEnv("SMTP_FROM", ""),
		FromName: getEnv("SMTP_FROM_NAME", "CRM System"),
	}

	// Load SMS configuration
	config.SMS = SMSConfig{
		Provider: getEnv("SMS_PROVIDER", "eskiz"),
		APIUrl:   getEnv("SMS_API_URL", ""),
		APIToken: getEnv("SMS_API_TOKEN", ""),
		From:     getEnv("SMS_FROM", "4546"),
	}

	// Load payment configuration
	config.Payment = PaymentConfig{
		UzCard: UzCardConfig{
			APIUrl:     getEnv("UZCARD_API_URL", ""),
			APIKey:     getEnv("UZCARD_API_KEY", ""),
			MerchantID: getEnv("UZCARD_MERCHANT_ID", ""),
		},
		Humo: HumoConfig{
			APIUrl:     getEnv("HUMO_API_URL", ""),
			APIKey:     getEnv("HUMO_API_KEY", ""),
			MerchantID: getEnv("HUMO_MERCHANT_ID", ""),
		},
		Payme: PaymeConfig{
			APIUrl:     getEnv("PAYME_API_URL", ""),
			APIKey:     getEnv("PAYME_API_KEY", ""),
			MerchantID: getEnv("PAYME_MERCHANT_ID", ""),
		},
		Click: ClickConfig{
			APIUrl:     getEnv("CLICK_API_URL", ""),
			APIKey:     getEnv("CLICK_API_KEY", ""),
			MerchantID: getEnv("CLICK_MERCHANT_ID", ""),
		},
	}

	// Load logging configuration
	config.Logging = LoggingConfig{
		Level:  getEnv("LOG_LEVEL", "info"),
		Format: getEnv("LOG_FORMAT", "json"),
		Output: getEnv("LOG_OUTPUT", "stdout"),
	}

	// Load security configuration
	config.Security = SecurityConfig{
		BcryptCost:               getEnvInt("BCRYPT_COST", 12),
		PasswordMinLength:        getEnvInt("PASSWORD_MIN_LENGTH", 8),
		PasswordRequireSpecial:   getEnvBool("PASSWORD_REQUIRE_SPECIAL", true),
		PasswordRequireNumber:    getEnvBool("PASSWORD_REQUIRE_NUMBER", true),
		PasswordRequireUppercase: getEnvBool("PASSWORD_REQUIRE_UPPERCASE", true),
		RateLimitRequests:        getEnvInt("RATE_LIMIT_REQUESTS", 100),
		RateLimitWindow:          getEnvDuration("RATE_LIMIT_WINDOW", time.Minute),
	}

	// Load feature flags
	config.Features = FeatureConfig{
		PaymentEnabled: getEnvBool("FEATURE_PAYMENT_ENABLED", true),
		SMSEnabled:     getEnvBool("FEATURE_SMS_ENABLED", true),
		EmailEnabled:   getEnvBool("FEATURE_EMAIL_ENABLED", true),
		AuditEnabled:   getEnvBool("FEATURE_AUDIT_ENABLED", true),
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Check if JWT secret is empty or contains common placeholder patterns
	if c.JWT.Secret == "" {
		return fmt.Errorf("JWT secret must be set (JWT_SECRET environment variable)")
	}

	// Check for common default/placeholder values
	defaultSecrets := []string{
		"your-super-secret-jwt-key-change-this-in-production",
		"change-this-in-production",
		"your-jwt-secret",
	}

	for _, defaultSecret := range defaultSecrets {
		if c.JWT.Secret == defaultSecret {
			return fmt.Errorf("JWT secret must not be a default placeholder value. Current value: %s", c.JWT.Secret)
		}
	}

	// Temporarily allow "your-secret-key" for debugging
	if c.JWT.Secret == "your-secret-key" {
		fmt.Printf("WARNING: Using default JWT secret 'your-secret-key' - this should be changed in production!\n")
		// Don't return error, just warn
	}

	// Check for placeholder patterns in the secret
	if strings.Contains(strings.ToLower(c.JWT.Secret), "change") &&
	   (strings.Contains(strings.ToLower(c.JWT.Secret), "production") ||
	    strings.Contains(strings.ToLower(c.JWT.Secret), "deployment")) {
		return fmt.Errorf("JWT secret appears to be a placeholder value. Current value: %s", c.JWT.Secret)
	}

	// Ensure minimum length for security (temporarily reduced for debugging)
	if len(c.JWT.Secret) < 8 {
		return fmt.Errorf("JWT secret must be at least 8 characters long for security. Current length: %d", len(c.JWT.Secret))
	}

	// Validate database configuration (either URL or individual fields)
	if c.Database.URL == "" {
		if c.Database.Host == "" {
			return fmt.Errorf("database host must be set when DATABASE_URL is not provided")
		}

		if c.Database.User == "" {
			return fmt.Errorf("database user must be set when DATABASE_URL is not provided")
		}

		if c.Database.DBName == "" {
			return fmt.Errorf("database name must be set when DATABASE_URL is not provided")
		}
	}

	return nil
}

// IsProduction returns true if running in production environment
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment returns true if running in development environment
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsTest returns true if running in test environment
func (c *Config) IsTest() bool {
	return c.Environment == "test"
}

// Helper functions

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// GetDatabaseDSN returns the database connection string
func (c *Config) GetDatabaseDSN() string {
	// If DATABASE_URL is provided, use it directly
	if c.Database.URL != "" {
		return c.Database.URL
	}

	// Otherwise, build DSN from individual fields
	return fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.User,
		c.Database.Password,
		c.Database.DBName,
		c.Database.SSLMode,
		c.Database.TimeZone,
	)
}

// GetRedisAddr returns the Redis address
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.Redis.Host, c.Redis.Port)
}

// GetServerAddr returns the server address
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf("%s:%s", c.Server.Host, c.Server.Port)
}

// GetCORSOrigins returns CORS allowed origins
func (c *Config) GetCORSOrigins() []string {
	origins := getEnv("CORS_ALLOWED_ORIGINS", "http://localhost:3000")
	return strings.Split(origins, ",")
}
