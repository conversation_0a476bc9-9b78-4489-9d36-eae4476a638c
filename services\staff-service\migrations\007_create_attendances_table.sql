-- Create attendances table
CREATE TABLE IF NOT EXISTS attendances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    course_id UUID NOT NULL,
    schedule_id UUID NOT NULL,
    date DATE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PRESENT',
    notes TEXT,
    recorded_by_id UUID NOT NULL,
    recorded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_attendances_student FOREIGN <PERSON> (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_attendances_course FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT fk_attendances_schedule FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate attendance records
    CONSTRAINT uk_attendances_student_schedule_date UNIQUE (student_id, schedule_id, date)
);

-- Create indexes for attendances table
CREATE INDEX IF NOT EXISTS idx_attendances_student_id ON attendances(student_id);
CREATE INDEX IF NOT EXISTS idx_attendances_course_id ON attendances(course_id);
CREATE INDEX IF NOT EXISTS idx_attendances_schedule_id ON attendances(schedule_id);
CREATE INDEX IF NOT EXISTS idx_attendances_date ON attendances(date);
CREATE INDEX IF NOT EXISTS idx_attendances_status ON attendances(status);
CREATE INDEX IF NOT EXISTS idx_attendances_recorded_by_id ON attendances(recorded_by_id);
CREATE INDEX IF NOT EXISTS idx_attendances_created_at ON attendances(created_at);
CREATE INDEX IF NOT EXISTS idx_attendances_deleted_at ON attendances(deleted_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_attendances_student_course_date ON attendances(student_id, course_id, date);
CREATE INDEX IF NOT EXISTS idx_attendances_course_date ON attendances(course_id, date);
CREATE INDEX IF NOT EXISTS idx_attendances_student_date ON attendances(student_id, date);

-- Add constraints
ALTER TABLE attendances ADD CONSTRAINT chk_attendances_status 
    CHECK (status IN ('PRESENT', 'ABSENT', 'LATE', 'EXCUSED'));

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_attendances_updated_at BEFORE UPDATE ON attendances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update student attendance rate
CREATE OR REPLACE FUNCTION update_student_attendance_rate()
RETURNS TRIGGER AS $$
DECLARE
    total_classes INTEGER;
    present_classes INTEGER;
    attendance_rate DECIMAL(5,2);
BEGIN
    -- Calculate attendance rate for the student in the specific course
    SELECT COUNT(*) INTO total_classes
    FROM attendances 
    WHERE student_id = COALESCE(NEW.student_id, OLD.student_id) 
    AND course_id = COALESCE(NEW.course_id, OLD.course_id)
    AND deleted_at IS NULL;
    
    SELECT COUNT(*) INTO present_classes
    FROM attendances 
    WHERE student_id = COALESCE(NEW.student_id, OLD.student_id) 
    AND course_id = COALESCE(NEW.course_id, OLD.course_id)
    AND status IN ('PRESENT', 'LATE')
    AND deleted_at IS NULL;
    
    IF total_classes > 0 THEN
        attendance_rate := (present_classes::DECIMAL / total_classes::DECIMAL) * 100;
        
        -- Update the enrollment record
        UPDATE enrollments 
        SET attendance_rate = attendance_rate
        WHERE student_id = COALESCE(NEW.student_id, OLD.student_id) 
        AND course_id = COALESCE(NEW.course_id, OLD.course_id);
        
        -- Update overall attendance rate for the student
        SELECT AVG(attendance_rate) INTO attendance_rate
        FROM enrollments 
        WHERE student_id = COALESCE(NEW.student_id, OLD.student_id)
        AND attendance_rate IS NOT NULL;
        
        UPDATE students 
        SET attendance_rate = attendance_rate
        WHERE id = COALESCE(NEW.student_id, OLD.student_id);
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Create trigger for attendance rate calculation
CREATE TRIGGER update_student_attendance_rate_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON attendances
    FOR EACH ROW EXECUTE FUNCTION update_student_attendance_rate();
