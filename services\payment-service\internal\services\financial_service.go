package services

import (
	"fmt"
	"time"

	"payment-service/internal/models"
	"payment-service/internal/repository"
	sharedModels "github.com/crm-microservices/shared/models"
)

// FinancialService handles financial reporting and analytics
type FinancialService interface {
	GenerateFinancialReport(req *FinancialReportRequest) (*FinancialReportResponse, error)
	GetPaymentAnalytics(req *AnalyticsRequest) (*PaymentAnalyticsResponse, error)
	GetRevenueMetrics(req *RevenueMetricsRequest) (*RevenueMetricsResponse, error)
	GetGatewayPerformance(req *GatewayPerformanceRequest) (*GatewayPerformanceResponse, error)
	GetPaymentTrends(req *PaymentTrendsRequest) (*PaymentTrendsResponse, error)
	ExportFinancialData(req *ExportRequest) (*ExportResponse, error)
	ReconcilePayments(req *ReconciliationRequest) (*ReconciliationResponse, error)
}

type financialService struct {
	paymentRepo     repository.PaymentRepository
	transactionRepo repository.TransactionRepository
}

// NewFinancialService creates a new financial service
func NewFinancialService(paymentRepo repository.PaymentRepository, transactionRepo repository.TransactionRepository) FinancialService {
	return &financialService{
		paymentRepo:     paymentRepo,
		transactionRepo: transactionRepo,
	}
}

// Request/Response types for financial operations

type FinancialReportRequest struct {
	Type      models.ReportType   `json:"type" binding:"required"`
	Period    models.ReportPeriod `json:"period" binding:"required"`
	StartDate time.Time           `json:"start_date" binding:"required"`
	EndDate   time.Time           `json:"end_date" binding:"required"`
	Title     string              `json:"title"`
	Filters   map[string]interface{} `json:"filters"`
}

type FinancialReportResponse struct {
	Report *models.FinancialReport `json:"report"`
}

type AnalyticsRequest struct {
	StartDate   time.Time `json:"start_date" binding:"required"`
	EndDate     time.Time `json:"end_date" binding:"required"`
	GatewayType *string   `json:"gateway_type"`
	Currency    *string   `json:"currency"`
}

type PaymentAnalyticsResponse struct {
	Analytics *models.PaymentAnalytics `json:"analytics"`
}

type RevenueMetricsRequest struct {
	Period      string    `json:"period" binding:"required"` // daily, weekly, monthly, yearly
	StartDate   time.Time `json:"start_date" binding:"required"`
	EndDate     time.Time `json:"end_date" binding:"required"`
	GatewayType *string   `json:"gateway_type"`
}

type RevenueMetricsResponse struct {
	Metrics []*models.RevenueMetrics `json:"metrics"`
}

type GatewayPerformanceRequest struct {
	StartDate time.Time `json:"start_date" binding:"required"`
	EndDate   time.Time `json:"end_date" binding:"required"`
}

type GatewayPerformanceResponse struct {
	Performance map[string]*models.GatewayMetrics `json:"performance"`
}

type PaymentTrendsRequest struct {
	StartDate   time.Time `json:"start_date" binding:"required"`
	EndDate     time.Time `json:"end_date" binding:"required"`
	Granularity string    `json:"granularity"` // daily, weekly, monthly
}

type PaymentTrendsResponse struct {
	Trends []*models.PaymentTrend `json:"trends"`
}

type ExportRequest struct {
	Type      string                 `json:"type" binding:"required"` // csv, excel, pdf
	StartDate time.Time              `json:"start_date" binding:"required"`
	EndDate   time.Time              `json:"end_date" binding:"required"`
	Filters   map[string]interface{} `json:"filters"`
}

type ExportResponse struct {
	FileURL  string `json:"file_url"`
	FileName string `json:"file_name"`
	FileSize int64  `json:"file_size"`
}

type ReconciliationRequest struct {
	GatewayType string    `json:"gateway_type" binding:"required"`
	Date        time.Time `json:"date" binding:"required"`
	GatewayData map[string]interface{} `json:"gateway_data"`
}

type ReconciliationResponse struct {
	Record *models.ReconciliationRecord `json:"record"`
}

// GenerateFinancialReport generates a comprehensive financial report
func (s *financialService) GenerateFinancialReport(req *FinancialReportRequest) (*FinancialReportResponse, error) {
	// Validate request
	if !req.Type.IsValid() {
		return nil, fmt.Errorf("invalid report type: %s", req.Type)
	}
	if !req.Period.IsValid() {
		return nil, fmt.Errorf("invalid report period: %s", req.Period)
	}

	// Get revenue statistics for the period
	revenueStats, err := s.paymentRepo.GetRevenueByPeriod(req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get revenue statistics: %w", err)
	}

	// Get payment statistics
	paymentStats, err := s.paymentRepo.GetPaymentStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get payment statistics: %w", err)
	}

	// Create financial report
	report := &models.FinancialReport{
		Title:              req.Title,
		Type:               req.Type,
		Period:             req.Period,
		StartDate:          req.StartDate,
		EndDate:            req.EndDate,
		TotalRevenue:       revenueStats.TotalRevenue,
		TotalRefunds:       revenueStats.TotalRefunds,
		NetRevenue:         revenueStats.NetRevenue,
		TotalFees:          revenueStats.TotalFees,
		TotalPayments:      int(revenueStats.PaymentCount),
		SuccessfulPayments: int(paymentStats.SuccessfulPayments),
		FailedPayments:     int(paymentStats.FailedPayments),
		Status:             models.ReportStatusCompleted,
		GeneratedAt:        &[]time.Time{time.Now()}[0],
	}

	return &FinancialReportResponse{Report: report}, nil
}

// GetPaymentAnalytics retrieves payment analytics for a specific period
func (s *financialService) GetPaymentAnalytics(req *AnalyticsRequest) (*PaymentAnalyticsResponse, error) {
	// Get payments for the period
	payments, err := s.paymentRepo.GetByDateRange(req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get payments: %w", err)
	}

	// Calculate analytics
	analytics := &models.PaymentAnalytics{
		Date:              req.StartDate,
		GatewayBreakdown:  make(map[string]models.GatewayMetrics),
		CurrencyBreakdown: make(map[string]float64),
	}

	totalRevenue := 0.0
	totalPayments := 0
	successfulPayments := 0
	gatewayStats := make(map[string]*models.GatewayMetrics)

	for _, payment := range payments {
		totalPayments++
		if payment.Status == sharedModels.PaymentCompleted {
			successfulPayments++
			totalRevenue += payment.Amount
			analytics.CurrencyBreakdown[payment.Currency] += payment.Amount
		}

		// Gateway breakdown
		if _, exists := gatewayStats[payment.GatewayType]; !exists {
			gatewayStats[payment.GatewayType] = &models.GatewayMetrics{
				GatewayType: payment.GatewayType,
			}
		}
		gatewayStats[payment.GatewayType].PaymentCount++
		if payment.Status == sharedModels.PaymentCompleted {
			gatewayStats[payment.GatewayType].Revenue += payment.Amount
		}
	}

	// Calculate derived metrics
	analytics.TotalRevenue = totalRevenue
	analytics.PaymentCount = totalPayments
	if totalPayments > 0 {
		analytics.SuccessRate = float64(successfulPayments) / float64(totalPayments) * 100
		analytics.AveragePayment = totalRevenue / float64(successfulPayments)
	}

	// Convert gateway stats
	for _, stats := range gatewayStats {
		if stats.PaymentCount > 0 {
			stats.SuccessRate = float64(successfulPayments) / float64(stats.PaymentCount) * 100
			if successfulPayments > 0 {
				stats.AverageAmount = stats.Revenue / float64(successfulPayments)
			}
		}
		analytics.GatewayBreakdown[stats.GatewayType] = *stats
	}

	return &PaymentAnalyticsResponse{Analytics: analytics}, nil
}

// GetRevenueMetrics retrieves revenue metrics broken down by period
func (s *financialService) GetRevenueMetrics(req *RevenueMetricsRequest) (*RevenueMetricsResponse, error) {
	var metrics []*models.RevenueMetrics

	// Calculate period intervals based on granularity
	current := req.StartDate
	for current.Before(req.EndDate) {
		var periodEnd time.Time
		var periodLabel string

		switch req.Period {
		case "daily":
			periodEnd = current.AddDate(0, 0, 1)
			periodLabel = current.Format("2006-01-02")
		case "weekly":
			periodEnd = current.AddDate(0, 0, 7)
			periodLabel = fmt.Sprintf("Week of %s", current.Format("2006-01-02"))
		case "monthly":
			periodEnd = current.AddDate(0, 1, 0)
			periodLabel = current.Format("2006-01")
		case "yearly":
			periodEnd = current.AddDate(1, 0, 0)
			periodLabel = current.Format("2006")
		default:
			return nil, fmt.Errorf("invalid period: %s", req.Period)
		}

		// Get revenue for this period
		revenueStats, err := s.paymentRepo.GetRevenueByPeriod(current, periodEnd)
		if err != nil {
			return nil, fmt.Errorf("failed to get revenue for period %s: %w", periodLabel, err)
		}

		metric := &models.RevenueMetrics{
			Period:         periodLabel,
			TotalRevenue:   revenueStats.TotalRevenue,
			NetRevenue:     revenueStats.NetRevenue,
			PaymentCount:   int(revenueStats.PaymentCount),
			AveragePayment: revenueStats.AveragePayment,
		}

		metrics = append(metrics, metric)
		current = periodEnd
	}

	return &RevenueMetricsResponse{Metrics: metrics}, nil
}

// GetGatewayPerformance retrieves performance metrics for all gateways
func (s *financialService) GetGatewayPerformance(req *GatewayPerformanceRequest) (*GatewayPerformanceResponse, error) {
	// Get all payments for the period
	payments, err := s.paymentRepo.GetByDateRange(req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get payments: %w", err)
	}

	performance := make(map[string]*models.GatewayMetrics)

	for _, payment := range payments {
		if _, exists := performance[payment.GatewayType]; !exists {
			performance[payment.GatewayType] = &models.GatewayMetrics{
				GatewayType: payment.GatewayType,
			}
		}

		metrics := performance[payment.GatewayType]
		metrics.PaymentCount++

		if payment.Status == sharedModels.PaymentCompleted {
			metrics.Revenue += payment.Amount
		}
	}

	// Calculate derived metrics
	for _, metrics := range performance {
		if metrics.PaymentCount > 0 {
			successfulCount := 0
			totalRevenue := metrics.Revenue
			
			// Count successful payments for this gateway
			for _, payment := range payments {
				if payment.GatewayType == metrics.GatewayType && payment.Status == sharedModels.PaymentCompleted {
					successfulCount++
				}
			}

			metrics.SuccessRate = float64(successfulCount) / float64(metrics.PaymentCount) * 100
			if successfulCount > 0 {
				metrics.AverageAmount = totalRevenue / float64(successfulCount)
			}
		}
	}

	return &GatewayPerformanceResponse{Performance: performance}, nil
}

// GetPaymentTrends retrieves payment trends over time
func (s *financialService) GetPaymentTrends(req *PaymentTrendsRequest) (*PaymentTrendsResponse, error) {
	var trends []*models.PaymentTrend

	// Calculate intervals based on granularity
	current := req.StartDate
	for current.Before(req.EndDate) {
		var periodEnd time.Time

		switch req.Granularity {
		case "daily":
			periodEnd = current.AddDate(0, 0, 1)
		case "weekly":
			periodEnd = current.AddDate(0, 0, 7)
		case "monthly":
			periodEnd = current.AddDate(0, 1, 0)
		default:
			return nil, fmt.Errorf("invalid granularity: %s", req.Granularity)
		}

		// Get payments for this period
		payments, err := s.paymentRepo.GetByDateRange(current, periodEnd)
		if err != nil {
			return nil, fmt.Errorf("failed to get payments for trend: %w", err)
		}

		// Calculate trend metrics
		totalAmount := 0.0
		totalCount := len(payments)
		successfulCount := 0

		for _, payment := range payments {
			if payment.Status == sharedModels.PaymentCompleted {
				totalAmount += payment.Amount
				successfulCount++
			}
		}

		successRate := 0.0
		if totalCount > 0 {
			successRate = float64(successfulCount) / float64(totalCount) * 100
		}

		trend := &models.PaymentTrend{
			Date:        current,
			Amount:      totalAmount,
			Count:       totalCount,
			SuccessRate: successRate,
		}

		trends = append(trends, trend)
		current = periodEnd
	}

	return &PaymentTrendsResponse{Trends: trends}, nil
}

// ExportFinancialData exports financial data in the requested format
func (s *financialService) ExportFinancialData(req *ExportRequest) (*ExportResponse, error) {
	// TODO: Implement actual export functionality
	// This would typically generate CSV, Excel, or PDF files
	
	fileName := fmt.Sprintf("financial_export_%s_%s.%s", 
		req.StartDate.Format("20060102"), 
		req.EndDate.Format("20060102"), 
		req.Type)
	
	return &ExportResponse{
		FileURL:  fmt.Sprintf("/exports/%s", fileName),
		FileName: fileName,
		FileSize: 0, // Would be calculated after file generation
	}, nil
}

// ReconcilePayments reconciles payments with gateway data
func (s *financialService) ReconcilePayments(req *ReconciliationRequest) (*ReconciliationResponse, error) {
	// Get system payments for the date and gateway
	payments, err := s.paymentRepo.GetByDateRange(req.Date, req.Date.AddDate(0, 0, 1))
	if err != nil {
		return nil, fmt.Errorf("failed to get payments for reconciliation: %w", err)
	}

	// Filter by gateway type
	var gatewayPayments []*models.Payment
	systemRevenue := 0.0
	systemCount := 0

	for _, payment := range payments {
		if payment.GatewayType == req.GatewayType && payment.Status == sharedModels.PaymentCompleted {
			gatewayPayments = append(gatewayPayments, payment)
			systemRevenue += payment.Amount
			systemCount++
		}
	}

	// Extract gateway data (this would be gateway-specific)
	gatewayRevenue := 0.0
	gatewayCount := 0

	if revenue, ok := req.GatewayData["revenue"].(float64); ok {
		gatewayRevenue = revenue
	}
	if count, ok := req.GatewayData["count"].(float64); ok {
		gatewayCount = int(count)
	}

	// Create reconciliation record
	record := &models.ReconciliationRecord{
		GatewayType:     req.GatewayType,
		Date:            req.Date,
		SystemRevenue:   systemRevenue,
		SystemCount:     systemCount,
		GatewayRevenue:  gatewayRevenue,
		GatewayCount:    gatewayCount,
		RevenueDiff:     systemRevenue - gatewayRevenue,
		CountDiff:       systemCount - gatewayCount,
		Status:          models.ReconciliationPending,
	}

	// Determine reconciliation status
	if record.RevenueDiff == 0 && record.CountDiff == 0 {
		record.Status = models.ReconciliationMatched
	} else {
		record.Status = models.ReconciliationMismatch
	}

	return &ReconciliationResponse{Record: record}, nil
}
