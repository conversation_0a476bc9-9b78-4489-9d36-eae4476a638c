#!/bin/bash

# Notification Service Test Script
# This script runs comprehensive tests for the notification service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run tests with proper error handling
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running $test_name..."
    
    if eval "$test_command"; then
        print_success "$test_name passed"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Main test function
main() {
    local test_type="${1:-all}"
    local failed_tests=0
    local total_tests=0
    
    print_status "Starting Notification Service Test Suite"
    print_status "Test type: $test_type"
    echo
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command_exists go; then
        print_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    if ! command_exists git; then
        print_warning "Git is not installed - some tests may fail"
    fi
    
    # Set test environment
    export GO_ENV=test
    export GIN_MODE=test
    
    # Load test environment variables
    if [ -f .env.test ]; then
        print_status "Loading test environment variables..."
        set -a
        source .env.test
        set +a
    else
        print_warning ".env.test file not found, using defaults"
    fi
    
    # Run different test suites based on type
    case $test_type in
        "unit"|"all")
            print_status "=== Running Unit Tests ==="
            ((total_tests++))
            if ! run_test "Unit Tests" "go test -v -race -timeout=30s ./internal/services/..."; then
                ((failed_tests++))
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "integration"|"all")
            print_status "=== Running Integration Tests ==="
            ((total_tests++))
            if ! run_test "Integration Tests" "go test -v -timeout=60s ./tests/integration/..."; then
                ((failed_tests++))
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "api"|"all")
            print_status "=== Running API Tests ==="
            ((total_tests++))
            if ! run_test "API Tests" "go test -v -timeout=60s ./tests/integration/api_test.go"; then
                ((failed_tests++))
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "coverage"|"all")
            print_status "=== Running Coverage Tests ==="
            ((total_tests++))
            if ! run_test "Coverage Tests" "go test -v -coverprofile=coverage.out ./... && go tool cover -func=coverage.out"; then
                ((failed_tests++))
            fi
            
            # Generate HTML coverage report
            if [ -f coverage.out ]; then
                print_status "Generating HTML coverage report..."
                go tool cover -html=coverage.out -o coverage.html
                print_success "Coverage report generated: coverage.html"
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "benchmark"|"all")
            print_status "=== Running Benchmark Tests ==="
            ((total_tests++))
            if ! run_test "Benchmark Tests" "go test -bench=. -benchmem -timeout=120s ./internal/services/..."; then
                ((failed_tests++))
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "lint"|"all")
            print_status "=== Running Code Quality Checks ==="
            
            # Format check
            ((total_tests++))
            if ! run_test "Go Format Check" "test -z \$(gofmt -l .)"; then
                ((failed_tests++))
                print_warning "Run 'go fmt ./...' to fix formatting issues"
            fi
            
            # Vet check
            ((total_tests++))
            if ! run_test "Go Vet Check" "go vet ./..."; then
                ((failed_tests++))
            fi
            
            # Lint check (if golangci-lint is available)
            if command_exists golangci-lint; then
                ((total_tests++))
                if ! run_test "Linter Check" "golangci-lint run"; then
                    ((failed_tests++))
                fi
            else
                print_warning "golangci-lint not found, skipping lint checks"
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "security"|"all")
            print_status "=== Running Security Checks ==="
            
            # Security scan (if gosec is available)
            if command_exists gosec; then
                ((total_tests++))
                if ! run_test "Security Scan" "gosec ./..."; then
                    ((failed_tests++))
                fi
            else
                print_warning "gosec not found, skipping security checks"
                print_warning "Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"
            fi
            echo
            ;;
    esac
    
    case $test_type in
        "build"|"all")
            print_status "=== Running Build Tests ==="
            ((total_tests++))
            if ! run_test "Build Test" "go build -o /tmp/notification-service-test main.go && rm -f /tmp/notification-service-test"; then
                ((failed_tests++))
            fi
            echo
            ;;
    esac
    
    # Summary
    print_status "=== Test Summary ==="
    echo "Total test suites: $total_tests"
    echo "Failed test suites: $failed_tests"
    echo "Passed test suites: $((total_tests - failed_tests))"
    echo
    
    if [ $failed_tests -eq 0 ]; then
        print_success "All tests passed! 🎉"
        exit 0
    else
        print_error "$failed_tests test suite(s) failed"
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [test_type]"
    echo
    echo "Test types:"
    echo "  all         - Run all tests (default)"
    echo "  unit        - Run unit tests only"
    echo "  integration - Run integration tests only"
    echo "  api         - Run API tests only"
    echo "  coverage    - Run tests with coverage"
    echo "  benchmark   - Run benchmark tests"
    echo "  lint        - Run code quality checks"
    echo "  security    - Run security checks"
    echo "  build       - Run build test"
    echo
    echo "Examples:"
    echo "  $0              # Run all tests"
    echo "  $0 unit         # Run unit tests only"
    echo "  $0 coverage     # Run tests with coverage"
}

# Handle command line arguments
case "${1:-}" in
    "-h"|"--help"|"help")
        show_usage
        exit 0
        ;;
    "")
        main "all"
        ;;
    *)
        main "$1"
        ;;
esac
