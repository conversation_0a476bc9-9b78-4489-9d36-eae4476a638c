package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CourseRepository handles course data operations
type CourseRepository interface {
	GetAll(req *models.CourseListRequest) ([]*models.Course, int64, error)
	GetByID(id uuid.UUID) (*models.Course, error)
	GetByCourseCode(courseCode string) (*models.Course, error)
	Create(course *models.Course) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.CourseStats, error)
	GetByInstructor(instructorID uuid.UUID) ([]*models.Course, error)
	GetAvailableCourses() ([]*models.Course, error)
	GetPopularCourses(limit int) ([]*models.Course, error)
	UpdateEnrollmentCount(courseID uuid.UUID) error
	GetUpcomingCourses() ([]*models.Course, error)
	ValidateCourseCode(courseCode string, excludeID *uuid.UUID) error
}

type courseRepository struct {
	db *gorm.DB
}

// NewCourseRepository creates a new course repository
func NewCourseRepository(db *gorm.DB) CourseRepository {
	return &courseRepository{db: db}
}

// GetAll retrieves courses with pagination and filtering
func (r *courseRepository) GetAll(req *models.CourseListRequest) ([]*models.Course, int64, error) {
	var courses []*models.Course
	var total int64

	query := r.db.Model(&models.Course{}).Preload("Instructor").Preload("CreatedBy").Preload("Enrollments")

	// Apply filters
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Level != nil {
		query = query.Where("level = ?", *req.Level)
	}

	if req.InstructorID != nil {
		query = query.Where("instructor_id = ?", *req.InstructorID)
	}

	if req.CreatedByID != nil {
		query = query.Where("created_by_id = ?", *req.CreatedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where(
			"LOWER(name) LIKE ? OR LOWER(course_code) LIKE ? OR LOWER(description) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	if req.StartDate != nil {
		query = query.Where("start_date >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("end_date <= ?", *req.EndDate)
	}

	if req.MinPrice != nil {
		query = query.Where("price >= ?", *req.MinPrice)
	}

	if req.MaxPrice != nil {
		query = query.Where("price <= ?", *req.MaxPrice)
	}

	if req.Available != nil && *req.Available {
		// Filter courses with available slots
		query = query.Where("status = ? AND (start_date IS NULL OR start_date > ?)", 
			models.CourseStatusActive, time.Now())
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count courses: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&courses).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get courses: %w", err)
	}

	return courses, total, nil
}

// GetByID retrieves a course by ID
func (r *courseRepository) GetByID(id uuid.UUID) (*models.Course, error) {
	var course models.Course
	if err := r.db.Preload("Instructor").Preload("CreatedBy").Preload("Enrollments").Preload("Schedules").
		First(&course, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("course not found")
		}
		return nil, fmt.Errorf("failed to get course: %w", err)
	}
	return &course, nil
}

// GetByCourseCode retrieves a course by course code
func (r *courseRepository) GetByCourseCode(courseCode string) (*models.Course, error) {
	var course models.Course
	if err := r.db.Preload("Instructor").Preload("CreatedBy").First(&course, "course_code = ?", courseCode).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("course not found")
		}
		return nil, fmt.Errorf("failed to get course: %w", err)
	}
	return &course, nil
}

// Create creates a new course
func (r *courseRepository) Create(course *models.Course) error {
	if err := r.db.Create(course).Error; err != nil {
		return fmt.Errorf("failed to create course: %w", err)
	}
	return nil
}

// Update updates a course
func (r *courseRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Course{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update course: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("course not found")
	}
	return nil
}

// Delete soft deletes a course
func (r *courseRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Course{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete course: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("course not found")
	}
	return nil
}

// GetStats retrieves course statistics
func (r *courseRepository) GetStats() (*models.CourseStats, error) {
	stats := &models.CourseStats{
		CoursesByStatus: make(map[models.CourseStatus]int64),
		CoursesByLevel:  make(map[models.CourseLevel]int64),
	}

	// Total courses
	if err := r.db.Model(&models.Course{}).Count(&stats.TotalCourses).Error; err != nil {
		return nil, fmt.Errorf("failed to count total courses: %w", err)
	}

	// Courses by status
	statuses := []models.CourseStatus{
		models.CourseStatusDraft, models.CourseStatusActive, models.CourseStatusInactive,
		models.CourseStatusCompleted, models.CourseStatusCancelled,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Course{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count courses by status %s: %w", status, err)
		}
		stats.CoursesByStatus[status] = count
	}

	// Courses by level
	levels := []models.CourseLevel{
		models.CourseLevelBeginner, models.CourseLevelIntermediate,
		models.CourseLevelAdvanced, models.CourseLevelExpert,
	}

	for _, level := range levels {
		var count int64
		if err := r.db.Model(&models.Course{}).Where("level = ?", level).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count courses by level %s: %w", level, err)
		}
		stats.CoursesByLevel[level] = count
	}

	// Average price
	var avgPrice float64
	if err := r.db.Model(&models.Course{}).Select("AVG(price)").Scan(&avgPrice).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average price: %w", err)
	}
	if avgPrice > 0 {
		stats.AveragePrice = &avgPrice
	}

	// Total revenue (from enrollments)
	var totalRevenue float64
	if err := r.db.Model(&models.Enrollment{}).Select("SUM(total_fee)").Scan(&totalRevenue).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total revenue: %w", err)
	}
	if totalRevenue > 0 {
		stats.TotalRevenue = &totalRevenue
	}

	// Average enrollment
	var avgEnrollment float64
	if err := r.db.Table("courses").
		Select("AVG(enrollment_count)").
		Joins("LEFT JOIN (SELECT course_id, COUNT(*) as enrollment_count FROM enrollments WHERE status = ? GROUP BY course_id) e ON courses.id = e.course_id", models.EnrollmentStatusActive).
		Scan(&avgEnrollment).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average enrollment: %w", err)
	}
	if avgEnrollment > 0 {
		stats.AverageEnrollment = &avgEnrollment
	}

	// Popular courses (top 5 by enrollment)
	var popularCourses []*models.Course
	if err := r.db.Table("courses").
		Select("courses.*, COUNT(enrollments.id) as enrollment_count").
		Joins("LEFT JOIN enrollments ON courses.id = enrollments.course_id AND enrollments.status = ?", models.EnrollmentStatusActive).
		Group("courses.id").
		Order("enrollment_count DESC").
		Limit(5).
		Preload("Instructor").
		Find(&popularCourses).Error; err != nil {
		return nil, fmt.Errorf("failed to get popular courses: %w", err)
	}

	// Convert to responses
	popularCoursesResponse := make([]*models.CourseResponse, len(popularCourses))
	for i, course := range popularCourses {
		popularCoursesResponse[i] = course.ToResponse()
	}
	stats.PopularCourses = popularCoursesResponse

	return stats, nil
}

// GetByInstructor retrieves courses assigned to a specific instructor
func (r *courseRepository) GetByInstructor(instructorID uuid.UUID) ([]*models.Course, error) {
	var courses []*models.Course
	if err := r.db.Preload("Instructor").Preload("CreatedBy").Preload("Enrollments").
		Where("instructor_id = ?", instructorID).
		Order("created_at DESC").Find(&courses).Error; err != nil {
		return nil, fmt.Errorf("failed to get courses by instructor: %w", err)
	}
	return courses, nil
}

// GetAvailableCourses retrieves courses available for enrollment
func (r *courseRepository) GetAvailableCourses() ([]*models.Course, error) {
	var courses []*models.Course
	if err := r.db.Preload("Instructor").Preload("CreatedBy").Preload("Enrollments").
		Where("status = ? AND (start_date IS NULL OR start_date > ?)", 
			models.CourseStatusActive, time.Now()).
		Order("start_date ASC").Find(&courses).Error; err != nil {
		return nil, fmt.Errorf("failed to get available courses: %w", err)
	}
	return courses, nil
}

// GetPopularCourses retrieves most popular courses by enrollment count
func (r *courseRepository) GetPopularCourses(limit int) ([]*models.Course, error) {
	var courses []*models.Course
	if err := r.db.Table("courses").
		Select("courses.*, COUNT(enrollments.id) as enrollment_count").
		Joins("LEFT JOIN enrollments ON courses.id = enrollments.course_id AND enrollments.status = ?", models.EnrollmentStatusActive).
		Group("courses.id").
		Order("enrollment_count DESC").
		Limit(limit).
		Preload("Instructor").
		Preload("Enrollments").
		Find(&courses).Error; err != nil {
		return nil, fmt.Errorf("failed to get popular courses: %w", err)
	}
	return courses, nil
}

// UpdateEnrollmentCount updates the cached enrollment count for a course
func (r *courseRepository) UpdateEnrollmentCount(courseID uuid.UUID) error {
	var count int64
	if err := r.db.Model(&models.Enrollment{}).Where("course_id = ? AND status = ?", 
		courseID, models.EnrollmentStatusActive).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count enrollments: %w", err)
	}

	// Note: This would require adding an enrollment_count field to the Course model
	// For now, we'll skip this update as the count is calculated dynamically
	return nil
}

// GetUpcomingCourses retrieves courses starting soon
func (r *courseRepository) GetUpcomingCourses() ([]*models.Course, error) {
	var courses []*models.Course
	nextWeek := time.Now().AddDate(0, 0, 7)
	
	if err := r.db.Preload("Instructor").Preload("CreatedBy").
		Where("status = ? AND start_date BETWEEN ? AND ?", 
			models.CourseStatusActive, time.Now(), nextWeek).
		Order("start_date ASC").Find(&courses).Error; err != nil {
		return nil, fmt.Errorf("failed to get upcoming courses: %w", err)
	}
	return courses, nil
}

// ValidateCourseCode checks if a course code is unique
func (r *courseRepository) ValidateCourseCode(courseCode string, excludeID *uuid.UUID) error {
	query := r.db.Model(&models.Course{}).Where("course_code = ?", courseCode)
	
	if excludeID != nil {
		query = query.Where("id != ?", *excludeID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("failed to validate course code: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("course code already exists")
	}

	return nil
}
