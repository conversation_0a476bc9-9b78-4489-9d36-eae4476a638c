-- Create grades table
CREATE TABLE IF NOT EXISTS grades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    course_id UUID NOT NULL,
    assessment_name VARCHAR(255) NOT NULL,
    assessment_type VARCHAR(20) NOT NULL,
    assessment_date DATE NOT NULL,
    score DECIMAL(6,2) NOT NULL,
    max_score DECIMAL(6,2) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL,
    weight DECIMAL(5,2) DEFAULT 1.0,
    notes TEXT,
    graded_by_id UUID NOT NULL,
    graded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_grades_student FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_grades_course FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Create indexes for grades table
CREATE INDEX IF NOT EXISTS idx_grades_student_id ON grades(student_id);
CREATE INDEX IF NOT EXISTS idx_grades_course_id ON grades(course_id);
CREATE INDEX IF NOT EXISTS idx_grades_assessment_type ON grades(assessment_type);
CREATE INDEX IF NOT EXISTS idx_grades_assessment_date ON grades(assessment_date);
CREATE INDEX IF NOT EXISTS idx_grades_graded_by_id ON grades(graded_by_id);
CREATE INDEX IF NOT EXISTS idx_grades_percentage ON grades(percentage);
CREATE INDEX IF NOT EXISTS idx_grades_created_at ON grades(created_at);
CREATE INDEX IF NOT EXISTS idx_grades_deleted_at ON grades(deleted_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_grades_student_course ON grades(student_id, course_id);
CREATE INDEX IF NOT EXISTS idx_grades_course_assessment_type ON grades(course_id, assessment_type);
CREATE INDEX IF NOT EXISTS idx_grades_student_assessment_date ON grades(student_id, assessment_date);

-- Add constraints
ALTER TABLE grades ADD CONSTRAINT chk_grades_assessment_type 
    CHECK (assessment_type IN ('ASSIGNMENT', 'QUIZ', 'EXAM', 'MIDTERM', 'FINAL', 'PROJECT', 'PARTICIPATION', 'OTHER'));

ALTER TABLE grades ADD CONSTRAINT chk_grades_score 
    CHECK (score >= 0 AND score <= max_score);

ALTER TABLE grades ADD CONSTRAINT chk_grades_max_score 
    CHECK (max_score > 0);

ALTER TABLE grades ADD CONSTRAINT chk_grades_percentage 
    CHECK (percentage >= 0 AND percentage <= 100);

ALTER TABLE grades ADD CONSTRAINT chk_grades_weight 
    CHECK (weight > 0);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_grades_updated_at BEFORE UPDATE ON grades
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate percentage automatically
CREATE OR REPLACE FUNCTION calculate_grade_percentage()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate percentage if not provided or if score/max_score changed
    IF NEW.max_score > 0 THEN
        NEW.percentage := (NEW.score / NEW.max_score) * 100;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for percentage calculation
CREATE TRIGGER calculate_grade_percentage_trigger 
    BEFORE INSERT OR UPDATE ON grades
    FOR EACH ROW EXECUTE FUNCTION calculate_grade_percentage();

-- Function to update student overall grade
CREATE OR REPLACE FUNCTION update_student_overall_grade()
RETURNS TRIGGER AS $$
DECLARE
    weighted_avg DECIMAL(5,2);
    overall_grade DECIMAL(5,2);
BEGIN
    -- Calculate weighted average for the student in the specific course
    SELECT 
        CASE 
            WHEN SUM(weight) > 0 THEN SUM(percentage * weight) / SUM(weight)
            ELSE AVG(percentage)
        END INTO weighted_avg
    FROM grades 
    WHERE student_id = COALESCE(NEW.student_id, OLD.student_id) 
    AND course_id = COALESCE(NEW.course_id, OLD.course_id)
    AND deleted_at IS NULL;
    
    -- Update the enrollment record with the final grade
    UPDATE enrollments 
    SET final_grade = weighted_avg
    WHERE student_id = COALESCE(NEW.student_id, OLD.student_id) 
    AND course_id = COALESCE(NEW.course_id, OLD.course_id);
    
    -- Calculate overall grade across all courses for the student
    SELECT AVG(final_grade) INTO overall_grade
    FROM enrollments 
    WHERE student_id = COALESCE(NEW.student_id, OLD.student_id)
    AND final_grade IS NOT NULL;
    
    -- Update the student's overall grade
    UPDATE students 
    SET overall_grade = overall_grade
    WHERE id = COALESCE(NEW.student_id, OLD.student_id);
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Create trigger for overall grade calculation
CREATE TRIGGER update_student_overall_grade_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON grades
    FOR EACH ROW EXECUTE FUNCTION update_student_overall_grade();
