# Staff Service

The Staff Service is a comprehensive microservice for managing leads, students, courses, teachers, enrollments, schedules, attendance, and grades in a CRM system for educational institutions.

## Features

### Lead Management
- Create, read, update, and delete leads
- Lead status tracking (NEW, CONTACTED, QUALIFIED, etc.)
- Lead source tracking (WE<PERSON><PERSON><PERSON>, SOCIAL_MEDIA, REFERRA<PERSON>, etc.)
- Lead assignment to staff members
- Follow-up scheduling and tracking
- Lead conversion to students
- Lead statistics and reporting

### Student Management
- Student registration and profile management
- Student status tracking (ACTIVE, INACTIVE, SUSPENDED, GRADUATED)
- Academic progress tracking
- Overall grade and attendance rate calculation
- Student suspension and reactivation
- Student graduation processing

### Course Management
- Course creation and management
- Course levels (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)
- Course status tracking (DRAFT, ACTIVE, INACTIVE, COMPLETED, CANCELLED)
- Instructor assignment
- Course capacity management
- Course popularity tracking

### Teacher Management
- Teacher profile management
- Teacher status tracking (ACTIVE, INACTIVE, SUSPENDED, TERMINATED)
- Department and specialization tracking
- Performance metrics and ratings
- Workload calculation
- Teacher availability tracking

### Enrollment Management
- Student course enrollment
- Enrollment status tracking (PENDING, ACTIVE, COMPLETED, DROPPED)
- Payment tracking and management
- Fee calculation with discounts
- Enrollment completion and dropping
- Revenue reporting

### Schedule Management
- Class scheduling with conflict detection
- Day of week and time slot management
- Room assignment and conflict prevention
- Teacher schedule management
- Schedule effectiveness periods

### Attendance Management
- Daily attendance tracking
- Attendance status (PRESENT, ABSENT, LATE, EXCUSED)
- Automatic attendance rate calculation
- Attendance reporting and analytics
- Absent student tracking

### Grade Management
- Assessment and grade recording
- Multiple assessment types (ASSIGNMENT, QUIZ, EXAM, etc.)
- Weighted grade calculations
- Automatic percentage calculations
- Grade distribution analytics
- Student performance tracking

## API Documentation

The service provides a comprehensive REST API with the following endpoints:

### Leads
- `GET /api/v1/leads` - List leads with pagination and filtering
- `POST /api/v1/leads` - Create a new lead
- `GET /api/v1/leads/{id}` - Get lead by ID
- `PUT /api/v1/leads/{id}` - Update lead
- `DELETE /api/v1/leads/{id}` - Delete lead
- `POST /api/v1/leads/{id}/assign` - Assign lead to user
- `POST /api/v1/leads/{id}/convert` - Convert lead to student
- `POST /api/v1/leads/{id}/follow-up` - Update follow-up information
- `GET /api/v1/leads/stats` - Get lead statistics
- `GET /api/v1/leads/follow-ups-due` - Get leads with follow-ups due
- `POST /api/v1/leads/bulk-update-status` - Bulk update lead status

### Students
- `GET /api/v1/students` - List students with pagination and filtering
- `POST /api/v1/students` - Create a new student
- `GET /api/v1/students/{id}` - Get student by ID
- `PUT /api/v1/students/{id}` - Update student
- `DELETE /api/v1/students/{id}` - Delete student
- `GET /api/v1/students/{id}/progress` - Get student academic progress
- `POST /api/v1/students/{id}/suspend` - Suspend student
- `POST /api/v1/students/{id}/reactivate` - Reactivate student
- `POST /api/v1/students/{id}/graduate` - Graduate student
- `GET /api/v1/students/stats` - Get student statistics
- `GET /api/v1/students/active` - Get active students

### Courses
- `GET /api/v1/courses` - List courses with pagination and filtering
- `POST /api/v1/courses` - Create a new course
- `GET /api/v1/courses/{id}` - Get course by ID
- `PUT /api/v1/courses/{id}` - Update course
- `DELETE /api/v1/courses/{id}` - Delete course
- `POST /api/v1/courses/{id}/assign-instructor` - Assign instructor
- `PUT /api/v1/courses/{id}/status` - Update course status
- `GET /api/v1/courses/stats` - Get course statistics
- `GET /api/v1/courses/available` - Get available courses
- `GET /api/v1/courses/popular` - Get popular courses

### Teachers
- `GET /api/v1/teachers` - List teachers with pagination and filtering
- `POST /api/v1/teachers` - Create a new teacher
- `GET /api/v1/teachers/{id}` - Get teacher by ID
- `PUT /api/v1/teachers/{id}` - Update teacher
- `DELETE /api/v1/teachers/{id}` - Delete teacher
- `GET /api/v1/teachers/{id}/workload` - Get teacher workload
- `POST /api/v1/teachers/{id}/suspend` - Suspend teacher
- `POST /api/v1/teachers/{id}/reactivate` - Reactivate teacher
- `PUT /api/v1/teachers/{id}/rating` - Update teacher rating
- `GET /api/v1/teachers/stats` - Get teacher statistics

### Enrollments
- `GET /api/v1/enrollments` - List enrollments with pagination and filtering
- `POST /api/v1/enrollments` - Create a new enrollment
- `GET /api/v1/enrollments/{id}` - Get enrollment by ID
- `PUT /api/v1/enrollments/{id}` - Update enrollment
- `DELETE /api/v1/enrollments/{id}` - Delete enrollment
- `PUT /api/v1/enrollments/{id}/payment` - Update payment status
- `POST /api/v1/enrollments/{id}/complete` - Complete enrollment
- `POST /api/v1/enrollments/{id}/drop` - Drop enrollment
- `GET /api/v1/enrollments/stats` - Get enrollment statistics
- `GET /api/v1/enrollments/revenue` - Get revenue by period

## Technology Stack

- **Language**: Go 1.21+
- **Framework**: Gin (HTTP web framework)
- **Database**: PostgreSQL with GORM ORM
- **Authentication**: JWT tokens
- **Documentation**: Swagger/OpenAPI
- **Containerization**: Docker
- **Database Migrations**: Custom SQL migrations

## Getting Started

### Prerequisites

- Go 1.21 or higher
- PostgreSQL database
- Docker (optional)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd services/staff-service
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update the `.env` file with your configuration.

4. Install dependencies:
```bash
go mod download
```

5. Run database migrations:
```bash
# Using the provided migration scripts
psql -d your_database -f migrations/run_migrations.sql
```

6. Run the service:
```bash
go run cmd/main.go
```

### Using Docker

1. Build and run with Docker Compose:
```bash
docker-compose up --build
```

2. For development with local PostgreSQL:
```bash
docker-compose --profile dev up --build
```

### API Documentation

Once the service is running, you can access the Swagger documentation at:
```
http://localhost:8080/swagger/index.html
```

## Configuration

The service can be configured using environment variables. See `.env.example` for all available options.

### Key Configuration Options

- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret key for JWT token signing
- `PORT`: Service port (default: 8080)
- `ALLOWED_ORIGINS`: CORS allowed origins
- `LOG_LEVEL`: Logging level (debug, info, warn, error)

## Database Schema

The service uses the following main entities:

- **Leads**: Potential students with contact information and status
- **Students**: Enrolled students with academic information
- **Teachers**: Instructors with qualifications and performance metrics
- **Courses**: Educational courses with details and capacity
- **Enrollments**: Student-course relationships with payment info
- **Schedules**: Class schedules with time and room assignments
- **Attendances**: Daily attendance records
- **Grades**: Assessment scores and grades

## Authentication

The service uses JWT-based authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

The API returns standard HTTP status codes and JSON error responses:

```json
{
  "error": "Error message description"
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
