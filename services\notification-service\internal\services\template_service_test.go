package services

import (
	"notification-service/internal/models"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TemplateServiceTestSuite defines the test suite for TemplateService
type TemplateServiceTestSuite struct {
	suite.Suite
	db      *gorm.DB
	service *TemplateService
}

// SetupSuite sets up the test suite
func (suite *TemplateServiceTestSuite) SetupSuite() {
	// Create in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)
	
	// Run migrations
	err = db.AutoMigrate(
		&models.NotificationTemplate{},
		&models.Notification{},
	)
	suite.Require().NoError(err)
	
	suite.db = db
	suite.service = NewTemplateService(db)
}

// TearDownSuite cleans up after the test suite
func (suite *TemplateServiceTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// SetupTest sets up each test
func (suite *TemplateServiceTestSuite) SetupTest() {
	// Clean up tables before each test
	suite.db.Exec("DELETE FROM notification_templates")
	suite.db.Exec("DELETE FROM notifications")
}

// TestCreateTemplate tests template creation
func (suite *TemplateServiceTestSuite) TestCreateTemplate() {
	createdByID := uuid.New()
	req := &models.CreateTemplateRequest{
		Name:        "Welcome Email",
		Type:        models.NotificationTypeEmail,
		Subject:     "Welcome to {{company_name}}!",
		Body:        "Hello {{user_name}}, welcome to our platform!",
		HTMLBody:    "<h1>Hello {{user_name}}</h1><p>Welcome to our platform!</p>",
		Description: "Welcome email template for new users",
		Variables:   []string{"company_name", "user_name"},
		IsActive:    boolPtr(true),
		IsDefault:   boolPtr(false),
	}
	
	template, err := suite.service.CreateTemplate(req, createdByID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), template)
	assert.Equal(suite.T(), req.Name, template.Name)
	assert.Equal(suite.T(), req.Type, template.Type)
	assert.Equal(suite.T(), req.Subject, template.Subject)
	assert.Equal(suite.T(), req.Body, template.Body)
	assert.Equal(suite.T(), req.HTMLBody, template.HTMLBody)
	assert.Equal(suite.T(), req.Description, template.Description)
	assert.Equal(suite.T(), req.Variables, template.Variables)
	assert.Equal(suite.T(), createdByID, template.CreatedByID)
	assert.True(suite.T(), template.IsActive)
	assert.False(suite.T(), template.IsDefault)
	assert.Equal(suite.T(), 1, template.Version)
	
	// Verify template was saved to database
	var dbTemplate models.NotificationTemplate
	err = suite.db.First(&dbTemplate, "id = ?", template.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), template.ID, dbTemplate.ID)
}

// TestCreateTemplateWithDuplicateName tests template creation with duplicate name
func (suite *TemplateServiceTestSuite) TestCreateTemplateWithDuplicateName() {
	createdByID := uuid.New()
	
	// Create first template
	req1 := &models.CreateTemplateRequest{
		Name: "Welcome Email",
		Type: models.NotificationTypeEmail,
		Body: "Welcome message 1",
	}
	_, err := suite.service.CreateTemplate(req1, createdByID)
	suite.Require().NoError(err)
	
	// Try to create second template with same name
	req2 := &models.CreateTemplateRequest{
		Name: "Welcome Email", // Duplicate name
		Type: models.NotificationTypeSMS,
		Body: "Welcome message 2",
	}
	template, err := suite.service.CreateTemplate(req2, createdByID)
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), template)
	assert.Contains(suite.T(), err.Error(), "already exists")
}

// TestGetTemplate tests template retrieval
func (suite *TemplateServiceTestSuite) TestGetTemplate() {
	// Create a test template
	template := &models.NotificationTemplate{
		Name:        "Test Template",
		Type:        models.NotificationTypeEmail,
		Subject:     "Test Subject",
		Body:        "Test Body",
		Description: "Test Description",
		IsActive:    true,
		CreatedByID: uuid.New(),
		Version:     1,
	}
	err := suite.db.Create(template).Error
	suite.Require().NoError(err)
	
	// Retrieve the template
	retrieved, err := suite.service.GetTemplate(template.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), template.ID, retrieved.ID)
	assert.Equal(suite.T(), template.Name, retrieved.Name)
	assert.Equal(suite.T(), template.Type, retrieved.Type)
}

// TestGetTemplateByName tests template retrieval by name
func (suite *TemplateServiceTestSuite) TestGetTemplateByName() {
	// Create a test template
	template := &models.NotificationTemplate{
		Name:        "Test Template",
		Type:        models.NotificationTypeEmail,
		Subject:     "Test Subject",
		Body:        "Test Body",
		IsActive:    true,
		CreatedByID: uuid.New(),
		Version:     1,
	}
	err := suite.db.Create(template).Error
	suite.Require().NoError(err)
	
	// Retrieve the template by name
	retrieved, err := suite.service.GetTemplateByName("Test Template")
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), template.ID, retrieved.ID)
	assert.Equal(suite.T(), template.Name, retrieved.Name)
}

// TestGetTemplates tests template listing with filters
func (suite *TemplateServiceTestSuite) TestGetTemplates() {
	createdByID := uuid.New()
	
	// Create test templates
	templates := []models.NotificationTemplate{
		{
			Name:        "Email Template 1",
			Type:        models.NotificationTypeEmail,
			Body:        "Email body 1",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
		{
			Name:        "SMS Template 1",
			Type:        models.NotificationTypeSMS,
			Body:        "SMS body 1",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
		{
			Name:        "Email Template 2",
			Type:        models.NotificationTypeEmail,
			Body:        "Email body 2",
			IsActive:    false,
			CreatedByID: createdByID,
			Version:     1,
		},
	}
	
	for i := range templates {
		err := suite.db.Create(&templates[i]).Error
		suite.Require().NoError(err)
	}
	
	// Test without filters
	result, total, err := suite.service.GetTemplates(map[string]interface{}{}, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), total)
	assert.Len(suite.T(), result, 3)
	
	// Test with type filter
	filters := map[string]interface{}{
		"type": models.NotificationTypeEmail,
	}
	result, total, err = suite.service.GetTemplates(filters, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), total)
	assert.Len(suite.T(), result, 2)
	
	// Test with active filter
	filters = map[string]interface{}{
		"is_active": true,
	}
	result, total, err = suite.service.GetTemplates(filters, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), total)
	assert.Len(suite.T(), result, 2)
}

// TestUpdateTemplate tests template updates
func (suite *TemplateServiceTestSuite) TestUpdateTemplate() {
	// Create a test template
	template := &models.NotificationTemplate{
		Name:        "Original Template",
		Type:        models.NotificationTypeEmail,
		Subject:     "Original Subject",
		Body:        "Original Body",
		IsActive:    true,
		CreatedByID: uuid.New(),
		Version:     1,
	}
	err := suite.db.Create(template).Error
	suite.Require().NoError(err)
	
	// Update the template
	updateReq := &models.UpdateTemplateRequest{
		Name:     stringPtr("Updated Template"),
		Subject:  stringPtr("Updated Subject"),
		Body:     stringPtr("Updated Body"),
		IsActive: boolPtr(false),
	}
	
	updated, err := suite.service.UpdateTemplate(template.ID, updateReq)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), updated)
	assert.Equal(suite.T(), "Updated Template", updated.Name)
	assert.Equal(suite.T(), "Updated Subject", updated.Subject)
	assert.Equal(suite.T(), "Updated Body", updated.Body)
	assert.False(suite.T(), updated.IsActive)
	assert.Equal(suite.T(), 2, updated.Version) // Version should increment
}

// TestDeleteTemplate tests template deletion
func (suite *TemplateServiceTestSuite) TestDeleteTemplate() {
	// Create a test template
	template := &models.NotificationTemplate{
		Name:        "Test Template",
		Type:        models.NotificationTypeEmail,
		Body:        "Test Body",
		IsActive:    true,
		CreatedByID: uuid.New(),
		Version:     1,
	}
	err := suite.db.Create(template).Error
	suite.Require().NoError(err)
	
	// Delete the template
	err = suite.service.DeleteTemplate(template.ID)
	assert.NoError(suite.T(), err)
	
	// Verify template was soft deleted
	var deleted models.NotificationTemplate
	err = suite.db.Unscoped().First(&deleted, "id = ?", template.ID).Error
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), deleted.DeletedAt)
}

// TestDeleteTemplateInUse tests deletion of template that's being used
func (suite *TemplateServiceTestSuite) TestDeleteTemplateInUse() {
	// Create a test template
	template := &models.NotificationTemplate{
		Name:        "Test Template",
		Type:        models.NotificationTypeEmail,
		Body:        "Test Body",
		IsActive:    true,
		CreatedByID: uuid.New(),
		Version:     1,
	}
	err := suite.db.Create(template).Error
	suite.Require().NoError(err)
	
	// Create a notification using this template
	notification := &models.Notification{
		Type:       models.NotificationTypeEmail,
		Recipient:  "<EMAIL>",
		Message:    "Test Message",
		TemplateID: &template.ID,
		Status:     models.NotificationStatusPending,
	}
	err = suite.db.Create(notification).Error
	suite.Require().NoError(err)
	
	// Try to delete the template
	err = suite.service.DeleteTemplate(template.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "being used")
}

// TestCloneTemplate tests template cloning
func (suite *TemplateServiceTestSuite) TestCloneTemplate() {
	createdByID := uuid.New()
	
	// Create original template
	original := &models.NotificationTemplate{
		Name:        "Original Template",
		Type:        models.NotificationTypeEmail,
		Subject:     "Original Subject",
		Body:        "Original Body",
		HTMLBody:    "<p>Original HTML</p>",
		Description: "Original Description",
		Variables:   []string{"var1", "var2"},
		IsActive:    true,
		IsDefault:   true,
		CreatedByID: createdByID,
		Version:     1,
	}
	err := suite.db.Create(original).Error
	suite.Require().NoError(err)
	
	// Clone the template
	newCreatedByID := uuid.New()
	cloned, err := suite.service.CloneTemplate(original.ID, "Cloned Template", newCreatedByID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), cloned)
	assert.Equal(suite.T(), "Cloned Template", cloned.Name)
	assert.Equal(suite.T(), original.Type, cloned.Type)
	assert.Equal(suite.T(), original.Subject, cloned.Subject)
	assert.Equal(suite.T(), original.Body, cloned.Body)
	assert.Equal(suite.T(), original.HTMLBody, cloned.HTMLBody)
	assert.Equal(suite.T(), original.Variables, cloned.Variables)
	assert.Equal(suite.T(), newCreatedByID, cloned.CreatedByID)
	assert.True(suite.T(), cloned.IsActive)
	assert.False(suite.T(), cloned.IsDefault) // Cloned templates are never default
	assert.Equal(suite.T(), 1, cloned.Version)
	assert.NotEqual(suite.T(), original.ID, cloned.ID)
}

// TestGetTemplatesByType tests retrieving templates by type
func (suite *TemplateServiceTestSuite) TestGetTemplatesByType() {
	createdByID := uuid.New()
	
	// Create templates of different types
	templates := []models.NotificationTemplate{
		{
			Name:        "Email Template 1",
			Type:        models.NotificationTypeEmail,
			Body:        "Email body 1",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
		{
			Name:        "Email Template 2",
			Type:        models.NotificationTypeEmail,
			Body:        "Email body 2",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
		{
			Name:        "SMS Template 1",
			Type:        models.NotificationTypeSMS,
			Body:        "SMS body 1",
			IsActive:    true,
			CreatedByID: createdByID,
			Version:     1,
		},
		{
			Name:        "Inactive Email Template",
			Type:        models.NotificationTypeEmail,
			Body:        "Inactive email body",
			IsActive:    false,
			CreatedByID: createdByID,
			Version:     1,
		},
	}
	
	for i := range templates {
		err := suite.db.Create(&templates[i]).Error
		suite.Require().NoError(err)
	}
	
	// Get email templates
	emailTemplates, err := suite.service.GetTemplatesByType(models.NotificationTypeEmail)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), emailTemplates, 2) // Only active templates
	
	// Get SMS templates
	smsTemplates, err := suite.service.GetTemplatesByType(models.NotificationTypeSMS)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), smsTemplates, 1)
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}

// TestSuite runs the template service test suite
func TestTemplateServiceSuite(t *testing.T) {
	suite.Run(t, new(TemplateServiceTestSuite))
}
