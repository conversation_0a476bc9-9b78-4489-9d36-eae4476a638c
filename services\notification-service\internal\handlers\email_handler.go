package handlers

import (
	"net/http"
	"notification-service/internal/services"

	"github.com/gin-gonic/gin"
)

// EmailHandler handles email-related HTTP requests
type Email<PERSON><PERSON><PERSON> struct {
	emailService *services.EmailService
}

// NewEmailHandler creates a new email handler
func NewEmailHandler(emailService *services.EmailService) *EmailHandler {
	return &EmailHandler{
		emailService: emailService,
	}
}

// SendEmail sends a single email
func (h *<PERSON>ail<PERSON>andler) SendEmail(c *gin.Context) {
	var req struct {
		To          string `json:"to" binding:"required,email"`
		Subject     string `json:"subject" binding:"required"`
		TextBody    string `json:"text_body"`
		HTMLBody    string `json:"html_body"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.<PERSON>rror(),
			},
		})
		return
	}
	
	err := h.emailService.SendEmail(req.To, req.Subject, req.TextBody, req.HTMLBody)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "SEND_FAILED",
				"message": "Failed to send email",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Email sent successfully",
	})
}

// SendBulkEmail sends emails to multiple recipients
func (h *EmailHandler) SendBulkEmail(c *gin.Context) {
	var req struct {
		Recipients []string `json:"recipients" binding:"required,min=1"`
		Subject    string   `json:"subject" binding:"required"`
		TextBody   string   `json:"text_body"`
		HTMLBody   string   `json:"html_body"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.emailService.SendBulkEmail(req.Recipients, req.Subject, req.TextBody, req.HTMLBody)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "BULK_SEND_FAILED",
				"message": "Failed to send bulk emails",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Bulk emails sent successfully",
	})
}

// SendTemplatedEmail sends an email using a template
func (h *EmailHandler) SendTemplatedEmail(c *gin.Context) {
	var req struct {
		To           string                 `json:"to" binding:"required,email"`
		Subject      string                 `json:"subject" binding:"required"`
		TextTemplate string                 `json:"text_template"`
		HTMLTemplate string                 `json:"html_template"`
		Data         map[string]interface{} `json:"data"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	var err error
	if req.HTMLTemplate != "" {
		err = h.emailService.SendHTMLTemplatedEmail(req.To, req.Subject, req.TextTemplate, req.HTMLTemplate, req.Data)
	} else {
		err = h.emailService.SendTemplatedEmail(req.To, req.Subject, req.TextTemplate, req.Data)
	}
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "TEMPLATED_SEND_FAILED",
				"message": "Failed to send templated email",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Templated email sent successfully",
	})
}

// SendWelcomeEmail sends a welcome email to new users
func (h *EmailHandler) SendWelcomeEmail(c *gin.Context) {
	var req struct {
		To       string `json:"to" binding:"required,email"`
		UserName string `json:"user_name" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.emailService.SendWelcomeEmail(req.To, req.UserName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "WELCOME_EMAIL_FAILED",
				"message": "Failed to send welcome email",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Welcome email sent successfully",
	})
}

// SendPasswordResetEmail sends a password reset email
func (h *EmailHandler) SendPasswordResetEmail(c *gin.Context) {
	var req struct {
		To         string `json:"to" binding:"required,email"`
		UserName   string `json:"user_name" binding:"required"`
		ResetToken string `json:"reset_token" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.emailService.SendPasswordResetEmail(req.To, req.UserName, req.ResetToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "PASSWORD_RESET_EMAIL_FAILED",
				"message": "Failed to send password reset email",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Password reset email sent successfully",
	})
}

// GetEmailStatus returns email service status
func (h *EmailHandler) GetEmailStatus(c *gin.Context) {
	stats := h.emailService.GetEmailStats()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// TestEmailConnection tests the email service connection
func (h *EmailHandler) TestEmailConnection(c *gin.Context) {
	err := h.emailService.TestEmailConnection()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "CONNECTION_TEST_FAILED",
				"message": "Email connection test failed",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Email connection test successful",
	})
}
