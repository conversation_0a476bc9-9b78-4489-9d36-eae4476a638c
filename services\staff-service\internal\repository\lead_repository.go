package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LeadRepository handles lead data operations
type LeadRepository interface {
	GetAll(req *models.LeadListRequest) ([]*models.Lead, int64, error)
	GetByID(id uuid.UUID) (*models.Lead, error)
	GetByEmail(email string) (*models.Lead, error)
	Create(lead *models.Lead) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.LeadStats, error)
	GetFollowUpsDue(date time.Time) ([]*models.Lead, error)
	GetByAssignedUser(userID uuid.UUID) ([]*models.Lead, error)
	GetConversionRate(startDate, endDate time.Time) (float64, error)
	BulkUpdateStatus(leadIDs []uuid.UUID, status models.LeadStatus, updatedBy uuid.UUID) error
}

type leadRepository struct {
	db *gorm.DB
}

// NewLeadRepository creates a new lead repository
func NewLeadRepository(db *gorm.DB) LeadRepository {
	return &leadRepository{db: db}
}

// GetAll retrieves leads with pagination and filtering
func (r *leadRepository) GetAll(req *models.LeadListRequest) ([]*models.Lead, int64, error) {
	var leads []*models.Lead
	var total int64

	query := r.db.Model(&models.Lead{}).Preload("AssignedTo").Preload("CreatedBy")

	// Apply filters
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Source != nil {
		query = query.Where("source = ?", *req.Source)
	}

	if req.AssignedToID != nil {
		query = query.Where("assigned_to_id = ?", *req.AssignedToID)
	}

	if req.CreatedByID != nil {
		query = query.Where("created_by_id = ?", *req.CreatedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where(
			"LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ? OR LOWER(interested_course) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	if req.StartDate != nil {
		query = query.Where("created_at >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("created_at <= ?", *req.EndDate)
	}

	if req.FollowUpDue != nil && *req.FollowUpDue {
		query = query.Where("next_follow_up_at <= ? AND status NOT IN (?)", time.Now(), []models.LeadStatus{models.LeadStatusConverted, models.LeadStatusLost})
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count leads: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&leads).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get leads: %w", err)
	}

	return leads, total, nil
}

// GetByID retrieves a lead by ID
func (r *leadRepository) GetByID(id uuid.UUID) (*models.Lead, error) {
	var lead models.Lead
	if err := r.db.Preload("AssignedTo").Preload("CreatedBy").First(&lead, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("lead not found")
		}
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}
	return &lead, nil
}

// GetByEmail retrieves a lead by email
func (r *leadRepository) GetByEmail(email string) (*models.Lead, error) {
	var lead models.Lead
	if err := r.db.Preload("AssignedTo").Preload("CreatedBy").First(&lead, "email = ?", email).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("lead not found")
		}
		return nil, fmt.Errorf("failed to get lead: %w", err)
	}
	return &lead, nil
}

// Create creates a new lead
func (r *leadRepository) Create(lead *models.Lead) error {
	if err := r.db.Create(lead).Error; err != nil {
		return fmt.Errorf("failed to create lead: %w", err)
	}
	return nil
}

// Update updates a lead
func (r *leadRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Lead{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update lead: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("lead not found")
	}
	return nil
}

// Delete soft deletes a lead
func (r *leadRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Lead{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete lead: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("lead not found")
	}
	return nil
}

// GetStats retrieves lead statistics
func (r *leadRepository) GetStats() (*models.LeadStats, error) {
	stats := &models.LeadStats{
		LeadsByStatus: make(map[models.LeadStatus]int64),
		LeadsBySource: make(map[models.LeadSource]int64),
	}

	// Total leads
	if err := r.db.Model(&models.Lead{}).Count(&stats.TotalLeads).Error; err != nil {
		return nil, fmt.Errorf("failed to count total leads: %w", err)
	}

	// Leads by status
	statuses := []models.LeadStatus{
		models.LeadStatusNew, models.LeadStatusContacted, models.LeadStatusQualified,
		models.LeadStatusConverted, models.LeadStatusLost, models.LeadStatusFollowUp,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Lead{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count leads by status %s: %w", status, err)
		}
		stats.LeadsByStatus[status] = count
	}

	// Leads by source
	sources := []models.LeadSource{
		models.LeadSourceWebsite, models.LeadSourceSocialMedia, models.LeadSourceReferral,
		models.LeadSourceAdvertising, models.LeadSourceWalkIn, models.LeadSourcePhone,
		models.LeadSourceEmail, models.LeadSourceOther,
	}

	for _, source := range sources {
		var count int64
		if err := r.db.Model(&models.Lead{}).Where("source = ?", source).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count leads by source %s: %w", source, err)
		}
		stats.LeadsBySource[source] = count
	}

	// Conversion rate
	convertedCount := stats.LeadsByStatus[models.LeadStatusConverted]
	if stats.TotalLeads > 0 {
		stats.ConversionRate = float64(convertedCount) / float64(stats.TotalLeads) * 100
	}

	// Follow-ups due
	if err := r.db.Model(&models.Lead{}).Where("next_follow_up_at <= ? AND status NOT IN (?)", 
		time.Now(), []models.LeadStatus{models.LeadStatusConverted, models.LeadStatusLost}).Count(&stats.FollowUpsDue).Error; err != nil {
		return nil, fmt.Errorf("failed to count follow-ups due: %w", err)
	}

	// New leads today
	if err := r.db.Model(&models.Lead{}).Where("DATE(created_at) = CURRENT_DATE").Count(&stats.NewLeadsToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count new leads today: %w", err)
	}

	// New leads this week
	if err := r.db.Model(&models.Lead{}).Where("created_at >= DATE_TRUNC('week', CURRENT_DATE)").Count(&stats.NewLeadsThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count new leads this week: %w", err)
	}

	return stats, nil
}

// GetFollowUpsDue retrieves leads with follow-ups due on or before the specified date
func (r *leadRepository) GetFollowUpsDue(date time.Time) ([]*models.Lead, error) {
	var leads []*models.Lead
	if err := r.db.Preload("AssignedTo").Preload("CreatedBy").
		Where("next_follow_up_at <= ? AND status NOT IN (?)", 
			date, []models.LeadStatus{models.LeadStatusConverted, models.LeadStatusLost}).
		Order("next_follow_up_at ASC").Find(&leads).Error; err != nil {
		return nil, fmt.Errorf("failed to get follow-ups due: %w", err)
	}
	return leads, nil
}

// GetByAssignedUser retrieves leads assigned to a specific user
func (r *leadRepository) GetByAssignedUser(userID uuid.UUID) ([]*models.Lead, error) {
	var leads []*models.Lead
	if err := r.db.Preload("AssignedTo").Preload("CreatedBy").
		Where("assigned_to_id = ?", userID).
		Order("created_at DESC").Find(&leads).Error; err != nil {
		return nil, fmt.Errorf("failed to get leads by assigned user: %w", err)
	}
	return leads, nil
}

// GetConversionRate calculates conversion rate for a date range
func (r *leadRepository) GetConversionRate(startDate, endDate time.Time) (float64, error) {
	var totalLeads, convertedLeads int64

	// Count total leads in date range
	if err := r.db.Model(&models.Lead{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate).
		Count(&totalLeads).Error; err != nil {
		return 0, fmt.Errorf("failed to count total leads: %w", err)
	}

	// Count converted leads in date range
	if err := r.db.Model(&models.Lead{}).
		Where("created_at >= ? AND created_at <= ? AND status = ?", startDate, endDate, models.LeadStatusConverted).
		Count(&convertedLeads).Error; err != nil {
		return 0, fmt.Errorf("failed to count converted leads: %w", err)
	}

	if totalLeads == 0 {
		return 0, nil
	}

	return float64(convertedLeads) / float64(totalLeads) * 100, nil
}

// BulkUpdateStatus updates status for multiple leads
func (r *leadRepository) BulkUpdateStatus(leadIDs []uuid.UUID, status models.LeadStatus, updatedBy uuid.UUID) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == models.LeadStatusConverted {
		updates["converted_at"] = time.Now()
	}

	result := r.db.Model(&models.Lead{}).Where("id IN ?", leadIDs).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to bulk update lead status: %w", result.Error)
	}

	return nil
}
