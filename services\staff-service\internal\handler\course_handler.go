package handler

import (
	"net/http"
	"strconv"

	"staff-service/internal/models"
	"staff-service/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CourseHandler handles course-related HTTP requests
type CourseHandler struct {
	courseService service.CourseService
}

// NewCourseHandler creates a new course handler
func NewCourseHandler(courseService service.CourseService) *CourseHandler {
	return &CourseHandler{
		courseService: courseService,
	}
}

// GetCourses retrieves courses with pagination and filtering
// @Summary Get courses
// @Description Get courses with pagination and filtering
// @Tags courses
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Course status"
// @Param level query string false "Course level"
// @Param search query string false "Search term"
// @Success 200 {object} models.CourseListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses [get]
func (h *CourseHandler) GetCourses(c *gin.Context) {
	req := &models.CourseListRequest{}

	// Parse pagination
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		} else {
			req.Page = 1
		}
	} else {
		req.Page = 1
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.PageSize = l
		} else {
			req.PageSize = 10
		}
	} else {
		req.PageSize = 10
	}

	// Parse filters
	if status := c.Query("status"); status != "" {
		courseStatus := models.CourseStatus(status)
		if courseStatus.IsValid() {
			req.Status = &courseStatus
		}
	}

	if level := c.Query("level"); level != "" {
		courseLevel := models.CourseLevel(level)
		if courseLevel.IsValid() {
			req.Level = &courseLevel
		}
	}

	if instructorID := c.Query("instructor_id"); instructorID != "" {
		if id, err := uuid.Parse(instructorID); err == nil {
			req.InstructorID = &id
		}
	}

	req.Search = c.Query("search")

	response, err := h.courseService.GetAll(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetCourse retrieves a course by ID
// @Summary Get course by ID
// @Description Get a specific course by ID
// @Tags courses
// @Accept json
// @Produce json
// @Param id path string true "Course ID"
// @Success 200 {object} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/{id} [get]
func (h *CourseHandler) GetCourse(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	response, err := h.courseService.GetByID(id)
	if err != nil {
		if err.Error() == "course not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetCourseByCourseCode retrieves a course by course code
// @Summary Get course by course code
// @Description Get a specific course by course code
// @Tags courses
// @Accept json
// @Produce json
// @Param course_code path string true "Course Code"
// @Success 200 {object} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/by-code/{course_code} [get]
func (h *CourseHandler) GetCourseByCourseCode(c *gin.Context) {
	courseCode := c.Param("course_code")
	if courseCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course code is required"})
		return
	}

	response, err := h.courseService.GetByCourseCode(courseCode)
	if err != nil {
		if err.Error() == "course not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateCourse creates a new course
// @Summary Create course
// @Description Create a new course
// @Tags courses
// @Accept json
// @Produce json
// @Param course body models.CourseCreateRequest true "Course data"
// @Success 201 {object} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses [post]
func (h *CourseHandler) CreateCourse(c *gin.Context) {
	var req models.CourseCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.courseService.Create(&req, createdBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateCourse updates a course
// @Summary Update course
// @Description Update an existing course
// @Tags courses
// @Accept json
// @Produce json
// @Param id path string true "Course ID"
// @Param course body models.CourseUpdateRequest true "Course update data"
// @Success 200 {object} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/{id} [put]
func (h *CourseHandler) UpdateCourse(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var req models.CourseUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.courseService.Update(id, &req)
	if err != nil {
		if err.Error() == "course not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteCourse deletes a course
// @Summary Delete course
// @Description Delete a course
// @Tags courses
// @Accept json
// @Produce json
// @Param id path string true "Course ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/{id} [delete]
func (h *CourseHandler) DeleteCourse(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	err = h.courseService.Delete(id)
	if err != nil {
		if err.Error() == "course not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetCourseStats retrieves course statistics
// @Summary Get course statistics
// @Description Get course statistics
// @Tags courses
// @Accept json
// @Produce json
// @Success 200 {object} models.CourseStats
// @Failure 500 {object} map[string]interface{}
// @Router /courses/stats [get]
func (h *CourseHandler) GetCourseStats(c *gin.Context) {
	stats, err := h.courseService.GetStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetCoursesByInstructor retrieves courses assigned to a specific instructor
// @Summary Get courses by instructor
// @Description Get courses assigned to a specific instructor
// @Tags courses
// @Accept json
// @Produce json
// @Param instructor_id path string true "Instructor ID"
// @Success 200 {array} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/by-instructor/{instructor_id} [get]
func (h *CourseHandler) GetCoursesByInstructor(c *gin.Context) {
	instructorIDStr := c.Param("instructor_id")
	instructorID, err := uuid.Parse(instructorIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid instructor ID"})
		return
	}

	responses, err := h.courseService.GetByInstructor(instructorID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetAvailableCourses retrieves courses available for enrollment
// @Summary Get available courses
// @Description Get courses available for enrollment
// @Tags courses
// @Accept json
// @Produce json
// @Success 200 {array} models.CourseResponse
// @Failure 500 {object} map[string]interface{}
// @Router /courses/available [get]
func (h *CourseHandler) GetAvailableCourses(c *gin.Context) {
	responses, err := h.courseService.GetAvailableCourses()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetPopularCourses retrieves most popular courses by enrollment count
// @Summary Get popular courses
// @Description Get most popular courses by enrollment count
// @Tags courses
// @Accept json
// @Produce json
// @Param limit query int false "Number of courses to return" default(10)
// @Success 200 {array} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/popular [get]
func (h *CourseHandler) GetPopularCourses(c *gin.Context) {
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 50 {
			limit = l
		}
	}

	responses, err := h.courseService.GetPopularCourses(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetUpcomingCourses retrieves courses starting soon
// @Summary Get upcoming courses
// @Description Get courses starting soon
// @Tags courses
// @Accept json
// @Produce json
// @Success 200 {array} models.CourseResponse
// @Failure 500 {object} map[string]interface{}
// @Router /courses/upcoming [get]
func (h *CourseHandler) GetUpcomingCourses(c *gin.Context) {
	responses, err := h.courseService.GetUpcomingCourses()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// AssignInstructor assigns an instructor to a course
// @Summary Assign instructor to course
// @Description Assign an instructor to a course
// @Tags courses
// @Accept json
// @Produce json
// @Param id path string true "Course ID"
// @Param assignment body models.CourseInstructorAssignRequest true "Assignment data"
// @Success 200 {object} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/{id}/assign-instructor [post]
func (h *CourseHandler) AssignInstructor(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var req models.CourseInstructorAssignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.courseService.AssignInstructor(id, req.InstructorID)
	if err != nil {
		if err.Error() == "course not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateCourseStatus updates the status of a course
// @Summary Update course status
// @Description Update the status of a course
// @Tags courses
// @Accept json
// @Produce json
// @Param id path string true "Course ID"
// @Param status body models.CourseStatusUpdateRequest true "Status update data"
// @Success 200 {object} models.CourseResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/{id}/status [put]
func (h *CourseHandler) UpdateCourseStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var req models.CourseStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.courseService.UpdateStatus(id, req.Status)
	if err != nil {
		if err.Error() == "course not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ValidateCourseCode checks if a course code is unique
// @Summary Validate course code
// @Description Check if a course code is unique
// @Tags courses
// @Accept json
// @Produce json
// @Param course_code query string true "Course code to validate"
// @Param exclude_id query string false "Course ID to exclude from validation"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /courses/validate-code [get]
func (h *CourseHandler) ValidateCourseCode(c *gin.Context) {
	courseCode := c.Query("course_code")
	if courseCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Course code is required"})
		return
	}

	var excludeID *uuid.UUID
	if excludeIDStr := c.Query("exclude_id"); excludeIDStr != "" {
		if id, err := uuid.Parse(excludeIDStr); err == nil {
			excludeID = &id
		}
	}

	err := h.courseService.ValidateCourseCode(courseCode, excludeID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error(), "valid": false})
		return
	}

	c.JSON(http.StatusOK, gin.H{"valid": true, "message": "Course code is available"})
}
