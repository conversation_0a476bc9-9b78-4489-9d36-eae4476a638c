package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// AttendanceStatus represents the attendance status
type AttendanceStatus string

const (
	AttendanceStatusPresent AttendanceStatus = "PRESENT"
	AttendanceStatusAbsent  AttendanceStatus = "ABSENT"
	AttendanceStatusLate    AttendanceStatus = "LATE"
	AttendanceStatusExcused AttendanceStatus = "EXCUSED"
)

// IsValid checks if the attendance status is valid
func (as AttendanceStatus) IsValid() bool {
	switch as {
	case AttendanceStatusPresent, AttendanceStatusAbsent, AttendanceStatusLate, AttendanceStatusExcused:
		return true
	}
	return false
}

// Attendance represents student attendance for a class
type Attendance struct {
	models.BaseModel
	StudentID        uuid.UUID        `json:"student_id" gorm:"not null;index"`
	Student          *Student         `json:"student,omitempty" gorm:"foreignKey:StudentID"`
	CourseID         uuid.UUID        `json:"course_id" gorm:"not null;index"`
	Course           *Course          `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	ScheduleID       uuid.UUID        `json:"schedule_id" gorm:"not null;index"`
	Schedule         *Schedule        `json:"schedule,omitempty" gorm:"foreignKey:ScheduleID"`
	
	// Attendance details
	Date             time.Time        `json:"date" gorm:"not null;index"`
	Status           AttendanceStatus `json:"status" gorm:"not null;index"`
	ArrivalTime      *time.Time       `json:"arrival_time"`
	DepartureTime    *time.Time       `json:"departure_time"`
	
	// Additional information
	Notes            string           `json:"notes" gorm:"type:text"`
	ExcuseReason     string           `json:"excuse_reason" gorm:"type:text"`
	
	// Metadata (no foreign key constraint - references auth service)
	RecordedByUserID uuid.UUID        `json:"recorded_by_id" gorm:"column:recorded_by_id;not null"`
	RecordedBy       *models.User     `json:"recorded_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
	RecordedAt       time.Time        `json:"recorded_at" gorm:"autoCreateTime"`
}

// IsPresent checks if the student was present
func (a *Attendance) IsPresent() bool {
	return a.Status == AttendanceStatusPresent || a.Status == AttendanceStatusLate
}

// IsAbsent checks if the student was absent
func (a *Attendance) IsAbsent() bool {
	return a.Status == AttendanceStatusAbsent
}

// AttendanceCreateRequest represents a request to create attendance record
type AttendanceCreateRequest struct {
	StudentID     uuid.UUID        `json:"student_id" binding:"required"`
	CourseID      uuid.UUID        `json:"course_id" binding:"required"`
	ScheduleID    uuid.UUID        `json:"schedule_id" binding:"required"`
	Date          time.Time        `json:"date" binding:"required"`
	Status        AttendanceStatus `json:"status" binding:"required"`
	ArrivalTime   *time.Time       `json:"arrival_time"`
	DepartureTime *time.Time       `json:"departure_time"`
	Notes         string           `json:"notes"`
	ExcuseReason  string           `json:"excuse_reason"`
}

// AttendanceUpdateRequest represents a request to update attendance record
type AttendanceUpdateRequest struct {
	Status        *AttendanceStatus `json:"status"`
	ArrivalTime   *time.Time        `json:"arrival_time"`
	DepartureTime *time.Time        `json:"departure_time"`
	Notes         *string           `json:"notes"`
	ExcuseReason  *string           `json:"excuse_reason"`
}

// AttendanceBulkCreateRequest represents a request to create multiple attendance records
type AttendanceBulkCreateRequest struct {
	CourseID   uuid.UUID                      `json:"course_id" binding:"required"`
	ScheduleID uuid.UUID                      `json:"schedule_id" binding:"required"`
	Date       time.Time                      `json:"date" binding:"required"`
	Records    []AttendanceRecordRequest      `json:"records" binding:"required,min=1"`
}

// AttendanceRecordRequest represents individual attendance record in bulk request
type AttendanceRecordRequest struct {
	StudentID     uuid.UUID        `json:"student_id" binding:"required"`
	Status        AttendanceStatus `json:"status" binding:"required"`
	ArrivalTime   *time.Time       `json:"arrival_time"`
	DepartureTime *time.Time       `json:"departure_time"`
	Notes         string           `json:"notes"`
	ExcuseReason  string           `json:"excuse_reason"`
}

// AttendanceResponse represents an attendance response
type AttendanceResponse struct {
	ID            uuid.UUID        `json:"id"`
	StudentID     uuid.UUID        `json:"student_id"`
	StudentName   string           `json:"student_name"`
	StudentEmail  string           `json:"student_email"`
	CourseID      uuid.UUID        `json:"course_id"`
	CourseName    string           `json:"course_name"`
	CourseCode    string           `json:"course_code"`
	ScheduleID    uuid.UUID        `json:"schedule_id"`
	ScheduleTitle string           `json:"schedule_title"`
	Date          time.Time        `json:"date"`
	Status        AttendanceStatus `json:"status"`
	ArrivalTime   *time.Time       `json:"arrival_time"`
	DepartureTime *time.Time       `json:"departure_time"`
	Notes         string           `json:"notes"`
	ExcuseReason  string           `json:"excuse_reason"`
	RecordedByID  uuid.UUID        `json:"recorded_by_id"`
	RecordedByName string          `json:"recorded_by_name"`
	RecordedAt    time.Time        `json:"recorded_at"`
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
}

// ToResponse converts an Attendance to AttendanceResponse
func (a *Attendance) ToResponse() *AttendanceResponse {
	response := &AttendanceResponse{
		ID:            a.ID,
		StudentID:     a.StudentID,
		CourseID:      a.CourseID,
		ScheduleID:    a.ScheduleID,
		Date:          a.Date,
		Status:        a.Status,
		ArrivalTime:   a.ArrivalTime,
		DepartureTime: a.DepartureTime,
		Notes:         a.Notes,
		ExcuseReason:  a.ExcuseReason,
		RecordedByID:  a.RecordedByUserID,
		RecordedAt:    a.RecordedAt,
		CreatedAt:     a.CreatedAt,
		UpdatedAt:     a.UpdatedAt,
	}

	// Include student information if available
	if a.Student != nil {
		response.StudentName = a.Student.GetFullName()
		response.StudentEmail = a.Student.Email
	}

	// Include course information if available
	if a.Course != nil {
		response.CourseName = a.Course.Name
		response.CourseCode = a.Course.CourseCode
	}

	// Include schedule information if available
	if a.Schedule != nil {
		response.ScheduleTitle = a.Schedule.Title
	}

	// Include recorded by user information if available
	if a.RecordedBy != nil {
		response.RecordedByName = a.RecordedBy.GetFullName()
	}

	return response
}

// AttendanceListRequest represents a request to list attendance records
type AttendanceListRequest struct {
	models.PaginationRequest
	StudentID    *uuid.UUID        `json:"student_id" form:"student_id"`
	CourseID     *uuid.UUID        `json:"course_id" form:"course_id"`
	ScheduleID   *uuid.UUID        `json:"schedule_id" form:"schedule_id"`
	Status       *AttendanceStatus `json:"status" form:"status"`
	RecordedByID *uuid.UUID        `json:"recorded_by_id" form:"recorded_by_id"`
	StartDate    *time.Time        `json:"start_date" form:"start_date"`
	EndDate      *time.Time        `json:"end_date" form:"end_date"`
	Search       string            `json:"search" form:"search"`
}

// AttendanceListResponse represents a response for attendance list
type AttendanceListResponse struct {
	Attendances []*AttendanceResponse      `json:"attendances"`
	Pagination  *models.PaginationResponse `json:"pagination"`
}

// AttendanceStats represents attendance statistics
type AttendanceStats struct {
	TotalRecords         int64                         `json:"total_records"`
	AttendanceByStatus   map[AttendanceStatus]int64    `json:"attendance_by_status"`
	OverallAttendanceRate float64                      `json:"overall_attendance_rate"`
	PresentCount         int64                         `json:"present_count"`
	AbsentCount          int64                         `json:"absent_count"`
	LateCount            int64                         `json:"late_count"`
	ExcusedCount         int64                         `json:"excused_count"`
}

// StudentAttendanceSummary represents attendance summary for a student
type StudentAttendanceSummary struct {
	StudentID        uuid.UUID `json:"student_id"`
	StudentName      string    `json:"student_name"`
	CourseID         uuid.UUID `json:"course_id"`
	CourseName       string    `json:"course_name"`
	TotalClasses     int64     `json:"total_classes"`
	PresentCount     int64     `json:"present_count"`
	AbsentCount      int64     `json:"absent_count"`
	LateCount        int64     `json:"late_count"`
	ExcusedCount     int64     `json:"excused_count"`
	AttendanceRate   float64   `json:"attendance_rate"`
	LastAttendance   *time.Time `json:"last_attendance"`
}

// CourseAttendanceSummary represents attendance summary for a course
type CourseAttendanceSummary struct {
	CourseID           uuid.UUID `json:"course_id"`
	CourseName         string    `json:"course_name"`
	TotalStudents      int64     `json:"total_students"`
	TotalClasses       int64     `json:"total_classes"`
	AverageAttendance  float64   `json:"average_attendance"`
	HighestAttendance  float64   `json:"highest_attendance"`
	LowestAttendance   float64   `json:"lowest_attendance"`
	TrendDirection     string    `json:"trend_direction"` // IMPROVING, DECLINING, STABLE
}
