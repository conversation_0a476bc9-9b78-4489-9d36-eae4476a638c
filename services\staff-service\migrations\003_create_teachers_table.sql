-- Create teachers table
CREATE TABLE IF NOT EXISTS teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    teacher_id VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    hire_date DATE NOT NULL,
    department VARCHAR(100),
    specialization VARCHAR(255),
    qualifications TEXT,
    experience INTEGER DEFAULT 0,
    salary DECIMAL(12,2),
    rating DECIMAL(3,2) DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    total_courses INTEGER DEFAULT 0,
    emergency_contact VARCHAR(255),
    emergency_phone VARCHAR(20),
    notes TEXT,
    created_by_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMES<PERSON>MP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Create indexes for teachers table
CREATE INDEX IF NOT EXISTS idx_teachers_email ON teachers(email);
CREATE INDEX IF NOT EXISTS idx_teachers_teacher_id ON teachers(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers(user_id);
CREATE INDEX IF NOT EXISTS idx_teachers_status ON teachers(status);
CREATE INDEX IF NOT EXISTS idx_teachers_department ON teachers(department);
CREATE INDEX IF NOT EXISTS idx_teachers_hire_date ON teachers(hire_date);
CREATE INDEX IF NOT EXISTS idx_teachers_rating ON teachers(rating);
CREATE INDEX IF NOT EXISTS idx_teachers_created_at ON teachers(created_at);
CREATE INDEX IF NOT EXISTS idx_teachers_deleted_at ON teachers(deleted_at);

-- Add constraints
ALTER TABLE teachers ADD CONSTRAINT chk_teachers_status 
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'TERMINATED'));

ALTER TABLE teachers ADD CONSTRAINT chk_teachers_rating 
    CHECK (rating >= 0 AND rating <= 5);

ALTER TABLE teachers ADD CONSTRAINT chk_teachers_experience 
    CHECK (experience >= 0);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_teachers_updated_at BEFORE UPDATE ON teachers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate teacher ID
CREATE OR REPLACE FUNCTION generate_teacher_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.teacher_id IS NULL OR NEW.teacher_id = '' THEN
        NEW.teacher_id := 'TCH' || TO_CHAR(CURRENT_DATE, 'YYYY') || LPAD(nextval('teacher_id_seq')::text, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for teacher ID generation
CREATE SEQUENCE IF NOT EXISTS teacher_id_seq START 1;

-- Create trigger for teacher ID generation
CREATE TRIGGER generate_teacher_id_trigger BEFORE INSERT ON teachers
    FOR EACH ROW EXECUTE FUNCTION generate_teacher_id();
