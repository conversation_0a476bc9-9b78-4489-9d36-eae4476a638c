-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID NOT NULL,
    
    -- Transaction details
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    type VARCHAR(20) NOT NULL,
    
    -- Gateway information
    gateway_type VARCHAR(50) NOT NULL,
    gateway_txn_id VARCHAR(255),
    gateway_response TEXT,
    gateway_fee DECIMAL(10,2) DEFAULT 0 CHECK (gateway_fee >= 0),
    
    -- Processing information
    processed_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0 CHECK (retry_count >= 0),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for transactions table
CREATE INDEX IF NOT EXISTS idx_transactions_payment_id ON transactions(payment_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_gateway_type ON transactions(gateway_type);
CREATE INDEX IF NOT EXISTS idx_transactions_gateway_txn_id ON transactions(gateway_txn_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_deleted_at ON transactions(deleted_at);
CREATE INDEX IF NOT EXISTS idx_transactions_processed_at ON transactions(processed_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_transactions_payment_status ON transactions(payment_id, status);
CREATE INDEX IF NOT EXISTS idx_transactions_gateway_status ON transactions(gateway_type, status);
CREATE INDEX IF NOT EXISTS idx_transactions_status_created ON transactions(status, created_at);

-- Add foreign key constraint to payments table
ALTER TABLE transactions ADD CONSTRAINT fk_transactions_payment_id 
FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE;

-- Add check constraints for valid enum values
ALTER TABLE transactions ADD CONSTRAINT chk_transactions_status 
CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'));

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_type 
CHECK (type IN ('PAYMENT', 'REFUND', 'FEE'));

ALTER TABLE transactions ADD CONSTRAINT chk_transactions_currency 
CHECK (currency IN ('USD', 'UZS', 'EUR'));

-- Add comments for documentation
COMMENT ON TABLE transactions IS 'Individual transaction records for payments';
COMMENT ON COLUMN transactions.id IS 'Unique identifier for the transaction';
COMMENT ON COLUMN transactions.payment_id IS 'Reference to the parent payment';
COMMENT ON COLUMN transactions.amount IS 'Transaction amount in the specified currency';
COMMENT ON COLUMN transactions.currency IS 'Currency code (USD, UZS, EUR)';
COMMENT ON COLUMN transactions.status IS 'Transaction status (PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED)';
COMMENT ON COLUMN transactions.type IS 'Transaction type (PAYMENT, REFUND, FEE)';
COMMENT ON COLUMN transactions.gateway_type IS 'Payment gateway used for this transaction';
COMMENT ON COLUMN transactions.gateway_txn_id IS 'Transaction ID from the gateway';
COMMENT ON COLUMN transactions.gateway_response IS 'Raw response from the payment gateway';
COMMENT ON COLUMN transactions.gateway_fee IS 'Fee charged by the gateway for this transaction';
COMMENT ON COLUMN transactions.processed_at IS 'When the transaction was processed';
COMMENT ON COLUMN transactions.failure_reason IS 'Reason for transaction failure';
COMMENT ON COLUMN transactions.retry_count IS 'Number of retry attempts for this transaction';
COMMENT ON COLUMN transactions.metadata IS 'Additional metadata in JSON format';
