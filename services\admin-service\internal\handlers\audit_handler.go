package handlers

import (
	"strconv"
	"time"

	"admin-service/internal/models"
	"admin-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// AuditHandler handles audit log-related HTTP requests
type AuditHandler struct {
	auditService services.AuditService
}

// NewAuditHandler creates a new audit handler
func NewAuditHandler(auditService services.AuditService) *AuditHandler {
	return &AuditHandler{
		auditService: auditService,
	}
}

// GetAuditLogs handles GET /audit-logs
func (h *AuditHandler) GetAuditLogs(c *gin.Context) {
	var req models.AuditLogListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	if userID := c.Query("user_id"); userID != "" {
		if id, err := uuid.Parse(userID); err == nil {
			req.UserID = &id
		}
	}

	if action := c.Query("action"); action != "" {
		auditAction := sharedModels.AuditAction(action)
		if auditAction.IsValid() {
			req.Action = &auditAction
		}
	}

	if resource := c.Query("resource"); resource != "" {
		req.Resource = &resource
	}

	if resourceID := c.Query("resource_id"); resourceID != "" {
		if id, err := uuid.Parse(resourceID); err == nil {
			req.ResourceID = &id
		}
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			// Set to end of day
			endOfDay := date.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			req.EndDate = &endOfDay
		}
	}

	if ipAddress := c.Query("ip_address"); ipAddress != "" {
		req.IPAddress = &ipAddress
	}

	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.auditService.GetAuditLogs(&req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, response, "Audit logs retrieved successfully")
}

// GetAuditLog handles GET /audit-logs/:id
func (h *AuditHandler) GetAuditLog(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid audit log ID")
		return
	}

	auditLog, err := h.auditService.GetAuditLog(id)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, auditLog, "Audit log retrieved successfully")
}

// GetAuditStats handles GET /audit-logs/stats
func (h *AuditHandler) GetAuditStats(c *gin.Context) {
	var startDate, endDate *time.Time

	if start := c.Query("start_date"); start != "" {
		if date, err := time.Parse("2006-01-02", start); err == nil {
			startDate = &date
		}
	}

	if end := c.Query("end_date"); end != "" {
		if date, err := time.Parse("2006-01-02", end); err == nil {
			// Set to end of day
			endOfDay := date.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			endDate = &endOfDay
		}
	}

	stats, err := h.auditService.GetAuditStats(startDate, endDate)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, stats, "Audit statistics retrieved successfully")
}

// GetRecentActivity handles GET /audit-logs/recent
func (h *AuditHandler) GetRecentActivity(c *gin.Context) {
	limit := 10
	if limitParam := c.Query("limit"); limitParam != "" {
		if l, err := strconv.Atoi(limitParam); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	activity, err := h.auditService.GetRecentActivity(limit)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, activity, "Recent activity retrieved successfully")
}
