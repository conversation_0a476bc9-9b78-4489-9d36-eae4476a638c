package services

import (
	"fmt"
	"log"
	"time"

	"admin-service/internal/models"
	"admin-service/internal/repository"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	sharedModels "github.com/crm-microservices/shared/models"
)

// UserService handles user business logic
type UserService interface {
	GetUsers(req *sharedModels.UserListRequest) (*sharedModels.UserListResponse, error)
	GetUser(id uuid.UUID) (*sharedModels.UserResponse, error)
	CreateUser(req *sharedModels.UserCreateRequest, createdBy uuid.UUID, ipAddress, userAgent string) (*sharedModels.UserResponse, error)
	UpdateUser(id uuid.UUID, req *sharedModels.UserUpdateRequest, updatedBy uuid.UUID, ipAddress, userAgent string) (*sharedModels.UserResponse, error)
	DeleteUser(id uuid.UUID, deletedBy uuid.UUID, ipAddress, userAgent string) error
	GetUserStats() (*repository.UserStats, error)
}

type userService struct {
	userRepo  repository.UserRepository
	auditRepo repository.AuditRepository
}

// NewUserService creates a new user service
func NewUserService(userRepo repository.UserRepository, auditRepo repository.AuditRepository) UserService {
	return &userService{
		userRepo:  userRepo,
		auditRepo: auditRepo,
	}
}

// GetUsers retrieves users with pagination and filtering
func (s *userService) GetUsers(req *sharedModels.UserListRequest) (*sharedModels.UserListResponse, error) {
	// Set default pagination if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	users, total, err := s.userRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert to response format
	userResponses := make([]*sharedModels.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = user.ToResponse()
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &sharedModels.UserListResponse{
		Users:      userResponses,
		Pagination: pagination,
	}, nil
}

// GetUser retrieves a user by ID
func (s *userService) GetUser(id uuid.UUID) (*sharedModels.UserResponse, error) {
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user.ToResponse(), nil
}

// CreateUser creates a new user
func (s *userService) CreateUser(req *sharedModels.UserCreateRequest, createdBy uuid.UUID, ipAddress, userAgent string) (*sharedModels.UserResponse, error) {
	// Validate role
	if !req.Role.IsValid() {
		return nil, fmt.Errorf("invalid role: %s", req.Role)
	}

	// Check if email already exists
	if existingUser, _ := s.userRepo.GetByEmail(req.Email); existingUser != nil {
		return nil, fmt.Errorf("user with email %s already exists", req.Email)
	}

	// Check if username already exists
	if existingUser, _ := s.userRepo.GetByUsername(req.Username); existingUser != nil {
		return nil, fmt.Errorf("user with username %s already exists", req.Username)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &sharedModels.User{
		Email:       req.Email,
		Username:    req.Username,
		Password:    string(hashedPassword),
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		Phone:       req.Phone,
		Role:        req.Role,
		Status:      sharedModels.StatusActive,
		DateOfBirth: req.DateOfBirth,
		Address:     req.Address,
		City:        req.City,
		Country:     req.Country,
	}

	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create audit log
	auditLog := &models.AuditLog{
		UserID:    createdBy,
		Action:    sharedModels.ActionCreate,
		Resource:  "user",
		ResourceID: &user.ID,
		Details: map[string]interface{}{
			"created_user_id":    user.ID,
			"created_user_email": user.Email,
			"created_user_role":  user.Role,
		},
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Timestamp: time.Now(),
	}

	if err := s.auditRepo.Create(auditLog); err != nil {
		// Log error but don't fail the operation
		log.Printf("Failed to create audit log: %v", err)
	}

	return user.ToResponse(), nil
}

// UpdateUser updates an existing user
func (s *userService) UpdateUser(id uuid.UUID, req *sharedModels.UserUpdateRequest, updatedBy uuid.UUID, ipAddress, userAgent string) (*sharedModels.UserResponse, error) {
	// Check if user exists
	existingUser, err := s.userRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Prepare updates
	updates := make(map[string]interface{})

	if req.Email != nil {
		// Check if email is already taken by another user
		if emailUser, _ := s.userRepo.GetByEmail(*req.Email); emailUser != nil && emailUser.ID != id {
			return nil, fmt.Errorf("email %s is already taken", *req.Email)
		}
		updates["email"] = *req.Email
	}

	if req.Username != nil {
		// Check if username is already taken by another user
		if usernameUser, _ := s.userRepo.GetByUsername(*req.Username); usernameUser != nil && usernameUser.ID != id {
			return nil, fmt.Errorf("username %s is already taken", *req.Username)
		}
		updates["username"] = *req.Username
	}

	if req.FirstName != nil {
		updates["first_name"] = *req.FirstName
	}

	if req.LastName != nil {
		updates["last_name"] = *req.LastName
	}

	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}

	if req.Role != nil {
		if !req.Role.IsValid() {
			return nil, fmt.Errorf("invalid role: %s", *req.Role)
		}
		updates["role"] = *req.Role
	}

	if req.Status != nil {
		if !req.Status.IsValid() {
			return nil, fmt.Errorf("invalid status: %s", *req.Status)
		}
		updates["status"] = *req.Status
	}

	if req.DateOfBirth != nil {
		updates["date_of_birth"] = *req.DateOfBirth
	}

	if req.Address != nil {
		updates["address"] = *req.Address
	}

	if req.City != nil {
		updates["city"] = *req.City
	}

	if req.Country != nil {
		updates["country"] = *req.Country
	}

	// Update user
	if err := s.userRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Create audit log
	auditLog := &models.AuditLog{
		UserID:     updatedBy,
		Action:     sharedModels.ActionUpdate,
		Resource:   "user",
		ResourceID: &id,
		Details: map[string]interface{}{
			"updated_user_id":    id,
			"updated_user_email": existingUser.Email,
			"changes":            updates,
		},
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Timestamp: time.Now(),
	}

	if err := s.auditRepo.Create(auditLog); err != nil {
		// Log error but don't fail the operation
		log.Printf("Failed to create audit log: %v", err)
	}

	// Get updated user
	updatedUser, err := s.userRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated user: %w", err)
	}

	return updatedUser.ToResponse(), nil
}

// DeleteUser soft deletes a user
func (s *userService) DeleteUser(id uuid.UUID, deletedBy uuid.UUID, ipAddress, userAgent string) error {
	// Check if user exists
	existingUser, err := s.userRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Delete user
	if err := s.userRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	// Create audit log
	auditLog := &models.AuditLog{
		UserID:     deletedBy,
		Action:     sharedModels.ActionDelete,
		Resource:   "user",
		ResourceID: &id,
		Details: map[string]interface{}{
			"deleted_user_id":    id,
			"deleted_user_email": existingUser.Email,
			"deleted_user_role":  existingUser.Role,
		},
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Timestamp: time.Now(),
	}

	if err := s.auditRepo.Create(auditLog); err != nil {
		// Log error but don't fail the operation
		log.Printf("Failed to create audit log: %v", err)
	}

	return nil
}

// GetUserStats retrieves user statistics
func (s *userService) GetUserStats() (*repository.UserStats, error) {
	return s.userRepo.GetUserStats()
}
