package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/auth-service/internal/services"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// <PERSON><PERSON><PERSON><PERSON><PERSON> handles authentication HTTP requests
type AuthHandler struct {
	authService *services.AuthService
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authService *services.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login handles user login
// @Summary User login
// @Description Authenticate user with email and password
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.LoginRequest true "Login credentials"
// @Success 200 {object} models.APIResponse{data=models.LoginResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /auth/login [post]
func (h *<PERSON>th<PERSON>and<PERSON>) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// Get client IP and User-Agent
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// Authenticate user
	response, err := h.authService.Login(&req, ipAddress, userAgent)
	if err != nil {
		switch err {
		case services.ErrInvalidCredentials:
			utils.UnauthorizedResponse(c, "Invalid email or password")
		case services.ErrUserInactive:
			utils.ForbiddenResponse(c, "User account is inactive")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.LoginResponse(c, response)
}

// Register handles user registration
// @Summary User registration
// @Description Register a new user account
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.UserCreateRequest true "User registration data"
// @Success 201 {object} models.APIResponse{data=models.UserResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 409 {object} models.APIResponse
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// Register user
	user, err := h.authService.Register(&req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.CreatedResponse(c, user, "User registered successfully")
}

// RefreshToken handles token refresh
// @Summary Refresh access token
// @Description Generate new access token using refresh token
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.RefreshTokenRequest true "Refresh token"
// @Success 200 {object} models.APIResponse{data=models.LoginResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// Refresh token
	response, err := h.authService.RefreshToken(&req)
	if err != nil {
		switch err {
		case services.ErrInvalidRefreshToken, services.ErrSessionNotFound, services.ErrSessionExpired:
			utils.UnauthorizedResponse(c, "Invalid or expired refresh token")
		case services.ErrUserNotFound:
			utils.UnauthorizedResponse(c, "User not found")
		case services.ErrUserInactive:
			utils.ForbiddenResponse(c, "User account is inactive")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.RefreshTokenResponse(c, response)
}

// Logout handles user logout
// @Summary User logout
// @Description Invalidate user session
// @Tags Authentication
// @Security BearerAuth
// @Success 200 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// Extract token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		utils.UnauthorizedResponse(c, "Authorization header is required")
		return
	}

	token, err := utils.ExtractTokenFromHeader(authHeader)
	if err != nil {
		utils.UnauthorizedResponse(c, "Invalid authorization header format")
		return
	}

	// Logout user
	if err := h.authService.Logout(token); err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.LogoutResponse(c)
}

// ValidateToken handles token validation
// @Summary Validate access token
// @Description Validate and return token claims
// @Tags Authentication
// @Security BearerAuth
// @Success 200 {object} models.APIResponse{data=utils.JWTClaims}
// @Failure 401 {object} models.APIResponse
// @Router /auth/validate [get]
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	// Extract token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		utils.UnauthorizedResponse(c, "Authorization header is required")
		return
	}

	token, err := utils.ExtractTokenFromHeader(authHeader)
	if err != nil {
		utils.UnauthorizedResponse(c, "Invalid authorization header format")
		return
	}

	// Validate token
	claims, err := h.authService.ValidateToken(token)
	if err != nil {
		switch err {
		case utils.ErrInvalidToken, utils.ErrExpiredToken, services.ErrSessionExpired:
			utils.UnauthorizedResponse(c, "Invalid or expired token")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.SuccessResponse(c, claims, "Token is valid")
}

// ForgotPassword handles password reset request
// @Summary Request password reset
// @Description Send password reset email
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.ResetPasswordRequest true "Email for password reset"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Router /auth/forgot-password [post]
func (h *AuthHandler) ForgotPassword(c *gin.Context) {
	var req models.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// Process forgot password request
	if err := h.authService.ForgotPassword(&req); err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.PasswordResetResponse(c)
}

// ResetPassword handles password reset confirmation
// @Summary Reset password
// @Description Reset password using reset token
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.ConfirmResetPasswordRequest true "Password reset data"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /auth/reset-password [post]
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var req models.ConfirmResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// Reset password
	if err := h.authService.ResetPassword(&req); err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, nil, "Password reset successfully")
}

// ChangePassword handles password change
// @Summary Change password
// @Description Change user password
// @Tags Authentication
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body models.ChangePasswordRequest true "Password change data"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userID, ok := userIDStr.(string)
	if !ok {
		utils.InternalServerErrorResponse(c, "Invalid user ID format")
		return
	}

	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// Parse user ID
	parsedUserID, err := utils.ParseUUID(userID)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	// Change password
	if err := h.authService.ChangePassword(parsedUserID, &req); err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		case services.ErrInvalidCredentials:
			utils.UnauthorizedResponse(c, "Current password is incorrect")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.PasswordChangeResponse(c)
}

// GetAuthStatus returns current authentication status
// @Summary Get authentication status
// @Description Get current user authentication status
// @Tags Authentication
// @Security BearerAuth
// @Success 200 {object} models.APIResponse{data=models.UserResponse}
// @Failure 401 {object} models.APIResponse
// @Router /auth/status [get]
func (h *AuthHandler) GetAuthStatus(c *gin.Context) {
	// Get user claims from context (set by auth middleware)
	claims, exists := c.Get("claims")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userClaims, ok := claims.(*utils.JWTClaims)
	if !ok {
		utils.InternalServerErrorResponse(c, "Invalid authentication claims")
		return
	}

	// Return user information from claims
	userInfo := map[string]interface{}{
		"user_id":    userClaims.UserID,
		"email":      userClaims.Email,
		"username":   userClaims.Username,
		"role":       userClaims.Role,
		"session_id": userClaims.SessionID,
		"expires_at": userClaims.ExpiresAt,
	}

	utils.SuccessResponse(c, userInfo, "Authentication status retrieved")
}
