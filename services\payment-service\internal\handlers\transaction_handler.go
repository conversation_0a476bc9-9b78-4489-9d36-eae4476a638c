package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"payment-service/internal/models"
	"payment-service/internal/repository"
	"payment-service/internal/services"
	sharedModels "github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// TransactionHandler handles transaction-related HTTP requests
type TransactionHandler struct {
	transactionService services.TransactionService
}

// NewTransactionHandler creates a new transaction handler
func NewTransactionHandler(transactionService services.TransactionService) *TransactionHandler {
	return &TransactionHandler{
		transactionService: transactionService,
	}
}

// GetTransactions handles GET /transactions
func (h *TransactionHandler) GetTransactions(c *gin.Context) {
	var req repository.TransactionListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	if paymentID := c.Query("payment_id"); paymentID != "" {
		if id, err := uuid.Parse(paymentID); err == nil {
			req.PaymentID = &id
		}
	}

	if status := c.Query("status"); status != "" {
		transactionStatus := sharedModels.TransactionStatus(status)
		if transactionStatus.IsValid() {
			req.Status = &transactionStatus
		}
	}

	if txnType := c.Query("type"); txnType != "" {
		transactionType := sharedModels.TransactionType(txnType)
		if transactionType.IsValid() {
			req.Type = &transactionType
		}
	}

	if gatewayType := c.Query("gateway_type"); gatewayType != "" {
		req.GatewayType = &gatewayType
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = &date
		}
	}

	if search := c.Query("search"); search != "" {
		req.Search = &search
	}

	response, err := h.transactionService.GetTransactions(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, sharedModels.PaginatedResponse(response.Transactions, response.Pagination, "Transactions retrieved successfully"))
}

// GetTransaction handles GET /transactions/:id
func (h *TransactionHandler) GetTransaction(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse("Invalid transaction ID"))
		return
	}

	response, err := h.transactionService.GetTransaction(id)
	if err != nil {
		c.JSON(http.StatusNotFound, sharedModels.ErrorResponse("Transaction not found"))
		return
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(response.Transaction, "Transaction retrieved successfully"))
}

// GetTransactionsByPayment handles GET /payments/:payment_id/transactions
func (h *TransactionHandler) GetTransactionsByPayment(c *gin.Context) {
	paymentIDParam := c.Param("payment_id")
	paymentID, err := uuid.Parse(paymentIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse("Invalid payment ID"))
		return
	}

	response, err := h.transactionService.GetTransactionsByPayment(paymentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(response.Transactions, "Transactions retrieved successfully"))
}

// CreateTransaction handles POST /transactions
func (h *TransactionHandler) CreateTransaction(c *gin.Context) {
	var req services.TransactionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.transactionService.CreateTransaction(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusCreated, sharedModels.SuccessResponse(response.Transaction, "Transaction created successfully"))
}

// UpdateTransaction handles PUT /transactions/:id
func (h *TransactionHandler) UpdateTransaction(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse("Invalid transaction ID"))
		return
	}

	var req services.TransactionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse(utils.FormatValidationError(err)))
		return
	}

	response, err := h.transactionService.UpdateTransaction(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(response.Transaction, "Transaction updated successfully"))
}

// RetryTransaction handles POST /transactions/:id/retry
func (h *TransactionHandler) RetryTransaction(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse("Invalid transaction ID"))
		return
	}

	response, err := h.transactionService.RetryTransaction(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(response.Transaction, "Transaction retry initiated successfully"))
}

// GetTransactionStats handles GET /transactions/stats
func (h *TransactionHandler) GetTransactionStats(c *gin.Context) {
	stats, err := h.transactionService.GetTransactionStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(stats, "Transaction statistics retrieved successfully"))
}

// GetFailedTransactions handles GET /transactions/failed
func (h *TransactionHandler) GetFailedTransactions(c *gin.Context) {
	transactions, err := h.transactionService.GetFailedTransactions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(transactions, "Failed transactions retrieved successfully"))
}

// GetTransactionReport handles GET /reports/transactions
func (h *TransactionHandler) GetTransactionReport(c *gin.Context) {
	// Parse date range parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	gatewayType := c.Query("gateway_type")

	var req repository.TransactionListRequest

	if startDateStr != "" {
		if date, err := time.Parse("2006-01-02", startDateStr); err == nil {
			req.StartDate = &date
		} else {
			c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse("Invalid start_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to last 30 days
		startDate := time.Now().AddDate(0, 0, -30)
		req.StartDate = &startDate
	}

	if endDateStr != "" {
		if date, err := time.Parse("2006-01-02", endDateStr); err == nil {
			req.EndDate = &date
		} else {
			c.JSON(http.StatusBadRequest, sharedModels.ErrorResponse("Invalid end_date format. Use YYYY-MM-DD"))
			return
		}
	} else {
		// Default to today
		endDate := time.Now()
		req.EndDate = &endDate
	}

	if gatewayType != "" {
		req.GatewayType = &gatewayType
	}

	// Set a large page size to get all transactions for the report
	req.PageSize = 10000

	response, err := h.transactionService.GetTransactions(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, sharedModels.ErrorResponse(err.Error()))
		return
	}

	// Generate report data
	reportData := map[string]interface{}{
		"period": map[string]interface{}{
			"start_date": req.StartDate.Format("2006-01-02"),
			"end_date":   req.EndDate.Format("2006-01-02"),
		},
		"transactions": response.Transactions,
		"summary": map[string]interface{}{
			"total_count":      len(response.Transactions),
			"total_amount":     calculateTotalAmount(response.Transactions),
			"success_count":    countByStatus(response.Transactions, models.TransactionCompleted),
			"failed_count":     countByStatus(response.Transactions, models.TransactionFailed),
			"pending_count":    countByStatus(response.Transactions, models.TransactionPending),
			"gateway_breakdown": getGatewayBreakdown(response.Transactions),
		},
	}

	c.JSON(http.StatusOK, sharedModels.SuccessResponse(reportData, "Transaction report generated successfully"))
}

// Helper functions for transaction report

func calculateTotalAmount(transactions []*models.Transaction) float64 {
	total := 0.0
	for _, txn := range transactions {
		if txn.Status == models.TransactionCompleted {
			total += txn.Amount
		}
	}
	return total
}

func countByStatus(transactions []*models.Transaction, status models.TransactionStatus) int {
	count := 0
	for _, txn := range transactions {
		if txn.Status == status {
			count++
		}
	}
	return count
}

func getGatewayBreakdown(transactions []*models.Transaction) map[string]interface{} {
	breakdown := make(map[string]interface{})
	gatewayCounts := make(map[string]int)
	gatewayAmounts := make(map[string]float64)

	for _, txn := range transactions {
		gatewayCounts[txn.GatewayType]++
		if txn.Status == models.TransactionCompleted {
			gatewayAmounts[txn.GatewayType] += txn.Amount
		}
	}

	for gateway := range gatewayCounts {
		breakdown[gateway] = map[string]interface{}{
			"count":  gatewayCounts[gateway],
			"amount": gatewayAmounts[gateway],
		}
	}

	return breakdown
}
