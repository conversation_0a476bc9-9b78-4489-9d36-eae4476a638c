package repository

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"payment-service/internal/models"
	sharedModels "github.com/crm-microservices/shared/models"
)

// TransactionRepository handles transaction data operations
type TransactionRepository interface {
	GetAll(req *TransactionListRequest) ([]*models.Transaction, int64, error)
	GetByID(id uuid.UUID) (*models.Transaction, error)
	GetByPaymentID(paymentID uuid.UUID) ([]*models.Transaction, error)
	GetByGatewayTxnID(gatewayTxnID string) (*models.Transaction, error)
	Create(transaction *models.Transaction) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetByStatus(status models.TransactionStatus) ([]*models.Transaction, error)
	GetFailedTransactions() ([]*models.Transaction, error)
	GetRetryableTransactions() ([]*models.Transaction, error)
	GetTransactionStats() (*TransactionStats, error)
}

type transactionRepository struct {
	db *gorm.DB
}

// NewTransactionRepository creates a new transaction repository
func NewTransactionRepository(db *gorm.DB) TransactionRepository {
	return &transactionRepository{db: db}
}

// TransactionListRequest represents request parameters for listing transactions
type TransactionListRequest struct {
	sharedModels.PaginationRequest
	PaymentID   *uuid.UUID                       `form:"payment_id"`
	Status      *sharedModels.TransactionStatus  `form:"status"`
	Type        *sharedModels.TransactionType    `form:"type"`
	GatewayType *string                          `form:"gateway_type"`
	StartDate   *time.Time                       `form:"start_date"`
	EndDate     *time.Time                       `form:"end_date"`
	Search      *string                          `form:"search"`
}

// TransactionStats represents transaction statistics
type TransactionStats struct {
	TotalTransactions     int64   `json:"total_transactions"`
	CompletedTransactions int64   `json:"completed_transactions"`
	FailedTransactions    int64   `json:"failed_transactions"`
	PendingTransactions   int64   `json:"pending_transactions"`
	TotalAmount           float64 `json:"total_amount"`
	TotalFees             float64 `json:"total_fees"`
	AverageAmount         float64 `json:"average_amount"`
	SuccessRate           float64 `json:"success_rate"`
}

// GetAll retrieves transactions with pagination and filtering
func (r *transactionRepository) GetAll(req *TransactionListRequest) ([]*models.Transaction, int64, error) {
	var transactions []*models.Transaction
	var total int64

	query := r.db.Model(&models.Transaction{}).Preload("Payment")

	// Apply filters
	if req.PaymentID != nil {
		query = query.Where("payment_id = ?", *req.PaymentID)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}

	if req.GatewayType != nil {
		query = query.Where("gateway_type = ?", *req.GatewayType)
	}

	if req.StartDate != nil {
		query = query.Where("created_at >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("created_at <= ?", *req.EndDate)
	}

	if req.Search != nil && *req.Search != "" {
		searchTerm := "%" + *req.Search + "%"
		query = query.Where("gateway_txn_id ILIKE ? OR failure_reason ILIKE ?", searchTerm, searchTerm)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()
	
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

// GetByID retrieves a transaction by ID
func (r *transactionRepository) GetByID(id uuid.UUID) (*models.Transaction, error) {
	var transaction models.Transaction
	if err := r.db.Preload("Payment").First(&transaction, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &transaction, nil
}

// GetByPaymentID retrieves transactions by payment ID
func (r *transactionRepository) GetByPaymentID(paymentID uuid.UUID) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	if err := r.db.Where("payment_id = ?", paymentID).Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, err
	}
	return transactions, nil
}

// GetByGatewayTxnID retrieves a transaction by gateway transaction ID
func (r *transactionRepository) GetByGatewayTxnID(gatewayTxnID string) (*models.Transaction, error) {
	var transaction models.Transaction
	if err := r.db.Preload("Payment").First(&transaction, "gateway_txn_id = ?", gatewayTxnID).Error; err != nil {
		return nil, err
	}
	return &transaction, nil
}

// Create creates a new transaction
func (r *transactionRepository) Create(transaction *models.Transaction) error {
	return r.db.Create(transaction).Error
}

// Update updates a transaction
func (r *transactionRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	return r.db.Model(&models.Transaction{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a transaction
func (r *transactionRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&models.Transaction{}, "id = ?", id).Error
}

// GetByStatus retrieves transactions by status
func (r *transactionRepository) GetByStatus(status models.TransactionStatus) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	if err := r.db.Preload("Payment").Where("status = ?", status).Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, err
	}
	return transactions, nil
}

// GetFailedTransactions retrieves all failed transactions
func (r *transactionRepository) GetFailedTransactions() ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	if err := r.db.Preload("Payment").Where("status = ?", models.TransactionFailed).Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, err
	}
	return transactions, nil
}

// GetRetryableTransactions retrieves transactions that can be retried
func (r *transactionRepository) GetRetryableTransactions() ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	if err := r.db.Preload("Payment").
		Where("status = ? AND retry_count < ?", models.TransactionFailed, 3).
		Where("created_at > ?", time.Now().Add(-24*time.Hour)). // Only retry transactions from last 24 hours
		Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, err
	}
	return transactions, nil
}

// GetTransactionStats retrieves transaction statistics
func (r *transactionRepository) GetTransactionStats() (*TransactionStats, error) {
	var stats TransactionStats
	
	// Get total transactions, amount, and fees
	if err := r.db.Model(&models.Transaction{}).
		Select("COUNT(*) as total_transactions, COALESCE(SUM(amount), 0) as total_amount, COALESCE(SUM(gateway_fee), 0) as total_fees, COALESCE(AVG(amount), 0) as average_amount").
		Scan(&stats).Error; err != nil {
		return nil, err
	}

	// Get transactions by status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	
	if err := r.db.Model(&models.Transaction{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, err
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.TransactionCompleted):
			stats.CompletedTransactions = sc.Count
		case string(models.TransactionFailed):
			stats.FailedTransactions = sc.Count
		case string(models.TransactionPending):
			stats.PendingTransactions = sc.Count
		}
	}

	// Calculate success rate
	if stats.TotalTransactions > 0 {
		stats.SuccessRate = float64(stats.CompletedTransactions) / float64(stats.TotalTransactions) * 100
	}

	return &stats, nil
}
