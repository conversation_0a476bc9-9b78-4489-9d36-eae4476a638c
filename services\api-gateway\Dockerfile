# Build stage
FROM golang:1.23-alpine AS builder

# Set working directory
WORKDIR /app

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates wget

# Copy go.mod and go.sum files
COPY services/api-gateway/go.mod services/api-gateway/go.sum ./

# Create shared directory
RUN mkdir -p shared

# Copy shared module
COPY shared/ ./shared/

# Copy api gateway service
COPY services/api-gateway/ .

# Fix shared module path in go.mod
RUN sed -i 's|=> ../../shared|=> ./shared|g' go.mod

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# Final stage
FROM alpine:latest

# Install ca-certificates and wget for health checks
RUN apk --no-cache add ca-certificates wget

# Create app directory
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main .

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./main"]
