package repository

import (
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/crm-microservices/shared/models"
)

// UserRepository handles user data operations
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// Create creates a new user
func (r *UserRepository) Create(user *models.User) error {
	if err := r.db.Create(user).Error; err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	return nil
}

// GetByID retrieves a user by ID
func (r *UserRepository) GetByID(id uuid.UUID) (*models.User, error) {
	var user models.User
	if err := r.db.Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}
	return &user, nil
}

// GetByEmail retrieves a user by email
func (r *UserRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}
	return &user, nil
}

// GetByUsername retrieves a user by username
func (r *UserRepository) GetByUsername(username string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}
	return &user, nil
}

// GetByEmailOrUsername retrieves a user by email or username
func (r *UserRepository) GetByEmailOrUsername(identifier string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ? OR username = ?", identifier, identifier).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by email or username: %w", err)
	}
	return &user, nil
}

// Update updates a user
func (r *UserRepository) Update(user *models.User) error {
	if err := r.db.Save(user).Error; err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}
	return nil
}

// Delete soft deletes a user
func (r *UserRepository) Delete(id uuid.UUID) error {
	if err := r.db.Delete(&models.User{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	return nil
}

// List retrieves users with pagination and filtering
func (r *UserRepository) List(req *models.UserListRequest) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := r.db.Model(&models.User{})

	// Apply filters
	if req.Role != nil {
		query = query.Where("role = ?", *req.Role)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where(
			"first_name ILIKE ? OR last_name ILIKE ? OR email ILIKE ? OR username ILIKE ?",
			searchPattern, searchPattern, searchPattern, searchPattern,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	return users, total, nil
}

// ExistsByEmail checks if a user exists with the given email
func (r *UserRepository) ExistsByEmail(email string) (bool, error) {
	var count int64
	if err := r.db.Model(&models.User{}).Where("email = ?", email).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if user exists by email: %w", err)
	}
	return count > 0, nil
}

// ExistsByUsername checks if a user exists with the given username
func (r *UserRepository) ExistsByUsername(username string) (bool, error) {
	var count int64
	if err := r.db.Model(&models.User{}).Where("username = ?", username).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if user exists by username: %w", err)
	}
	return count > 0, nil
}

// ExistsByPhone checks if a user exists with the given phone
func (r *UserRepository) ExistsByPhone(phone string) (bool, error) {
	var count int64
	if err := r.db.Model(&models.User{}).Where("phone = ?", phone).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if user exists by phone: %w", err)
	}
	return count > 0, nil
}

// UpdateLastLogin updates the user's last login time
func (r *UserRepository) UpdateLastLogin(id uuid.UUID) error {
	if err := r.db.Model(&models.User{}).Where("id = ?", id).Update("last_login_at", gorm.Expr("NOW()")).Error; err != nil {
		return fmt.Errorf("failed to update last login: %w", err)
	}
	return nil
}

// UpdatePassword updates the user's password
func (r *UserRepository) UpdatePassword(id uuid.UUID, hashedPassword string) error {
	updates := map[string]interface{}{
		"password":            hashedPassword,
		"password_changed_at": gorm.Expr("NOW()"),
	}
	
	if err := r.db.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}
	return nil
}

// UpdateStatus updates the user's status
func (r *UserRepository) UpdateStatus(id uuid.UUID, status models.UserStatus) error {
	if err := r.db.Model(&models.User{}).Where("id = ?", id).Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update user status: %w", err)
	}
	return nil
}

// VerifyEmail marks the user's email as verified
func (r *UserRepository) VerifyEmail(id uuid.UUID) error {
	updates := map[string]interface{}{
		"email_verified":    true,
		"email_verified_at": gorm.Expr("NOW()"),
	}
	
	if err := r.db.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to verify email: %w", err)
	}
	return nil
}

// VerifyPhone marks the user's phone as verified
func (r *UserRepository) VerifyPhone(id uuid.UUID) error {
	updates := map[string]interface{}{
		"phone_verified":    true,
		"phone_verified_at": gorm.Expr("NOW()"),
	}
	
	if err := r.db.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to verify phone: %w", err)
	}
	return nil
}

// GetActiveUserCount returns the count of active users
func (r *UserRepository) GetActiveUserCount() (int64, error) {
	var count int64
	if err := r.db.Model(&models.User{}).Where("status = ?", models.StatusActive).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to get active user count: %w", err)
	}
	return count, nil
}

// GetUsersByRole returns users with a specific role
func (r *UserRepository) GetUsersByRole(role models.UserRole) ([]*models.User, error) {
	var users []*models.User
	if err := r.db.Where("role = ? AND status = ?", role, models.StatusActive).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get users by role: %w", err)
	}
	return users, nil
}

// GetRecentUsers returns recently created users
func (r *UserRepository) GetRecentUsers(limit int) ([]*models.User, error) {
	var users []*models.User
	if err := r.db.Order("created_at DESC").Limit(limit).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent users: %w", err)
	}
	return users, nil
}
