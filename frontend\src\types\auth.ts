// Authentication types based on backend models

export type UserRole = 
  | 'ADMIN'
  | 'CASHIER'
  | 'RECEPTION'
  | 'TEACHER'
  | 'MANAGER'
  | 'ACADEMIC_MANAGER';

export type UserStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';

export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  role: UserRole;
  status: UserStatus;
  last_login_at?: string;
  avatar?: string;
  date_of_birth?: string;
  address?: string;
  city?: string;
  country?: string;
  email_verified: boolean;
  email_verified_at?: string;
  phone_verified: boolean;
  phone_verified_at?: string;
  two_factor_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_at: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ConfirmResetPasswordRequest {
  token: string;
  new_password: string;
}

export interface UserCreateRequest {
  email: string;
  username: string;
  password: string;
  first_name: string;
  last_name: string;
  phone: string;
  role: UserRole;
  date_of_birth?: string;
  address?: string;
  city?: string;
  country?: string;
}

export interface UserUpdateRequest {
  email?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  role?: UserRole;
  status?: UserStatus;
  date_of_birth?: string;
  address?: string;
  city?: string;
  country?: string;
}

export interface Session {
  id: string;
  user_id: string;
  expires_at: string;
  is_active: boolean;
  ip_address?: string;
  user_agent?: string;
  last_used_at: string;
  user?: User;
}

// Auth context type
export interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (user: User) => void;
}
