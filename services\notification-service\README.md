# 📧 Notification Service

A comprehensive notification service for the Innovative Centre CRM system, providing multi-channel notification delivery including email, SMS, in-app, and push notifications.

## 🚀 Features

### Core Functionality
- **Multi-Channel Notifications**: Email, SMS, in-app, and push notifications
- **Template Management**: Dynamic template system with variable substitution
- **User Preferences**: Granular notification preferences per user
- **Queue Management**: Reliable notification processing with retry logic
- **Delivery Tracking**: Comprehensive delivery logs and analytics
- **Scheduling**: Support for scheduled and delayed notifications

### Email Notifications
- SMTP integration with TLS support
- HTML and plain text email support
- Template-based emails with dynamic content
- Bulk email sending capabilities
- Welcome emails and password reset emails

### SMS Notifications
- Twilio integration for SMS delivery
- International phone number support
- Template-based SMS with dynamic content
- Bulk SMS sending capabilities
- Class reminders, payment reminders, and verification codes

### Template System
- Dynamic template creation and management
- Variable substitution with `{{variable}}` syntax
- Template versioning and cloning
- Default templates per notification type
- Template validation and preview

### User Preferences
- Per-user notification preferences
- Channel-specific settings (email, SMS, in-app, push)
- Category-based preferences (marketing, transactional, reminders)
- Quiet hours and timezone support
- Digest notifications and frequency control

## 📋 API Endpoints

### Health Checks
- `GET /health` - Basic health check
- `GET /health/ready` - Readiness check
- `GET /health/live` - Liveness check

### Notifications
- `POST /api/v1/notifications` - Create notification
- `GET /api/v1/notifications` - List notifications with filters
- `GET /api/v1/notifications/:id` - Get notification by ID
- `PUT /api/v1/notifications/:id/status` - Update notification status
- `DELETE /api/v1/notifications/:id` - Delete notification
- `GET /api/v1/notifications/stats` - Get notification statistics

### Email Notifications
- `POST /api/v1/notifications/email` - Send email
- `POST /api/v1/notifications/email/bulk` - Send bulk emails
- `POST /api/v1/notifications/email/templated` - Send templated email
- `POST /api/v1/notifications/email/welcome` - Send welcome email
- `POST /api/v1/notifications/email/password-reset` - Send password reset email

### SMS Notifications
- `POST /api/v1/notifications/sms` - Send SMS
- `POST /api/v1/notifications/sms/bulk` - Send bulk SMS
- `POST /api/v1/notifications/sms/templated` - Send templated SMS
- `POST /api/v1/notifications/sms/class-reminder` - Send class reminder
- `POST /api/v1/notifications/sms/payment-reminder` - Send payment reminder
- `POST /api/v1/notifications/sms/welcome` - Send welcome SMS
- `POST /api/v1/notifications/sms/verification` - Send verification code
- `POST /api/v1/notifications/sms/emergency` - Send emergency notification

### Templates
- `POST /api/v1/templates` - Create template
- `GET /api/v1/templates` - List templates with filters
- `GET /api/v1/templates/:id` - Get template by ID
- `PUT /api/v1/templates/:id` - Update template
- `DELETE /api/v1/templates/:id` - Delete template
- `POST /api/v1/templates/:id/clone` - Clone template
- `GET /api/v1/templates/type/:type` - Get templates by type
- `GET /api/v1/templates/default/:type` - Get default template
- `GET /api/v1/templates/stats` - Get template statistics

### User Preferences
- `POST /api/v1/preferences` - Create user preferences
- `GET /api/v1/preferences/:user_id` - Get user preferences
- `PUT /api/v1/preferences/:user_id` - Update user preferences
- `DELETE /api/v1/preferences/:user_id` - Delete user preferences
- `PUT /api/v1/preferences/:user_id/email` - Update email preferences
- `PUT /api/v1/preferences/:user_id/sms` - Update SMS preferences
- `PUT /api/v1/preferences/:user_id/in-app` - Update in-app preferences
- `PUT /api/v1/preferences/:user_id/push` - Update push preferences
- `PUT /api/v1/preferences/:user_id/contact` - Update contact info
- `PUT /api/v1/preferences/:user_id/quiet-hours` - Update quiet hours
- `PUT /api/v1/preferences/:user_id/digest` - Update digest settings
- `GET /api/v1/preferences/stats` - Get preference statistics

### Service Status
- `GET /api/v1/status/email` - Email service status
- `GET /api/v1/status/sms` - SMS service status
- `POST /api/v1/status/email/test` - Test email connection
- `POST /api/v1/status/sms/test` - Test SMS connection

## 🔧 Configuration

### Environment Variables

#### Server Configuration
- `PORT` - Server port (default: 8084)
- `ENVIRONMENT` - Environment (development/production)
- `LOG_LEVEL` - Log level (debug/info/warn/error)

#### Database Configuration
- `DATABASE_URL` - PostgreSQL connection string

#### Authentication
- `AUTH_SERVICE_URL` - Auth service URL
- `JWT_SECRET` - JWT secret key

#### Email Configuration (SMTP)
- `SMTP_HOST` - SMTP server host
- `SMTP_PORT` - SMTP server port
- `SMTP_USER` - SMTP username
- `SMTP_PASSWORD` - SMTP password
- `SMTP_FROM` - From email address

#### SMS Configuration (Twilio)
- `TWILIO_ACCOUNT_SID` - Twilio Account SID
- `TWILIO_AUTH_TOKEN` - Twilio Auth Token
- `TWILIO_FROM_NUMBER` - Twilio phone number

#### Notification Settings
- `MAX_RETRIES` - Maximum retry attempts (default: 3)
- `RETRY_DELAY_SECONDS` - Retry delay in seconds (default: 60)
- `BATCH_SIZE` - Queue processing batch size (default: 100)
- `QUEUE_PROCESS_INTERVAL` - Queue processing interval in seconds (default: 30)

## 🚀 Getting Started

### Prerequisites
- Go 1.21 or later
- PostgreSQL database
- SMTP server (for email notifications)
- Twilio account (for SMS notifications)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd services/notification-service
   ```

2. **Install dependencies**
   ```bash
   go mod download
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run database migrations**
   ```bash
   # Migrations are automatically run on startup
   ```

5. **Start the service**
   ```bash
   go run main.go
   ```

### Docker Deployment

1. **Build Docker image**
   ```bash
   docker build -t notification-service .
   ```

2. **Run with Docker**
   ```bash
   docker run -p 8084:8084 \
     -e DATABASE_URL="your-database-url" \
     -e SMTP_HOST="your-smtp-host" \
     -e SMTP_USER="your-smtp-user" \
     -e SMTP_PASSWORD="your-smtp-password" \
     -e TWILIO_ACCOUNT_SID="your-twilio-sid" \
     -e TWILIO_AUTH_TOKEN="your-twilio-token" \
     notification-service
   ```

## 📊 Database Schema

The service uses the following main tables:
- `notifications` - Notification records
- `notification_templates` - Template definitions
- `notification_preferences` - User preferences
- `delivery_logs` - Delivery attempt logs
- `notification_queue` - Processing queue

## 🔐 Authentication

All API endpoints (except health checks) require JWT authentication:

```bash
curl -H "Authorization: Bearer <jwt-token>" \
     -H "Content-Type: application/json" \
     http://localhost:8084/api/v1/notifications
```

## 📈 Monitoring

### Health Checks
- `/health` - Basic health status
- `/health/ready` - Service readiness
- `/health/live` - Service liveness

### Metrics
- Notification delivery rates
- Template usage statistics
- User preference analytics
- Queue processing metrics

## 🧪 Testing

### Unit Tests
```bash
go test ./...
```

### Integration Tests
```bash
go test -tags=integration ./...
```

### API Testing
Use the provided test scripts or tools like Postman to test API endpoints.

## 🤝 Integration

### With Other Services
- **Admin Service**: User management and authentication
- **Staff Service**: Student and course management
- **Payment Service**: Payment notifications

### Webhook Support
- Email delivery webhooks
- SMS delivery webhooks
- Real-time delivery status updates

## 📚 Documentation

- [API Documentation](../docs/API_DOCUMENTATION.md)
- [Deployment Guide](../docs/DEPLOYMENT_GUIDE.md)
- [Architecture Overview](../docs/MICROSERVICES_ARCHITECTURE.md)

## 🔧 Development

### Project Structure
```
notification-service/
├── main.go                 # Application entry point
├── internal/
│   ├── config/            # Configuration management
│   ├── database/          # Database connection and migrations
│   ├── handlers/          # HTTP request handlers
│   ├── middleware/        # HTTP middleware
│   ├── models/           # Data models
│   ├── router/           # HTTP routing
│   └── services/         # Business logic services
├── migrations/           # Database migrations
├── Dockerfile           # Docker configuration
└── README.md           # This file
```

### Adding New Features
1. Define models in `internal/models/`
2. Implement business logic in `internal/services/`
3. Create HTTP handlers in `internal/handlers/`
4. Add routes in `internal/router/`
5. Create database migrations in `migrations/`

## 📄 License

This project is part of the Innovative Centre CRM system.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the project repository.
