package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if base.ID == uuid.Nil {
		base.ID = uuid.New()
	}
	return nil
}

// UserRole represents user roles in the system
type UserRole string

const (
	RoleAdmin           UserRole = "ADMIN"
	RoleCashier         UserRole = "CASHIER"
	RoleReception       UserRole = "RECEPTION"
	RoleTeacher         UserRole = "TEACHER"
	RoleManager         UserRole = "MANAGER"
	RoleAcademicManager UserRole = "ACADEMIC_MANAGER"
)

// IsValid checks if the role is valid
func (r UserRole) IsValid() bool {
	switch r {
	case RoleAdmin, RoleCashier, RoleReception, RoleTeacher, RoleManager, RoleAcademicManager:
		return true
	}
	return false
}

// UserStatus represents user status
type UserStatus string

const (
	StatusActive   UserStatus = "ACTIVE"
	StatusInactive UserStatus = "INACTIVE"
	StatusSuspended UserStatus = "SUSPENDED"
)

// IsValid checks if the status is valid
func (s UserStatus) IsValid() bool {
	switch s {
	case StatusActive, StatusInactive, StatusSuspended:
		return true
	}
	return false
}

// PaymentStatus represents payment status
type PaymentStatus string

const (
	PaymentPending   PaymentStatus = "PENDING"
	PaymentCompleted PaymentStatus = "COMPLETED"
	PaymentFailed    PaymentStatus = "FAILED"
	PaymentCancelled PaymentStatus = "CANCELLED"
	PaymentRefunded  PaymentStatus = "REFUNDED"
)

// IsValid checks if the payment status is valid
func (p PaymentStatus) IsValid() bool {
	switch p {
	case PaymentPending, PaymentCompleted, PaymentFailed, PaymentCancelled, PaymentRefunded:
		return true
	}
	return false
}

// PaymentMethod represents payment methods
type PaymentMethod string

const (
	PaymentCash   PaymentMethod = "CASH"
	PaymentCard   PaymentMethod = "CARD"
	PaymentUzCard PaymentMethod = "UZCARD"
	PaymentHumo   PaymentMethod = "HUMO"
	PaymentPayme  PaymentMethod = "PAYME"
	PaymentClick  PaymentMethod = "CLICK"
)

// IsValid checks if the payment method is valid
func (p PaymentMethod) IsValid() bool {
	switch p {
	case PaymentCash, PaymentCard, PaymentUzCard, PaymentHumo, PaymentPayme, PaymentClick:
		return true
	}
	return false
}

// TransactionStatus represents transaction status
type TransactionStatus string

const (
	TransactionPending   TransactionStatus = "PENDING"
	TransactionCompleted TransactionStatus = "COMPLETED"
	TransactionFailed    TransactionStatus = "FAILED"
	TransactionCancelled TransactionStatus = "CANCELLED"
)

// IsValid checks if the transaction status is valid
func (t TransactionStatus) IsValid() bool {
	switch t {
	case TransactionPending, TransactionCompleted, TransactionFailed, TransactionCancelled:
		return true
	}
	return false
}

// TransactionType represents transaction types
type TransactionType string

const (
	TransactionPayment TransactionType = "PAYMENT"
	TransactionRefund  TransactionType = "REFUND"
	TransactionFee     TransactionType = "FEE"
)

// IsValid checks if the transaction type is valid
func (t TransactionType) IsValid() bool {
	switch t {
	case TransactionPayment, TransactionRefund, TransactionFee:
		return true
	}
	return false
}

// LeadStatus represents lead status
type LeadStatus string

const (
	LeadNew        LeadStatus = "NEW"
	LeadContacted  LeadStatus = "CONTACTED"
	LeadQualified  LeadStatus = "QUALIFIED"
	LeadConverted  LeadStatus = "CONVERTED"
	LeadLost       LeadStatus = "LOST"
)

// IsValid checks if the lead status is valid
func (l LeadStatus) IsValid() bool {
	switch l {
	case LeadNew, LeadContacted, LeadQualified, LeadConverted, LeadLost:
		return true
	}
	return false
}

// StudentStatus represents student status
type StudentStatus string

const (
	StudentActive    StudentStatus = "ACTIVE"
	StudentInactive  StudentStatus = "INACTIVE"
	StudentGraduated StudentStatus = "GRADUATED"
	StudentDropped   StudentStatus = "DROPPED"
)

// IsValid checks if the student status is valid
func (s StudentStatus) IsValid() bool {
	switch s {
	case StudentActive, StudentInactive, StudentGraduated, StudentDropped:
		return true
	}
	return false
}

// CourseStatus represents course status
type CourseStatus string

const (
	CourseActive    CourseStatus = "ACTIVE"
	CourseInactive  CourseStatus = "INACTIVE"
	CourseCompleted CourseStatus = "COMPLETED"
	CourseCancelled CourseStatus = "CANCELLED"
)

// IsValid checks if the course status is valid
func (c CourseStatus) IsValid() bool {
	switch c {
	case CourseActive, CourseInactive, CourseCompleted, CourseCancelled:
		return true
	}
	return false
}

// NotificationType represents notification types
type NotificationType string

const (
	NotificationEmail NotificationType = "EMAIL"
	NotificationSMS   NotificationType = "SMS"
	NotificationInApp NotificationType = "IN_APP"
)

// IsValid checks if the notification type is valid
func (n NotificationType) IsValid() bool {
	switch n {
	case NotificationEmail, NotificationSMS, NotificationInApp:
		return true
	}
	return false
}

// NotificationStatus represents notification status
type NotificationStatus string

const (
	NotificationPending   NotificationStatus = "PENDING"
	NotificationSent      NotificationStatus = "SENT"
	NotificationDelivered NotificationStatus = "DELIVERED"
	NotificationFailed    NotificationStatus = "FAILED"
)

// IsValid checks if the notification status is valid
func (n NotificationStatus) IsValid() bool {
	switch n {
	case NotificationPending, NotificationSent, NotificationDelivered, NotificationFailed:
		return true
	}
	return false
}

// AuditAction represents audit actions
type AuditAction string

const (
	ActionCreate AuditAction = "CREATE"
	ActionRead   AuditAction = "READ"
	ActionUpdate AuditAction = "UPDATE"
	ActionDelete AuditAction = "DELETE"
	ActionLogin  AuditAction = "LOGIN"
	ActionLogout AuditAction = "LOGOUT"
)

// IsValid checks if the audit action is valid
func (a AuditAction) IsValid() bool {
	switch a {
	case ActionCreate, ActionRead, ActionUpdate, ActionDelete, ActionLogin, ActionLogout:
		return true
	}
	return false
}

// PaginationRequest represents pagination parameters
type PaginationRequest struct {
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// GetOffset calculates the offset for database queries
func (p *PaginationRequest) GetOffset() int {
	if p.Page <= 0 {
		p.Page = 1
	}
	return (p.Page - 1) * p.PageSize
}

// GetLimit returns the limit for database queries
func (p *PaginationRequest) GetLimit() int {
	if p.PageSize <= 0 {
		p.PageSize = 10
	}
	if p.PageSize > 100 {
		p.PageSize = 100
	}
	return p.PageSize
}

// PaginationResponse represents pagination metadata
type PaginationResponse struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// NewPaginationResponse creates a new pagination response
func NewPaginationResponse(page, pageSize int, total int64) *PaginationResponse {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	return &PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success    bool        `json:"success"`
	Message    string      `json:"message,omitempty"`
	Data       interface{} `json:"data,omitempty"`
	Error      string      `json:"error,omitempty"`
	Pagination *PaginationResponse `json:"pagination,omitempty"`
}

// SuccessResponse creates a success response
func SuccessResponse(data interface{}, message string) *APIResponse {
	return &APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// ErrorResponse creates an error response
func ErrorResponse(error string) *APIResponse {
	return &APIResponse{
		Success: false,
		Error:   error,
	}
}

// PaginatedResponse creates a paginated response
func PaginatedResponse(data interface{}, pagination *PaginationResponse, message string) *APIResponse {
	return &APIResponse{
		Success:    true,
		Message:    message,
		Data:       data,
		Pagination: pagination,
	}
}
