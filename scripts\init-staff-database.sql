-- Staff Service Database Initialization Script
-- Database: ep-falling-forest (Staff Service)
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- gen_random_uuid() function is already available in PostgreSQL 13+

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS grades CASCADE;
DROP TABLE IF EXISTS attendances CASCADE;
DROP TABLE IF EXISTS schedules CASCADE;
DROP TABLE IF EXISTS enrollments CASCADE;
DROP TABLE IF EXISTS courses CASCADE;
DROP TABLE IF EXISTS teachers CASCADE;
DROP TABLE IF EXISTS students CASCADE;
DROP TABLE IF EXISTS leads CASCADE;

-- Create leads table (independent table)
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'NEW',
    source VARCHAR(20) NOT NULL,
    interested_course VARCHAR(255),
    budget DECIMAL(10,2),
    notes TEXT,
    assigned_to_id UUID, -- References auth service users (no FK constraint)
    next_follow_up_at TIMESTAMPTZ,
    last_contacted_at TIMESTAMPTZ,
    converted_at TIMESTAMPTZ,
    converted_to_id UUID, -- Student ID if converted
    created_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create students table (independent table)
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id VARCHAR(50) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    enrollment_date DATE NOT NULL,
    graduation_date DATE,
    overall_grade DECIMAL(5,2),
    attendance_rate DECIMAL(5,2),
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_email VARCHAR(255),
    notes TEXT,
    created_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    converted_from_id UUID, -- Lead ID if converted from lead
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create teachers table (independent table)
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    teacher_id VARCHAR(20) UNIQUE NOT NULL,
    user_id UUID UNIQUE, -- Link to User table if teacher has system access (no FK constraint)
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    hire_date DATE NOT NULL,
    termination_date DATE,
    salary DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'USD',
    qualifications TEXT,
    specializations TEXT, -- JSON array of specialization areas
    experience INTEGER DEFAULT 0, -- Years of experience
    bio TEXT,
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_email VARCHAR(255),
    rating DECIMAL(3,2), -- 1-5 scale
    total_students INTEGER DEFAULT 0,
    total_courses INTEGER DEFAULT 0,
    notes TEXT,
    created_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create courses table (depends on teachers)
CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    course_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    level VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    credits INTEGER DEFAULT 0,
    duration INTEGER, -- in hours
    max_students INTEGER DEFAULT 30,
    current_students INTEGER DEFAULT 0,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    start_date DATE,
    end_date DATE,
    instructor_id UUID REFERENCES teachers(id), -- Foreign key to teachers
    prerequisites TEXT,
    objectives TEXT,
    materials TEXT,
    created_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create enrollments table (depends on students and courses)
CREATE TABLE enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    enrollment_date TIMESTAMPTZ NOT NULL,
    completion_date TIMESTAMPTZ,
    drop_date TIMESTAMPTZ,
    final_grade DECIMAL(5,2), -- 0-100 scale
    attendance_rate DECIMAL(5,2), -- 0-100 percentage
    total_fee DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- PENDING, PARTIAL, PAID, OVERDUE
    notes TEXT,
    enrolled_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create schedules table (depends on courses and teachers)
CREATE TABLE schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    day_of_week VARCHAR(20) NOT NULL, -- MONDAY, TUESDAY, etc.
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ NOT NULL,
    duration BIGINT NOT NULL, -- Duration in minutes
    room VARCHAR(100),
    building VARCHAR(100),
    location VARCHAR(200),
    max_capacity INTEGER DEFAULT 30,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    is_recurring BOOLEAN DEFAULT TRUE,
    recurrence_end TIMESTAMPTZ,
    specific_date TIMESTAMPTZ,
    notes TEXT,
    requirements TEXT,
    created_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create attendances table (depends on students, courses, and schedules)
CREATE TABLE attendances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    schedule_id UUID NOT NULL REFERENCES schedules(id) ON DELETE CASCADE,
    attendance_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PRESENT', -- PRESENT, ABSENT, LATE, EXCUSED
    check_in_time TIMESTAMPTZ,
    check_out_time TIMESTAMPTZ,
    notes TEXT,
    recorded_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create grades table (depends on students and courses)
CREATE TABLE grades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    assessment_type VARCHAR(50) NOT NULL, -- ASSIGNMENT, EXAM, QUIZ, PROJECT, etc.
    assessment_name VARCHAR(200) NOT NULL,
    score DECIMAL(5,2) NOT NULL, -- 0-100 scale
    max_score DECIMAL(5,2) NOT NULL DEFAULT 100,
    weight DECIMAL(5,2) DEFAULT 1.0, -- Weight in final grade calculation
    assessment_date DATE NOT NULL,
    due_date DATE,
    submitted_at TIMESTAMPTZ,
    graded_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    comments TEXT,
    private_notes TEXT, -- Internal notes, not visible to student
    graded_by_id UUID NOT NULL, -- References auth service users (no FK constraint)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for leads table
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_phone ON leads(phone);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_source ON leads(source);
CREATE INDEX idx_leads_assigned_to_id ON leads(assigned_to_id);
CREATE INDEX idx_leads_next_follow_up_at ON leads(next_follow_up_at);
CREATE INDEX idx_leads_created_at ON leads(created_at);
CREATE INDEX idx_leads_deleted_at ON leads(deleted_at);

-- Create indexes for students table
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_students_email ON students(email);
CREATE INDEX idx_students_phone ON students(phone);
CREATE INDEX idx_students_status ON students(status);
CREATE INDEX idx_students_enrollment_date ON students(enrollment_date);
CREATE INDEX idx_students_created_at ON students(created_at);
CREATE INDEX idx_students_deleted_at ON students(deleted_at);

-- Create indexes for teachers table
CREATE INDEX idx_teachers_teacher_id ON teachers(teacher_id);
CREATE INDEX idx_teachers_user_id ON teachers(user_id);
CREATE INDEX idx_teachers_email ON teachers(email);
CREATE INDEX idx_teachers_phone ON teachers(phone);
CREATE INDEX idx_teachers_status ON teachers(status);
CREATE INDEX idx_teachers_hire_date ON teachers(hire_date);
CREATE INDEX idx_teachers_created_at ON teachers(created_at);
CREATE INDEX idx_teachers_deleted_at ON teachers(deleted_at);

-- Create indexes for courses table
CREATE INDEX idx_courses_course_code ON courses(course_code);
CREATE INDEX idx_courses_level ON courses(level);
CREATE INDEX idx_courses_status ON courses(status);
CREATE INDEX idx_courses_instructor_id ON courses(instructor_id);
CREATE INDEX idx_courses_start_date ON courses(start_date);
CREATE INDEX idx_courses_created_at ON courses(created_at);
CREATE INDEX idx_courses_deleted_at ON courses(deleted_at);

-- Create indexes for enrollments table
CREATE INDEX idx_enrollments_student_id ON enrollments(student_id);
CREATE INDEX idx_enrollments_course_id ON enrollments(course_id);
CREATE INDEX idx_enrollments_status ON enrollments(status);
CREATE INDEX idx_enrollments_enrollment_date ON enrollments(enrollment_date);
CREATE INDEX idx_enrollments_payment_status ON enrollments(payment_status);
CREATE INDEX idx_enrollments_created_at ON enrollments(created_at);
CREATE INDEX idx_enrollments_deleted_at ON enrollments(deleted_at);

-- Create indexes for schedules table
CREATE INDEX idx_schedules_course_id ON schedules(course_id);
CREATE INDEX idx_schedules_teacher_id ON schedules(teacher_id);
CREATE INDEX idx_schedules_day_of_week ON schedules(day_of_week);
CREATE INDEX idx_schedules_start_time ON schedules(start_time);
CREATE INDEX idx_schedules_status ON schedules(status);
CREATE INDEX idx_schedules_specific_date ON schedules(specific_date);
CREATE INDEX idx_schedules_created_at ON schedules(created_at);
CREATE INDEX idx_schedules_deleted_at ON schedules(deleted_at);

-- Create indexes for attendances table
CREATE INDEX idx_attendances_student_id ON attendances(student_id);
CREATE INDEX idx_attendances_course_id ON attendances(course_id);
CREATE INDEX idx_attendances_schedule_id ON attendances(schedule_id);
CREATE INDEX idx_attendances_attendance_date ON attendances(attendance_date);
CREATE INDEX idx_attendances_status ON attendances(status);
CREATE INDEX idx_attendances_created_at ON attendances(created_at);
CREATE INDEX idx_attendances_deleted_at ON attendances(deleted_at);

-- Create indexes for grades table
CREATE INDEX idx_grades_student_id ON grades(student_id);
CREATE INDEX idx_grades_course_id ON grades(course_id);
CREATE INDEX idx_grades_assessment_type ON grades(assessment_type);
CREATE INDEX idx_grades_assessment_date ON grades(assessment_date);
CREATE INDEX idx_grades_graded_at ON grades(graded_at);
CREATE INDEX idx_grades_created_at ON grades(created_at);
CREATE INDEX idx_grades_deleted_at ON grades(deleted_at);

-- Create composite indexes for common queries
CREATE INDEX idx_enrollments_student_course ON enrollments(student_id, course_id);
CREATE INDEX idx_attendances_student_date ON attendances(student_id, attendance_date);
CREATE INDEX idx_grades_student_course ON grades(student_id, course_id);
CREATE INDEX idx_schedules_course_day_time ON schedules(course_id, day_of_week, start_time);

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_leads_updated_at
    BEFORE UPDATE ON leads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at
    BEFORE UPDATE ON students
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teachers_updated_at
    BEFORE UPDATE ON teachers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courses_updated_at
    BEFORE UPDATE ON courses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_enrollments_updated_at
    BEFORE UPDATE ON enrollments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedules_updated_at
    BEFORE UPDATE ON schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendances_updated_at
    BEFORE UPDATE ON attendances
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_grades_updated_at
    BEFORE UPDATE ON grades
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add check constraints
ALTER TABLE leads ADD CONSTRAINT chk_leads_status
    CHECK (status IN ('NEW', 'CONTACTED', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'CLOSED_WON', 'CLOSED_LOST'));

ALTER TABLE leads ADD CONSTRAINT chk_leads_source
    CHECK (source IN ('WEBSITE', 'REFERRAL', 'SOCIAL_MEDIA', 'EMAIL', 'PHONE', 'WALK_IN', 'ADVERTISEMENT', 'OTHER'));

ALTER TABLE students ADD CONSTRAINT chk_students_status
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'GRADUATED', 'DROPPED', 'SUSPENDED'));

ALTER TABLE teachers ADD CONSTRAINT chk_teachers_status
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'TERMINATED', 'ON_LEAVE'));

ALTER TABLE courses ADD CONSTRAINT chk_courses_level
    CHECK (level IN ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'));

ALTER TABLE courses ADD CONSTRAINT chk_courses_status
    CHECK (status IN ('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED'));

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_status
    CHECK (status IN ('PENDING', 'ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED'));

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_payment_status
    CHECK (payment_status IN ('PENDING', 'PARTIAL', 'PAID', 'OVERDUE', 'REFUNDED'));

ALTER TABLE schedules ADD CONSTRAINT chk_schedules_day_of_week
    CHECK (day_of_week IN ('MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'));

ALTER TABLE schedules ADD CONSTRAINT chk_schedules_status
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'CANCELLED'));

ALTER TABLE schedules ADD CONSTRAINT chk_schedules_times
    CHECK (end_time > start_time);

ALTER TABLE attendances ADD CONSTRAINT chk_attendances_status
    CHECK (status IN ('PRESENT', 'ABSENT', 'LATE', 'EXCUSED'));

ALTER TABLE grades ADD CONSTRAINT chk_grades_assessment_type
    CHECK (assessment_type IN ('ASSIGNMENT', 'EXAM', 'QUIZ', 'PROJECT', 'PRESENTATION', 'PARTICIPATION', 'FINAL'));

ALTER TABLE grades ADD CONSTRAINT chk_grades_score
    CHECK (score >= 0 AND score <= max_score);

-- Add unique constraints
ALTER TABLE enrollments ADD CONSTRAINT uk_enrollments_student_course
    UNIQUE (student_id, course_id);

ALTER TABLE attendances ADD CONSTRAINT uk_attendances_student_schedule_date
    UNIQUE (student_id, schedule_id, attendance_date);

-- Generate sample student IDs function
CREATE OR REPLACE FUNCTION generate_student_id() RETURNS VARCHAR(50) AS $$
DECLARE
    new_id VARCHAR(50);
    counter INTEGER := 1;
BEGIN
    LOOP
        new_id := 'STU' || LPAD(counter::TEXT, 6, '0');
        EXIT WHEN NOT EXISTS (SELECT 1 FROM students WHERE student_id = new_id);
        counter := counter + 1;
    END LOOP;
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Generate sample teacher IDs function
CREATE OR REPLACE FUNCTION generate_teacher_id() RETURNS VARCHAR(20) AS $$
DECLARE
    new_id VARCHAR(20);
    counter INTEGER := 1;
BEGIN
    LOOP
        new_id := 'TCH' || LPAD(counter::TEXT, 4, '0');
        EXIT WHEN NOT EXISTS (SELECT 1 FROM teachers WHERE teacher_id = new_id);
        counter := counter + 1;
    END LOOP;
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

COMMIT;
