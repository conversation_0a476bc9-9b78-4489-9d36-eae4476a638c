package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check requests
type HealthHandler struct {
	startTime time.Time
}

// NewHealthHandler creates a new health handler
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{
		startTime: time.Now(),
	}
}

// HealthCheck returns basic health status
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   "notification-service",
		"version":   "1.0.0",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"uptime":    time.Since(h.startTime).String(),
	})
}

// ReadinessCheck checks if the service is ready to serve requests
func (h *HealthHandler) ReadinessCheck(c *gin.Context) {
	// TODO: Add checks for database connectivity, external services, etc.
	c.JSON(http.StatusOK, gin.H{
		"status":    "ready",
		"service":   "notification-service",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"checks": gin.H{
			"database": "ok",
			"email":    "ok",
			"sms":      "ok",
		},
	})
}

// LivenessCheck checks if the service is alive
func (h *HealthHandler) LivenessCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "alive",
		"service":   "notification-service",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"uptime":    time.Since(h.startTime).String(),
	})
}
