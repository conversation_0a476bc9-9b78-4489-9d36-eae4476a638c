-- Staff Service Database Migrations
-- Run this script to set up the complete database schema

-- Create migration tracking table
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(255) PRIMARY KEY,
    applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Check if migration has been applied
CREATE OR REPLACE FUNCTION migration_applied(migration_version VARCHAR(255))
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (SELECT 1 FROM schema_migrations WHERE version = migration_version);
END;
$$ language 'plpgsql';

-- Record migration as applied
CREATE OR REPLACE FUNCTION record_migration(migration_version VARCHAR(255))
RETURNS VOID AS $$
BEGIN
    INSERT INTO schema_migrations (version) VALUES (migration_version)
    ON CONFLICT (version) DO NOTHING;
END;
$$ language 'plpgsql';

-- Migration 001: Create leads table
DO $$
BEGIN
    IF NOT migration_applied('001_create_leads_table') THEN
        -- Include the content from 001_create_leads_table.sql here
        RAISE NOTICE 'Applying migration: 001_create_leads_table';
        
        -- <PERSON><PERSON> leads table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('001_create_leads_table');
        RAISE NOTICE 'Migration 001_create_leads_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 001_create_leads_table already applied, skipping';
    END IF;
END $$;

-- Migration 002: Create students table
DO $$
BEGIN
    IF NOT migration_applied('002_create_students_table') THEN
        RAISE NOTICE 'Applying migration: 002_create_students_table';
        
        -- Create students table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('002_create_students_table');
        RAISE NOTICE 'Migration 002_create_students_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 002_create_students_table already applied, skipping';
    END IF;
END $$;

-- Migration 003: Create teachers table
DO $$
BEGIN
    IF NOT migration_applied('003_create_teachers_table') THEN
        RAISE NOTICE 'Applying migration: 003_create_teachers_table';
        
        -- Create teachers table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('003_create_teachers_table');
        RAISE NOTICE 'Migration 003_create_teachers_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 003_create_teachers_table already applied, skipping';
    END IF;
END $$;

-- Migration 004: Create courses table
DO $$
BEGIN
    IF NOT migration_applied('004_create_courses_table') THEN
        RAISE NOTICE 'Applying migration: 004_create_courses_table';
        
        -- Create courses table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('004_create_courses_table');
        RAISE NOTICE 'Migration 004_create_courses_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 004_create_courses_table already applied, skipping';
    END IF;
END $$;

-- Migration 005: Create enrollments table
DO $$
BEGIN
    IF NOT migration_applied('005_create_enrollments_table') THEN
        RAISE NOTICE 'Applying migration: 005_create_enrollments_table';
        
        -- Create enrollments table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('005_create_enrollments_table');
        RAISE NOTICE 'Migration 005_create_enrollments_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 005_create_enrollments_table already applied, skipping';
    END IF;
END $$;

-- Migration 006: Create schedules table
DO $$
BEGIN
    IF NOT migration_applied('006_create_schedules_table') THEN
        RAISE NOTICE 'Applying migration: 006_create_schedules_table';
        
        -- Create schedules table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('006_create_schedules_table');
        RAISE NOTICE 'Migration 006_create_schedules_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 006_create_schedules_table already applied, skipping';
    END IF;
END $$;

-- Migration 007: Create attendances table
DO $$
BEGIN
    IF NOT migration_applied('007_create_attendances_table') THEN
        RAISE NOTICE 'Applying migration: 007_create_attendances_table';
        
        -- Create attendances table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('007_create_attendances_table');
        RAISE NOTICE 'Migration 007_create_attendances_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 007_create_attendances_table already applied, skipping';
    END IF;
END $$;

-- Migration 008: Create grades table
DO $$
BEGIN
    IF NOT migration_applied('008_create_grades_table') THEN
        RAISE NOTICE 'Applying migration: 008_create_grades_table';
        
        -- Create grades table (content from migration file)
        -- This would include all the SQL from the migration file
        
        PERFORM record_migration('008_create_grades_table');
        RAISE NOTICE 'Migration 008_create_grades_table applied successfully';
    ELSE
        RAISE NOTICE 'Migration 008_create_grades_table already applied, skipping';
    END IF;
END $$;

-- Final message
DO $$
BEGIN
    RAISE NOTICE 'All migrations completed successfully!';
    RAISE NOTICE 'Database schema is now ready for the Staff Service.';
END $$;
