package router

import (
	"staff-service/internal/handler"
	"staff-service/internal/middleware"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Router holds all the handlers
type Router struct {
	leadHandler       *handler.LeadHandler
	studentHandler    *handler.StudentHandler
	courseHandler     *handler.CourseHandler
	teacherHandler    *handler.TeacherHandler
	enrollmentHandler *handler.EnrollmentHandler
}

// NewRouter creates a new router instance
func NewRouter(
	leadHandler *handler.LeadHandler,
	studentHandler *handler.StudentHandler,
	courseHandler *handler.CourseHandler,
	teacherHandler *handler.TeacherHandler,
	enrollmentHandler *handler.EnrollmentHandler,
) *Router {
	return &Router{
		leadHandler:       leadHandler,
		studentHandler:    studentHandler,
		courseHandler:     courseHandler,
		teacherHandler:    teacherHandler,
		enrollmentHandler: enrollmentHandler,
	}
}

// SetupRoutes configures all the routes
func (r *Router) SetupRoutes() *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "staff-service",
		})
	})

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Apply authentication middleware to all v1 routes
		v1.Use(middleware.AuthMiddleware())

		// Lead routes
		leads := v1.Group("/leads")
		{
			leads.GET("", r.leadHandler.GetLeads)
			leads.POST("", r.leadHandler.CreateLead)
			leads.GET("/stats", r.leadHandler.GetLeadStats)
			leads.GET("/follow-ups-due", r.leadHandler.GetFollowUpsDue)
			leads.POST("/bulk-update-status", r.leadHandler.BulkUpdateLeadStatus)
			leads.GET("/assigned/:user_id", r.leadHandler.GetLeadsByAssignedUser)
			leads.GET("/:id", r.leadHandler.GetLead)
			leads.PUT("/:id", r.leadHandler.UpdateLead)
			leads.DELETE("/:id", r.leadHandler.DeleteLead)
			leads.POST("/:id/assign", r.leadHandler.AssignLead)
			leads.POST("/:id/convert", r.leadHandler.ConvertLead)
			leads.POST("/:id/follow-up", r.leadHandler.UpdateFollowUp)
		}

		// Student routes
		students := v1.Group("/students")
		{
			students.GET("", r.studentHandler.GetStudents)
			students.POST("", r.studentHandler.CreateStudent)
			students.GET("/stats", r.studentHandler.GetStudentStats)
			students.GET("/active", r.studentHandler.GetActiveStudents)
			students.GET("/by-student-id/:student_id", r.studentHandler.GetStudentByStudentID)
			students.GET("/by-course/:course_id", r.studentHandler.GetStudentsByCourse)
			students.GET("/:id", r.studentHandler.GetStudent)
			students.PUT("/:id", r.studentHandler.UpdateStudent)
			students.DELETE("/:id", r.studentHandler.DeleteStudent)
			students.GET("/:id/progress", r.studentHandler.GetStudentProgress)
			students.POST("/:id/suspend", r.studentHandler.SuspendStudent)
			students.POST("/:id/reactivate", r.studentHandler.ReactivateStudent)
			students.POST("/:id/graduate", r.studentHandler.GraduateStudent)
			students.POST("/:id/update-grades", r.studentHandler.UpdateStudentGrades)
		}

		// Course routes
		courses := v1.Group("/courses")
		{
			courses.GET("", r.courseHandler.GetCourses)
			courses.POST("", r.courseHandler.CreateCourse)
			courses.GET("/stats", r.courseHandler.GetCourseStats)
			courses.GET("/available", r.courseHandler.GetAvailableCourses)
			courses.GET("/popular", r.courseHandler.GetPopularCourses)
			courses.GET("/upcoming", r.courseHandler.GetUpcomingCourses)
			courses.GET("/validate-code", r.courseHandler.ValidateCourseCode)
			courses.GET("/by-code/:course_code", r.courseHandler.GetCourseByCourseCode)
			courses.GET("/by-instructor/:instructor_id", r.courseHandler.GetCoursesByInstructor)
			courses.GET("/:id", r.courseHandler.GetCourse)
			courses.PUT("/:id", r.courseHandler.UpdateCourse)
			courses.DELETE("/:id", r.courseHandler.DeleteCourse)
			courses.POST("/:id/assign-instructor", r.courseHandler.AssignInstructor)
			courses.PUT("/:id/status", r.courseHandler.UpdateCourseStatus)
		}

		// Teacher routes
		teachers := v1.Group("/teachers")
		{
			teachers.GET("", r.teacherHandler.GetTeachers)
			teachers.POST("", r.teacherHandler.CreateTeacher)
			teachers.GET("/stats", r.teacherHandler.GetTeacherStats)
			teachers.GET("/available", r.teacherHandler.GetAvailableTeachers)
			teachers.GET("/top-rated", r.teacherHandler.GetTopRatedTeachers)
			teachers.GET("/by-teacher-id/:teacher_id", r.teacherHandler.GetTeacherByTeacherID)
			teachers.GET("/by-user-id/:user_id", r.teacherHandler.GetTeacherByUserID)
			teachers.GET("/:id", r.teacherHandler.GetTeacher)
			teachers.PUT("/:id", r.teacherHandler.UpdateTeacher)
			teachers.DELETE("/:id", r.teacherHandler.DeleteTeacher)
			teachers.GET("/:id/workload", r.teacherHandler.GetTeacherWorkload)
			teachers.POST("/:id/suspend", r.teacherHandler.SuspendTeacher)
			teachers.POST("/:id/reactivate", r.teacherHandler.ReactivateTeacher)
			teachers.PUT("/:id/rating", r.teacherHandler.UpdateTeacherRating)
			teachers.POST("/:id/update-metrics", r.teacherHandler.UpdateTeacherPerformanceMetrics)
		}

		// Enrollment routes
		enrollments := v1.Group("/enrollments")
		{
			enrollments.GET("", r.enrollmentHandler.GetEnrollments)
			enrollments.POST("", r.enrollmentHandler.CreateEnrollment)
			enrollments.GET("/stats", r.enrollmentHandler.GetEnrollmentStats)
			enrollments.GET("/active", r.enrollmentHandler.GetActiveEnrollments)
			enrollments.GET("/overdue-payments", r.enrollmentHandler.GetOverduePayments)
			enrollments.GET("/revenue", r.enrollmentHandler.GetRevenueByPeriod)
			enrollments.POST("/bulk-update-payment", r.enrollmentHandler.BulkUpdatePaymentStatus)
			enrollments.GET("/by-student/:student_id", r.enrollmentHandler.GetEnrollmentsByStudent)
			enrollments.GET("/by-course/:course_id", r.enrollmentHandler.GetEnrollmentsByCourse)
			enrollments.GET("/:id", r.enrollmentHandler.GetEnrollment)
			enrollments.PUT("/:id", r.enrollmentHandler.UpdateEnrollment)
			enrollments.DELETE("/:id", r.enrollmentHandler.DeleteEnrollment)
			enrollments.PUT("/:id/payment", r.enrollmentHandler.UpdatePaymentStatus)
			enrollments.POST("/:id/complete", r.enrollmentHandler.CompleteEnrollment)
			enrollments.POST("/:id/drop", r.enrollmentHandler.DropEnrollment)
		}
	}

	return router
}
