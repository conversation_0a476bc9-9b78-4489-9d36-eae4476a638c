-- <PERSON>reate function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON><PERSON> triggers to automatically update updated_at for all tables
CREATE TRIGGER update_payments_updated_at 
    BEFORE UPDATE ON payments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at 
    BEFORE UPDATE ON payment_methods 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_refunds_updated_at 
    BEFORE UPDATE ON refunds 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_reports_updated_at 
    BEFORE UPDATE ON financial_reports 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reconciliation_records_updated_at 
    BEFORE UPDATE ON reconciliation_records 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to generate invoice numbers
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    year_month TEXT;
    sequence_num INTEGER;
    invoice_num TEXT;
BEGIN
    -- Get current year and month
    year_month := TO_CHAR(NOW(), 'YYYYMM');
    
    -- Get the next sequence number for this month
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 8) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM payments
    WHERE invoice_number LIKE 'INV' || year_month || '%';
    
    -- Format the invoice number
    invoice_num := 'INV' || year_month || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN invoice_num;
END;
$$ language 'plpgsql';

-- Create function to automatically set invoice number if not provided
CREATE OR REPLACE FUNCTION set_invoice_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := generate_invoice_number();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically set invoice number
CREATE TRIGGER set_payment_invoice_number 
    BEFORE INSERT ON payments 
    FOR EACH ROW 
    EXECUTE FUNCTION set_invoice_number();

-- Create function to update payment status based on transactions
CREATE OR REPLACE FUNCTION update_payment_status()
RETURNS TRIGGER AS $$
DECLARE
    payment_record payments%ROWTYPE;
    completed_count INTEGER;
    failed_count INTEGER;
    total_count INTEGER;
BEGIN
    -- Get the payment record
    SELECT * INTO payment_record FROM payments WHERE id = NEW.payment_id;
    
    -- Count transaction statuses for this payment
    SELECT 
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END),
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END),
        COUNT(*)
    INTO completed_count, failed_count, total_count
    FROM transactions 
    WHERE payment_id = NEW.payment_id AND deleted_at IS NULL;
    
    -- Update payment status based on transaction results
    IF completed_count > 0 THEN
        UPDATE payments SET status = 'COMPLETED', payment_date = NOW() 
        WHERE id = NEW.payment_id AND status != 'COMPLETED';
    ELSIF failed_count = total_count AND total_count > 0 THEN
        UPDATE payments SET status = 'FAILED' 
        WHERE id = NEW.payment_id AND status != 'FAILED';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to update payment status when transactions change
CREATE TRIGGER update_payment_status_on_transaction_change 
    AFTER INSERT OR UPDATE ON transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_payment_status();

-- Create function to validate refund amount
CREATE OR REPLACE FUNCTION validate_refund_amount()
RETURNS TRIGGER AS $$
DECLARE
    payment_amount DECIMAL(10,2);
    total_refunded DECIMAL(10,2);
BEGIN
    -- Get the original payment amount
    SELECT amount INTO payment_amount FROM payments WHERE id = NEW.payment_id;
    
    -- Calculate total already refunded (excluding current refund if updating)
    SELECT COALESCE(SUM(amount), 0) INTO total_refunded
    FROM refunds 
    WHERE payment_id = NEW.payment_id 
    AND status = 'COMPLETED' 
    AND deleted_at IS NULL
    AND (TG_OP = 'INSERT' OR id != NEW.id);
    
    -- Check if refund amount would exceed payment amount
    IF (total_refunded + NEW.amount) > payment_amount THEN
        RAISE EXCEPTION 'Refund amount (%) would exceed remaining refundable amount (%)', 
            NEW.amount, (payment_amount - total_refunded);
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to validate refund amounts
CREATE TRIGGER validate_refund_amount_trigger 
    BEFORE INSERT OR UPDATE ON refunds 
    FOR EACH ROW 
    EXECUTE FUNCTION validate_refund_amount();
