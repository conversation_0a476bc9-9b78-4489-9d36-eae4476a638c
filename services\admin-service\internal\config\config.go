package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the admin service
type Config struct {
	Port        string
	Environment string
	DatabaseURL string
	JWTSecret   string
	
	// Redis configuration
	RedisURL string
	
	// Service URLs
	AuthServiceURL string
	
	// Pagination defaults
	DefaultPageSize int
	MaxPageSize     int
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	cfg := &Config{
		Port:        getEnv("PORT", "8082"),
		Environment: getEnv("ENVIRONMENT", "development"),
		DatabaseURL: getEnv("DATABASE_URL", "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"),
		JWTSecret:   getEnv("JWT_SECRET", "your-super-secret-jwt-key-change-this-in-production"),
		
		// Redis
		RedisURL: getEnv("REDIS_URL", "redis://localhost:6379"),
		
		// Service URLs
		AuthServiceURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8081"),
		
		// Pagination
		DefaultPageSize: getEnvAsInt("DEFAULT_PAGE_SIZE", 10),
		MaxPageSize:     getEnvAsInt("MAX_PAGE_SIZE", 100),
	}

	return cfg, nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return fallback
}
