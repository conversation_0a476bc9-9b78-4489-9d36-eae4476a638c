package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// DashboardAnalytics represents the main dashboard analytics
type DashboardAnalytics struct {
	TotalStudents    int64                `json:"total_students"`
	ActiveStudents   int64                `json:"active_students"`
	TotalRevenue     float64              `json:"total_revenue"`
	MonthlyRevenue   float64              `json:"monthly_revenue"`
	ConversionRate   float64              `json:"conversion_rate"`
	TeacherCount     int64                `json:"teacher_count"`
	GroupCount       int64                `json:"group_count"`
	RecentPayments   []*RecentPayment     `json:"recent_payments"`
	UserStats        *UserStats           `json:"user_stats"`
	RevenueChart     []*RevenueDataPoint  `json:"revenue_chart"`
	StudentChart     []*StudentDataPoint  `json:"student_chart"`
}

// UserStats represents user statistics
type UserStats struct {
	TotalUsers       int64                    `json:"total_users"`
	ActiveUsers      int64                    `json:"active_users"`
	InactiveUsers    int64                    `json:"inactive_users"`
	SuspendedUsers   int64                    `json:"suspended_users"`
	UsersByRole      map[models.UserRole]int64 `json:"users_by_role"`
	NewUsersToday    int64                    `json:"new_users_today"`
	NewUsersThisWeek int64                    `json:"new_users_this_week"`
	LastLoginStats   *LastLoginStats          `json:"last_login_stats"`
}

// LastLoginStats represents last login statistics
type LastLoginStats struct {
	LoggedInToday     int64 `json:"logged_in_today"`
	LoggedInThisWeek  int64 `json:"logged_in_this_week"`
	LoggedInThisMonth int64 `json:"logged_in_this_month"`
	NeverLoggedIn     int64 `json:"never_logged_in"`
}

// RecentPayment represents a recent payment for dashboard
type RecentPayment struct {
	ID          uuid.UUID             `json:"id"`
	StudentName string                `json:"student_name"`
	Amount      float64               `json:"amount"`
	Method      models.PaymentMethod  `json:"method"`
	Status      models.PaymentStatus  `json:"status"`
	CreatedAt   time.Time             `json:"created_at"`
}

// RevenueDataPoint represents a data point for revenue chart
type RevenueDataPoint struct {
	Date   time.Time `json:"date"`
	Amount float64   `json:"amount"`
	Count  int64     `json:"count"`
}

// StudentDataPoint represents a data point for student chart
type StudentDataPoint struct {
	Date   time.Time `json:"date"`
	Total  int64     `json:"total"`
	Active int64     `json:"active"`
	New    int64     `json:"new"`
}

// FinancialReport represents a financial report
type FinancialReport struct {
	StartDate        time.Time                    `json:"start_date"`
	EndDate          time.Time                    `json:"end_date"`
	TotalRevenue     float64                      `json:"total_revenue"`
	TotalPayments    int64                        `json:"total_payments"`
	AveragePayment   float64                      `json:"average_payment"`
	RevenueByMethod  map[models.PaymentMethod]float64 `json:"revenue_by_method"`
	PaymentsByMethod map[models.PaymentMethod]int64   `json:"payments_by_method"`
	RevenueByStatus  map[models.PaymentStatus]float64 `json:"revenue_by_status"`
	PaymentsByStatus map[models.PaymentStatus]int64   `json:"payments_by_status"`
	DailyRevenue     []*RevenueDataPoint          `json:"daily_revenue"`
	TopPayments      []*RecentPayment             `json:"top_payments"`
	RefundedAmount   float64                      `json:"refunded_amount"`
	PendingAmount    float64                      `json:"pending_amount"`
}

// UserAnalytics represents user analytics
type UserAnalytics struct {
	TotalUsers         int64                     `json:"total_users"`
	UsersByRole        map[models.UserRole]int64 `json:"users_by_role"`
	UsersByStatus      map[models.UserStatus]int64 `json:"users_by_status"`
	RegistrationTrend  []*UserRegistrationPoint  `json:"registration_trend"`
	LoginActivity      []*UserLoginPoint         `json:"login_activity"`
	MostActiveUsers    []*UserActivitySummary    `json:"most_active_users"`
	RecentRegistrations []*models.UserResponse   `json:"recent_registrations"`
}

// UserRegistrationPoint represents user registration data point
type UserRegistrationPoint struct {
	Date  time.Time `json:"date"`
	Count int64     `json:"count"`
}

// UserLoginPoint represents user login activity data point
type UserLoginPoint struct {
	Date        time.Time `json:"date"`
	UniqueUsers int64     `json:"unique_users"`
	TotalLogins int64     `json:"total_logins"`
}

// UserActivitySummary represents user activity summary
type UserActivitySummary struct {
	UserID      uuid.UUID `json:"user_id"`
	UserName    string    `json:"user_name"`
	UserEmail   string    `json:"user_email"`
	Role        models.UserRole `json:"role"`
	LoginCount  int64     `json:"login_count"`
	LastLoginAt *time.Time `json:"last_login_at"`
	ActivityScore float64 `json:"activity_score"`
}

// AnalyticsRequest represents a request for analytics
type AnalyticsRequest struct {
	StartDate *time.Time `json:"start_date" form:"start_date"`
	EndDate   *time.Time `json:"end_date" form:"end_date"`
	Branch    *string    `json:"branch" form:"branch"`
	GroupBy   *string    `json:"group_by" form:"group_by"` // day, week, month
}

// SystemMetrics represents system-wide metrics
type SystemMetrics struct {
	DatabaseStats   *DatabaseStats   `json:"database_stats"`
	ServiceStats    *ServiceStats    `json:"service_stats"`
	PerformanceStats *PerformanceStats `json:"performance_stats"`
}

// DatabaseStats represents database statistics
type DatabaseStats struct {
	TotalTables     int64   `json:"total_tables"`
	TotalRecords    int64   `json:"total_records"`
	DatabaseSize    string  `json:"database_size"`
	ActiveConnections int   `json:"active_connections"`
	SlowQueries     int64   `json:"slow_queries"`
}

// ServiceStats represents service statistics
type ServiceStats struct {
	Uptime          time.Duration `json:"uptime"`
	RequestCount    int64         `json:"request_count"`
	ErrorCount      int64         `json:"error_count"`
	AverageResponse time.Duration `json:"average_response"`
}

// PerformanceStats represents performance statistics
type PerformanceStats struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
}
