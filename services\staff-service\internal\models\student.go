package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// StudentStatus represents the status of a student
type StudentStatus string

const (
	StudentStatusActive    StudentStatus = "ACTIVE"
	StudentStatusInactive  StudentStatus = "INACTIVE"
	StudentStatusSuspended StudentStatus = "SUSPENDED"
	StudentStatusGraduated StudentStatus = "GRADUATED"
	StudentStatusDropped   StudentStatus = "DROPPED"
)

// IsValid checks if the student status is valid
func (ss StudentStatus) IsValid() bool {
	switch ss {
	case StudentStatusActive, StudentStatusInactive, StudentStatusSuspended,
		 StudentStatusGraduated, StudentStatusDropped:
		return true
	}
	return false
}

// Student represents a student in the system
type Student struct {
	models.BaseModel
	StudentID        string        `json:"student_id" gorm:"uniqueIndex;not null;size:20"` // Auto-generated student ID
	FirstName        string        `json:"first_name" gorm:"not null;size:100"`
	LastName         string        `json:"last_name" gorm:"not null;size:100"`
	Email            string        `json:"email" gorm:"not null;size:255;index"`
	Phone            string        `json:"phone" gorm:"size:20;index"`
	DateOfBirth      *time.Time    `json:"date_of_birth"`
	Address          string        `json:"address" gorm:"size:500"`
	City             string        `json:"city" gorm:"size:100"`
	Country          string        `json:"country" gorm:"size:100"`
	
	// Student specific fields
	Status           StudentStatus `json:"status" gorm:"not null;default:'ACTIVE';index"`
	EnrollmentDate   time.Time     `json:"enrollment_date" gorm:"not null;index"`
	GraduationDate   *time.Time    `json:"graduation_date"`
	
	// Emergency contact
	EmergencyContactName  string `json:"emergency_contact_name" gorm:"size:200"`
	EmergencyContactPhone string `json:"emergency_contact_phone" gorm:"size:20"`
	EmergencyContactEmail string `json:"emergency_contact_email" gorm:"size:255"`
	
	// Academic information
	OverallGrade     *float64 `json:"overall_grade"` // 0-100 scale
	AttendanceRate   *float64 `json:"attendance_rate"` // 0-100 percentage
	Notes            string   `json:"notes" gorm:"type:text"`
	
	// Relationships
	Enrollments      []Enrollment `json:"enrollments,omitempty" gorm:"foreignKey:StudentID"`
	Attendances      []Attendance `json:"attendances,omitempty" gorm:"foreignKey:StudentID"`
	Grades           []Grade      `json:"grades,omitempty" gorm:"foreignKey:StudentID"`
	
	// Metadata (no foreign key constraint - references auth service)
	CreatedByUserID  uuid.UUID    `json:"created_by_id" gorm:"column:created_by_id;not null"`
	CreatedBy        *models.User `json:"created_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
	ConvertedFromID  *uuid.UUID   `json:"converted_from_id"` // Lead ID if converted from lead
}

// GetFullName returns the full name of the student
func (s *Student) GetFullName() string {
	return s.FirstName + " " + s.LastName
}

// IsActive checks if the student is active
func (s *Student) IsActive() bool {
	return s.Status == StudentStatusActive
}

// CanEnroll checks if the student can enroll in new courses
func (s *Student) CanEnroll() bool {
	return s.Status == StudentStatusActive
}

// StudentCreateRequest represents a request to create a student
type StudentCreateRequest struct {
	FirstName             string     `json:"first_name" binding:"required,min=2,max=100"`
	LastName              string     `json:"last_name" binding:"required,min=2,max=100"`
	Email                 string     `json:"email" binding:"required,email,max=255"`
	Phone                 string     `json:"phone" binding:"max=20"`
	DateOfBirth           *time.Time `json:"date_of_birth"`
	Address               string     `json:"address" binding:"max=500"`
	City                  string     `json:"city" binding:"max=100"`
	Country               string     `json:"country" binding:"max=100"`
	EmergencyContactName  string     `json:"emergency_contact_name" binding:"max=200"`
	EmergencyContactPhone string     `json:"emergency_contact_phone" binding:"max=20"`
	EmergencyContactEmail string     `json:"emergency_contact_email" binding:"omitempty,email,max=255"`
	Notes                 string     `json:"notes"`
	ConvertedFromID       *uuid.UUID `json:"converted_from_id"` // Lead ID if converted
}

// StudentUpdateRequest represents a request to update a student
type StudentUpdateRequest struct {
	FirstName             *string        `json:"first_name" binding:"omitempty,min=2,max=100"`
	LastName              *string        `json:"last_name" binding:"omitempty,min=2,max=100"`
	Email                 *string        `json:"email" binding:"omitempty,email,max=255"`
	Phone                 *string        `json:"phone" binding:"omitempty,max=20"`
	DateOfBirth           *time.Time     `json:"date_of_birth"`
	Address               *string        `json:"address" binding:"omitempty,max=500"`
	City                  *string        `json:"city" binding:"omitempty,max=100"`
	Country               *string        `json:"country" binding:"omitempty,max=100"`
	Status                *StudentStatus `json:"status"`
	GraduationDate        *time.Time     `json:"graduation_date"`
	EmergencyContactName  *string        `json:"emergency_contact_name" binding:"omitempty,max=200"`
	EmergencyContactPhone *string        `json:"emergency_contact_phone" binding:"omitempty,max=20"`
	EmergencyContactEmail *string        `json:"emergency_contact_email" binding:"omitempty,email,max=255"`
	Notes                 *string        `json:"notes"`
}

// StudentResponse represents a student response
type StudentResponse struct {
	ID                    uuid.UUID     `json:"id"`
	StudentID             string        `json:"student_id"`
	FirstName             string        `json:"first_name"`
	LastName              string        `json:"last_name"`
	FullName              string        `json:"full_name"`
	Email                 string        `json:"email"`
	Phone                 string        `json:"phone"`
	DateOfBirth           *time.Time    `json:"date_of_birth"`
	Address               string        `json:"address"`
	City                  string        `json:"city"`
	Country               string        `json:"country"`
	Status                StudentStatus `json:"status"`
	EnrollmentDate        time.Time     `json:"enrollment_date"`
	GraduationDate        *time.Time    `json:"graduation_date"`
	EmergencyContactName  string        `json:"emergency_contact_name"`
	EmergencyContactPhone string        `json:"emergency_contact_phone"`
	EmergencyContactEmail string        `json:"emergency_contact_email"`
	OverallGrade          *float64      `json:"overall_grade"`
	AttendanceRate        *float64      `json:"attendance_rate"`
	Notes                 string        `json:"notes"`
	CreatedByID           uuid.UUID     `json:"created_by_id"`
	CreatedByName         string        `json:"created_by_name"`
	ConvertedFromID       *uuid.UUID    `json:"converted_from_id"`
	CreatedAt             time.Time     `json:"created_at"`
	UpdatedAt             time.Time     `json:"updated_at"`
}

// ToResponse converts a Student to StudentResponse
func (s *Student) ToResponse() *StudentResponse {
	response := &StudentResponse{
		ID:                    s.ID,
		StudentID:             s.StudentID,
		FirstName:             s.FirstName,
		LastName:              s.LastName,
		FullName:              s.GetFullName(),
		Email:                 s.Email,
		Phone:                 s.Phone,
		DateOfBirth:           s.DateOfBirth,
		Address:               s.Address,
		City:                  s.City,
		Country:               s.Country,
		Status:                s.Status,
		EnrollmentDate:        s.EnrollmentDate,
		GraduationDate:        s.GraduationDate,
		EmergencyContactName:  s.EmergencyContactName,
		EmergencyContactPhone: s.EmergencyContactPhone,
		EmergencyContactEmail: s.EmergencyContactEmail,
		OverallGrade:          s.OverallGrade,
		AttendanceRate:        s.AttendanceRate,
		Notes:                 s.Notes,
		CreatedByID:           s.CreatedByUserID,
		ConvertedFromID:       s.ConvertedFromID,
		CreatedAt:             s.CreatedAt,
		UpdatedAt:             s.UpdatedAt,
	}

	// Include created by user information if available
	if s.CreatedBy != nil {
		response.CreatedByName = s.CreatedBy.GetFullName()
	}

	return response
}

// StudentListRequest represents a request to list students
type StudentListRequest struct {
	models.PaginationRequest
	Status      *StudentStatus `json:"status" form:"status"`
	CreatedByID *uuid.UUID     `json:"created_by_id" form:"created_by_id"`
	Search      string         `json:"search" form:"search"`
	StartDate   *time.Time     `json:"start_date" form:"start_date"`
	EndDate     *time.Time     `json:"end_date" form:"end_date"`
	CourseID    *uuid.UUID     `json:"course_id" form:"course_id"` // Filter by enrolled course
}

// StudentListResponse represents a response for student list
type StudentListResponse struct {
	Students   []*StudentResponse         `json:"students"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// StudentProgress represents student academic progress
type StudentProgress struct {
	StudentID        uuid.UUID              `json:"student_id"`
	StudentName      string                 `json:"student_name"`
	OverallGrade     *float64               `json:"overall_grade"`
	AttendanceRate   *float64               `json:"attendance_rate"`
	EnrolledCourses  int                    `json:"enrolled_courses"`
	CompletedCourses int                    `json:"completed_courses"`
	CourseProgress   []*CourseProgress      `json:"course_progress"`
	RecentGrades     []*Grade               `json:"recent_grades"`
	AttendanceRecord []*AttendanceRecord    `json:"attendance_record"`
}

// CourseProgress represents progress in a specific course
type CourseProgress struct {
	CourseID       uuid.UUID `json:"course_id"`
	CourseName     string    `json:"course_name"`
	Grade          *float64  `json:"grade"`
	AttendanceRate *float64  `json:"attendance_rate"`
	Status         string    `json:"status"` // ENROLLED, COMPLETED, DROPPED
	EnrollmentDate time.Time `json:"enrollment_date"`
	CompletionDate *time.Time `json:"completion_date"`
}

// AttendanceRecord represents attendance record for analytics
type AttendanceRecord struct {
	Date       time.Time `json:"date"`
	CourseID   uuid.UUID `json:"course_id"`
	CourseName string    `json:"course_name"`
	Status     string    `json:"status"` // PRESENT, ABSENT, LATE
}

// StudentStats represents student statistics
type StudentStats struct {
	TotalStudents       int64                      `json:"total_students"`
	StudentsByStatus    map[StudentStatus]int64    `json:"students_by_status"`
	NewStudentsToday    int64                      `json:"new_students_today"`
	NewStudentsThisWeek int64                      `json:"new_students_this_week"`
	AverageGrade        *float64                   `json:"average_grade"`
	AverageAttendance   *float64                   `json:"average_attendance"`
}

// StudentSuspensionRequest represents a request to suspend a student
type StudentSuspensionRequest struct {
	SuspensionDate   *time.Time `json:"suspension_date"`
	SuspensionReason string     `json:"suspension_reason" binding:"required"`
	SuspensionEnd    *time.Time `json:"suspension_end"`
	Notes            string     `json:"notes"`
}
