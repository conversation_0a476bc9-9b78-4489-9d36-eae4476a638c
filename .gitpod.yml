image: gitpod/workspace-go

ports:
  - port: 8081
    onOpen: notify
    name: Auth Service
    description: Authentication Service API
  - port: 8082
    onOpen: notify
    name: Admin Service
    description: Admin Service API
  - port: 5432
    onOpen: ignore
    name: Auth Database
  - port: 5433
    onOpen: ignore
    name: Admin Database
  - port: 6379
    onOpen: ignore
    name: Redis

tasks:
  - name: Setup Environment
    init: |
      # Install Docker Compose
      sudo curl -L "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
      sudo chmod +x /usr/local/bin/docker-compose
      
      # Verify installations
      go version
      docker --version
      docker-compose --version
      
      echo "Environment setup complete!"
    command: |
      echo "Ready to test CRM Admin Service!"
      echo "Run: ./test-online.sh to start testing"

vscode:
  extensions:
    - golang.go
    - ms-vscode.vscode-json
    - ms-azuretools.vscode-docker
    - humao.rest-client
