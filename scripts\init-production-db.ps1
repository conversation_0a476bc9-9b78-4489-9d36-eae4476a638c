# Production Database Initialization Script
# This script initializes the production databases with default users

param(
    [string]$Service = "auth",
    [switch]$All = $false
)

# Database connection strings
$connections = @{
    "auth" = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
    "admin" = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
    "staff" = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
    "payment" = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
    "notification" = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
}

function Initialize-Database {
    param(
        [string]$ServiceName,
        [string]$ConnectionString
    )
    
    Write-Host "Initializing $ServiceName database..." -ForegroundColor Green
    
    try {
        # Check if psql is available
        $psqlPath = Get-Command psql -ErrorAction SilentlyContinue
        if (-not $psqlPath) {
            Write-Host "PostgreSQL client (psql) not found. Please install PostgreSQL client tools." -ForegroundColor Red
            return $false
        }
        
        # Run the initialization script
        $scriptPath = "scripts/initialize-production-database.sql"
        if (Test-Path $scriptPath) {
            Write-Host "Running initialization script for $ServiceName..." -ForegroundColor Yellow
            & psql $ConnectionString -f $scriptPath
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Successfully initialized $ServiceName database!" -ForegroundColor Green
                return $true
            } else {
                Write-Host "Failed to initialize $ServiceName database. Exit code: $LASTEXITCODE" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "Initialization script not found: $scriptPath" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Error initializing $ServiceName database: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-DatabaseConnection {
    param(
        [string]$ServiceName,
        [string]$ConnectionString
    )
    
    Write-Host "Testing connection to $ServiceName database..." -ForegroundColor Yellow
    
    try {
        $result = & psql $ConnectionString -c "SELECT 1;" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Connection to $ServiceName database successful!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Connection to $ServiceName database failed!" -ForegroundColor Red
            Write-Host $result -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Error connecting to $ServiceName database: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "=== Production Database Initialization ===" -ForegroundColor Cyan
Write-Host ""

if ($All) {
    Write-Host "Initializing all databases..." -ForegroundColor Cyan
    $success = $true
    
    foreach ($kvp in $connections.GetEnumerator()) {
        Write-Host ""
        Write-Host "--- $($kvp.Key.ToUpper()) SERVICE ---" -ForegroundColor Magenta
        
        if (-not (Test-DatabaseConnection -ServiceName $kvp.Key -ConnectionString $kvp.Value)) {
            $success = $false
            continue
        }
        
        if (-not (Initialize-Database -ServiceName $kvp.Key -ConnectionString $kvp.Value)) {
            $success = $false
        }
    }
    
    Write-Host ""
    if ($success) {
        Write-Host "=== All databases initialized successfully! ===" -ForegroundColor Green
    } else {
        Write-Host "=== Some databases failed to initialize ===" -ForegroundColor Red
    }
} else {
    if ($connections.ContainsKey($Service)) {
        Write-Host "Initializing $Service database..." -ForegroundColor Cyan
        Write-Host ""
        
        if (Test-DatabaseConnection -ServiceName $Service -ConnectionString $connections[$Service]) {
            if (Initialize-Database -ServiceName $Service -ConnectionString $connections[$Service]) {
                Write-Host ""
                Write-Host "=== $Service database initialized successfully! ===" -ForegroundColor Green
            } else {
                Write-Host ""
                Write-Host "=== Failed to initialize $Service database ===" -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host ""
            Write-Host "=== Cannot connect to $Service database ===" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "Unknown service: $Service" -ForegroundColor Red
        Write-Host "Available services: $($connections.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host ""
Write-Host "Default login credentials:" -ForegroundColor Cyan
Write-Host "  Email: <EMAIL>" -ForegroundColor White
Write-Host "  Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "Additional test users:" -ForegroundColor Cyan
Write-Host "  <EMAIL> / admin123 (RECEPTION)" -ForegroundColor White
Write-Host "  <EMAIL> / admin123 (TEACHER)" -ForegroundColor White
Write-Host "  <EMAIL> / admin123 (MANAGER)" -ForegroundColor White
