# Server Configuration
PORT=8085
ENVIRONMENT=development
LOG_LEVEL=info

# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Authentication
AUTH_SERVICE_URL=http://localhost:8081
JWT_SECRET=your-secret-key

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_FROM_NUMBER=+**********

# SendGrid Configuration (Alternative to SMTP)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM=<EMAIL>

# Notification Settings
MAX_RETRIES=3
RETRY_DELAY_SECONDS=60
BATCH_SIZE=100
QUEUE_PROCESS_INTERVAL=30
