package proxy

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/crm-microservices/api-gateway/internal/registry"
	"github.com/gin-gonic/gin"
)

// ServiceProxy handles proxying requests to downstream services
type ServiceProxy struct {
	registry *registry.ServiceRegistry
	client   *http.Client
}

// NewServiceProxy creates a new service proxy
func NewServiceProxy(registry *registry.ServiceRegistry) *ServiceProxy {
	return &ServiceProxy{
		registry: registry,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ProxyRequest proxies a request to a downstream service
func (sp *ServiceProxy) ProxyRequest(c *gin.Context, serviceName string, targetPath string) {
	// Get service information
	service, err := sp.registry.GetService(serviceName)
	if err != nil {
		// Log detailed error information for debugging
		allServices := sp.registry.GetAllServices()
		serviceNames := make([]string, 0, len(allServices))
		for name := range allServices {
			serviceNames = append(serviceNames, name)
		}

		fmt.Printf("ERROR: Service '%s' not available. Error: %v\n", serviceName, err)
		fmt.Printf("Available services: %v\n", serviceNames)

		for name, svc := range allServices {
			fmt.Printf("Service %s: URL=%s, Healthy=%v, LastCheck=%v\n",
				name, svc.URL, svc.Healthy, svc.LastCheck)
		}

		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Service '%s' is not available: %v", serviceName, err),
			"debug": gin.H{
				"available_services": serviceNames,
				"requested_service":  serviceName,
			},
		})
		return
	}

	fmt.Printf("INFO: Proxying request to service '%s' at URL: %s, Path: %s\n",
		serviceName, service.URL, targetPath)

	// Build target URL
	targetURL, err := sp.buildTargetURL(service.URL, targetPath, c.Request.URL.RawQuery)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to build target URL: %v", err),
		})
		return
	}

	// Create proxy request
	proxyReq, err := sp.createProxyRequest(c, targetURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to create proxy request: %v", err),
		})
		return
	}

	// Add tracing headers
	sp.addTracingHeaders(proxyReq, c)

	// Execute request
	resp, err := sp.client.Do(proxyReq)
	if err != nil {
		c.JSON(http.StatusBadGateway, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to proxy request to %s: %v", serviceName, err),
		})
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	sp.copyResponseHeaders(c, resp)

	// Set status code
	c.Status(resp.StatusCode)

	// Copy response body
	if _, err := io.Copy(c.Writer, resp.Body); err != nil {
		// Log error but don't return JSON as headers are already sent
		fmt.Printf("Failed to copy response body: %v\n", err)
	}
}

// buildTargetURL constructs the target URL for the downstream service
func (sp *ServiceProxy) buildTargetURL(serviceURL, targetPath, query string) (string, error) {
	baseURL, err := url.Parse(serviceURL)
	if err != nil {
		return "", fmt.Errorf("invalid service URL: %w", err)
	}

	// Clean and join paths
	targetPath = strings.TrimPrefix(targetPath, "/")
	baseURL.Path = strings.TrimSuffix(baseURL.Path, "/") + "/" + targetPath

	// Add query parameters
	if query != "" {
		baseURL.RawQuery = query
	}

	return baseURL.String(), nil
}

// createProxyRequest creates a new HTTP request for proxying
func (sp *ServiceProxy) createProxyRequest(c *gin.Context, targetURL string) (*http.Request, error) {
	// Read request body
	var body io.Reader
	if c.Request.Body != nil {
		bodyBytes, err := io.ReadAll(c.Request.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read request body: %w", err)
		}
		body = bytes.NewReader(bodyBytes)
	}

	// Create new request
	req, err := http.NewRequest(c.Request.Method, targetURL, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Copy headers (excluding hop-by-hop headers)
	sp.copyRequestHeaders(req, c.Request)

	return req, nil
}

// copyRequestHeaders copies headers from the original request to the proxy request
func (sp *ServiceProxy) copyRequestHeaders(proxyReq *http.Request, originalReq *http.Request) {
	// Headers that should not be forwarded
	hopByHopHeaders := map[string]bool{
		"Connection":          true,
		"Keep-Alive":          true,
		"Proxy-Authenticate":  true,
		"Proxy-Authorization": true,
		"Te":                  true,
		"Trailers":            true,
		"Transfer-Encoding":   true,
		"Upgrade":             true,
	}

	for name, values := range originalReq.Header {
		if !hopByHopHeaders[name] {
			for _, value := range values {
				proxyReq.Header.Add(name, value)
			}
		}
	}

	// Set Host header to target service
	if targetURL, err := url.Parse(proxyReq.URL.String()); err == nil {
		proxyReq.Host = targetURL.Host
	}
}

// copyResponseHeaders copies headers from the proxy response to the client response
func (sp *ServiceProxy) copyResponseHeaders(c *gin.Context, resp *http.Response) {
	// Headers that should not be forwarded
	hopByHopHeaders := map[string]bool{
		"Connection":          true,
		"Keep-Alive":          true,
		"Proxy-Authenticate":  true,
		"Proxy-Authorization": true,
		"Te":                  true,
		"Trailers":            true,
		"Transfer-Encoding":   true,
		"Upgrade":             true,
	}

	for name, values := range resp.Header {
		if !hopByHopHeaders[name] {
			for _, value := range values {
				c.Header(name, value)
			}
		}
	}
}

// addTracingHeaders adds tracing and correlation headers
func (sp *ServiceProxy) addTracingHeaders(proxyReq *http.Request, c *gin.Context) {
	// Add correlation ID if not present
	correlationID := c.GetHeader("X-Correlation-ID")
	if correlationID == "" {
		correlationID = generateCorrelationID()
	}
	proxyReq.Header.Set("X-Correlation-ID", correlationID)

	// Add request ID
	requestID := c.GetHeader("X-Request-ID")
	if requestID == "" {
		requestID = generateRequestID()
	}
	proxyReq.Header.Set("X-Request-ID", requestID)

	// Add forwarded headers
	proxyReq.Header.Set("X-Forwarded-For", c.ClientIP())
	proxyReq.Header.Set("X-Forwarded-Proto", "http") // or https based on original request
	proxyReq.Header.Set("X-Forwarded-Host", c.Request.Host)

	// Add API Gateway identifier
	proxyReq.Header.Set("X-Gateway", "crm-api-gateway")
	proxyReq.Header.Set("X-Gateway-Version", "1.0.0")
}

// generateCorrelationID generates a correlation ID for request tracing
func generateCorrelationID() string {
	// Simple implementation - in production, use proper UUID generation
	return fmt.Sprintf("corr-%d", time.Now().UnixNano())
}

// generateRequestID generates a request ID
func generateRequestID() string {
	// Simple implementation - in production, use proper UUID generation
	return fmt.Sprintf("req-%d", time.Now().UnixNano())
}

// ProxyWithRetry proxies a request with retry logic
func (sp *ServiceProxy) ProxyWithRetry(c *gin.Context, serviceName string, targetPath string, maxRetries int) {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry (exponential backoff could be implemented here)
			time.Sleep(time.Duration(attempt) * 100 * time.Millisecond)
		}

		// Check if service is still healthy before retry
		if !sp.registry.IsServiceHealthy(serviceName) {
			lastErr = fmt.Errorf("service '%s' is unhealthy", serviceName)
			continue
		}

		// Try to proxy the request
		sp.ProxyRequest(c, serviceName, targetPath)

		// If we reach here without error, the request was successful
		return
	}

	// All retries failed
	c.JSON(http.StatusServiceUnavailable, gin.H{
		"success": false,
		"error":   fmt.Sprintf("Service '%s' failed after %d retries: %v", serviceName, maxRetries, lastErr),
	})
}

// GetProxyStats returns statistics about proxy operations
func (sp *ServiceProxy) GetProxyStats() map[string]interface{} {
	return map[string]interface{}{
		"client_timeout": sp.client.Timeout.String(),
		"registry_stats": sp.registry.GetServiceStats(),
	}
}
