import { apiService } from './api';
import type {
  Course,
  CourseCreateRequest,
  CourseUpdateRequest,
  CourseFilters,
  CourseStats,
  PaginatedResponse
} from '@/types/course';

class CourseService {
  private readonly API_PREFIX = '/api/v1/staff/courses';

  // Get all courses with pagination and filters
  async getCourses(params?: {
    page?: number;
    limit?: number;
    filters?: CourseFilters;
  }): Promise<PaginatedResponse<Course>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.filters?.search) queryParams.append('search', params.filters.search);
    if (params?.filters?.status) queryParams.append('status', params.filters.status);
    if (params?.filters?.category) queryParams.append('category', params.filters.category);
    if (params?.filters?.instructor_id) queryParams.append('instructor_id', params.filters.instructor_id);
    if (params?.filters?.start_date_from) queryParams.append('start_date_from', params.filters.start_date_from);
    if (params?.filters?.start_date_to) queryParams.append('start_date_to', params.filters.start_date_to);

    const url = queryParams.toString() ? `${this.API_PREFIX}?${queryParams}` : this.API_PREFIX;
    return apiService.get<PaginatedResponse<Course>>(url);
  }

  // Get course by ID
  async getCourse(id: string): Promise<Course> {
    return apiService.get<Course>(`${this.API_PREFIX}/${id}`);
  }

  // Create new course
  async createCourse(data: CourseCreateRequest): Promise<Course> {
    return apiService.post<Course>(this.API_PREFIX, data);
  }

  // Update course
  async updateCourse(id: string, data: CourseUpdateRequest): Promise<Course> {
    return apiService.put<Course>(`${this.API_PREFIX}/${id}`, data);
  }

  // Delete course
  async deleteCourse(id: string): Promise<void> {
    return apiService.delete<void>(`${this.API_PREFIX}/${id}`);
  }

  // Get course statistics
  async getCourseStats(): Promise<CourseStats> {
    return apiService.get<CourseStats>(`${this.API_PREFIX}/stats`);
  }

  // Course status management
  async updateCourseStatus(id: string, status: string): Promise<Course> {
    return apiService.patch<Course>(`${this.API_PREFIX}/${id}/status`, { status });
  }

  // Course enrollment management
  async getCourseEnrollments(id: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/${id}/enrollments?${queryParams}` 
      : `${this.API_PREFIX}/${id}/enrollments`;
    
    return apiService.get<any>(url);
  }

  // Course schedule management
  async getCourseSchedule(id: string): Promise<any[]> {
    return apiService.get<any[]>(`${this.API_PREFIX}/${id}/schedule`);
  }

  async updateCourseSchedule(id: string, schedule: any[]): Promise<void> {
    return apiService.put<void>(`${this.API_PREFIX}/${id}/schedule`, { schedule });
  }

  // Course materials management
  async getCourseMaterials(id: string): Promise<any[]> {
    return apiService.get<any[]>(`${this.API_PREFIX}/${id}/materials`);
  }

  async addCourseMaterial(id: string, material: any): Promise<any> {
    return apiService.post<any>(`${this.API_PREFIX}/${id}/materials`, material);
  }

  async deleteCourseMaterial(courseId: string, materialId: string): Promise<void> {
    return apiService.delete<void>(`${this.API_PREFIX}/${courseId}/materials/${materialId}`);
  }

  // Course analytics
  async getCourseAnalytics(id: string, params?: {
    date_from?: string;
    date_to?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.date_from) queryParams.append('date_from', params.date_from);
    if (params?.date_to) queryParams.append('date_to', params.date_to);

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/${id}/analytics?${queryParams}` 
      : `${this.API_PREFIX}/${id}/analytics`;
    
    return apiService.get<any>(url);
  }

  // Bulk operations
  async bulkUpdateCourses(ids: string[], data: Partial<CourseUpdateRequest>): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/bulk-update`, {
      course_ids: ids,
      ...data
    });
  }

  async bulkDeleteCourses(ids: string[]): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/bulk-delete`, {
      course_ids: ids
    });
  }
}

// Create and export singleton instance
export const courseService = new CourseService();
export default courseService;
