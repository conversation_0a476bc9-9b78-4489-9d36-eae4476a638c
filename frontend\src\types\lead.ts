export interface Lead {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  source: LeadSource;
  status: LeadStatus;
  interested_course: string;
  notes?: string;
  assigned_to?: string;
  assigned_to_name?: string;
  follow_up_date?: string;
  conversion_date?: string;
  created_at: string;
  updated_at: string;
  last_contact_date?: string;
}

export type LeadSource = 
  | 'WEBSITE' 
  | 'SOCIAL_MEDIA' 
  | 'REFERRAL' 
  | 'ADVERTISEMENT' 
  | 'WALK_IN' 
  | 'PHONE_CALL' 
  | 'EMAIL' 
  | 'OTHER';

export type LeadStatus = 
  | 'NEW' 
  | 'CONTACTED' 
  | 'QUALIFIED' 
  | 'PROPOSAL_SENT' 
  | 'NEGOTIATION' 
  | 'CONVERTED' 
  | 'LOST' 
  | 'UNQUALIFIED';

export interface LeadCreateRequest {
  full_name: string;
  email: string;
  phone: string;
  source: LeadSource;
  interested_course: string;
  notes?: string;
  assigned_to?: string;
  follow_up_date?: string;
}

export interface LeadUpdateRequest {
  full_name?: string;
  email?: string;
  phone?: string;
  source?: LeadSource;
  status?: LeadStatus;
  interested_course?: string;
  notes?: string;
  assigned_to?: string;
  follow_up_date?: string;
}

export interface LeadFilters {
  search?: string;
  status?: LeadStatus;
  source?: LeadSource;
  assigned_to?: string;
  created_from?: string;
  created_to?: string;
  follow_up_overdue?: boolean;
}

export interface LeadStats {
  total_leads: number;
  new_leads: number;
  qualified_leads: number;
  converted_leads: number;
  lost_leads: number;
  conversion_rate: number;
  leads_by_source: Record<LeadSource, number>;
  leads_by_status: Record<LeadStatus, number>;
  average_conversion_time: number;
}

export interface LeadFollowUp {
  id: string;
  lead_id: string;
  notes: string;
  follow_up_type: string;
  next_follow_up?: string;
  created_at: string;
  created_by: string;
  created_by_name: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
