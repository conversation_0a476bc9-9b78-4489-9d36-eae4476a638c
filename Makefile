# Go Microservices CRM - Makefile

# Variables
SERVICES := api-gateway auth-service admin-service staff-service payment-service notification-service
GO_VERSION := 1.21
DOCKER_COMPOSE := docker-compose
DOCKER_COMPOSE_DEV := docker-compose -f docker-compose.yml
DOCKER_COMPOSE_PROD := docker-compose -f docker-compose.prod.yml

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

.PHONY: help build test clean dev prod stop logs migrate seed lint fmt docs

# Default target
help: ## Show this help message
	@echo "$(BLUE)Go Microservices CRM - Available Commands$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

# Build targets
build: ## Build all services
	@echo "$(BLUE)Building all services...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Building $$service...$(NC)"; \
		cd services/$$service && go build -o bin/$$service main.go && cd ../..; \
	done
	@echo "$(GREEN)All services built successfully!$(NC)"

build-%: ## Build specific service (e.g., make build-auth-service)
	@echo "$(BLUE)Building $*...$(NC)"
	@cd services/$* && go build -o bin/$* main.go
	@echo "$(GREEN)$* built successfully!$(NC)"

# Test targets
test: ## Run tests for all services
	@echo "$(BLUE)Running tests for all services...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Testing $$service...$(NC)"; \
		cd services/$$service && go test ./... && cd ../..; \
	done
	@echo "$(GREEN)All tests passed!$(NC)"

test-%: ## Run tests for specific service (e.g., make test-auth-service)
	@echo "$(BLUE)Running tests for $*...$(NC)"
	@cd services/$* && go test ./...
	@echo "$(GREEN)Tests for $* passed!$(NC)"

test-coverage: ## Run tests with coverage for all services
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Testing $$service with coverage...$(NC)"; \
		cd services/$$service && go test -cover ./... && cd ../..; \
	done

test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(NC)"
	@go test ./tests/integration/...

# Development targets
dev: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(NC)"
	@$(DOCKER_COMPOSE_DEV) up -d
	@echo "$(GREEN)Development environment started!$(NC)"
	@echo "$(YELLOW)API Gateway: http://localhost:8080$(NC)"
	@echo "$(YELLOW)Auth Service: http://localhost:8081$(NC)"
	@echo "$(YELLOW)Admin Service: http://localhost:8082$(NC)"
	@echo "$(YELLOW)Staff Service: http://localhost:8083$(NC)"

dev-build: ## Build and start development environment
	@echo "$(BLUE)Building and starting development environment...$(NC)"
	@$(DOCKER_COMPOSE_DEV) up -d --build

stop: ## Stop all services
	@echo "$(BLUE)Stopping all services...$(NC)"
	@$(DOCKER_COMPOSE_DEV) down
	@echo "$(GREEN)All services stopped!$(NC)"

restart: ## Restart all services
	@echo "$(BLUE)Restarting all services...$(NC)"
	@$(DOCKER_COMPOSE_DEV) restart
	@echo "$(GREEN)All services restarted!$(NC)"

restart-%: ## Restart specific service (e.g., make restart-auth-service)
	@echo "$(BLUE)Restarting $*...$(NC)"
	@$(DOCKER_COMPOSE_DEV) restart $*
	@echo "$(GREEN)$* restarted!$(NC)"

logs: ## Show logs for all services
	@$(DOCKER_COMPOSE_DEV) logs -f

logs-%: ## Show logs for specific service (e.g., make logs-auth-service)
	@$(DOCKER_COMPOSE_DEV) logs -f $*

# Database targets
migrate: ## Run migrations for all services
	@echo "$(BLUE)Running migrations for all services...$(NC)"
	@./scripts/migrate.sh
	@echo "$(GREEN)All migrations completed!$(NC)"

migrate-%: ## Run migrations for specific service (e.g., make migrate-auth-service)
	@echo "$(BLUE)Running migrations for $*...$(NC)"
	@cd services/$* && go run cmd/migrate/main.go
	@echo "$(GREEN)Migrations for $* completed!$(NC)"

seed: ## Seed databases with test data
	@echo "$(BLUE)Seeding databases...$(NC)"
	@./scripts/seed.sh
	@echo "$(GREEN)Database seeding completed!$(NC)"

seed-%: ## Seed specific service database (e.g., make seed-auth-service)
	@echo "$(BLUE)Seeding $* database...$(NC)"
	@cd services/$* && go run cmd/seed/main.go
	@echo "$(GREEN)$* database seeded!$(NC)"

# Code quality targets
lint: ## Run linter for all services
	@echo "$(BLUE)Running linter for all services...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Linting $$service...$(NC)"; \
		cd services/$$service && golangci-lint run && cd ../..; \
	done
	@echo "$(GREEN)Linting completed!$(NC)"

fmt: ## Format code for all services
	@echo "$(BLUE)Formatting code for all services...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Formatting $$service...$(NC)"; \
		cd services/$$service && go fmt ./... && cd ../..; \
	done
	@echo "$(GREEN)Code formatting completed!$(NC)"

vet: ## Run go vet for all services
	@echo "$(BLUE)Running go vet for all services...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Vetting $$service...$(NC)"; \
		cd services/$$service && go vet ./... && cd ../..; \
	done
	@echo "$(GREEN)Go vet completed!$(NC)"

# Documentation targets
docs: ## Generate API documentation
	@echo "$(BLUE)Generating API documentation...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Generating docs for $$service...$(NC)"; \
		cd services/$$service && swag init && cd ../..; \
	done
	@echo "$(GREEN)Documentation generated!$(NC)"

# Dependency targets
deps: ## Download dependencies for all services
	@echo "$(BLUE)Downloading dependencies...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Downloading deps for $$service...$(NC)"; \
		cd services/$$service && go mod download && cd ../..; \
	done
	@echo "$(GREEN)Dependencies downloaded!$(NC)"

tidy: ## Tidy dependencies for all services
	@echo "$(BLUE)Tidying dependencies...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Tidying $$service...$(NC)"; \
		cd services/$$service && go mod tidy && cd ../..; \
	done
	@echo "$(GREEN)Dependencies tidied!$(NC)"

# Production targets
prod: ## Start production environment
	@echo "$(BLUE)Starting production environment...$(NC)"
	@$(DOCKER_COMPOSE_PROD) up -d
	@echo "$(GREEN)Production environment started!$(NC)"

prod-build: ## Build and start production environment
	@echo "$(BLUE)Building and starting production environment...$(NC)"
	@$(DOCKER_COMPOSE_PROD) up -d --build

# Utility targets
clean: ## Clean build artifacts
	@echo "$(BLUE)Cleaning build artifacts...$(NC)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)Cleaning $$service...$(NC)"; \
		cd services/$$service && rm -rf bin/ build/ dist/ && cd ../..; \
	done
	@echo "$(GREEN)Clean completed!$(NC)"

health: ## Check health of all services
	@echo "$(BLUE)Checking service health...$(NC)"
	@curl -s http://localhost:8080/health || echo "$(RED)API Gateway not responding$(NC)"
	@curl -s http://localhost:8081/health || echo "$(RED)Auth Service not responding$(NC)"
	@curl -s http://localhost:8082/health || echo "$(RED)Admin Service not responding$(NC)"
	@curl -s http://localhost:8083/health || echo "$(RED)Staff Service not responding$(NC)"
	@curl -s http://localhost:8084/health || echo "$(RED)Payment Service not responding$(NC)"
	@curl -s http://localhost:8085/health || echo "$(RED)Notification Service not responding$(NC)"

setup: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@cp .env.example .env
	@echo "$(YELLOW)Please edit .env file with your configuration$(NC)"
	@echo "$(GREEN)Setup completed!$(NC)"

# Docker targets
docker-build: ## Build Docker images for all services
	@echo "$(BLUE)Building Docker images...$(NC)"
	@$(DOCKER_COMPOSE_DEV) build
	@echo "$(GREEN)Docker images built!$(NC)"

docker-pull: ## Pull latest Docker images
	@echo "$(BLUE)Pulling Docker images...$(NC)"
	@$(DOCKER_COMPOSE_DEV) pull
	@echo "$(GREEN)Docker images pulled!$(NC)"

docker-clean: ## Clean Docker containers and images
	@echo "$(BLUE)Cleaning Docker containers and images...$(NC)"
	@$(DOCKER_COMPOSE_DEV) down -v --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)Docker cleanup completed!$(NC)"
