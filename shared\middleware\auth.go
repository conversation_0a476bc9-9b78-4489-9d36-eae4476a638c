package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// AuthMiddleware provides authentication middleware
type AuthMiddleware struct {
	jwtManager *utils.JWTManager
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(jwtManager *utils.JWTManager) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: jwtManager,
	}
}

// RequireAuth middleware that requires authentication
func (am *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			utils.UnauthorizedResponse(c, "Authorization header is required")
			c.Abort()
			return
		}

		// Extract token from "Bearer <token>" format
		token, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			utils.UnauthorizedResponse(c, "Invalid authorization header format")
			c.Abort()
			return
		}

		// Validate token
		claims, err := am.jwtManager.ValidateAccessToken(token)
		if err != nil {
			utils.UnauthorizedResponse(c, "Invalid or expired token")
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_username", claims.Username)
		c.Set("user_role", claims.Role)
		c.Set("session_id", claims.SessionID)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequireRole middleware that requires specific role
func (am *AuthMiddleware) RequireRole(roles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		// First check if user is authenticated
		claims, exists := c.Get("claims")
		if !exists {
			utils.UnauthorizedResponse(c, "Authentication required")
			c.Abort()
			return
		}

		userClaims, ok := claims.(*utils.JWTClaims)
		if !ok {
			utils.UnauthorizedResponse(c, "Invalid authentication claims")
			c.Abort()
			return
		}

		// Check if user has required role
		hasRole := false
		for _, role := range roles {
			if userClaims.Role == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			utils.ForbiddenResponse(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdminAccess middleware for admin service access
func (am *AuthMiddleware) RequireAdminAccess() gin.HandlerFunc {
	return am.RequireRole(models.RoleAdmin, models.RoleCashier)
}

// RequireStaffAccess middleware for staff service access
func (am *AuthMiddleware) RequireStaffAccess() gin.HandlerFunc {
	return am.RequireRole(models.RoleReception, models.RoleTeacher, models.RoleManager, models.RoleAcademicManager)
}

// RequireManagerAccess middleware for manager level access
func (am *AuthMiddleware) RequireManagerAccess() gin.HandlerFunc {
	return am.RequireRole(models.RoleAdmin, models.RoleManager, models.RoleAcademicManager)
}

// RequireTeacherAccess middleware for teacher access
func (am *AuthMiddleware) RequireTeacherAccess() gin.HandlerFunc {
	return am.RequireRole(models.RoleTeacher, models.RoleAcademicManager, models.RoleManager, models.RoleAdmin)
}

// OptionalAuth middleware that optionally authenticates user
func (am *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Extract token from "Bearer <token>" format
		token, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.Next()
			return
		}

		// Validate token
		claims, err := am.jwtManager.ValidateAccessToken(token)
		if err != nil {
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_username", claims.Username)
		c.Set("user_role", claims.Role)
		c.Set("session_id", claims.SessionID)
		c.Set("claims", claims)

		c.Next()
	}
}

// ServiceAuth middleware for service-to-service authentication
func (am *AuthMiddleware) ServiceAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for service token in header
		serviceToken := c.GetHeader("X-Service-Token")
		if serviceToken == "" {
			utils.UnauthorizedResponse(c, "Service token is required")
			c.Abort()
			return
		}

		// Validate service token (implement your service token validation logic)
		if !am.validateServiceToken(serviceToken) {
			utils.UnauthorizedResponse(c, "Invalid service token")
			c.Abort()
			return
		}

		c.Set("service_auth", true)
		c.Next()
	}
}

// validateServiceToken validates service-to-service token
func (am *AuthMiddleware) validateServiceToken(token string) bool {
	// Implement your service token validation logic here
	// This could be a shared secret, JWT token, or other mechanism
	// For now, we'll use a simple shared secret approach
	expectedToken := "service-secret-token" // This should come from config
	return token == expectedToken
}

// GetUserFromContext extracts user information from gin context
func GetUserFromContext(c *gin.Context) (*utils.JWTClaims, bool) {
	claims, exists := c.Get("claims")
	if !exists {
		return nil, false
	}

	userClaims, ok := claims.(*utils.JWTClaims)
	return userClaims, ok
}

// GetUserIDFromContext extracts user ID from gin context
func GetUserIDFromContext(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}

// GetUserRoleFromContext extracts user role from gin context
func GetUserRoleFromContext(c *gin.Context) (models.UserRole, bool) {
	role, exists := c.Get("user_role")
	if !exists {
		return "", false
	}

	userRole, ok := role.(models.UserRole)
	return userRole, ok
}

// HasRole checks if the current user has a specific role
func HasRole(c *gin.Context, role models.UserRole) bool {
	userRole, exists := GetUserRoleFromContext(c)
	if !exists {
		return false
	}
	return userRole == role
}

// HasAnyRole checks if the current user has any of the specified roles
func HasAnyRole(c *gin.Context, roles ...models.UserRole) bool {
	userRole, exists := GetUserRoleFromContext(c)
	if !exists {
		return false
	}

	for _, role := range roles {
		if userRole == role {
			return true
		}
	}
	return false
}

// IsAdmin checks if the current user is an admin
func IsAdmin(c *gin.Context) bool {
	return HasRole(c, models.RoleAdmin)
}

// CanAccessAdminService checks if user can access admin service
func CanAccessAdminService(c *gin.Context) bool {
	return HasAnyRole(c, models.RoleAdmin, models.RoleCashier)
}

// CanAccessStaffService checks if user can access staff service
func CanAccessStaffService(c *gin.Context) bool {
	return HasAnyRole(c, models.RoleReception, models.RoleTeacher, models.RoleManager, models.RoleAcademicManager)
}

// APIKeyAuth middleware for API key authentication
func APIKeyAuth(validAPIKeys []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			utils.UnauthorizedResponse(c, "API key is required")
			c.Abort()
			return
		}

		// Check if API key is valid
		isValid := false
		for _, validKey := range validAPIKeys {
			if apiKey == validKey {
				isValid = true
				break
			}
		}

		if !isValid {
			utils.UnauthorizedResponse(c, "Invalid API key")
			c.Abort()
			return
		}

		c.Set("api_key_auth", true)
		c.Next()
	}
}

// BasicAuth middleware for basic authentication
func BasicAuth(username, password string) gin.HandlerFunc {
	return gin.BasicAuth(gin.Accounts{
		username: password,
	})
}

// CombinedAuth middleware that accepts either JWT or API key authentication
func (am *AuthMiddleware) CombinedAuth(validAPIKeys []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try JWT authentication first
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			token, err := utils.ExtractTokenFromHeader(authHeader)
			if err == nil {
				claims, err := am.jwtManager.ValidateAccessToken(token)
				if err == nil {
					// JWT authentication successful
					c.Set("user_id", claims.UserID)
					c.Set("user_email", claims.Email)
					c.Set("user_username", claims.Username)
					c.Set("user_role", claims.Role)
					c.Set("session_id", claims.SessionID)
					c.Set("claims", claims)
					c.Set("auth_type", "jwt")
					c.Next()
					return
				}
			}
		}

		// Try API key authentication
		apiKey := c.GetHeader("X-API-Key")
		if apiKey != "" {
			for _, validKey := range validAPIKeys {
				if apiKey == validKey {
					c.Set("api_key_auth", true)
					c.Set("auth_type", "api_key")
					c.Next()
					return
				}
			}
		}

		// No valid authentication found
		utils.UnauthorizedResponse(c, "Valid authentication required (JWT token or API key)")
		c.Abort()
	}
}
