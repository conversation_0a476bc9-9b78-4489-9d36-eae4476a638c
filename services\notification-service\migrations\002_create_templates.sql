-- Create notification_templates table
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL,
    
    -- Template content
    subject TEXT,
    body TEXT NOT NULL,
    html_body TEXT,
    
    -- Template metadata
    description TEXT,
    variables JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    
    -- Template settings
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    version INTEGER DEFAULT 1,
    
    -- Audit fields
    created_by_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create unique index for name (excluding soft deleted records)
CREATE UNIQUE INDEX IF NOT EXISTS idx_notification_templates_name_unique 
    ON notification_templates(name) 
    WHERE deleted_at IS NULL;

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notification_templates_name ON notification_templates(name);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_templates_is_active ON notification_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_templates_is_default ON notification_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_notification_templates_created_by_id ON notification_templates(created_by_id);
CREATE INDEX IF NOT EXISTS idx_notification_templates_deleted_at ON notification_templates(deleted_at);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_notification_templates_updated_at 
    BEFORE UPDATE ON notification_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add foreign key constraint to notifications table
ALTER TABLE notifications 
ADD CONSTRAINT fk_notifications_template_id 
FOREIGN KEY (template_id) REFERENCES notification_templates(id) 
ON DELETE SET NULL;
