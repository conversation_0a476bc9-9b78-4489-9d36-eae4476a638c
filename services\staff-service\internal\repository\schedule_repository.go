package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ScheduleRepository handles schedule data operations
type ScheduleRepository interface {
	GetAll(req *models.ScheduleListRequest) ([]*models.Schedule, int64, error)
	GetByID(id uuid.UUID) (*models.Schedule, error)
	Create(schedule *models.Schedule) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.ScheduleStats, error)
	GetByTeacher(teacherID uuid.UUID) ([]*models.Schedule, error)
	GetByCourse(courseID uuid.UUID) ([]*models.Schedule, error)
	GetByDateRange(startDate, endDate time.Time) ([]*models.Schedule, error)
	CheckConflicts(req *models.ScheduleConflictRequest) ([]*models.Schedule, error)
	GetUpcomingSchedules(teacherID *uuid.UUID, limit int) ([]*models.Schedule, error)
	GetSchedulesByRoom(room string, date time.Time) ([]*models.Schedule, error)
	GetWeeklySchedule(teacherID *uuid.UUID, courseID *uuid.UUID, startOfWeek time.Time) ([]*models.Schedule, error)
	BulkUpdateStatus(scheduleIDs []uuid.UUID, status models.ScheduleStatus, updatedBy uuid.UUID) error
}

type scheduleRepository struct {
	db *gorm.DB
}

// NewScheduleRepository creates a new schedule repository
func NewScheduleRepository(db *gorm.DB) ScheduleRepository {
	return &scheduleRepository{db: db}
}

// GetAll retrieves schedules with pagination and filtering
func (r *scheduleRepository) GetAll(req *models.ScheduleListRequest) ([]*models.Schedule, int64, error) {
	var schedules []*models.Schedule
	var total int64

	query := r.db.Model(&models.Schedule{}).Preload("Course").Preload("Teacher").Preload("CreatedBy")

	// Apply filters
	if req.CourseID != nil {
		query = query.Where("course_id = ?", *req.CourseID)
	}

	if req.TeacherID != nil {
		query = query.Where("teacher_id = ?", *req.TeacherID)
	}

	if req.DayOfWeek != nil {
		query = query.Where("day_of_week = ?", *req.DayOfWeek)
	}

	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.Room != nil {
		query = query.Where("room = ?", *req.Room)
	}

	if req.Building != nil {
		query = query.Where("building = ?", *req.Building)
	}

	if req.CreatedByID != nil {
		query = query.Where("created_by_id = ?", *req.CreatedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where(
			"LOWER(title) LIKE ? OR LOWER(room) LIKE ? OR LOWER(building) LIKE ? OR LOWER(location) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	if req.StartDate != nil {
		query = query.Where("start_time >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("end_time <= ?", *req.EndDate)
	}

	if req.IsRecurring != nil {
		query = query.Where("is_recurring = ?", *req.IsRecurring)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count schedules: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("day_of_week, start_time").Find(&schedules).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get schedules: %w", err)
	}

	return schedules, total, nil
}

// GetByID retrieves a schedule by ID
func (r *scheduleRepository) GetByID(id uuid.UUID) (*models.Schedule, error) {
	var schedule models.Schedule
	if err := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").Preload("Attendances").
		First(&schedule, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("schedule not found")
		}
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}
	return &schedule, nil
}

// Create creates a new schedule
func (r *scheduleRepository) Create(schedule *models.Schedule) error {
	// Calculate duration if not provided
	if schedule.Duration == 0 {
		duration := schedule.EndTime.Sub(schedule.StartTime)
		schedule.Duration = int(duration.Minutes())
	}

	if err := r.db.Create(schedule).Error; err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}
	return nil
}

// Update updates a schedule
func (r *scheduleRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Schedule{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update schedule: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("schedule not found")
	}
	return nil
}

// Delete soft deletes a schedule
func (r *scheduleRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Schedule{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete schedule: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("schedule not found")
	}
	return nil
}

// GetStats retrieves schedule statistics
func (r *scheduleRepository) GetStats() (*models.ScheduleStats, error) {
	stats := &models.ScheduleStats{
		SchedulesByStatus: make(map[models.ScheduleStatus]int64),
		SchedulesByDay:    make(map[models.DayOfWeek]int64),
		RoomUtilization:   make(map[string]int64),
	}

	// Total schedules
	if err := r.db.Model(&models.Schedule{}).Count(&stats.TotalSchedules).Error; err != nil {
		return nil, fmt.Errorf("failed to count total schedules: %w", err)
	}

	// Schedules by status
	statuses := []models.ScheduleStatus{
		models.ScheduleStatusActive, models.ScheduleStatusCancelled,
		models.ScheduleStatusCompleted, models.ScheduleStatusPostponed,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Schedule{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count schedules by status %s: %w", status, err)
		}
		stats.SchedulesByStatus[status] = count
	}

	// Schedules by day
	days := []models.DayOfWeek{
		models.Monday, models.Tuesday, models.Wednesday, models.Thursday,
		models.Friday, models.Saturday, models.Sunday,
	}

	for _, day := range days {
		var count int64
		if err := r.db.Model(&models.Schedule{}).Where("day_of_week = ?", day).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count schedules by day %s: %w", day, err)
		}
		stats.SchedulesByDay[day] = count
	}

	// Average class duration
	var avgDuration float64
	if err := r.db.Model(&models.Schedule{}).Select("AVG(duration)").Scan(&avgDuration).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average duration: %w", err)
	}
	stats.AverageClassDuration = avgDuration / 60.0 // Convert to hours

	// Total teaching hours
	var totalMinutes float64
	if err := r.db.Model(&models.Schedule{}).Where("status = ?", models.ScheduleStatusActive).
		Select("SUM(duration)").Scan(&totalMinutes).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total teaching hours: %w", err)
	}
	stats.TotalTeachingHours = totalMinutes / 60.0 // Convert to hours

	// Room utilization
	type RoomCount struct {
		Room  string `json:"room"`
		Count int64  `json:"count"`
	}
	var roomCounts []RoomCount
	if err := r.db.Model(&models.Schedule{}).
		Select("room, COUNT(*) as count").
		Where("room != '' AND status = ?", models.ScheduleStatusActive).
		Group("room").Find(&roomCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate room utilization: %w", err)
	}

	for _, rc := range roomCounts {
		stats.RoomUtilization[rc.Room] = rc.Count
	}

	return stats, nil
}

// GetByTeacher retrieves schedules for a specific teacher
func (r *scheduleRepository) GetByTeacher(teacherID uuid.UUID) ([]*models.Schedule, error) {
	var schedules []*models.Schedule
	if err := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").
		Where("teacher_id = ?", teacherID).
		Order("day_of_week, start_time").Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get schedules by teacher: %w", err)
	}
	return schedules, nil
}

// GetByCourse retrieves schedules for a specific course
func (r *scheduleRepository) GetByCourse(courseID uuid.UUID) ([]*models.Schedule, error) {
	var schedules []*models.Schedule
	if err := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").
		Where("course_id = ?", courseID).
		Order("day_of_week, start_time").Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get schedules by course: %w", err)
	}
	return schedules, nil
}

// GetByDateRange retrieves schedules within a date range
func (r *scheduleRepository) GetByDateRange(startDate, endDate time.Time) ([]*models.Schedule, error) {
	var schedules []*models.Schedule
	if err := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").
		Where("start_time >= ? AND end_time <= ?", startDate, endDate).
		Order("start_time").Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get schedules by date range: %w", err)
	}
	return schedules, nil
}

// CheckConflicts checks for scheduling conflicts
func (r *scheduleRepository) CheckConflicts(req *models.ScheduleConflictRequest) ([]*models.Schedule, error) {
	var conflicts []*models.Schedule
	
	query := r.db.Model(&models.Schedule{}).Preload("Course").Preload("Teacher").
		Where("day_of_week = ? AND status = ?", req.DayOfWeek, models.ScheduleStatusActive).
		Where("(start_time < ? AND end_time > ?)", req.EndTime, req.StartTime)

	// Exclude specific schedule if provided
	if req.ExcludeID != nil {
		query = query.Where("id != ?", *req.ExcludeID)
	}

	// Check teacher conflicts
	if req.TeacherID != nil {
		teacherQuery := query.Where("teacher_id = ?", *req.TeacherID)
		if err := teacherQuery.Find(&conflicts).Error; err != nil {
			return nil, fmt.Errorf("failed to check teacher conflicts: %w", err)
		}
	}

	// Check room conflicts
	if req.Room != nil && *req.Room != "" {
		roomQuery := query.Where("room = ?", *req.Room)
		var roomConflicts []*models.Schedule
		if err := roomQuery.Find(&roomConflicts).Error; err != nil {
			return nil, fmt.Errorf("failed to check room conflicts: %w", err)
		}
		conflicts = append(conflicts, roomConflicts...)
	}

	return conflicts, nil
}

// GetUpcomingSchedules retrieves upcoming schedules
func (r *scheduleRepository) GetUpcomingSchedules(teacherID *uuid.UUID, limit int) ([]*models.Schedule, error) {
	var schedules []*models.Schedule
	
	query := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").
		Where("status = ? AND start_time > ?", models.ScheduleStatusActive, time.Now())

	if teacherID != nil {
		query = query.Where("teacher_id = ?", *teacherID)
	}

	if err := query.Order("start_time").Limit(limit).Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get upcoming schedules: %w", err)
	}
	return schedules, nil
}

// GetSchedulesByRoom retrieves schedules for a specific room on a date
func (r *scheduleRepository) GetSchedulesByRoom(room string, date time.Time) ([]*models.Schedule, error) {
	var schedules []*models.Schedule
	
	dayOfWeek := models.DayOfWeek(strings.ToUpper(date.Weekday().String()))
	
	if err := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").
		Where("room = ? AND day_of_week = ? AND status = ?", room, dayOfWeek, models.ScheduleStatusActive).
		Order("start_time").Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get schedules by room: %w", err)
	}
	return schedules, nil
}

// GetWeeklySchedule retrieves weekly schedule for teacher or course
func (r *scheduleRepository) GetWeeklySchedule(teacherID *uuid.UUID, courseID *uuid.UUID, startOfWeek time.Time) ([]*models.Schedule, error) {
	var schedules []*models.Schedule
	
	query := r.db.Preload("Course").Preload("Teacher").Preload("CreatedBy").
		Where("status = ?", models.ScheduleStatusActive)

	if teacherID != nil {
		query = query.Where("teacher_id = ?", *teacherID)
	}

	if courseID != nil {
		query = query.Where("course_id = ?", *courseID)
	}

	if err := query.Order("day_of_week, start_time").Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get weekly schedule: %w", err)
	}
	return schedules, nil
}

// BulkUpdateStatus updates status for multiple schedules
func (r *scheduleRepository) BulkUpdateStatus(scheduleIDs []uuid.UUID, status models.ScheduleStatus, updatedBy uuid.UUID) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	result := r.db.Model(&models.Schedule{}).Where("id IN ?", scheduleIDs).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to bulk update schedule status: %w", result.Error)
	}

	return nil
}
