-- Create enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    course_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    enrollment_date DATE NOT NULL,
    completion_date DATE,
    total_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    payment_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    payment_method VARCHAR(50),
    payment_date DATE,
    final_grade DECIMAL(5,2),
    attendance_rate DECIMAL(5,2),
    notes TEXT,
    enrolled_by_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_enrollments_student FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_enrollments_course FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate enrollments
    CONSTRAINT uk_enrollments_student_course UNIQUE (student_id, course_id)
);

-- Create indexes for enrollments table
CREATE INDEX IF NOT EXISTS idx_enrollments_student_id ON enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course_id ON enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status);
CREATE INDEX IF NOT EXISTS idx_enrollments_payment_status ON enrollments(payment_status);
CREATE INDEX IF NOT EXISTS idx_enrollments_enrollment_date ON enrollments(enrollment_date);
CREATE INDEX IF NOT EXISTS idx_enrollments_payment_date ON enrollments(payment_date);
CREATE INDEX IF NOT EXISTS idx_enrollments_created_at ON enrollments(created_at);
CREATE INDEX IF NOT EXISTS idx_enrollments_deleted_at ON enrollments(deleted_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_enrollments_student_course ON enrollments(student_id, course_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course_status ON enrollments(course_id, status);

-- Add constraints
ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_status 
    CHECK (status IN ('PENDING', 'ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED'));

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_payment_status 
    CHECK (payment_status IN ('PENDING', 'PARTIAL', 'PAID', 'OVERDUE', 'REFUNDED'));

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_total_fee 
    CHECK (total_fee >= 0);

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_paid_amount 
    CHECK (paid_amount >= 0 AND paid_amount <= total_fee);

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_discount_amount 
    CHECK (discount_amount >= 0);

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_final_grade 
    CHECK (final_grade IS NULL OR (final_grade >= 0 AND final_grade <= 100));

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_attendance_rate 
    CHECK (attendance_rate IS NULL OR (attendance_rate >= 0 AND attendance_rate <= 100));

ALTER TABLE enrollments ADD CONSTRAINT chk_enrollments_dates 
    CHECK (completion_date IS NULL OR completion_date >= enrollment_date);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_enrollments_updated_at BEFORE UPDATE ON enrollments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update course current_students count
CREATE OR REPLACE FUNCTION update_course_student_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update course current_students count
    IF TG_OP = 'INSERT' AND NEW.status = 'ACTIVE' THEN
        UPDATE courses SET current_students = current_students + 1 WHERE id = NEW.course_id;
    ELSIF TG_OP = 'UPDATE' THEN
        -- If status changed from ACTIVE to something else
        IF OLD.status = 'ACTIVE' AND NEW.status != 'ACTIVE' THEN
            UPDATE courses SET current_students = current_students - 1 WHERE id = NEW.course_id;
        -- If status changed from something else to ACTIVE
        ELSIF OLD.status != 'ACTIVE' AND NEW.status = 'ACTIVE' THEN
            UPDATE courses SET current_students = current_students + 1 WHERE id = NEW.course_id;
        END IF;
    ELSIF TG_OP = 'DELETE' AND OLD.status = 'ACTIVE' THEN
        UPDATE courses SET current_students = current_students - 1 WHERE id = OLD.course_id;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Create triggers for course student count
CREATE TRIGGER update_course_student_count_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON enrollments
    FOR EACH ROW EXECUTE FUNCTION update_course_student_count();
