package handlers

import (
	"net/http"
	"notification-service/internal/services"

	"github.com/gin-gonic/gin"
)

// SMSHandler handles SMS-related HTTP requests
type SMSHandler struct {
	smsService *services.SMSService
}

// NewSMSHandler creates a new SMS handler
func NewSMSHandler(smsService *services.SMSService) *SMSHandler {
	return &SMSHandler{
		smsService: smsService,
	}
}

// SendSMS sends a single SMS
func (h *SMSHandler) SendSMS(c *gin.Context) {
	var req struct {
		To      string `json:"to" binding:"required"`
		Message string `json:"message" binding:"required"`
	}
	
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.<PERSON>r(),
			},
		})
		return
	}
	
	err := h.smsService.SendSMS(req.To, req.Message)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "SEND_FAILED",
				"message": "Failed to send SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SMS sent successfully",
	})
}

// SendBulkSMS sends SMS to multiple recipients
func (h *SMSHandler) SendBulkSMS(c *gin.Context) {
	var req struct {
		Recipients []string `json:"recipients" binding:"required,min=1"`
		Message    string   `json:"message" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendBulkSMS(req.Recipients, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "BULK_SEND_FAILED",
				"message": "Failed to send bulk SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Bulk SMS sent successfully",
	})
}

// SendTemplatedSMS sends an SMS using a template
func (h *SMSHandler) SendTemplatedSMS(c *gin.Context) {
	var req struct {
		To       string                 `json:"to" binding:"required"`
		Template string                 `json:"template" binding:"required"`
		Data     map[string]interface{} `json:"data"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendTemplatedSMS(req.To, req.Template, req.Data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "TEMPLATED_SEND_FAILED",
				"message": "Failed to send templated SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Templated SMS sent successfully",
	})
}

// SendClassReminderSMS sends a class reminder SMS
func (h *SMSHandler) SendClassReminderSMS(c *gin.Context) {
	var req struct {
		To          string `json:"to" binding:"required"`
		StudentName string `json:"student_name" binding:"required"`
		ClassName   string `json:"class_name" binding:"required"`
		ClassTime   string `json:"class_time" binding:"required"`
		Room        string `json:"room" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendClassReminderSMS(req.To, req.StudentName, req.ClassName, req.ClassTime, req.Room)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "CLASS_REMINDER_FAILED",
				"message": "Failed to send class reminder SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Class reminder SMS sent successfully",
	})
}

// SendPaymentReminderSMS sends a payment reminder SMS
func (h *SMSHandler) SendPaymentReminderSMS(c *gin.Context) {
	var req struct {
		To          string `json:"to" binding:"required"`
		StudentName string `json:"student_name" binding:"required"`
		Amount      string `json:"amount" binding:"required"`
		DueDate     string `json:"due_date" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendPaymentReminderSMS(req.To, req.StudentName, req.Amount, req.DueDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "PAYMENT_REMINDER_FAILED",
				"message": "Failed to send payment reminder SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Payment reminder SMS sent successfully",
	})
}

// SendWelcomeSMS sends a welcome SMS to new students
func (h *SMSHandler) SendWelcomeSMS(c *gin.Context) {
	var req struct {
		To          string `json:"to" binding:"required"`
		StudentName string `json:"student_name" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendWelcomeSMS(req.To, req.StudentName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "WELCOME_SMS_FAILED",
				"message": "Failed to send welcome SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Welcome SMS sent successfully",
	})
}

// SendVerificationCodeSMS sends a verification code SMS
func (h *SMSHandler) SendVerificationCodeSMS(c *gin.Context) {
	var req struct {
		To   string `json:"to" binding:"required"`
		Code string `json:"code" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendVerificationCodeSMS(req.To, req.Code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VERIFICATION_SMS_FAILED",
				"message": "Failed to send verification code SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Verification code SMS sent successfully",
	})
}

// SendEmergencyNotificationSMS sends an emergency notification SMS
func (h *SMSHandler) SendEmergencyNotificationSMS(c *gin.Context) {
	var req struct {
		To      string `json:"to" binding:"required"`
		Message string `json:"message" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "Invalid request data",
				"details": err.Error(),
			},
		})
		return
	}
	
	err := h.smsService.SendEmergencyNotificationSMS(req.To, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "EMERGENCY_SMS_FAILED",
				"message": "Failed to send emergency notification SMS",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Emergency notification SMS sent successfully",
	})
}

// GetSMSStatus returns SMS service status
func (h *SMSHandler) GetSMSStatus(c *gin.Context) {
	stats := h.smsService.GetSMSStats()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// TestSMSConnection tests the SMS service connection
func (h *SMSHandler) TestSMSConnection(c *gin.Context) {
	err := h.smsService.TestSMSConnection()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "CONNECTION_TEST_FAILED",
				"message": "SMS connection test failed",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SMS connection test successful",
	})
}
