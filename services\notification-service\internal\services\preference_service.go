package services

import (
	"fmt"
	"notification-service/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PreferenceService handles notification preference operations
type PreferenceService struct {
	db *gorm.DB
}

// NewPreferenceService creates a new preference service
func NewPreferenceService(db *gorm.DB) *PreferenceService {
	return &PreferenceService{
		db: db,
	}
}

// CreatePreference creates notification preferences for a user
func (s *PreferenceService) CreatePreference(req *models.CreatePreferenceRequest) (*models.NotificationPreference, error) {
	// Check if preferences already exist for this user
	var existingPreference models.NotificationPreference
	err := s.db.Where("user_id = ?", req.UserID).First(&existingPreference).Error
	if err == nil {
		return nil, fmt.Errorf("preferences already exist for user %s", req.UserID)
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing preferences: %w", err)
	}
	
	preference := req.ToPreference()
	
	// Create preference in database
	if err := s.db.Create(preference).Error; err != nil {
		return nil, fmt.Errorf("failed to create preference: %w", err)
	}
	
	return preference, nil
}

// GetPreference retrieves notification preferences for a user
func (s *PreferenceService) GetPreference(userID uuid.UUID) (*models.NotificationPreference, error) {
	var preference models.NotificationPreference
	err := s.db.Where("user_id = ?", userID).First(&preference).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create default preferences if none exist
			return s.createDefaultPreference(userID)
		}
		return nil, fmt.Errorf("failed to get preference: %w", err)
	}
	return &preference, nil
}

// UpdatePreference updates notification preferences for a user
func (s *PreferenceService) UpdatePreference(userID uuid.UUID, updates map[string]interface{}) (*models.NotificationPreference, error) {
	var preference models.NotificationPreference
	err := s.db.Where("user_id = ?", userID).First(&preference).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("preferences not found for user %s", userID)
		}
		return nil, fmt.Errorf("failed to get preference: %w", err)
	}
	
	// Update preference
	if err := s.db.Model(&preference).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update preference: %w", err)
	}
	
	return &preference, nil
}

// DeletePreference deletes notification preferences for a user
func (s *PreferenceService) DeletePreference(userID uuid.UUID) error {
	err := s.db.Where("user_id = ?", userID).Delete(&models.NotificationPreference{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete preference: %w", err)
	}
	return nil
}

// IsNotificationAllowed checks if a notification is allowed for a user
func (s *PreferenceService) IsNotificationAllowed(userID uuid.UUID, notificationType models.NotificationType, category string) (bool, error) {
	preference, err := s.GetPreference(userID)
	if err != nil {
		// If we can't get preferences, allow by default
		return true, nil
	}
	
	return preference.IsNotificationAllowed(notificationType, category), nil
}

// UpdateEmailPreferences updates email notification preferences
func (s *PreferenceService) UpdateEmailPreferences(userID uuid.UUID, enabled, marketing, transactional, reminders, announcements bool) error {
	updates := map[string]interface{}{
		"email_enabled":       enabled,
		"email_marketing":     marketing,
		"email_transactional": transactional,
		"email_reminders":     reminders,
		"email_announcements": announcements,
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// UpdateSMSPreferences updates SMS notification preferences
func (s *PreferenceService) UpdateSMSPreferences(userID uuid.UUID, enabled, marketing, transactional, reminders, urgent bool) error {
	updates := map[string]interface{}{
		"sms_enabled":       enabled,
		"sms_marketing":     marketing,
		"sms_transactional": transactional,
		"sms_reminders":     reminders,
		"sms_urgent":        urgent,
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// UpdateInAppPreferences updates in-app notification preferences
func (s *PreferenceService) UpdateInAppPreferences(userID uuid.UUID, enabled, marketing, transactional, reminders, announcements bool) error {
	updates := map[string]interface{}{
		"in_app_enabled":       enabled,
		"in_app_marketing":     marketing,
		"in_app_transactional": transactional,
		"in_app_reminders":     reminders,
		"in_app_announcements": announcements,
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// UpdatePushPreferences updates push notification preferences
func (s *PreferenceService) UpdatePushPreferences(userID uuid.UUID, enabled, marketing, transactional, reminders, urgent bool) error {
	updates := map[string]interface{}{
		"push_enabled":       enabled,
		"push_marketing":     marketing,
		"push_transactional": transactional,
		"push_reminders":     reminders,
		"push_urgent":        urgent,
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// UpdateContactInfo updates user contact information
func (s *PreferenceService) UpdateContactInfo(userID uuid.UUID, email, phoneNumber string) error {
	updates := map[string]interface{}{
		"email":        email,
		"phone_number": phoneNumber,
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// UpdateQuietHours updates quiet hours settings
func (s *PreferenceService) UpdateQuietHours(userID uuid.UUID, enabled bool, startTime, endTime, timezone string) error {
	updates := map[string]interface{}{
		"quiet_hours_enabled": enabled,
		"timezone":            timezone,
	}
	
	if startTime != "" {
		updates["quiet_hours_start"] = startTime
	}
	if endTime != "" {
		updates["quiet_hours_end"] = endTime
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// UpdateDigestSettings updates digest notification settings
func (s *PreferenceService) UpdateDigestSettings(userID uuid.UUID, enabled bool, frequency string, maxPerDay int) error {
	updates := map[string]interface{}{
		"digest_enabled":              enabled,
		"digest_frequency":            frequency,
		"max_notifications_per_day":   maxPerDay,
	}
	
	_, err := s.UpdatePreference(userID, updates)
	return err
}

// GetPreferencesByType gets all users who have enabled a specific notification type
func (s *PreferenceService) GetPreferencesByType(notificationType models.NotificationType, category string) ([]models.NotificationPreference, error) {
	var preferences []models.NotificationPreference
	query := s.db.Model(&models.NotificationPreference{})
	
	switch notificationType {
	case models.NotificationTypeEmail:
		query = query.Where("email_enabled = true")
		switch category {
		case "marketing":
			query = query.Where("email_marketing = true")
		case "transactional":
			query = query.Where("email_transactional = true")
		case "reminders":
			query = query.Where("email_reminders = true")
		case "announcements":
			query = query.Where("email_announcements = true")
		}
	case models.NotificationTypeSMS:
		query = query.Where("sms_enabled = true")
		switch category {
		case "marketing":
			query = query.Where("sms_marketing = true")
		case "transactional":
			query = query.Where("sms_transactional = true")
		case "reminders":
			query = query.Where("sms_reminders = true")
		case "urgent":
			query = query.Where("sms_urgent = true")
		}
	case models.NotificationTypeInApp:
		query = query.Where("in_app_enabled = true")
		switch category {
		case "marketing":
			query = query.Where("in_app_marketing = true")
		case "transactional":
			query = query.Where("in_app_transactional = true")
		case "reminders":
			query = query.Where("in_app_reminders = true")
		case "announcements":
			query = query.Where("in_app_announcements = true")
		}
	case models.NotificationTypePush:
		query = query.Where("push_enabled = true")
		switch category {
		case "marketing":
			query = query.Where("push_marketing = true")
		case "transactional":
			query = query.Where("push_transactional = true")
		case "reminders":
			query = query.Where("push_reminders = true")
		case "urgent":
			query = query.Where("push_urgent = true")
		}
	}
	
	err := query.Find(&preferences).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get preferences by type: %w", err)
	}
	
	return preferences, nil
}

// GetPreferenceStats returns preference statistics
func (s *PreferenceService) GetPreferenceStats() (map[string]interface{}, error) {
	var stats struct {
		TotalUsers           int64 `json:"total_users"`
		EmailEnabled         int64 `json:"email_enabled"`
		SMSEnabled           int64 `json:"sms_enabled"`
		InAppEnabled         int64 `json:"in_app_enabled"`
		PushEnabled          int64 `json:"push_enabled"`
		QuietHoursEnabled    int64 `json:"quiet_hours_enabled"`
		DigestEnabled        int64 `json:"digest_enabled"`
	}
	
	// Get total count
	if err := s.db.Model(&models.NotificationPreference{}).Count(&stats.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	
	// Get counts by preference type
	s.db.Model(&models.NotificationPreference{}).Where("email_enabled = true").Count(&stats.EmailEnabled)
	s.db.Model(&models.NotificationPreference{}).Where("sms_enabled = true").Count(&stats.SMSEnabled)
	s.db.Model(&models.NotificationPreference{}).Where("in_app_enabled = true").Count(&stats.InAppEnabled)
	s.db.Model(&models.NotificationPreference{}).Where("push_enabled = true").Count(&stats.PushEnabled)
	s.db.Model(&models.NotificationPreference{}).Where("quiet_hours_enabled = true").Count(&stats.QuietHoursEnabled)
	s.db.Model(&models.NotificationPreference{}).Where("digest_enabled = true").Count(&stats.DigestEnabled)
	
	result := map[string]interface{}{
		"total_users":         stats.TotalUsers,
		"email_enabled":       stats.EmailEnabled,
		"sms_enabled":         stats.SMSEnabled,
		"in_app_enabled":      stats.InAppEnabled,
		"push_enabled":        stats.PushEnabled,
		"quiet_hours_enabled": stats.QuietHoursEnabled,
		"digest_enabled":      stats.DigestEnabled,
	}
	
	if stats.TotalUsers > 0 {
		result["email_enabled_rate"] = float64(stats.EmailEnabled) / float64(stats.TotalUsers) * 100
		result["sms_enabled_rate"] = float64(stats.SMSEnabled) / float64(stats.TotalUsers) * 100
		result["in_app_enabled_rate"] = float64(stats.InAppEnabled) / float64(stats.TotalUsers) * 100
		result["push_enabled_rate"] = float64(stats.PushEnabled) / float64(stats.TotalUsers) * 100
	}
	
	return result, nil
}

// createDefaultPreference creates default preferences for a user
func (s *PreferenceService) createDefaultPreference(userID uuid.UUID) (*models.NotificationPreference, error) {
	preference := &models.NotificationPreference{
		UserID:                 userID,
		EmailEnabled:           true,
		EmailTransactional:     true,
		EmailReminders:         true,
		SMSEnabled:             true,
		SMSTransactional:       true,
		SMSReminders:           true,
		InAppEnabled:           true,
		InAppTransactional:     true,
		InAppReminders:         true,
		PushEnabled:            true,
		PushTransactional:      true,
		PushReminders:          true,
		MaxNotificationsPerDay: 50,
		DigestFrequency:        "DAILY",
		Timezone:               "UTC",
	}
	
	if err := s.db.Create(preference).Error; err != nil {
		return nil, fmt.Errorf("failed to create default preference: %w", err)
	}
	
	return preference, nil
}

// BulkUpdatePreferences updates preferences for multiple users
func (s *PreferenceService) BulkUpdatePreferences(userIDs []uuid.UUID, updates map[string]interface{}) error {
	err := s.db.Model(&models.NotificationPreference{}).
		Where("user_id IN ?", userIDs).
		Updates(updates).Error
	
	if err != nil {
		return fmt.Errorf("failed to bulk update preferences: %w", err)
	}
	
	return nil
}
