package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// CourseStatus represents the status of a course
type CourseStatus string

const (
	CourseStatusDraft     CourseStatus = "DRAFT"
	CourseStatusActive    CourseStatus = "ACTIVE"
	CourseStatusInactive  CourseStatus = "INACTIVE"
	CourseStatusCompleted CourseStatus = "COMPLETED"
	CourseStatusCancelled CourseStatus = "CANCELLED"
)

// IsValid checks if the course status is valid
func (cs CourseStatus) IsValid() bool {
	switch cs {
	case CourseStatusDraft, CourseStatusActive, CourseStatusInactive,
		 CourseStatusCompleted, CourseStatusCancelled:
		return true
	}
	return false
}

// CourseLevel represents the level/difficulty of a course
type CourseLevel string

const (
	CourseLevelBeginner     CourseLevel = "BEGINNER"
	CourseLevelIntermediate CourseLevel = "INTERMEDIATE"
	CourseLevelAdvanced     CourseLevel = "ADVANCED"
	CourseLevelExpert       CourseLevel = "EXPERT"
)

// IsValid checks if the course level is valid
func (cl CourseLevel) IsValid() bool {
	switch cl {
	case CourseLevelBeginner, CourseLevelIntermediate, CourseLevelAdvanced, CourseLevelExpert:
		return true
	}
	return false
}

// Course represents a course in the system
type Course struct {
	models.BaseModel
	CourseCode       string        `json:"course_code" gorm:"uniqueIndex;not null;size:20"`
	Name             string        `json:"name" gorm:"not null;size:200;index"`
	Description      string        `json:"description" gorm:"type:text"`
	Level            CourseLevel   `json:"level" gorm:"not null;index"`
	Status           CourseStatus  `json:"status" gorm:"not null;default:'DRAFT';index"`
	
	// Course details
	Duration         int           `json:"duration" gorm:"not null"` // Duration in hours
	MaxStudents      int           `json:"max_students" gorm:"not null;default:30"`
	Price            float64       `json:"price" gorm:"not null"`
	Currency         string        `json:"currency" gorm:"not null;default:'USD';size:3"`
	
	// Schedule information
	StartDate        *time.Time    `json:"start_date" gorm:"index"`
	EndDate          *time.Time    `json:"end_date" gorm:"index"`
	
	// Prerequisites and requirements
	Prerequisites    string        `json:"prerequisites" gorm:"type:text"`
	Requirements     string        `json:"requirements" gorm:"type:text"`
	LearningOutcomes string        `json:"learning_outcomes" gorm:"type:text"`
	
	// Instructor assignment
	InstructorID     *uuid.UUID    `json:"instructor_id" gorm:"index"`
	Instructor       *Teacher      `json:"instructor,omitempty" gorm:"foreignKey:InstructorID"`
	
	// Relationships
	Enrollments      []Enrollment  `json:"enrollments,omitempty" gorm:"foreignKey:CourseID"`
	Schedules        []Schedule    `json:"schedules,omitempty" gorm:"foreignKey:CourseID"`
	Grades           []Grade       `json:"grades,omitempty" gorm:"foreignKey:CourseID"`
	
	// Metadata (no foreign key constraint - references auth service)
	CreatedByUserID  uuid.UUID     `json:"created_by_id" gorm:"column:created_by_id;not null"`
	CreatedBy        *models.User  `json:"created_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
}

// IsActive checks if the course is active
func (c *Course) IsActive() bool {
	return c.Status == CourseStatusActive
}

// CanEnroll checks if students can enroll in this course
func (c *Course) CanEnroll() bool {
	return c.Status == CourseStatusActive && (c.StartDate == nil || c.StartDate.After(time.Now()))
}

// GetEnrolledCount returns the number of enrolled students (would be calculated in service)
func (c *Course) GetEnrolledCount() int {
	return len(c.Enrollments)
}

// HasAvailableSlots checks if the course has available enrollment slots
func (c *Course) HasAvailableSlots() bool {
	return c.GetEnrolledCount() < c.MaxStudents
}

// CourseCreateRequest represents a request to create a course
type CourseCreateRequest struct {
	CourseCode       string      `json:"course_code" binding:"required,min=2,max=20"`
	Name             string      `json:"name" binding:"required,min=2,max=200"`
	Description      string      `json:"description"`
	Level            CourseLevel `json:"level" binding:"required"`
	Duration         int         `json:"duration" binding:"required,min=1"`
	MaxStudents      int         `json:"max_students" binding:"required,min=1,max=100"`
	Price            float64     `json:"price" binding:"required,min=0"`
	Currency         string      `json:"currency" binding:"required,len=3"`
	StartDate        *time.Time  `json:"start_date"`
	EndDate          *time.Time  `json:"end_date"`
	Prerequisites    string      `json:"prerequisites"`
	Requirements     string      `json:"requirements"`
	LearningOutcomes string      `json:"learning_outcomes"`
	InstructorID     *uuid.UUID  `json:"instructor_id"`
}

// CourseUpdateRequest represents a request to update a course
type CourseUpdateRequest struct {
	Name             *string       `json:"name" binding:"omitempty,min=2,max=200"`
	Description      *string       `json:"description"`
	Level            *CourseLevel  `json:"level"`
	Status           *CourseStatus `json:"status"`
	Duration         *int          `json:"duration" binding:"omitempty,min=1"`
	MaxStudents      *int          `json:"max_students" binding:"omitempty,min=1,max=100"`
	Price            *float64      `json:"price" binding:"omitempty,min=0"`
	Currency         *string       `json:"currency" binding:"omitempty,len=3"`
	StartDate        *time.Time    `json:"start_date"`
	EndDate          *time.Time    `json:"end_date"`
	Prerequisites    *string       `json:"prerequisites"`
	Requirements     *string       `json:"requirements"`
	LearningOutcomes *string       `json:"learning_outcomes"`
	InstructorID     *uuid.UUID    `json:"instructor_id"`
}

// CourseResponse represents a course response
type CourseResponse struct {
	ID               uuid.UUID     `json:"id"`
	CourseCode       string        `json:"course_code"`
	Name             string        `json:"name"`
	Description      string        `json:"description"`
	Level            CourseLevel   `json:"level"`
	Status           CourseStatus  `json:"status"`
	Duration         int           `json:"duration"`
	MaxStudents      int           `json:"max_students"`
	EnrolledStudents int           `json:"enrolled_students"`
	AvailableSlots   int           `json:"available_slots"`
	Price            float64       `json:"price"`
	Currency         string        `json:"currency"`
	StartDate        *time.Time    `json:"start_date"`
	EndDate          *time.Time    `json:"end_date"`
	Prerequisites    string        `json:"prerequisites"`
	Requirements     string        `json:"requirements"`
	LearningOutcomes string        `json:"learning_outcomes"`
	InstructorID     *uuid.UUID    `json:"instructor_id"`
	InstructorName   string        `json:"instructor_name"`
	CreatedByID      uuid.UUID     `json:"created_by_id"`
	CreatedByName    string        `json:"created_by_name"`
	CreatedAt        time.Time     `json:"created_at"`
	UpdatedAt        time.Time     `json:"updated_at"`
}

// ToResponse converts a Course to CourseResponse
func (c *Course) ToResponse() *CourseResponse {
	enrolledCount := c.GetEnrolledCount()
	response := &CourseResponse{
		ID:               c.ID,
		CourseCode:       c.CourseCode,
		Name:             c.Name,
		Description:      c.Description,
		Level:            c.Level,
		Status:           c.Status,
		Duration:         c.Duration,
		MaxStudents:      c.MaxStudents,
		EnrolledStudents: enrolledCount,
		AvailableSlots:   c.MaxStudents - enrolledCount,
		Price:            c.Price,
		Currency:         c.Currency,
		StartDate:        c.StartDate,
		EndDate:          c.EndDate,
		Prerequisites:    c.Prerequisites,
		Requirements:     c.Requirements,
		LearningOutcomes: c.LearningOutcomes,
		InstructorID:     c.InstructorID,
		CreatedByID:      c.CreatedByUserID,
		CreatedAt:        c.CreatedAt,
		UpdatedAt:        c.UpdatedAt,
	}

	// Include instructor information if available
	if c.Instructor != nil {
		response.InstructorName = c.Instructor.GetFullName()
	}

	// Include created by user information if available
	if c.CreatedBy != nil {
		response.CreatedByName = c.CreatedBy.GetFullName()
	}

	return response
}

// CourseListRequest represents a request to list courses
type CourseListRequest struct {
	models.PaginationRequest
	Status       *CourseStatus `json:"status" form:"status"`
	Level        *CourseLevel  `json:"level" form:"level"`
	InstructorID *uuid.UUID    `json:"instructor_id" form:"instructor_id"`
	CreatedByID  *uuid.UUID    `json:"created_by_id" form:"created_by_id"`
	Search       string        `json:"search" form:"search"`
	StartDate    *time.Time    `json:"start_date" form:"start_date"`
	EndDate      *time.Time    `json:"end_date" form:"end_date"`
	MinPrice     *float64      `json:"min_price" form:"min_price"`
	MaxPrice     *float64      `json:"max_price" form:"max_price"`
	Available    *bool         `json:"available" form:"available"` // Has available slots
}

// CourseListResponse represents a response for course list
type CourseListResponse struct {
	Courses    []*CourseResponse          `json:"courses"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// CourseStats represents course statistics
type CourseStats struct {
	TotalCourses       int64                    `json:"total_courses"`
	CoursesByStatus    map[CourseStatus]int64   `json:"courses_by_status"`
	CoursesByLevel     map[CourseLevel]int64    `json:"courses_by_level"`
	AveragePrice       *float64                 `json:"average_price"`
	TotalRevenue       *float64                 `json:"total_revenue"`
	AverageEnrollment  *float64                 `json:"average_enrollment"`
	PopularCourses     []*CourseResponse        `json:"popular_courses"`
}

// CourseInstructorAssignRequest represents a request to assign an instructor to a course
type CourseInstructorAssignRequest struct {
	InstructorID uuid.UUID `json:"instructor_id" binding:"required"`
}

// CourseStatusUpdateRequest represents a request to update course status
type CourseStatusUpdateRequest struct {
	Status CourseStatus `json:"status" binding:"required"`
}
