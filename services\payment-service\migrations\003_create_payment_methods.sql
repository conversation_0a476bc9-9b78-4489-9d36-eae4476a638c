-- Create payment_methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    
    -- Payment method details
    type VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Card information (encrypted/tokenized)
    card_last4 VARCHAR(4),
    card_brand VARCHAR(20),
    card_exp_month INTEGER CHECK (card_exp_month >= 1 AND card_exp_month <= 12),
    card_exp_year INTEGER CHECK (card_exp_year >= 2024),
    
    -- Gateway information
    gateway_type VARCHAR(50) NOT NULL,
    gateway_method_id VARCHAR(255),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for payment_methods table
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods(type);
CREATE INDEX IF NOT EXISTS idx_payment_methods_gateway_type ON payment_methods(gateway_type);
CREATE INDEX IF NOT EXISTS idx_payment_methods_gateway_method_id ON payment_methods(gateway_method_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_default ON payment_methods(is_default);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_active ON payment_methods(is_active);
CREATE INDEX IF NOT EXISTS idx_payment_methods_created_at ON payment_methods(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_methods_deleted_at ON payment_methods(deleted_at);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_active ON payment_methods(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_default ON payment_methods(user_id, is_default);

-- Add check constraints for valid enum values
ALTER TABLE payment_methods ADD CONSTRAINT chk_payment_methods_type 
CHECK (type IN ('CASH', 'CARD', 'UZCARD', 'HUMO', 'PAYME', 'CLICK'));

-- Add comments for documentation
COMMENT ON TABLE payment_methods IS 'Stored payment methods for users';
COMMENT ON COLUMN payment_methods.id IS 'Unique identifier for the payment method';
COMMENT ON COLUMN payment_methods.user_id IS 'Reference to the user who owns this payment method';
COMMENT ON COLUMN payment_methods.type IS 'Type of payment method (CASH, CARD, UZCARD, HUMO, PAYME, CLICK)';
COMMENT ON COLUMN payment_methods.is_default IS 'Whether this is the default payment method for the user';
COMMENT ON COLUMN payment_methods.is_active IS 'Whether this payment method is active and can be used';
COMMENT ON COLUMN payment_methods.card_last4 IS 'Last 4 digits of the card (for display purposes)';
COMMENT ON COLUMN payment_methods.card_brand IS 'Card brand (Visa, MasterCard, etc.)';
COMMENT ON COLUMN payment_methods.card_exp_month IS 'Card expiration month';
COMMENT ON COLUMN payment_methods.card_exp_year IS 'Card expiration year';
COMMENT ON COLUMN payment_methods.gateway_type IS 'Payment gateway that manages this method';
COMMENT ON COLUMN payment_methods.gateway_method_id IS 'Payment method ID from the gateway';
COMMENT ON COLUMN payment_methods.metadata IS 'Additional metadata in JSON format';
