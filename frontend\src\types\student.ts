// Student management types

export type StudentStatus = 'ACTIVE' | 'INACTIVE' | 'GRADUATED' | 'SUSPENDED' | 'TRANSFERRED';

export type EnrollmentStatus = 'ENROLLED' | 'PENDING' | 'COMPLETED' | 'DROPPED' | 'TRANSFERRED';

export type PaymentStatus = 'PAID' | 'PENDING' | 'OVERDUE' | 'PARTIAL';

export interface Student {
  id: string;
  student_id: string; // Unique student identifier
  first_name: string;
  last_name: string;
  full_name: string;
  email?: string;
  phone: string;
  date_of_birth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  address: string;
  city: string;
  country: string;
  status: StudentStatus;
  enrollment_date: string;
  graduation_date?: string;
  
  // Parent/Guardian Information
  parent_name: string;
  parent_phone: string;
  parent_email?: string;
  emergency_contact: string;
  emergency_phone: string;
  
  // Academic Information
  grade_level?: string;
  previous_school?: string;
  notes?: string;
  
  // System fields
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface Course {
  id: string;
  name: string;
  code: string;
  description?: string;
  duration_months: number;
  price: number;
  max_students: number;
  status: 'ACTIVE' | 'INACTIVE' | 'COMPLETED';
  start_date: string;
  end_date?: string;
  teacher_id?: string;
  teacher_name?: string;
  created_at: string;
  updated_at: string;
}

export interface Enrollment {
  id: string;
  student_id: string;
  course_id: string;
  status: EnrollmentStatus;
  enrollment_date: string;
  completion_date?: string;
  grade?: string;
  attendance_percentage?: number;
  payment_status: PaymentStatus;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  notes?: string;
  
  // Related data
  student?: Student;
  course?: Course;
  
  created_at: string;
  updated_at: string;
}

export interface StudentCreateRequest {
  first_name: string;
  last_name: string;
  email?: string;
  phone: string;
  date_of_birth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  address: string;
  city: string;
  country: string;
  
  // Parent/Guardian Information
  parent_name: string;
  parent_phone: string;
  parent_email?: string;
  emergency_contact: string;
  emergency_phone: string;
  
  // Academic Information
  grade_level?: string;
  previous_school?: string;
  notes?: string;
}

export interface StudentUpdateRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  date_of_birth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  address?: string;
  city?: string;
  country?: string;
  status?: StudentStatus;
  
  // Parent/Guardian Information
  parent_name?: string;
  parent_phone?: string;
  parent_email?: string;
  emergency_contact?: string;
  emergency_phone?: string;
  
  // Academic Information
  grade_level?: string;
  previous_school?: string;
  notes?: string;
}

export interface EnrollmentCreateRequest {
  student_id: string;
  course_id: string;
  enrollment_date: string;
  payment_status: PaymentStatus;
  total_amount: number;
  paid_amount: number;
  notes?: string;
}

export interface StudentFilters {
  search: string;
  status: StudentStatus | 'ALL';
  grade_level: string | 'ALL';
  enrollment_status: EnrollmentStatus | 'ALL';
  payment_status: PaymentStatus | 'ALL';
}

export interface StudentStats {
  total_students: number;
  active_students: number;
  new_enrollments_this_month: number;
  graduated_students: number;
  pending_payments: number;
  total_revenue: number;
}

// Course Management Types
export interface CourseCreateRequest {
  name: string;
  code: string;
  description?: string;
  duration_months: number;
  price: number;
  max_students: number;
  start_date: string;
  end_date?: string;
  teacher_id?: string;
}

export interface CourseUpdateRequest {
  name?: string;
  code?: string;
  description?: string;
  duration_months?: number;
  price?: number;
  max_students?: number;
  status?: 'ACTIVE' | 'INACTIVE' | 'COMPLETED';
  start_date?: string;
  end_date?: string;
  teacher_id?: string;
}

export interface CourseFilters {
  search: string;
  status: 'ACTIVE' | 'INACTIVE' | 'COMPLETED' | 'ALL';
  teacher: string | 'ALL';
}

export interface CourseStats {
  total_courses: number;
  active_courses: number;
  completed_courses: number;
  total_enrolled_students: number;
  average_enrollment_rate: number;
  total_course_revenue: number;
}

// Payment Management Types
export type PaymentMethod = 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'ONLINE' | 'CHECK';
export type TransactionType = 'PAYMENT' | 'REFUND' | 'PARTIAL_PAYMENT' | 'LATE_FEE';
export type TransactionStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REFUNDED';

export interface Payment {
  id: string;
  transaction_id: string;
  student_id: string;
  enrollment_id?: string;
  amount: number;
  method: PaymentMethod;
  type: TransactionType;
  status: TransactionStatus;
  description?: string;
  reference_number?: string;

  // Student information
  student_name: string;
  student_id_number: string;

  // Course information
  course_name?: string;
  course_code?: string;

  // Payment details
  payment_date: string;
  due_date?: string;
  late_fee?: number;
  discount?: number;
  tax?: number;

  // Processing information
  processed_by: string;
  processor_name: string;
  notes?: string;

  created_at: string;
  updated_at: string;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  student_id: string;
  enrollment_id?: string;

  // Student information
  student_name: string;
  student_email?: string;
  student_phone: string;
  student_address: string;

  // Course information
  course_name?: string;
  course_code?: string;

  // Financial details
  subtotal: number;
  discount: number;
  tax: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;

  // Dates
  issue_date: string;
  due_date: string;
  payment_date?: string;

  // Status
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';

  // Items
  items: InvoiceItem[];

  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface PaymentCreateRequest {
  student_id: string;
  enrollment_id?: string;
  amount: number;
  method: PaymentMethod;
  type: TransactionType;
  description?: string;
  reference_number?: string;
  due_date?: string;
  notes?: string;
}

export interface PaymentUpdateRequest {
  amount?: number;
  method?: PaymentMethod;
  status?: TransactionStatus;
  description?: string;
  reference_number?: string;
  notes?: string;
}

export interface PaymentFilters {
  search: string;
  status: TransactionStatus | 'ALL';
  method: PaymentMethod | 'ALL';
  type: TransactionType | 'ALL';
  date_from: string;
  date_to: string;
}

export interface PaymentStats {
  total_revenue: number;
  monthly_revenue: number;
  pending_payments: number;
  overdue_payments: number;
  total_transactions: number;
  average_payment_amount: number;
  refunds_this_month: number;
  collection_rate: number;
}

// Lead Management Types
export type LeadStatus = 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'ENROLLED' | 'LOST' | 'FOLLOW_UP';
export type LeadSource = 'WEBSITE' | 'REFERRAL' | 'SOCIAL_MEDIA' | 'ADVERTISEMENT' | 'WALK_IN' | 'PHONE_CALL' | 'EMAIL' | 'OTHER';
export type LeadPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

export interface Lead {
  id: string;
  lead_number: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email?: string;
  phone: string;
  date_of_birth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  address?: string;
  city?: string;
  country?: string;

  // Lead specific information
  status: LeadStatus;
  source: LeadSource;
  priority: LeadPriority;
  interested_courses: string[];
  budget_range?: string;
  preferred_start_date?: string;

  // Parent/Guardian Information (for minors)
  parent_name?: string;
  parent_phone?: string;
  parent_email?: string;

  // Tracking information
  assigned_to?: string;
  assigned_to_name?: string;
  last_contact_date?: string;
  next_follow_up_date?: string;
  conversion_probability?: number; // 0-100

  // Notes and communication
  initial_notes?: string;
  tags?: string[];

  // System fields
  created_at: string;
  updated_at: string;
  created_by: string;
  converted_to_student_id?: string;
  conversion_date?: string;
}

export interface LeadActivity {
  id: string;
  lead_id: string;
  type: 'CALL' | 'EMAIL' | 'MEETING' | 'NOTE' | 'STATUS_CHANGE' | 'FOLLOW_UP';
  title: string;
  description?: string;
  outcome?: string;
  scheduled_date?: string;
  completed_date?: string;
  created_by: string;
  created_by_name: string;
  created_at: string;
}

export interface LeadCreateRequest {
  first_name: string;
  last_name: string;
  email?: string;
  phone: string;
  date_of_birth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  address?: string;
  city?: string;
  country?: string;
  source: LeadSource;
  priority: LeadPriority;
  interested_courses: string[];
  budget_range?: string;
  preferred_start_date?: string;
  parent_name?: string;
  parent_phone?: string;
  parent_email?: string;
  initial_notes?: string;
  tags?: string[];
}

export interface LeadUpdateRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  date_of_birth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  address?: string;
  city?: string;
  country?: string;
  status?: LeadStatus;
  source?: LeadSource;
  priority?: LeadPriority;
  interested_courses?: string[];
  budget_range?: string;
  preferred_start_date?: string;
  parent_name?: string;
  parent_phone?: string;
  parent_email?: string;
  assigned_to?: string;
  next_follow_up_date?: string;
  conversion_probability?: number;
  initial_notes?: string;
  tags?: string[];
}

export interface LeadFilters {
  search: string;
  status: LeadStatus | 'ALL';
  source: LeadSource | 'ALL';
  priority: LeadPriority | 'ALL';
  assigned_to: string | 'ALL';
  date_from: string;
  date_to: string;
}

export interface LeadStats {
  total_leads: number;
  new_leads_this_month: number;
  qualified_leads: number;
  converted_leads: number;
  conversion_rate: number;
  average_conversion_time: number; // in days
  leads_by_source: Record<LeadSource, number>;
  leads_by_status: Record<LeadStatus, number>;
}

// Notification System Types
export type NotificationType = 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'REMINDER';
export type NotificationChannel = 'IN_APP' | 'EMAIL' | 'SMS' | 'PUSH';
export type NotificationStatus = 'UNREAD' | 'READ' | 'ARCHIVED';
export type NotificationPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  status: NotificationStatus;
  channels: NotificationChannel[];

  // Recipients
  recipient_id?: string;
  recipient_type: 'USER' | 'STUDENT' | 'PARENT' | 'ALL_USERS' | 'ALL_STUDENTS';
  recipient_name?: string;

  // Metadata
  category?: string;
  action_url?: string;
  action_text?: string;
  expires_at?: string;

  // Delivery tracking
  sent_at?: string;
  delivered_at?: string;
  read_at?: string;
  clicked_at?: string;

  // System fields
  created_at: string;
  updated_at: string;
  created_by: string;
  created_by_name: string;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  type: NotificationType;
  channels: NotificationChannel[];
  variables: string[]; // Available template variables
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface NotificationPreferences {
  id: string;
  user_id: string;

  // Channel preferences
  email_enabled: boolean;
  sms_enabled: boolean;
  push_enabled: boolean;
  in_app_enabled: boolean;

  // Category preferences
  payment_reminders: boolean;
  course_updates: boolean;
  system_alerts: boolean;
  marketing_messages: boolean;

  // Timing preferences
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone?: string;

  created_at: string;
  updated_at: string;
}

export interface BulkNotification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  channels: NotificationChannel[];

  // Targeting
  target_type: 'ALL_USERS' | 'ALL_STUDENTS' | 'ROLE_BASED' | 'CUSTOM';
  target_roles?: string[];
  target_user_ids?: string[];

  // Scheduling
  send_immediately: boolean;
  scheduled_at?: string;

  // Status
  status: 'DRAFT' | 'SCHEDULED' | 'SENDING' | 'SENT' | 'FAILED';
  total_recipients: number;
  sent_count: number;
  delivered_count: number;
  read_count: number;

  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface NotificationCreateRequest {
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  channels: NotificationChannel[];
  recipient_id?: string;
  recipient_type: 'USER' | 'STUDENT' | 'PARENT' | 'ALL_USERS' | 'ALL_STUDENTS';
  category?: string;
  action_url?: string;
  action_text?: string;
  expires_at?: string;
}

export interface NotificationFilters {
  search: string;
  type: NotificationType | 'ALL';
  status: NotificationStatus | 'ALL';
  priority: NotificationPriority | 'ALL';
  channel: NotificationChannel | 'ALL';
  date_from: string;
  date_to: string;
}

export interface NotificationStats {
  total_notifications: number;
  unread_notifications: number;
  sent_today: number;
  delivered_today: number;
  read_rate: number;
  click_rate: number;
  notifications_by_type: Record<NotificationType, number>;
  notifications_by_channel: Record<NotificationChannel, number>;
}
