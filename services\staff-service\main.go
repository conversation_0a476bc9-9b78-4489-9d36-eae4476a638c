package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"staff-service/internal/config"
	"staff-service/internal/handler"
	"staff-service/internal/repository"
	"staff-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/shared/database"
	"github.com/crm-microservices/shared/middleware"
	"github.com/crm-microservices/shared/utils"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	dbManager, err := createDatabaseConnection(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer dbManager.Close()

	db := dbManager.GetDB()

	// Verify database connection and tables exist (tables are pre-created)
	if err := verifyDatabaseTables(dbManager); err != nil {
		log.Fatalf("Failed to verify database tables: %v", err)
	}

	// Initialize repositories
	leadRepo := repository.NewLeadRepository(db)
	studentRepo := repository.NewStudentRepository(db)
	courseRepo := repository.NewCourseRepository(db)
	teacherRepo := repository.NewTeacherRepository(db)
	enrollmentRepo := repository.NewEnrollmentRepository(db)
	gradeRepo := repository.NewGradeRepository(db)
	attendanceRepo := repository.NewAttendanceRepository(db)

	// Initialize services
	leadService := service.NewLeadService(leadRepo, studentRepo, courseRepo)
	studentService := service.NewStudentService(studentRepo, enrollmentRepo, gradeRepo, attendanceRepo)
	courseService := service.NewCourseService(courseRepo, teacherRepo, enrollmentRepo)
	teacherService := service.NewTeacherService(teacherRepo, courseRepo)
	enrollmentService := service.NewEnrollmentService(enrollmentRepo, studentRepo, courseRepo)

	// Initialize handlers
	leadHandler := handler.NewLeadHandler(leadService)
	studentHandler := handler.NewStudentHandler(studentService)
	courseHandler := handler.NewCourseHandler(courseService)
	teacherHandler := handler.NewTeacherHandler(teacherService)
	enrollmentHandler := handler.NewEnrollmentHandler(enrollmentService)

	// Setup Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	// Initialize JWT manager and auth middleware
	jwtManager := utils.NewJWTManager(
		cfg.JWTSecret,
		15*time.Minute,  // access token TTL
		7*24*time.Hour,  // refresh token TTL (7 days)
		"crm-staff-service",
		"crm-system",
	)
	authMiddleware := middleware.NewAuthMiddleware(jwtManager)

	// Health check endpoint (no auth required)
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "staff-service",
			"time":    time.Now().UTC(),
		})
	})

	// API routes with authentication
	api := router.Group("/api/v1")
	api.Use(authMiddleware.RequireAuth())
	api.Use(authMiddleware.RequireStaffAccess()) // ADMIN, CASHIER, RECEPTION, TEACHER, MANAGER, ACADEMIC_MANAGER

	// Lead management routes
	leads := api.Group("/leads")
	{
		leads.GET("", leadHandler.GetLeads)
		leads.GET("/:id", leadHandler.GetLead)
		leads.POST("", leadHandler.CreateLead)
		leads.PUT("/:id", leadHandler.UpdateLead)
		leads.DELETE("/:id", leadHandler.DeleteLead)
		leads.POST("/:id/convert", leadHandler.ConvertLead)
		leads.POST("/:id/assign", leadHandler.AssignLead)
	}

	// Student management routes
	students := api.Group("/students")
	{
		students.GET("", studentHandler.GetStudents)
		students.GET("/:id", studentHandler.GetStudent)
		students.POST("", studentHandler.CreateStudent)
		students.PUT("/:id", studentHandler.UpdateStudent)
		students.DELETE("/:id", studentHandler.DeleteStudent)
		// TODO: Add student enrollments and progress endpoints when implemented
	}

	// Course management routes
	courses := api.Group("/courses")
	{
		courses.GET("", courseHandler.GetCourses)
		courses.GET("/:id", courseHandler.GetCourse)
		courses.POST("", courseHandler.CreateCourse)
		courses.PUT("/:id", courseHandler.UpdateCourse)
		courses.DELETE("/:id", courseHandler.DeleteCourse)
		// TODO: Add course students and schedule endpoints when implemented
	}

	// Teacher management routes
	teachers := api.Group("/teachers")
	{
		teachers.GET("", teacherHandler.GetTeachers)
		teachers.GET("/:id", teacherHandler.GetTeacher)
		teachers.POST("", teacherHandler.CreateTeacher)
		teachers.PUT("/:id", teacherHandler.UpdateTeacher)
		teachers.DELETE("/:id", teacherHandler.DeleteTeacher)
		// TODO: Add teacher courses and schedule endpoints when implemented
	}

	// Enrollment management routes
	enrollments := api.Group("/enrollments")
	{
		enrollments.GET("", enrollmentHandler.GetEnrollments)
		enrollments.GET("/:id", enrollmentHandler.GetEnrollment)
		enrollments.POST("", enrollmentHandler.CreateEnrollment)
		enrollments.PUT("/:id", enrollmentHandler.UpdateEnrollment)
		enrollments.DELETE("/:id", enrollmentHandler.DeleteEnrollment)
	}

	// TODO: Add schedule management routes when schedule service is implemented

	// Start server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%s", cfg.Port),
		Handler: router,
	}

	// Graceful shutdown
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	log.Printf("Staff Service started on port %s", cfg.Port)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// The context is used to inform the server it has 5 seconds to finish
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exiting")
}

// corsMiddleware provides CORS support
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// createDatabaseConnection creates a database connection from URL
func createDatabaseConnection(databaseURL string) (*database.ConnectionManager, error) {
	// Always prioritize DATABASE_URL if provided
	if databaseURL != "" {
		log.Printf("Using DATABASE_URL for connection: %s", databaseURL)
		dbManager := database.NewConnectionManagerFromURL(databaseURL)
		if err := dbManager.Connect(); err != nil {
			log.Printf("Failed to connect with DATABASE_URL: %v", err)
			return nil, fmt.Errorf("failed to connect with DATABASE_URL: %w", err)
		}
		log.Printf("Successfully connected using DATABASE_URL")
		return dbManager, nil
	}

	// Only use individual environment variables if DATABASE_URL is not set
	log.Printf("DATABASE_URL not provided, using individual environment variables for database connection")
	host := getEnv("DB_HOST", "")
	port := getEnv("DB_PORT", "5432")
	user := getEnv("DB_USER", "")
	password := getEnv("DB_PASSWORD", "")
	dbname := getEnv("DB_NAME_STAFF", "")
	sslmode := getEnv("DB_SSL_MODE", "require")

	// If any individual env vars are empty, return error to prevent fallback to wrong database
	if host == "" || user == "" || password == "" || dbname == "" {
		return nil, fmt.Errorf("DATABASE_URL not provided and individual database environment variables are incomplete (host=%s, user=%s, password=***, dbname=%s)", host, user, dbname)
	}

	dbConfig := &database.Config{
		Host:     host,
		Port:     port,
		User:     user,
		Password: password,
		DBName:   dbname,
		SSLMode:  sslmode,
		TimeZone: "UTC",
	}

	log.Printf("Connecting to database: host=%s, port=%s, user=%s, dbname=%s, sslmode=%s",
		host, port, user, dbname, sslmode)

	dbManager := database.NewConnectionManager(dbConfig)
	if err := dbManager.Connect(); err != nil {
		log.Printf("Failed to connect with individual config: %v", err)
		return nil, fmt.Errorf("failed to connect with individual config: %w", err)
	}

	return dbManager, nil
}

// verifyDatabaseTables verifies that required tables exist
func verifyDatabaseTables(dbManager *database.ConnectionManager) error {
	db := dbManager.GetDB()

	// Verify that required tables exist (tables are pre-created)
	tables := []string{"leads", "students", "teachers", "courses", "enrollments", "schedules", "attendances", "grades"}
	for _, table := range tables {
		err := db.Exec("SELECT 1 FROM " + table + " LIMIT 1").Error
		if err != nil {
			return fmt.Errorf("failed to connect to database or table %s doesn't exist: %w", table, err)
		}
	}

	log.Println("Database connection verified - using pre-created tables")
	return nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
