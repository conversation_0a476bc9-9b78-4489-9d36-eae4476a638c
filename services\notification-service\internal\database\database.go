package database

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(databaseURL string) (*gorm.DB, error) {
	// Configure GORM logger
	gormLogger := logger.Default.LogMode(logger.Info)

	// Connect to database
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying SQL DB for connection pooling
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying SQL DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	log.Println("Database connection established successfully")
	return db, nil
}

// VerifyDatabaseTables verifies that required tables exist
func VerifyDatabaseTables(db *gorm.DB) error {
	log.Println("Verifying database tables...")

	// Verify that required tables exist (tables are pre-created)
	tables := []string{"notification_templates", "notifications", "notification_preferences", "notification_queue", "delivery_logs"}
	for _, table := range tables {
		err := db.Exec("SELECT 1 FROM " + table + " LIMIT 1").Error
		if err != nil {
			return fmt.Errorf("failed to connect to database or table %s doesn't exist: %w", table, err)
		}
	}

	log.Println("Database connection verified - using pre-created tables")
	return nil
}


