# ⚙️ Configuration Guide - Go Docker Platform

Comprehensive configuration guide for environment variables, service settings, and security configurations.

## 📋 Overview

The CRM microservices system uses environment-based configuration for maximum flexibility and security. Each service can be configured independently while maintaining consistency across the system.

## 🔧 Core Configuration

### Environment Variables Structure

#### Service Identification
```env
# Service Configuration
PORT=8081                           # Service port
ENVIRONMENT=production              # Environment (development/staging/production)
SERVICE_NAME=auth-service          # Service identifier
LOG_LEVEL=info                     # Logging level (debug/info/warn/error)
```

#### Database Configuration
```env
# Database Connection
DATABASE_URL=postgresql://user:pass@host:port/db?sslmode=require&channel_binding=require
AUTO_MIGRATE=false                 # Disable auto-migration in production
DB_MAX_OPEN_CONNS=25              # Maximum open connections
DB_MAX_IDLE_CONNS=5               # Maximum idle connections
DB_CONN_MAX_LIFETIME=5m           # Connection maximum lifetime
```

#### Security Configuration
```env
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=15m                    # Access token expiry
REFRESH_TOKEN_EXPIRY=7d           # Refresh token expiry
JWT_ISSUER=crm-microservices      # Token issuer
JWT_AUDIENCE=crm-users            # Token audience

# Password Security
BCRYPT_COST=12                    # bcrypt cost factor
PASSWORD_MIN_LENGTH=8             # Minimum password length
PASSWORD_REQUIRE_SPECIAL=true     # Require special characters
PASSWORD_REQUIRE_NUMBER=true      # Require numbers
PASSWORD_REQUIRE_UPPERCASE=true   # Require uppercase letters
```

## 🌐 Service-Specific Configuration

### Auth Service Configuration
```env
# Auth Service (Port: 8081)
PORT=8081
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
REDIS_URL=redis://localhost:6379
AUTO_MIGRATE=false

# Session Management
SESSION_TIMEOUT=30m
SESSION_CLEANUP_INTERVAL=1h
MAX_SESSIONS_PER_USER=5

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=1m
RATE_LIMIT_BURST=10

# Security Headers
CORS_ALLOWED_ORIGINS=https://your-frontend.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
```

### Admin Service Configuration
```env
# Admin Service (Port: 8082)
PORT=8082
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
AUTO_MIGRATE=false

# Pagination
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# Analytics
ANALYTICS_RETENTION_DAYS=365
ANALYTICS_BATCH_SIZE=1000

# Audit Logging
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years
AUDIT_LOG_SENSITIVE_FIELDS=password,credit_card
```

### Staff Service Configuration
```env
# Staff Service (Port: 8083)
PORT=8083
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
AUTO_MIGRATE=false

# Lead Management
LEAD_AUTO_ASSIGNMENT=true
LEAD_FOLLOW_UP_DAYS=3
LEAD_EXPIRY_DAYS=30

# Student Management
STUDENT_ID_PREFIX=STU
ENROLLMENT_GRACE_PERIOD_DAYS=7

# Course Management
COURSE_MAX_CAPACITY=20
COURSE_MIN_ENROLLMENT=5
```

### Payment Service Configuration
```env
# Payment Service (Port: 8084)
PORT=8084
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
STAFF_SERVICE_URL=https://crm-staff-service.onrender.com
AUTO_MIGRATE=false

# Payment Gateways
UZCARD_API_URL=https://api.uzcard.uz
UZCARD_API_KEY=your-uzcard-key
UZCARD_MERCHANT_ID=your-uzcard-merchant-id

HUMO_API_URL=https://api.humo.uz
HUMO_API_KEY=your-humo-key
HUMO_MERCHANT_ID=your-humo-merchant-id

PAYME_API_URL=https://api.payme.uz
PAYME_API_KEY=your-payme-key
PAYME_MERCHANT_ID=your-payme-merchant-id

CLICK_API_URL=https://api.click.uz
CLICK_API_KEY=your-click-key
CLICK_MERCHANT_ID=your-click-merchant-id

# Payment Processing
PAYMENT_TIMEOUT=30s
PAYMENT_RETRY_ATTEMPTS=3
PAYMENT_RETRY_DELAY=5s
REFUND_PROCESSING_DAYS=7
```

### Notification Service Configuration
```env
# Notification Service (Port: 8085)
PORT=8085
ENVIRONMENT=production
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
AUTO_MIGRATE=false

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=CRM System
SMTP_TIMEOUT=30s

# SMS Configuration
SMS_PROVIDER=eskiz
SMS_API_URL=https://notify.eskiz.uz/api
SMS_API_TOKEN=your-sms-api-token
SMS_FROM=4546
SMS_TIMEOUT=30s

# Notification Processing
NOTIFICATION_BATCH_SIZE=100
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=5m
NOTIFICATION_QUEUE_SIZE=1000
```

### API Gateway Configuration
```env
# API Gateway (Port: 8080)
PORT=8080
ENVIRONMENT=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Service URLs
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
STAFF_SERVICE_URL=https://crm-staff-service.onrender.com
PAYMENT_SERVICE_URL=https://crm-payment-service.onrender.com
NOTIFICATION_SERVICE_URL=https://crm-notification-service.onrender.com

# Load Balancing
LOAD_BALANCER_STRATEGY=round_robin
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=5s
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60s

# Rate Limiting
GLOBAL_RATE_LIMIT=1000
SERVICE_RATE_LIMIT=200
USER_RATE_LIMIT=100
RATE_LIMIT_WINDOW=1m

# Timeouts
REQUEST_TIMEOUT=30s
IDLE_TIMEOUT=60s
READ_TIMEOUT=10s
WRITE_TIMEOUT=10s
```

## 🔐 Security Configuration

### TLS/SSL Configuration
```env
# TLS Settings
TLS_ENABLED=true
TLS_CERT_FILE=/path/to/cert.pem
TLS_KEY_FILE=/path/to/key.pem
TLS_MIN_VERSION=1.2
TLS_CIPHER_SUITES=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305

# Certificate Management
CERT_AUTO_RENEWAL=true
CERT_RENEWAL_DAYS=30
CERT_PROVIDER=letsencrypt
```

### Authentication & Authorization
```env
# JWT Configuration
JWT_ALGORITHM=RS256
JWT_PUBLIC_KEY_PATH=/path/to/public.pem
JWT_PRIVATE_KEY_PATH=/path/to/private.pem
JWT_KEY_ID=key-1

# OAuth Configuration (if applicable)
OAUTH_GOOGLE_CLIENT_ID=your-google-client-id
OAUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
OAUTH_REDIRECT_URL=https://your-app.com/auth/callback

# API Key Management
API_KEY_HEADER=X-API-Key
API_KEY_EXPIRY=365d
API_KEY_RATE_LIMIT=1000
```

### Data Protection
```env
# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key
ENCRYPTION_ALGORITHM=AES-256-GCM
FIELD_ENCRYPTION_ENABLED=true

# Data Retention
DATA_RETENTION_DAYS=2555  # 7 years
LOG_RETENTION_DAYS=90
BACKUP_RETENTION_DAYS=30
TEMP_FILE_CLEANUP_HOURS=24

# Privacy
GDPR_COMPLIANCE=true
DATA_ANONYMIZATION=true
CONSENT_TRACKING=true
```

## 📊 Monitoring Configuration

### Logging Configuration
```env
# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_FILE_PATH=/var/log/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_BACKUPS=5
LOG_MAX_AGE=30

# Structured Logging
LOG_INCLUDE_CALLER=true
LOG_INCLUDE_TIMESTAMP=true
LOG_INCLUDE_REQUEST_ID=true
LOG_CORRELATION_ID_HEADER=X-Correlation-ID
```

### Metrics Configuration
```env
# Metrics
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics
METRICS_NAMESPACE=crm
METRICS_SUBSYSTEM=microservice

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=5s
```

### Tracing Configuration
```env
# Distributed Tracing
TRACING_ENABLED=true
TRACING_ENDPOINT=http://jaeger:14268/api/traces
TRACING_SERVICE_NAME=crm-service
TRACING_SAMPLE_RATE=0.1
```

## 🚀 Performance Configuration

### Caching Configuration
```env
# Redis Caching
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=30s

# Cache Settings
CACHE_TTL=1h
CACHE_PREFIX=crm:
CACHE_COMPRESSION=true
CACHE_SERIALIZATION=json
```

### Database Performance
```env
# Connection Pool
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m
DB_CONN_MAX_IDLE_TIME=10m

# Query Performance
DB_SLOW_QUERY_THRESHOLD=1s
DB_QUERY_TIMEOUT=30s
DB_PREPARED_STATEMENTS=true
DB_BATCH_SIZE=1000
```

### Resource Limits
```env
# Memory Limits
MAX_MEMORY_USAGE=512MB
GC_TARGET_PERCENTAGE=70
GC_MAX_PAUSE=10ms

# CPU Limits
MAX_CPU_USAGE=80%
GOROUTINE_LIMIT=10000
WORKER_POOL_SIZE=10

# File Limits
MAX_FILE_SIZE=10MB
MAX_UPLOAD_SIZE=50MB
TEMP_DIR=/tmp
MAX_TEMP_FILES=1000
```

## 🔄 Environment-Specific Configurations

### Development Environment
```env
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=debug
AUTO_MIGRATE=true
SEED_DATA=true
HOT_RELOAD=true
CORS_ALLOWED_ORIGINS=*
RATE_LIMIT_ENABLED=false
```

### Staging Environment
```env
ENVIRONMENT=staging
DEBUG=false
LOG_LEVEL=info
AUTO_MIGRATE=false
SEED_DATA=false
HOT_RELOAD=false
CORS_ALLOWED_ORIGINS=https://staging.yourapp.com
RATE_LIMIT_ENABLED=true
```

### Production Environment
```env
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=warn
AUTO_MIGRATE=false
SEED_DATA=false
HOT_RELOAD=false
CORS_ALLOWED_ORIGINS=https://yourapp.com
RATE_LIMIT_ENABLED=true
SECURITY_HEADERS_ENABLED=true
```

## 🛠️ Configuration Management

### Environment File Structure
```
.env                    # Default environment variables
.env.local             # Local overrides (not in git)
.env.development       # Development-specific variables
.env.staging           # Staging-specific variables
.env.production        # Production-specific variables
```

### Configuration Validation
```go
// Example configuration validation
func ValidateConfig(cfg *Config) error {
    if cfg.DatabaseURL == "" {
        return errors.New("DATABASE_URL is required")
    }
    if cfg.JWTSecret == "" || len(cfg.JWTSecret) < 32 {
        return errors.New("JWT_SECRET must be at least 32 characters")
    }
    if cfg.Port < 1024 || cfg.Port > 65535 {
        return errors.New("PORT must be between 1024 and 65535")
    }
    return nil
}
```

### Configuration Loading Priority
1. **Environment Variables** (highest priority)
2. **Environment-specific files** (.env.production)
3. **Local environment file** (.env.local)
4. **Default environment file** (.env)
5. **Default values in code** (lowest priority)

## 🔧 Configuration Best Practices

### Security Best Practices
- **Never commit secrets** to version control
- **Use strong, unique secrets** for each environment
- **Rotate secrets regularly** (every 90 days)
- **Use environment-specific configurations**
- **Validate all configuration values**

### Performance Best Practices
- **Tune connection pools** based on load
- **Configure appropriate timeouts**
- **Enable caching** for frequently accessed data
- **Monitor resource usage** and adjust limits
- **Use compression** for large payloads

### Operational Best Practices
- **Document all configuration options**
- **Use consistent naming conventions**
- **Provide sensible defaults**
- **Validate configurations on startup**
- **Log configuration changes**

## 🆘 Troubleshooting Configuration

### Common Configuration Issues

#### Database Connection Issues
```bash
# Test database connectivity
psql "$DATABASE_URL" -c "SELECT 1;"

# Check connection pool status
curl http://localhost:8081/health/db
```

#### JWT Configuration Issues
```bash
# Validate JWT secret length
echo -n "$JWT_SECRET" | wc -c  # Should be >= 32

# Test JWT token generation
curl -X POST http://localhost:8081/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

#### Service Communication Issues
```bash
# Test service URLs
curl -I "$AUTH_SERVICE_URL/health"
curl -I "$ADMIN_SERVICE_URL/health"
curl -I "$STAFF_SERVICE_URL/health"
```

### Configuration Debugging
```bash
# Check environment variables
env | grep -E "(PORT|DATABASE_URL|JWT_SECRET)" | head -10

# Validate configuration file
cat .env | grep -v '^#' | grep -v '^$'

# Test configuration loading
go run -tags debug main.go --validate-config
```

---

**Configuration complete! ⚙️**

Your CRM microservices system is now properly configured with secure, scalable, and maintainable settings for all environments.
