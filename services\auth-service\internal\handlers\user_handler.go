package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/crm-microservices/auth-service/internal/services"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// UserHandler handles user management HTTP requests
type UserHandler struct {
	userService *services.UserService
}

// NewUserHandler creates a new user handler
func NewUserHandler(userService *services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// GetProfile handles getting user profile
// @Summary Get user profile
// @Description Get current user's profile
// @Tags Users
// @Security BearerAuth
// @Success 200 {object} models.APIResponse{data=models.UserResponse}
// @Failure 401 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /users/profile [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, err := h.getUserIDFromContext(c)
	if err != nil {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	user, err := h.userService.GetProfile(userID)
	if err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.SuccessResponse(c, user, "Profile retrieved successfully")
}

// UpdateProfile handles updating user profile
// @Summary Update user profile
// @Description Update current user's profile
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body models.UserUpdateRequest true "Profile update data"
// @Success 200 {object} models.APIResponse{data=models.UserResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /users/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, err := h.getUserIDFromContext(c)
	if err != nil {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	user, err := h.userService.UpdateProfile(userID, &req)
	if err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.ProfileUpdateResponse(c, user)
}

// ChangePassword handles password change
// @Summary Change password
// @Description Change current user's password
// @Tags Users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body models.ChangePasswordRequest true "Password change data"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /users/change-password [post]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	_, err := h.getUserIDFromContext(c)
	if err != nil {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	// This would typically be handled by AuthService, but we can delegate
	// For now, return not implemented
	utils.BadRequestResponse(c, "Password change should be done through /auth/change-password endpoint")
}

// VerifyEmail handles email verification
// @Summary Verify email
// @Description Verify user's email address
// @Tags Users
// @Security BearerAuth
// @Success 200 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /users/verify-email [post]
func (h *UserHandler) VerifyEmail(c *gin.Context) {
	userID, err := h.getUserIDFromContext(c)
	if err != nil {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	if err := h.userService.VerifyEmail(userID); err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.EmailVerificationResponse(c)
}

// ResendVerification handles resending verification email
// @Summary Resend verification email
// @Description Resend email verification link
// @Tags Users
// @Security BearerAuth
// @Success 200 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Router /users/resend-verification [post]
func (h *UserHandler) ResendVerification(c *gin.Context) {
	userID, err := h.getUserIDFromContext(c)
	if err != nil {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	// Get user profile to send verification email
	user, err := h.userService.GetProfile(userID)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	// In a real implementation, you would send verification email here
	// For now, just return success
	utils.SuccessResponse(c, map[string]string{"email": user.Email}, "Verification email sent")
}

// Admin-only endpoints

// ListUsers handles listing users (admin only)
// @Summary List users
// @Description Get paginated list of users (admin only)
// @Tags Admin
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Page size" default(10)
// @Param role query string false "Filter by role"
// @Param status query string false "Filter by status"
// @Param search query string false "Search term"
// @Success 200 {object} models.APIResponse{data=models.UserListResponse}
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Router /admin/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// Parse query parameters
	req := &models.UserListRequest{
		PaginationRequest: models.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			req.PageSize = ps
		}
	}

	if role := c.Query("role"); role != "" {
		userRole := models.UserRole(role)
		if userRole.IsValid() {
			req.Role = &userRole
		}
	}

	if status := c.Query("status"); status != "" {
		userStatus := models.UserStatus(status)
		if userStatus.IsValid() {
			req.Status = &userStatus
		}
	}

	req.Search = c.Query("search")

	response, err := h.userService.ListUsers(req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.PaginatedResponse(c, response.Users, response.Pagination, "Users retrieved successfully")
}

// GetUser handles getting a specific user (admin only)
// @Summary Get user
// @Description Get user by ID (admin only)
// @Tags Admin
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} models.APIResponse{data=models.UserResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /admin/users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	userID, err := h.parseUserIDFromParam(c)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	user, err := h.userService.GetUser(userID)
	if err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.SuccessResponse(c, user, "User retrieved successfully")
}

// CreateUser handles creating a new user (admin only)
// @Summary Create user
// @Description Create a new user (admin only)
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body models.UserCreateRequest true "User creation data"
// @Success 201 {object} models.APIResponse{data=models.UserResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Router /admin/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	user, err := h.userService.CreateUser(&req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.CreatedResponse(c, user, "User created successfully")
}

// UpdateUser handles updating a user (admin only)
// @Summary Update user
// @Description Update user by ID (admin only)
// @Tags Admin
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "User ID"
// @Param request body models.UserUpdateRequest true "User update data"
// @Success 200 {object} models.APIResponse{data=models.UserResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /admin/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := h.parseUserIDFromParam(c)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request format")
		return
	}

	user, err := h.userService.UpdateUser(userID, &req)
	if err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.SuccessResponse(c, user, "User updated successfully")
}

// DeleteUser handles deleting a user (admin only)
// @Summary Delete user
// @Description Delete user by ID (admin only)
// @Tags Admin
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /admin/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := h.parseUserIDFromParam(c)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	if err := h.userService.DeleteUser(userID); err != nil {
		switch err {
		case services.ErrUserNotFound:
			utils.NotFoundResponse(c, "User not found")
		default:
			utils.HandleError(c, err)
		}
		return
	}

	utils.SuccessResponse(c, nil, "User deleted successfully")
}

// ActivateUser handles activating a user (admin only)
// @Summary Activate user
// @Description Activate user account (admin only)
// @Tags Admin
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /admin/users/{id}/activate [post]
func (h *UserHandler) ActivateUser(c *gin.Context) {
	userID, err := h.parseUserIDFromParam(c)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	if err := h.userService.ActivateUser(userID); err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, nil, "User activated successfully")
}

// DeactivateUser handles deactivating a user (admin only)
// @Summary Deactivate user
// @Description Deactivate user account (admin only)
// @Tags Admin
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Router /admin/users/{id}/deactivate [post]
func (h *UserHandler) DeactivateUser(c *gin.Context) {
	userID, err := h.parseUserIDFromParam(c)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	if err := h.userService.DeactivateUser(userID); err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, nil, "User deactivated successfully")
}

// Helper methods

func (h *UserHandler) getUserIDFromContext(c *gin.Context) (uuid.UUID, error) {
	userIDStr, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, services.ErrUserNotFound
	}

	userIDUUID, ok := userIDStr.(uuid.UUID)
	if !ok {
		// Try to parse as string
		userIDString, ok := userIDStr.(string)
		if !ok {
			return uuid.Nil, services.ErrUserNotFound
		}
		return uuid.Parse(userIDString)
	}

	return userIDUUID, nil
}

func (h *UserHandler) parseUserIDFromParam(c *gin.Context) (uuid.UUID, error) {
	userIDStr := c.Param("id")
	return uuid.Parse(userIDStr)
}
