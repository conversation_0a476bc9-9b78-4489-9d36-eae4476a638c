// Re-export from auth types for compatibility
export type {
  User,
  UserRole,
  UserStatus,
  UserCreateRequest,
  UserUpdateRequest
} from './auth';

// Additional user-specific types
export interface UserFilters {
  search?: string;
  role?: string;
  status?: string;
  email_verified?: boolean;
}

export interface UserStats {
  total_users: number;
  active_users: number;
  inactive_users: number;
  suspended_users: number;
  users_by_role: Record<string, number>;
  recent_registrations: number;
  verified_emails: number;
  two_factor_enabled: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
