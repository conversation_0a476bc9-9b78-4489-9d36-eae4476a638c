-- Admin Service Database Initialization Script
-- Database: ep-red-heart (Admin Service)
-- Connection: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- gen_random_uuid() function is already available in PostgreSQL 13+

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS audit_logs CASCADE;
DROP TABLE IF EXISTS system_settings CASCADE;
DROP TABLE IF EXISTS dashboard_widgets CASCADE;

-- Create system_settings table
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    category VARCHAR(100),
    data_type VARCHAR(50) DEFAULT 'string',
    is_public BOOLEAN DEFAULT FALSE,
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_by_id UUID NOT NULL,
    updated_by_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create audit_logs table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create dashboard_widgets table
CREATE TABLE dashboard_widgets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    widget_type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    configuration JSONB,
    position_x INTEGER DEFAULT 0,
    position_y INTEGER DEFAULT 0,
    width INTEGER DEFAULT 1,
    height INTEGER DEFAULT 1,
    is_visible BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for system_settings table
CREATE INDEX idx_system_settings_key ON system_settings(key);
CREATE INDEX idx_system_settings_category ON system_settings(category);
CREATE INDEX idx_system_settings_is_public ON system_settings(is_public);
CREATE INDEX idx_system_settings_created_by_id ON system_settings(created_by_id);
CREATE INDEX idx_system_settings_deleted_at ON system_settings(deleted_at);

-- Create indexes for audit_logs table
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX idx_audit_logs_resource_id ON audit_logs(resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Create indexes for dashboard_widgets table
CREATE INDEX idx_dashboard_widgets_user_id ON dashboard_widgets(user_id);
CREATE INDEX idx_dashboard_widgets_widget_type ON dashboard_widgets(widget_type);
CREATE INDEX idx_dashboard_widgets_is_visible ON dashboard_widgets(is_visible);
CREATE INDEX idx_dashboard_widgets_deleted_at ON dashboard_widgets(deleted_at);

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_system_settings_updated_at 
    BEFORE UPDATE ON system_settings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dashboard_widgets_updated_at 
    BEFORE UPDATE ON dashboard_widgets 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add check constraints
ALTER TABLE system_settings ADD CONSTRAINT chk_system_settings_data_type 
    CHECK (data_type IN ('string', 'number', 'boolean', 'json', 'array'));

ALTER TABLE dashboard_widgets ADD CONSTRAINT chk_dashboard_widgets_position 
    CHECK (position_x >= 0 AND position_y >= 0);

ALTER TABLE dashboard_widgets ADD CONSTRAINT chk_dashboard_widgets_size 
    CHECK (width > 0 AND height > 0);

-- Insert default system settings
INSERT INTO system_settings (key, value, description, category, data_type, is_public, created_by_id) VALUES
('app_name', 'CRM Platform', 'Application name', 'general', 'string', true, gen_random_uuid()),
('app_version', '1.0.0', 'Application version', 'general', 'string', true, gen_random_uuid()),
('maintenance_mode', 'false', 'Enable maintenance mode', 'system', 'boolean', false, gen_random_uuid()),
('max_file_upload_size', '10485760', 'Maximum file upload size in bytes (10MB)', 'system', 'number', false, gen_random_uuid()),
('session_timeout', '3600', 'Session timeout in seconds (1 hour)', 'security', 'number', false, gen_random_uuid()),
('password_min_length', '8', 'Minimum password length', 'security', 'number', true, gen_random_uuid()),
('email_notifications_enabled', 'true', 'Enable email notifications', 'notifications', 'boolean', false, gen_random_uuid()),
('sms_notifications_enabled', 'false', 'Enable SMS notifications', 'notifications', 'boolean', false, gen_random_uuid())
ON CONFLICT (key) DO NOTHING;

COMMIT;
