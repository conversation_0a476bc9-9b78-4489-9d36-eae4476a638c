# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=crm_user
DB_PASSWORD=crm_password
DB_NAME_AUTH=crm_auth
DB_NAME_ADMIN=crm_admin
DB_NAME_STAFF=crm_staff
DB_NAME_PAYMENT=crm_payment
DB_NAME_NOTIFICATION=crm_notification
DB_SSL_MODE=disable

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d
JWT_ISSUER=crm-microservices
JWT_AUDIENCE=crm-users

# Service Ports
API_GATEWAY_PORT=8080
AUTH_SERVICE_PORT=8081
ADMIN_SERVICE_PORT=8082
STAFF_SERVICE_PORT=8083
PAYMENT_SERVICE_PORT=8084
NOTIFICATION_SERVICE_PORT=8085

# Service URLs (for inter-service communication)
AUTH_SERVICE_URL=http://localhost:8081
ADMIN_SERVICE_URL=http://localhost:8082
STAFF_SERVICE_URL=http://localhost:8083
PAYMENT_SERVICE_URL=http://localhost:8084
NOTIFICATION_SERVICE_URL=http://localhost:8085

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=CRM System

# SMS Configuration
SMS_PROVIDER=eskiz
SMS_API_URL=https://notify.eskiz.uz/api
SMS_API_TOKEN=your-sms-api-token
SMS_FROM=4546

# Payment Gateway Configuration
# UzCard
UZCARD_API_URL=https://api.uzcard.uz
UZCARD_API_KEY=your-uzcard-key
UZCARD_MERCHANT_ID=your-uzcard-merchant-id

# Humo
HUMO_API_URL=https://api.humo.uz
HUMO_API_KEY=your-humo-key
HUMO_MERCHANT_ID=your-humo-merchant-id

# Payme
PAYME_API_URL=https://api.payme.uz
PAYME_API_KEY=your-payme-key
PAYME_MERCHANT_ID=your-payme-merchant-id

# Click
CLICK_API_URL=https://api.click.uz
CLICK_API_KEY=your-click-key
CLICK_MERCHANT_ID=your-click-merchant-id

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_OUTPUT=stdout

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=1m

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# File Upload Configuration
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_PATH=./uploads

# Session Configuration
SESSION_TIMEOUT=30m
SESSION_CLEANUP_INTERVAL=1h

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30s

# Development Settings
HOT_RELOAD=true
AUTO_MIGRATE=true
SEED_DATA=true

# Production Settings (override in production)
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=info
# AUTO_MIGRATE=false
# SEED_DATA=false

# Security Settings
BCRYPT_COST=12
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_SPECIAL=true
PASSWORD_REQUIRE_NUMBER=true
PASSWORD_REQUIRE_UPPERCASE=true

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_HOST=localhost:8080
SWAGGER_BASE_PATH=/api/v1

# Cache Configuration
CACHE_TTL=1h
CACHE_CLEANUP_INTERVAL=10m

# Notification Settings
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=5s
NOTIFICATION_BATCH_SIZE=100

# Audit Settings
AUDIT_ENABLED=true
AUDIT_RETENTION_DAYS=365

# Backup Settings
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Feature Flags
FEATURE_PAYMENT_ENABLED=true
FEATURE_SMS_ENABLED=true
FEATURE_EMAIL_ENABLED=true
FEATURE_AUDIT_ENABLED=true
