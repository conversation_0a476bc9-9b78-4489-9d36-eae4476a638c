package handler

import (
	"net/http"
	"strconv"

	"staff-service/internal/models"
	"staff-service/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// TeacherHandler handles teacher-related HTTP requests
type TeacherHandler struct {
	teacherService service.TeacherService
}

// NewTeacherHandler creates a new teacher handler
func NewTeacherHandler(teacherService service.TeacherService) *TeacherHandler {
	return &TeacherHandler{
		teacherService: teacherService,
	}
}

// GetTeachers retrieves teachers with pagination and filtering
// @Summary Get teachers
// @Description Get teachers with pagination and filtering
// @Tags teachers
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Teacher status"
// @Param department query string false "Department"
// @Param search query string false "Search term"
// @Success 200 {object} models.TeacherListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers [get]
func (h *TeacherHandler) GetTeachers(c *gin.Context) {
	req := &models.TeacherListRequest{}

	// Parse pagination
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		} else {
			req.Page = 1
		}
	} else {
		req.Page = 1
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.PageSize = l
		} else {
			req.PageSize = 10
		}
	} else {
		req.PageSize = 10
	}

	// Parse filters
	if status := c.Query("status"); status != "" {
		teacherStatus := models.TeacherStatus(status)
		if teacherStatus.IsValid() {
			req.Status = &teacherStatus
		}
	}

	req.Search = c.Query("search")

	response, err := h.teacherService.GetAll(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTeacher retrieves a teacher by ID
// @Summary Get teacher by ID
// @Description Get a specific teacher by ID
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id} [get]
func (h *TeacherHandler) GetTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	response, err := h.teacherService.GetByID(id)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTeacherByTeacherID retrieves a teacher by teacher ID
// @Summary Get teacher by teacher ID
// @Description Get a specific teacher by teacher ID
// @Tags teachers
// @Accept json
// @Produce json
// @Param teacher_id path string true "Teacher ID"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/by-teacher-id/{teacher_id} [get]
func (h *TeacherHandler) GetTeacherByTeacherID(c *gin.Context) {
	teacherID := c.Param("teacher_id")
	if teacherID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Teacher ID is required"})
		return
	}

	response, err := h.teacherService.GetByTeacherID(teacherID)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTeacherByUserID retrieves a teacher by user ID
// @Summary Get teacher by user ID
// @Description Get a specific teacher by user ID
// @Tags teachers
// @Accept json
// @Produce json
// @Param user_id path string true "User ID"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/by-user-id/{user_id} [get]
func (h *TeacherHandler) GetTeacherByUserID(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.teacherService.GetByUserID(userID)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateTeacher creates a new teacher
// @Summary Create teacher
// @Description Create a new teacher
// @Tags teachers
// @Accept json
// @Produce json
// @Param teacher body models.TeacherCreateRequest true "Teacher data"
// @Success 201 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers [post]
func (h *TeacherHandler) CreateTeacher(c *gin.Context) {
	var req models.TeacherCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.teacherService.Create(&req, createdBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateTeacher updates a teacher
// @Summary Update teacher
// @Description Update an existing teacher
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Param teacher body models.TeacherUpdateRequest true "Teacher update data"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id} [put]
func (h *TeacherHandler) UpdateTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	var req models.TeacherUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.teacherService.Update(id, &req)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteTeacher deletes a teacher
// @Summary Delete teacher
// @Description Delete a teacher
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id} [delete]
func (h *TeacherHandler) DeleteTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	err = h.teacherService.Delete(id)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetTeacherStats retrieves teacher statistics
// @Summary Get teacher statistics
// @Description Get teacher statistics
// @Tags teachers
// @Accept json
// @Produce json
// @Success 200 {object} models.TeacherStats
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/stats [get]
func (h *TeacherHandler) GetTeacherStats(c *gin.Context) {
	stats, err := h.teacherService.GetStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetAvailableTeachers retrieves teachers available for course assignment
// @Summary Get available teachers
// @Description Get teachers available for course assignment
// @Tags teachers
// @Accept json
// @Produce json
// @Success 200 {array} models.TeacherResponse
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/available [get]
func (h *TeacherHandler) GetAvailableTeachers(c *gin.Context) {
	responses, err := h.teacherService.GetAvailableTeachers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetTeacherWorkload calculates a teacher's current workload
// @Summary Get teacher workload
// @Description Calculate a teacher's current workload
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Success 200 {object} repository.TeacherWorkload
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id}/workload [get]
func (h *TeacherHandler) GetTeacherWorkload(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	workload, err := h.teacherService.GetTeacherWorkload(id)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, workload)
}

// GetTopRatedTeachers retrieves top-rated teachers
// @Summary Get top rated teachers
// @Description Get top-rated teachers
// @Tags teachers
// @Accept json
// @Produce json
// @Param limit query int false "Number of teachers to return" default(10)
// @Success 200 {array} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/top-rated [get]
func (h *TeacherHandler) GetTopRatedTeachers(c *gin.Context) {
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 50 {
			limit = l
		}
	}

	responses, err := h.teacherService.GetTopRatedTeachers(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// SuspendTeacher suspends a teacher
// @Summary Suspend teacher
// @Description Suspend a teacher
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Param suspension body models.TeacherSuspensionRequest true "Suspension data"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id}/suspend [post]
func (h *TeacherHandler) SuspendTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	var req models.TeacherSuspensionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	suspendedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.teacherService.SuspendTeacher(id, req.SuspensionReason, suspendedBy)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ReactivateTeacher reactivates a suspended teacher
// @Summary Reactivate teacher
// @Description Reactivate a suspended teacher
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id}/reactivate [post]
func (h *TeacherHandler) ReactivateTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	reactivatedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.teacherService.ReactivateTeacher(id, reactivatedBy)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateTeacherRating updates a teacher's rating
// @Summary Update teacher rating
// @Description Update a teacher's rating
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Param rating body models.TeacherRatingUpdateRequest true "Rating data"
// @Success 200 {object} models.TeacherResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id}/rating [put]
func (h *TeacherHandler) UpdateTeacherRating(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	var req models.TeacherRatingUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.teacherService.UpdateRating(id, req.Rating)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateTeacherPerformanceMetrics updates teacher's performance metrics
// @Summary Update teacher performance metrics
// @Description Update teacher's performance metrics
// @Tags teachers
// @Accept json
// @Produce json
// @Param id path string true "Teacher ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /teachers/{id}/update-metrics [post]
func (h *TeacherHandler) UpdateTeacherPerformanceMetrics(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid teacher ID"})
		return
	}

	err = h.teacherService.UpdatePerformanceMetrics(id)
	if err != nil {
		if err.Error() == "teacher not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Teacher not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Teacher performance metrics updated successfully"})
}
