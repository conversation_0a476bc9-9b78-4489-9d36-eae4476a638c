package services

import (
	"fmt"
	"log"
	"notification-service/internal/config"
	"strings"

	"github.com/twilio/twilio-go"
	twilioApi "github.com/twilio/twilio-go/rest/api/v2010"
)

// SMSService handles SMS operations
type SMSService struct {
	config     *config.Config
	client     *twilio.RestClient
	fromNumber string
}

// NewSMSService creates a new SMS service
func NewSMSService(config *config.Config) *SMSService {
	var client *twilio.RestClient
	
	// Initialize Twilio client if credentials are provided
	if config.TwilioAccountSID != "" && config.TwilioAuthToken != "" {
		client = twilio.NewRestClientWithParams(twilio.ClientParams{
			Username: config.TwilioAccountSID,
			Password: config.TwilioAuthToken,
		})
	}
	
	return &SMSService{
		config:     config,
		client:     client,
		fromNumber: config.TwilioFromNumber,
	}
}

// SendSMS sends an SMS notification
func (s *SMSService) SendSMS(to, message string) error {
	// Validate inputs
	if to == "" {
		return fmt.Errorf("recipient phone number is required")
	}
	if message == "" {
		return fmt.Errorf("SMS message is required")
	}
	
	// Validate configuration
	if err := s.ValidateSMSConfig(); err != nil {
		return fmt.Errorf("SMS configuration invalid: %w", err)
	}
	
	// Format phone number
	formattedTo := s.formatPhoneNumber(to)
	
	// Truncate message if too long (SMS limit is 160 characters for single SMS)
	if len(message) > 1600 { // Allow up to 10 SMS segments
		message = message[:1597] + "..."
	}
	
	// Create SMS parameters
	params := &twilioApi.CreateMessageParams{}
	params.SetTo(formattedTo)
	params.SetFrom(s.fromNumber)
	params.SetBody(message)
	
	// Send SMS via Twilio
	resp, err := s.client.Api.CreateMessage(params)
	if err != nil {
		return fmt.Errorf("failed to send SMS via Twilio: %w", err)
	}
	
	log.Printf("SMS sent successfully to %s, SID: %s", to, *resp.Sid)
	return nil
}

// SendBulkSMS sends SMS to multiple recipients
func (s *SMSService) SendBulkSMS(recipients []string, message string) error {
	if len(recipients) == 0 {
		return fmt.Errorf("no recipients provided")
	}
	
	var errors []string
	successCount := 0
	
	for _, recipient := range recipients {
		if err := s.SendSMS(recipient, message); err != nil {
			errors = append(errors, fmt.Sprintf("%s: %v", recipient, err))
		} else {
			successCount++
		}
	}
	
	log.Printf("Bulk SMS completed: %d successful, %d failed", successCount, len(errors))
	
	if len(errors) > 0 {
		return fmt.Errorf("some SMS messages failed to send: %v", errors)
	}
	
	return nil
}

// SendTemplatedSMS sends an SMS using a template with data
func (s *SMSService) SendTemplatedSMS(to, template string, data map[string]interface{}) error {
	// Process template with data
	processedMessage := s.processTemplate(template, data)
	
	return s.SendSMS(to, processedMessage)
}

// processTemplate processes template content with data
func (s *SMSService) processTemplate(template string, data map[string]interface{}) string {
	result := template
	
	if data != nil {
		for key, value := range data {
			placeholder := fmt.Sprintf("{{%s}}", key)
			replacement := fmt.Sprintf("%v", value)
			
			// Simple string replacement for template variables
			for i := 0; i < len(result); i++ {
				if i+len(placeholder) <= len(result) && result[i:i+len(placeholder)] == placeholder {
					result = result[:i] + replacement + result[i+len(placeholder):]
					i += len(replacement) - 1
				}
			}
		}
	}
	
	return result
}

// formatPhoneNumber formats phone number for Twilio (E.164 format)
func (s *SMSService) formatPhoneNumber(phoneNumber string) string {
	// Remove all non-digit characters
	cleaned := strings.ReplaceAll(phoneNumber, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")
	cleaned = strings.ReplaceAll(cleaned, "(", "")
	cleaned = strings.ReplaceAll(cleaned, ")", "")
	cleaned = strings.ReplaceAll(cleaned, "+", "")
	
	// If it starts with 998 (Uzbekistan), add + prefix
	if strings.HasPrefix(cleaned, "998") {
		return "+" + cleaned
	}
	
	// If it starts with 1 (US/Canada), add + prefix
	if strings.HasPrefix(cleaned, "1") && len(cleaned) == 11 {
		return "+" + cleaned
	}
	
	// If it doesn't start with +, assume it needs country code
	if !strings.HasPrefix(phoneNumber, "+") {
		// Default to Uzbekistan country code for local numbers
		if len(cleaned) == 9 {
			return "+998" + cleaned
		}
		// For other cases, add + if not present
		return "+" + cleaned
	}
	
	return phoneNumber
}

// ValidateSMSConfig validates the SMS configuration
func (s *SMSService) ValidateSMSConfig() error {
	if s.config.TwilioAccountSID == "" {
		return fmt.Errorf("Twilio Account SID is not configured")
	}
	if s.config.TwilioAuthToken == "" {
		return fmt.Errorf("Twilio Auth Token is not configured")
	}
	if s.config.TwilioFromNumber == "" {
		return fmt.Errorf("Twilio From Number is not configured")
	}
	if s.client == nil {
		return fmt.Errorf("Twilio client is not initialized")
	}
	
	return nil
}

// TestSMSConnection tests the SMS service connection
func (s *SMSService) TestSMSConnection() error {
	// Validate configuration first
	if err := s.ValidateSMSConfig(); err != nil {
		return fmt.Errorf("SMS configuration invalid: %w", err)
	}
	
	// Try to get account information to test connection
	account, err := s.client.Api.FetchAccount(s.config.TwilioAccountSID)
	if err != nil {
		return fmt.Errorf("failed to connect to Twilio: %w", err)
	}
	
	log.Printf("SMS connection test successful, Account: %s", *account.FriendlyName)
	return nil
}

// SendClassReminderSMS sends a class reminder SMS
func (s *SMSService) SendClassReminderSMS(to, studentName, className, classTime, room string) error {
	message := fmt.Sprintf(
		"Hi %s! Reminder: Your %s class is scheduled for %s in %s. See you there!",
		studentName, className, classTime, room,
	)
	
	return s.SendSMS(to, message)
}

// SendPaymentReminderSMS sends a payment reminder SMS
func (s *SMSService) SendPaymentReminderSMS(to, studentName, amount, dueDate string) error {
	message := fmt.Sprintf(
		"Hi %s! Your payment of %s is due on %s. Please make your payment to avoid any interruption in your classes.",
		studentName, amount, dueDate,
	)
	
	return s.SendSMS(to, message)
}

// SendWelcomeSMS sends a welcome SMS to new students
func (s *SMSService) SendWelcomeSMS(to, studentName string) error {
	message := fmt.Sprintf(
		"Welcome to Innovative Centre, %s! We're excited to have you join our learning community. Your journey to success starts here!",
		studentName,
	)
	
	return s.SendSMS(to, message)
}

// SendVerificationCodeSMS sends a verification code SMS
func (s *SMSService) SendVerificationCodeSMS(to, code string) error {
	message := fmt.Sprintf(
		"Your Innovative Centre verification code is: %s. This code will expire in 10 minutes. Do not share this code with anyone.",
		code,
	)
	
	return s.SendSMS(to, message)
}

// SendEmergencyNotificationSMS sends an emergency notification SMS
func (s *SMSService) SendEmergencyNotificationSMS(to, message string) error {
	emergencyMessage := fmt.Sprintf("URGENT: %s", message)
	
	return s.SendSMS(to, emergencyMessage)
}

// GetSMSStats returns SMS service statistics
func (s *SMSService) GetSMSStats() map[string]interface{} {
	return map[string]interface{}{
		"twilio_account_sid": s.config.TwilioAccountSID,
		"twilio_from_number": s.config.TwilioFromNumber,
		"sms_configured":     s.ValidateSMSConfig() == nil,
		"client_initialized": s.client != nil,
	}
}

// GetMessageCost estimates the cost of an SMS message
func (s *SMSService) GetMessageCost(message string) map[string]interface{} {
	messageLength := len(message)
	segments := 1
	
	// Calculate SMS segments (160 characters per segment for GSM 7-bit encoding)
	if messageLength > 160 {
		segments = (messageLength + 152) / 153 // 153 chars per segment for multi-part SMS
	}
	
	// Estimated cost (Twilio pricing varies by country)
	estimatedCost := float64(segments) * 0.0075 // $0.0075 per segment (approximate)
	
	return map[string]interface{}{
		"message_length": messageLength,
		"sms_segments":   segments,
		"estimated_cost": estimatedCost,
		"currency":       "USD",
	}
}
