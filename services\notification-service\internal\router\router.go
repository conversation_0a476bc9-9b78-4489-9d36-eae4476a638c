package router

import (
	"notification-service/internal/config"
	"notification-service/internal/handlers"
	"notification-service/internal/middleware"
	"notification-service/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRouter sets up the HTTP router with all routes
func SetupRouter(
	notificationService *services.NotificationService,
	emailService *services.EmailService,
	smsService *services.SMSService,
	templateService *services.TemplateService,
	schedulerService *services.SchedulerService,
	analyticsService *services.AnalyticsService,
	config *config.Config,
) *gin.Engine {
	// Set Gin mode based on environment
	if config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	
	router := gin.New()
	
	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	
	// Initialize handlers
	notificationHandler := handlers.NewNotificationHandler(notificationService)
	templateHandler := handlers.NewTemplateHandler(templateService)
	emailHandler := handlers.NewEmailHandler(emailService)
	smsHandler := handlers.NewSMSHandler(smsService)
	schedulerHandler := handlers.NewSchedulerHandler(schedulerService)
	analyticsHandler := handlers.NewAnalyticsHandler(analyticsService)
	healthHandler := handlers.NewHealthHandler()
	
	// Health check routes (no auth required)
	router.GET("/health", healthHandler.HealthCheck)
	router.GET("/health/ready", healthHandler.ReadinessCheck)
	router.GET("/health/live", healthHandler.LivenessCheck)
	
	// API routes
	api := router.Group("/api/v1")
	
	// Apply JWT authentication middleware to API routes
	api.Use(middleware.JWTAuth(config.JWTSecret))
	
	// Notification routes
	notifications := api.Group("/notifications")
	{
		notifications.POST("", notificationHandler.CreateNotification)
		notifications.GET("", notificationHandler.GetNotifications)
		notifications.GET("/:id", notificationHandler.GetNotification)
		notifications.PUT("/:id/status", notificationHandler.UpdateNotificationStatus)
		notifications.DELETE("/:id", notificationHandler.DeleteNotification)
		notifications.GET("/stats", notificationHandler.GetNotificationStats)
		
		// Email notifications
		notifications.POST("/email", emailHandler.SendEmail)
		notifications.POST("/email/bulk", emailHandler.SendBulkEmail)
		notifications.POST("/email/templated", emailHandler.SendTemplatedEmail)
		notifications.POST("/email/welcome", emailHandler.SendWelcomeEmail)
		notifications.POST("/email/password-reset", emailHandler.SendPasswordResetEmail)
		
		// SMS notifications
		notifications.POST("/sms", smsHandler.SendSMS)
		notifications.POST("/sms/bulk", smsHandler.SendBulkSMS)
		notifications.POST("/sms/templated", smsHandler.SendTemplatedSMS)
		notifications.POST("/sms/class-reminder", smsHandler.SendClassReminderSMS)
		notifications.POST("/sms/payment-reminder", smsHandler.SendPaymentReminderSMS)
		notifications.POST("/sms/welcome", smsHandler.SendWelcomeSMS)
		notifications.POST("/sms/verification", smsHandler.SendVerificationCodeSMS)
		notifications.POST("/sms/emergency", smsHandler.SendEmergencyNotificationSMS)
	}

	// Scheduling routes
	schedule := api.Group("/schedule")
	{
		schedule.POST("/notification", schedulerHandler.ScheduleNotification)
		schedule.POST("/recurring", schedulerHandler.ScheduleRecurringNotification)
		schedule.POST("/reminders", schedulerHandler.CreateReminderSchedule)
		schedule.GET("/notifications", schedulerHandler.GetScheduledNotifications)
		schedule.DELETE("/notifications/:id", schedulerHandler.CancelScheduledNotification)
	}

	// Analytics routes
	analytics := api.Group("/analytics")
	{
		analytics.GET("/notifications", analyticsHandler.GetNotificationAnalytics)
		analytics.GET("/users/:user_id", analyticsHandler.GetUserAnalytics)
		analytics.GET("/templates", analyticsHandler.GetTemplateAnalytics)
		analytics.GET("/templates/top", analyticsHandler.GetTopPerformingTemplates)
		analytics.GET("/failures", analyticsHandler.GetFailureAnalysis)
		analytics.GET("/dashboard", analyticsHandler.GetDashboardData)
	}
	
	// Template routes
	templates := api.Group("/templates")
	{
		templates.POST("", templateHandler.CreateTemplate)
		templates.GET("", templateHandler.GetTemplates)
		templates.GET("/:id", templateHandler.GetTemplate)
		templates.PUT("/:id", templateHandler.UpdateTemplate)
		templates.DELETE("/:id", templateHandler.DeleteTemplate)
		templates.POST("/:id/clone", templateHandler.CloneTemplate)
		templates.GET("/type/:type", templateHandler.GetTemplatesByType)
		templates.GET("/default/:type", templateHandler.GetDefaultTemplate)
		templates.GET("/stats", templateHandler.GetTemplateStats)
	}
	
	// Preference routes
	preferences := api.Group("/preferences")
	{
		preferences.POST("", notificationHandler.CreatePreference)
		preferences.GET("/:user_id", notificationHandler.GetPreference)
		preferences.PUT("/:user_id", notificationHandler.UpdatePreference)
		preferences.DELETE("/:user_id", notificationHandler.DeletePreference)
		preferences.PUT("/:user_id/email", notificationHandler.UpdateEmailPreferences)
		preferences.PUT("/:user_id/sms", notificationHandler.UpdateSMSPreferences)
		preferences.PUT("/:user_id/in-app", notificationHandler.UpdateInAppPreferences)
		preferences.PUT("/:user_id/push", notificationHandler.UpdatePushPreferences)
		preferences.PUT("/:user_id/contact", notificationHandler.UpdateContactInfo)
		preferences.PUT("/:user_id/quiet-hours", notificationHandler.UpdateQuietHours)
		preferences.PUT("/:user_id/digest", notificationHandler.UpdateDigestSettings)
		preferences.GET("/stats", notificationHandler.GetPreferenceStats)
	}
	
	// Queue routes (admin only)
	queue := api.Group("/queue")
	queue.Use(middleware.RequireRole("ADMIN"))
	{
		queue.GET("/stats", notificationHandler.GetQueueStats)
		queue.POST("/process", notificationHandler.ProcessQueue)
		queue.POST("/cleanup", notificationHandler.CleanupExpiredLocks)
		queue.POST("/retry", notificationHandler.RetryFailedNotifications)
	}
	
	// Delivery routes
	delivery := api.Group("/delivery")
	{
		delivery.GET("/logs", notificationHandler.GetDeliveryLogs)
		delivery.GET("/logs/:id", notificationHandler.GetDeliveryLog)
		delivery.GET("/stats", notificationHandler.GetDeliveryStats)
		delivery.POST("/webhook/email", notificationHandler.HandleEmailWebhook)
		delivery.POST("/webhook/sms", notificationHandler.HandleSMSWebhook)
	}
	
	// Service status routes
	status := api.Group("/status")
	{
		status.GET("/email", emailHandler.GetEmailStatus)
		status.GET("/sms", smsHandler.GetSMSStatus)
		status.POST("/email/test", emailHandler.TestEmailConnection)
		status.POST("/sms/test", smsHandler.TestSMSConnection)
	}
	
	return router
}
