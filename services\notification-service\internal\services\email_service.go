package services

import (
	"crypto/tls"
	"fmt"
	"log"
	"notification-service/internal/config"
	"strconv"

	"gopkg.in/gomail.v2"
)

// EmailService handles email operations
type EmailService struct {
	config *config.Config
	dialer *gomail.Dialer
}

// NewEmailService creates a new email service
func NewEmailService(config *config.Config) *EmailService {
	// Create SMTP dialer
	dialer := gomail.NewDialer(
		config.SMTPHost,
		config.SMTPPort,
		config.SMTPUser,
		config.SMTPPassword,
	)
	
	// Configure TLS
	dialer.TLSConfig = &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         config.SMTPHost,
	}
	
	return &EmailService{
		config: config,
		dialer: dialer,
	}
}

// SendEmail sends an email notification
func (s *EmailService) SendEmail(to, subject, textBody, htmlBody string) error {
	// Validate inputs
	if to == "" {
		return fmt.Errorf("recipient email is required")
	}
	if subject == "" {
		return fmt.Errorf("email subject is required")
	}
	if textBody == "" && htmlBody == "" {
		return fmt.Errorf("email body is required")
	}
	
	// Create message
	message := gomail.NewMessage()
	message.SetHeader("From", s.config.SMTPFrom)
	message.SetHeader("To", to)
	message.SetHeader("Subject", subject)
	
	// Set body content
	if htmlBody != "" {
		message.SetBody("text/html", htmlBody)
		if textBody != "" {
			message.AddAlternative("text/plain", textBody)
		}
	} else {
		message.SetBody("text/plain", textBody)
	}
	
	// Add headers for tracking
	message.SetHeader("X-Mailer", "Innovative Centre Notification Service")
	message.SetHeader("X-Priority", "3") // Normal priority
	
	// Send email
	if err := s.dialer.DialAndSend(message); err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	
	log.Printf("Email sent successfully to %s", to)
	return nil
}

// SendBulkEmail sends emails to multiple recipients
func (s *EmailService) SendBulkEmail(recipients []string, subject, textBody, htmlBody string) error {
	if len(recipients) == 0 {
		return fmt.Errorf("no recipients provided")
	}
	
	var errors []string
	successCount := 0
	
	for _, recipient := range recipients {
		if err := s.SendEmail(recipient, subject, textBody, htmlBody); err != nil {
			errors = append(errors, fmt.Sprintf("%s: %v", recipient, err))
		} else {
			successCount++
		}
	}
	
	log.Printf("Bulk email completed: %d successful, %d failed", successCount, len(errors))
	
	if len(errors) > 0 {
		return fmt.Errorf("some emails failed to send: %v", errors)
	}
	
	return nil
}

// SendTemplatedEmail sends an email using a template with data
func (s *EmailService) SendTemplatedEmail(to, subject, template string, data map[string]interface{}) error {
	// Process template with data
	processedSubject := s.processTemplate(subject, data)
	processedBody := s.processTemplate(template, data)
	
	return s.SendEmail(to, processedSubject, processedBody, "")
}

// SendHTMLTemplatedEmail sends an HTML email using templates with data
func (s *EmailService) SendHTMLTemplatedEmail(to, subject, textTemplate, htmlTemplate string, data map[string]interface{}) error {
	// Process templates with data
	processedSubject := s.processTemplate(subject, data)
	processedTextBody := s.processTemplate(textTemplate, data)
	processedHTMLBody := s.processTemplate(htmlTemplate, data)
	
	return s.SendEmail(to, processedSubject, processedTextBody, processedHTMLBody)
}

// processTemplate processes template content with data
func (s *EmailService) processTemplate(template string, data map[string]interface{}) string {
	result := template
	
	if data != nil {
		for key, value := range data {
			placeholder := fmt.Sprintf("{{%s}}", key)
			replacement := fmt.Sprintf("%v", value)
			
			// Simple string replacement for template variables
			for i := 0; i < len(result); i++ {
				if i+len(placeholder) <= len(result) && result[i:i+len(placeholder)] == placeholder {
					result = result[:i] + replacement + result[i+len(placeholder):]
					i += len(replacement) - 1
				}
			}
		}
	}
	
	return result
}

// ValidateEmailConfig validates the email configuration
func (s *EmailService) ValidateEmailConfig() error {
	if s.config.SMTPHost == "" {
		return fmt.Errorf("SMTP host is not configured")
	}
	if s.config.SMTPPort == 0 {
		return fmt.Errorf("SMTP port is not configured")
	}
	if s.config.SMTPUser == "" {
		return fmt.Errorf("SMTP user is not configured")
	}
	if s.config.SMTPPassword == "" {
		return fmt.Errorf("SMTP password is not configured")
	}
	if s.config.SMTPFrom == "" {
		return fmt.Errorf("SMTP from address is not configured")
	}
	
	return nil
}

// TestEmailConnection tests the email connection
func (s *EmailService) TestEmailConnection() error {
	// Validate configuration first
	if err := s.ValidateEmailConfig(); err != nil {
		return fmt.Errorf("email configuration invalid: %w", err)
	}
	
	// Try to establish connection
	conn, err := s.dialer.Dial()
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer conn.Close()
	
	log.Printf("Email connection test successful to %s:%d", s.config.SMTPHost, s.config.SMTPPort)
	return nil
}

// SendWelcomeEmail sends a welcome email to new users
func (s *EmailService) SendWelcomeEmail(to, userName string) error {
	subject := "Welcome to Innovative Centre!"
	
	textBody := fmt.Sprintf(`
Dear %s,

Welcome to Innovative Centre! We're excited to have you join our learning community.

Your account has been successfully created and you can now access all our services.

If you have any questions, please don't hesitate to contact our support team.

Best regards,
The Innovative Centre Team
`, userName)
	
	htmlBody := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Welcome to Innovative Centre</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2c3e50;">Welcome to Innovative Centre!</h1>
        
        <p>Dear %s,</p>
        
        <p>Welcome to Innovative Centre! We're excited to have you join our learning community.</p>
        
        <p>Your account has been successfully created and you can now access all our services.</p>
        
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0;"><strong>What's next?</strong></p>
            <ul style="margin: 10px 0;">
                <li>Explore our course catalog</li>
                <li>Complete your profile</li>
                <li>Join your first class</li>
            </ul>
        </div>
        
        <p>If you have any questions, please don't hesitate to contact our support team.</p>
        
        <p>Best regards,<br>
        The Innovative Centre Team</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666;">
            This email was sent from Innovative Centre Notification Service.
        </p>
    </div>
</body>
</html>
`, userName, userName)
	
	return s.SendEmail(to, subject, textBody, htmlBody)
}

// SendPasswordResetEmail sends a password reset email
func (s *EmailService) SendPasswordResetEmail(to, userName, resetToken string) error {
	subject := "Password Reset Request - Innovative Centre"
	
	resetURL := fmt.Sprintf("https://app.innovativecentre.com/reset-password?token=%s", resetToken)
	
	textBody := fmt.Sprintf(`
Dear %s,

You have requested to reset your password for your Innovative Centre account.

Please click the following link to reset your password:
%s

This link will expire in 24 hours for security reasons.

If you did not request this password reset, please ignore this email.

Best regards,
The Innovative Centre Team
`, userName, resetURL)
	
	htmlBody := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Password Reset Request</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2c3e50;">Password Reset Request</h1>
        
        <p>Dear %s,</p>
        
        <p>You have requested to reset your password for your Innovative Centre account.</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="%s" style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
        </div>
        
        <p>Or copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 3px;">%s</p>
        
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>Security Notice:</strong> This link will expire in 24 hours for security reasons.</p>
        </div>
        
        <p>If you did not request this password reset, please ignore this email.</p>
        
        <p>Best regards,<br>
        The Innovative Centre Team</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666;">
            This email was sent from Innovative Centre Notification Service.
        </p>
    </div>
</body>
</html>
`, userName, resetURL, resetURL)
	
	return s.SendEmail(to, subject, textBody, htmlBody)
}

// GetEmailStats returns email sending statistics
func (s *EmailService) GetEmailStats() map[string]interface{} {
	return map[string]interface{}{
		"smtp_host":      s.config.SMTPHost,
		"smtp_port":      strconv.Itoa(s.config.SMTPPort),
		"smtp_from":      s.config.SMTPFrom,
		"smtp_configured": s.ValidateEmailConfig() == nil,
	}
}
