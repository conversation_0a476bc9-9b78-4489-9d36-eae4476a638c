# 💳 Payment Service API Documentation

## Overview

The Payment Service provides comprehensive payment processing capabilities including multiple gateway integrations, transaction management, financial reporting, and analytics.

**Base URL**: `http://localhost:8083`  
**Authentication**: <PERSON><PERSON> (JWT)  
**Content-Type**: `application/json`

## Authentication

All API endpoints (except health checks) require authentication using a JWT token obtained from the Auth Service.

```http
Authorization: Bearer <your-jwt-token>
```

## Health Endpoints

### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "service": "payment-service",
  "version": "1.0.0",
  "database": "healthy",
  "gateways": {
    "stripe": "configured",
    "paypal": "configured",
    "local": "configured"
  }
}
```

## Payment Management

### List Payments
```http
GET /api/v1/payments?page=1&page_size=10&status=COMPLETED&method=CARD
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `page_size` (int): Items per page (default: 10, max: 100)
- `status` (string): Filter by payment status
- `method` (string): Filter by payment method
- `student_id` (uuid): Filter by student ID
- `course_id` (uuid): Filter by course ID
- `gateway_type` (string): Filter by gateway type
- `start_date` (date): Filter from date (YYYY-MM-DD)
- `end_date` (date): Filter to date (YYYY-MM-DD)
- `search` (string): Search in invoice number, description, gateway payment ID

**Response:**
```json
{
  "success": true,
  "message": "Payments retrieved successfully",
  "data": [
    {
      "id": "uuid",
      "amount": 100.00,
      "currency": "USD",
      "status": "COMPLETED",
      "method": "CARD",
      "student_id": "uuid",
      "course_id": "uuid",
      "enrollment_id": "uuid",
      "invoice_number": "INV202401010001",
      "gateway_type": "stripe",
      "gateway_payment_id": "pi_1234567890",
      "description": "Course enrollment payment",
      "payment_date": "2024-01-01T12:00:00Z",
      "due_date": "2024-01-15T23:59:59Z",
      "processed_by_id": "uuid",
      "metadata": {},
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z",
      "transactions": [],
      "refunds": []
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

### Get Payment
```http
GET /api/v1/payments/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment retrieved successfully",
  "data": {
    "id": "uuid",
    "amount": 100.00,
    "currency": "USD",
    "status": "COMPLETED",
    "method": "CARD",
    "gateway_type": "stripe",
    "invoice_number": "INV202401010001",
    "description": "Course enrollment payment",
    "transactions": [
      {
        "id": "uuid",
        "payment_id": "uuid",
        "amount": 100.00,
        "currency": "USD",
        "status": "COMPLETED",
        "type": "PAYMENT",
        "gateway_type": "stripe",
        "gateway_txn_id": "txn_1234567890",
        "gateway_fee": 2.90,
        "processed_at": "2024-01-01T12:00:00Z"
      }
    ],
    "refunds": []
  }
}
```

### Create Payment
```http
POST /api/v1/payments
```

**Request Body:**
```json
{
  "amount": 100.00,
  "currency": "USD",
  "method": "CARD",
  "student_id": "uuid",
  "course_id": "uuid",
  "enrollment_id": "uuid",
  "description": "Course enrollment payment",
  "due_date": "2024-01-15T23:59:59Z",
  "gateway_type": "stripe",
  "metadata": {
    "course_name": "Advanced Programming",
    "student_email": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment created successfully",
  "data": {
    "id": "uuid",
    "amount": 100.00,
    "currency": "USD",
    "status": "PENDING",
    "method": "CARD",
    "invoice_number": "INV202401010001",
    "gateway_type": "stripe",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

### Process Payment
```http
POST /api/v1/payments/{id}/process
```

**Request Body:**
```json
{
  "gateway_type": "stripe"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment processed successfully",
  "data": {
    "id": "uuid",
    "status": "COMPLETED",
    "payment_date": "2024-01-01T12:00:00Z",
    "gateway_payment_id": "pi_1234567890"
  }
}
```

### Refund Payment
```http
POST /api/v1/payments/{id}/refund
```

**Request Body:**
```json
{
  "amount": 50.00,
  "reason": "Customer requested partial refund",
  "metadata": {
    "refund_type": "partial",
    "requested_by": "customer_service"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment refunded successfully",
  "data": {
    "id": "uuid",
    "status": "REFUNDED",
    "refunds": [
      {
        "id": "uuid",
        "amount": 50.00,
        "status": "COMPLETED",
        "reason": "Customer requested partial refund",
        "processed_at": "2024-01-01T15:00:00Z"
      }
    ]
  }
}
```

## Transaction Management

### List Transactions
```http
GET /api/v1/transactions?payment_id=uuid&status=COMPLETED&gateway_type=stripe
```

**Query Parameters:**
- `page` (int): Page number
- `page_size` (int): Items per page
- `payment_id` (uuid): Filter by payment ID
- `status` (string): Filter by transaction status
- `type` (string): Filter by transaction type
- `gateway_type` (string): Filter by gateway type
- `start_date` (date): Filter from date
- `end_date` (date): Filter to date
- `search` (string): Search in gateway transaction ID, failure reason

### Get Transaction
```http
GET /api/v1/transactions/{id}
```

### Retry Transaction
```http
POST /api/v1/transactions/{id}/retry
```

**Response:**
```json
{
  "success": true,
  "message": "Transaction retry initiated successfully",
  "data": {
    "id": "uuid",
    "status": "PENDING",
    "retry_count": 2
  }
}
```

## Gateway Management

### List Supported Gateways
```http
GET /api/v1/gateways
```

**Response:**
```json
{
  "success": true,
  "message": "Supported gateways retrieved successfully",
  "data": [
    {
      "type": "stripe",
      "name": "Stripe",
      "enabled": true,
      "currencies": ["USD", "EUR"],
      "methods": ["CARD"],
      "features": ["payments", "refunds", "webhooks"]
    },
    {
      "type": "uzcard",
      "name": "UzCard",
      "enabled": true,
      "currencies": ["UZS"],
      "methods": ["UZCARD"],
      "features": ["payments"]
    }
  ]
}
```

### Gateway Webhooks
```http
POST /api/v1/gateways/stripe/webhook
POST /api/v1/gateways/paypal/webhook
POST /api/v1/gateways/uzcard/webhook
POST /api/v1/gateways/humo/webhook
POST /api/v1/gateways/payme/webhook
POST /api/v1/gateways/click/webhook
```

## Financial Reporting & Analytics

### Payment Statistics
```http
GET /api/v1/payments/stats
```

**Response:**
```json
{
  "success": true,
  "message": "Payment statistics retrieved successfully",
  "data": {
    "total_payments": 1000,
    "total_revenue": 50000.00,
    "successful_payments": 950,
    "failed_payments": 30,
    "pending_payments": 20,
    "refunded_payments": 15,
    "average_payment": 52.63,
    "success_rate": 95.0
  }
}
```

### Financial Report
```http
GET /api/v1/reports/financial?start_date=2024-01-01&end_date=2024-01-31
```

**Response:**
```json
{
  "success": true,
  "message": "Financial report retrieved successfully",
  "data": {
    "total_revenue": 50000.00,
    "net_revenue": 48500.00,
    "total_refunds": 1000.00,
    "total_fees": 500.00,
    "payment_count": 1000,
    "average_payment": 50.00
  }
}
```

### Payment Analytics
```http
GET /api/v1/analytics/payments?start_date=2024-01-01&end_date=2024-01-31&gateway_type=stripe
```

**Response:**
```json
{
  "success": true,
  "message": "Payment analytics retrieved successfully",
  "data": {
    "date": "2024-01-01T00:00:00Z",
    "total_revenue": 50000.00,
    "net_revenue": 48500.00,
    "average_payment": 50.00,
    "payment_count": 1000,
    "success_rate": 95.0,
    "refund_rate": 1.5,
    "gateway_breakdown": {
      "stripe": {
        "gateway_type": "stripe",
        "revenue": 30000.00,
        "payment_count": 600,
        "success_rate": 96.0,
        "average_amount": 50.00,
        "total_fees": 870.00
      }
    },
    "currency_breakdown": {
      "USD": 45000.00,
      "UZS": 5000.00
    }
  }
}
```

### Revenue Metrics
```http
GET /api/v1/analytics/revenue?start_date=2024-01-01&end_date=2024-01-31&period=daily
```

**Query Parameters:**
- `period` (string): daily, weekly, monthly, yearly

### Gateway Performance
```http
GET /api/v1/analytics/gateways?start_date=2024-01-01&end_date=2024-01-31
```

### Payment Trends
```http
GET /api/v1/analytics/trends?start_date=2024-01-01&end_date=2024-01-31&granularity=daily
```

**Query Parameters:**
- `granularity` (string): daily, weekly, monthly

### Financial Dashboard
```http
GET /api/v1/dashboard?start_date=2024-01-01&end_date=2024-01-31
```

**Response:**
```json
{
  "success": true,
  "message": "Financial dashboard data retrieved successfully",
  "data": {
    "period": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "summary": {
      "total_revenue": 50000.00,
      "payment_count": 1000,
      "success_rate": 95.0,
      "average_payment": 50.00
    },
    "analytics": {},
    "gateway_performance": {},
    "trends": []
  }
}
```

### Revenue Comparison
```http
GET /api/v1/analytics/revenue/comparison?start_date=2024-01-01&end_date=2024-01-31&compare_with=previous_period
```

**Query Parameters:**
- `compare_with` (string): previous_period, previous_year

## Export & Reconciliation

### Export Financial Data
```http
POST /api/v1/reports/export
```

**Request Body:**
```json
{
  "type": "csv",
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-31T23:59:59Z",
  "filters": {
    "gateway_type": "stripe",
    "status": "COMPLETED"
  }
}
```

### Payment Reconciliation
```http
POST /api/v1/reconciliation
```

**Request Body:**
```json
{
  "gateway_type": "stripe",
  "date": "2024-01-01T00:00:00Z",
  "gateway_data": {
    "revenue": 10000.00,
    "count": 200
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "VALIDATION_ERROR",
  "details": {
    "field": "amount",
    "message": "Amount must be greater than 0"
  }
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate limited:
- **General endpoints**: 100 requests per minute
- **Payment processing**: 10 requests per minute
- **Webhook endpoints**: 1000 requests per minute

## Webhooks

The service supports webhooks from various payment gateways. Each webhook endpoint validates the signature and processes the event accordingly.

### Webhook Security
- All webhooks validate signatures
- Events are processed idempotently
- Failed webhook processing is retried with exponential backoff

## Testing

Use the provided test script to verify API functionality:

```powershell
.\tests\test-payment-service.ps1
```

## Support

For API support and questions:
- Check the service logs for detailed error information
- Use the health endpoints to verify service status
- Refer to the main README.md for setup and configuration
