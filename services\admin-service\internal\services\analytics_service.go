package services

import (
	"fmt"
	"time"

	"admin-service/internal/models"
	"admin-service/internal/repository"
)

// AnalyticsService handles analytics business logic
type AnalyticsService interface {
	GetDashboardAnalytics() (*models.DashboardAnalytics, error)
	GetFinancialReport(req *models.AnalyticsRequest) (*models.FinancialReport, error)
	GetUserAnalytics(req *models.AnalyticsRequest) (*models.UserAnalytics, error)
	GetSystemMetrics() (*models.SystemMetrics, error)
}

type analyticsService struct {
	analyticsRepo repository.AnalyticsRepository
	userRepo      repository.UserRepository
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(analyticsRepo repository.AnalyticsRepository, userRepo repository.UserRepository) AnalyticsService {
	return &analyticsService{
		analyticsRepo: analyticsRepo,
		userRepo:      userRepo,
	}
}

// GetDashboardAnalytics retrieves dashboard analytics
func (s *analyticsService) GetDashboardAnalytics() (*models.DashboardAnalytics, error) {
	analytics, err := s.analyticsRepo.GetDashboardAnalytics()
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard analytics: %w", err)
	}

	return analytics, nil
}

// GetFinancialReport retrieves financial report
func (s *analyticsService) GetFinancialReport(req *models.AnalyticsRequest) (*models.FinancialReport, error) {
	// Set default date range if not provided
	startDate := time.Now().AddDate(0, -1, 0) // Last month
	endDate := time.Now()

	if req.StartDate != nil {
		startDate = *req.StartDate
	}
	if req.EndDate != nil {
		endDate = *req.EndDate
	}

	// Validate date range
	if startDate.After(endDate) {
		return nil, fmt.Errorf("start date cannot be after end date")
	}

	report, err := s.analyticsRepo.GetFinancialReport(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get financial report: %w", err)
	}

	return report, nil
}

// GetUserAnalytics retrieves user analytics
func (s *analyticsService) GetUserAnalytics(req *models.AnalyticsRequest) (*models.UserAnalytics, error) {
	// Set default date range if not provided
	startDate := time.Now().AddDate(0, -1, 0) // Last month
	endDate := time.Now()

	if req.StartDate != nil {
		startDate = *req.StartDate
	}
	if req.EndDate != nil {
		endDate = *req.EndDate
	}

	// Validate date range
	if startDate.After(endDate) {
		return nil, fmt.Errorf("start date cannot be after end date")
	}

	analytics, err := s.analyticsRepo.GetUserAnalytics(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get user analytics: %w", err)
	}

	// Enhance with additional user data
	if err := s.enhanceUserAnalytics(analytics); err != nil {
		return nil, fmt.Errorf("failed to enhance user analytics: %w", err)
	}

	return analytics, nil
}

// GetSystemMetrics retrieves system metrics
func (s *analyticsService) GetSystemMetrics() (*models.SystemMetrics, error) {
	metrics, err := s.analyticsRepo.GetSystemMetrics()
	if err != nil {
		return nil, fmt.Errorf("failed to get system metrics: %w", err)
	}

	return metrics, nil
}

// enhanceUserAnalytics adds additional user data to analytics
func (s *analyticsService) enhanceUserAnalytics(analytics *models.UserAnalytics) error {
	// Get most active users
	activeUsers, err := s.userRepo.GetMostActiveUsers(10)
	if err != nil {
		return fmt.Errorf("failed to get most active users: %w", err)
	}

	// Convert to analytics format
	analytics.MostActiveUsers = make([]*models.UserActivitySummary, len(activeUsers))
	for i, user := range activeUsers {
		var lastLoginAt *time.Time
		if user.LastLoginAt != nil && *user.LastLoginAt != "" {
			if parsed, err := time.Parse(time.RFC3339, *user.LastLoginAt); err == nil {
				lastLoginAt = &parsed
			}
		}

		analytics.MostActiveUsers[i] = &models.UserActivitySummary{
			UserID:        user.UserID,
			UserName:      user.UserName,
			UserEmail:     user.UserEmail,
			Role:          user.Role,
			LoginCount:    user.LoginCount,
			LastLoginAt:   lastLoginAt,
			ActivityScore: calculateActivityScore(user.LoginCount, lastLoginAt),
		}
	}

	return nil
}

// calculateActivityScore calculates an activity score based on login count and recency
func calculateActivityScore(loginCount int64, lastLoginAt *time.Time) float64 {
	score := float64(loginCount)

	// Boost score based on recency of last login
	if lastLoginAt != nil {
		daysSinceLastLogin := time.Since(*lastLoginAt).Hours() / 24
		if daysSinceLastLogin <= 1 {
			score *= 2.0 // Double score for login within last day
		} else if daysSinceLastLogin <= 7 {
			score *= 1.5 // 1.5x score for login within last week
		} else if daysSinceLastLogin <= 30 {
			score *= 1.2 // 1.2x score for login within last month
		}
	}

	return score
}
