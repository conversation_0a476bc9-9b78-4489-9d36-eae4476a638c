{"name": "CRM Microservices Development", "image": "mcr.microsoft.com/devcontainers/go:1.21", "features": {"ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "forwardPorts": [8081, 8082, 5432, 5433, 6379], "portsAttributes": {"8081": {"label": "Auth Service", "onAutoForward": "notify"}, "8082": {"label": "Admin Service", "onAutoForward": "notify"}, "5432": {"label": "Auth Database", "onAutoForward": "silent"}, "5433": {"label": "Admin Database", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}}, "customizations": {"vscode": {"extensions": ["golang.go", "ms-vscode.vscode-json", "ms-azuretools.vscode-docker", "humao.rest-client"]}}, "postCreateCommand": "go version && docker --version && docker-compose --version", "remoteUser": "vscode"}