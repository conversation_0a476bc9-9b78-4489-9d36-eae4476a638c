import { apiService } from './api';
import type {
  User,
  UserCreateRequest,
  UserUpdateRequest,
  UserFilters,
  UserStats,
  PaginatedResponse
} from '@/types/user';

class UserService {
  private readonly API_PREFIX = '/api/v1/admin/users';

  // Get all users with pagination and filters
  async getUsers(params?: {
    page?: number;
    limit?: number;
    filters?: UserFilters;
  }): Promise<PaginatedResponse<User>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.filters?.search) queryParams.append('search', params.filters.search);
    if (params?.filters?.role) queryParams.append('role', params.filters.role);
    if (params?.filters?.status) queryParams.append('status', params.filters.status);
    if (params?.filters?.email_verified !== undefined) {
      queryParams.append('email_verified', params.filters.email_verified.toString());
    }

    const url = queryParams.toString() ? `${this.API_PREFIX}?${queryParams}` : this.API_PREFIX;
    return apiService.get<PaginatedResponse<User>>(url);
  }

  // Get user by ID
  async getUser(id: string): Promise<User> {
    return apiService.get<User>(`${this.API_PREFIX}/${id}`);
  }

  // Create new user
  async createUser(data: UserCreateRequest): Promise<User> {
    return apiService.post<User>(this.API_PREFIX, data);
  }

  // Update user
  async updateUser(id: string, data: UserUpdateRequest): Promise<User> {
    return apiService.put<User>(`${this.API_PREFIX}/${id}`, data);
  }

  // Delete user
  async deleteUser(id: string): Promise<void> {
    return apiService.delete<void>(`${this.API_PREFIX}/${id}`);
  }

  // Get user statistics
  async getUserStats(): Promise<UserStats> {
    return apiService.get<UserStats>(`${this.API_PREFIX}/stats`);
  }

  // Bulk operations
  async bulkUpdateUsers(ids: string[], data: Partial<UserUpdateRequest>): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/bulk-update`, {
      user_ids: ids,
      ...data
    });
  }

  async bulkDeleteUsers(ids: string[]): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/bulk-delete`, {
      user_ids: ids
    });
  }

  // User role management
  async updateUserRole(id: string, role: string): Promise<User> {
    return apiService.patch<User>(`${this.API_PREFIX}/${id}/role`, { role });
  }

  // User status management
  async updateUserStatus(id: string, status: string): Promise<User> {
    return apiService.patch<User>(`${this.API_PREFIX}/${id}/status`, { status });
  }

  // Email verification
  async resendVerificationEmail(id: string): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/${id}/resend-verification`);
  }

  // Password reset
  async resetUserPassword(id: string): Promise<{ temporary_password: string }> {
    return apiService.post<{ temporary_password: string }>(`${this.API_PREFIX}/${id}/reset-password`);
  }
}

// Create and export singleton instance
export const userService = new UserService();
export default userService;
