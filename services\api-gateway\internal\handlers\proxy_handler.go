package handlers

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/crm-microservices/api-gateway/internal/middleware"
	"github.com/crm-microservices/api-gateway/internal/proxy"
	"github.com/crm-microservices/shared/utils"
)

// ProxyHandler handles proxying requests to downstream services
type ProxyHandler struct {
	serviceProxy   *proxy.ServiceProxy
	authMiddleware *middleware.AuthMiddleware
}

// NewProxyHandler creates a new proxy handler
func NewProxyHandler(serviceProxy *proxy.ServiceProxy, authMiddleware *middleware.AuthMiddleware) *ProxyHandler {
	return &ProxyHandler{
		serviceProxy:   serviceProxy,
		authMiddleware: authMiddleware,
	}
}

// ProxyToAuth proxies requests to the authentication service
// @Summary Proxy to Auth Service
// @Description Proxy requests to the authentication service
// @Tags Proxy
// @Param path path string true "Request path"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /api/v1/auth/{path} [get,post,put,delete,patch]
func (ph *ProxyHandler) ProxyToAuth(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}
	
	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")
	
	// Construct the target path for auth service
	targetPath := "/api/v1/auth/" + path
	
	ph.serviceProxy.ProxyRequest(c, "auth", targetPath)
}

// ProxyToUsers proxies requests to the auth service for user management
// @Summary Proxy to Auth Service for Users
// @Description Proxy user management requests to the authentication service
// @Tags Proxy
// @Security BearerAuth
// @Param path path string true "Request path"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /api/v1/users/{path} [get,post,put,delete,patch]
func (ph *ProxyHandler) ProxyToUsers(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}

	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")

	// Construct the target path for user endpoints in auth service
	targetPath := "/api/v1/users/" + path

	ph.serviceProxy.ProxyRequest(c, "auth", targetPath)
}

// ProxyToAdmin proxies requests to the admin service
// @Summary Proxy to Admin Service
// @Description Proxy requests to the admin service (admin/cashier access required)
// @Tags Proxy
// @Security BearerAuth
// @Param path path string true "Request path"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /api/v1/admin/{path} [get,post,put,delete,patch]
func (ph *ProxyHandler) ProxyToAdmin(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}
	
	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")
	
	// Construct the target path for admin service
	targetPath := "/api/v1/admin/" + path
	
	ph.serviceProxy.ProxyRequest(c, "admin", targetPath)
}

// ProxyToStaff proxies requests to the staff service
// @Summary Proxy to Staff Service
// @Description Proxy requests to the staff service (staff access required)
// @Tags Proxy
// @Security BearerAuth
// @Param path path string true "Request path"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 403 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /api/v1/staff/{path} [get,post,put,delete,patch]
func (ph *ProxyHandler) ProxyToStaff(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}
	
	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")
	
	// Construct the target path for staff service
	targetPath := "/api/v1/staff/" + path
	
	ph.serviceProxy.ProxyRequest(c, "staff", targetPath)
}

// ProxyToPayment proxies requests to the payment service
// @Summary Proxy to Payment Service
// @Description Proxy requests to the payment service
// @Tags Proxy
// @Security BearerAuth
// @Param path path string true "Request path"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /api/v1/payment/{path} [get,post,put,delete,patch]
func (ph *ProxyHandler) ProxyToPayment(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}
	
	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")
	
	// Construct the target path for payment service
	targetPath := "/api/v1/payment/" + path
	
	ph.serviceProxy.ProxyRequest(c, "payment", targetPath)
}

// ProxyToNotification proxies requests to the notification service
// @Summary Proxy to Notification Service
// @Description Proxy requests to the notification service
// @Tags Proxy
// @Security BearerAuth
// @Param path path string true "Request path"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 401 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /api/v1/notification/{path} [get,post,put,delete,patch]
func (ph *ProxyHandler) ProxyToNotification(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}
	
	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")
	
	// Construct the target path for notification service
	targetPath := "/api/v1/" + path
	
	ph.serviceProxy.ProxyRequest(c, "notification", targetPath)
}

// ProxyWithRetry proxies requests with retry logic
func (ph *ProxyHandler) ProxyWithRetry(c *gin.Context, serviceName string, maxRetries int) {
	path := c.Param("path")
	if path == "" {
		path = "/"
	}
	
	// Remove leading slash if present
	path = strings.TrimPrefix(path, "/")
	
	// Construct the target path
	targetPath := "/api/v1/" + serviceName + "/" + path
	
	ph.serviceProxy.ProxyWithRetry(c, serviceName, targetPath, maxRetries)
}

// HandleServiceUnavailable handles cases where a service is unavailable
func (ph *ProxyHandler) HandleServiceUnavailable(c *gin.Context, serviceName string) {
	utils.BadRequestResponse(c, "Service '"+serviceName+"' is currently unavailable. Please try again later.")
}

// HandleInvalidRoute handles invalid route requests
func (ph *ProxyHandler) HandleInvalidRoute(c *gin.Context) {
	utils.NotFoundResponse(c, "The requested endpoint does not exist")
}

// GetProxyStats returns proxy statistics
// @Summary Get proxy statistics
// @Description Get statistics about proxy operations
// @Tags Proxy
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /proxy/stats [get]
func (ph *ProxyHandler) GetProxyStats(c *gin.Context) {
	stats := ph.serviceProxy.GetProxyStats()
	utils.SuccessResponse(c, stats, "Proxy statistics retrieved")
}

// ProxyHealthCheck proxies health check requests to downstream services
// @Summary Proxy health check
// @Description Proxy health check requests to specific services
// @Tags Proxy
// @Param service path string true "Service name"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /proxy/health/{service} [get]
func (ph *ProxyHandler) ProxyHealthCheck(c *gin.Context) {
	serviceName := c.Param("service")
	if serviceName == "" {
		utils.BadRequestResponse(c, "Service name is required")
		return
	}
	
	ph.serviceProxy.ProxyRequest(c, serviceName, "/health")
}

// ProxyServiceVersion proxies version requests to downstream services
// @Summary Proxy version check
// @Description Proxy version requests to specific services
// @Tags Proxy
// @Param service path string true "Service name"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /proxy/version/{service} [get]
func (ph *ProxyHandler) ProxyServiceVersion(c *gin.Context) {
	serviceName := c.Param("service")
	if serviceName == "" {
		utils.BadRequestResponse(c, "Service name is required")
		return
	}
	
	ph.serviceProxy.ProxyRequest(c, serviceName, "/version")
}

// ProxyServiceMetrics proxies metrics requests to downstream services
// @Summary Proxy metrics check
// @Description Proxy metrics requests to specific services
// @Tags Proxy
// @Param service path string true "Service name"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.APIResponse
// @Failure 502 {object} models.APIResponse
// @Failure 503 {object} models.APIResponse
// @Router /proxy/metrics/{service} [get]
func (ph *ProxyHandler) ProxyServiceMetrics(c *gin.Context) {
	serviceName := c.Param("service")
	if serviceName == "" {
		utils.BadRequestResponse(c, "Service name is required")
		return
	}
	
	ph.serviceProxy.ProxyRequest(c, serviceName, "/metrics")
}
