# Server Configuration
PORT=8083
HOST=0.0.0.0
GIN_MODE=debug

# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# JWT Configuration
JWT_SECRET=your-secret-key-change-in-production

# CORS Configuration
ALLOWED_ORIGINS=*

# Logging Configuration
LOG_LEVEL=info

# External Service URLs
ADMIN_SERVICE_URL=http://localhost:8081

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>

# Redis Configuration (Optional)
REDIS_URL=

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=false
RATE_LIMIT_RPS=100

# Development Configuration
# Uncomment for local development with PostgreSQL
# DATABASE_URL=postgres://postgres:postgres@localhost:5432/staff_service_dev?sslmode=disable
