package utils

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"golang.org/x/crypto/argon2"
)

var (
	ErrInvalidPassword = errors.New("invalid password")
	ErrWeakPassword    = errors.New("password does not meet security requirements")
)

// PasswordConfig holds configuration for password hashing
type PasswordConfig struct {
	Memory      uint32
	Iterations  uint32
	Parallelism uint8
	SaltLength  uint32
	KeyLength   uint32
}

// DefaultPasswordConfig returns default configuration for password hashing
func DefaultPasswordConfig() *PasswordConfig {
	return &PasswordConfig{
		Memory:      64 * 1024, // 64 MB
		Iterations:  3,
		Parallelism: 2,
		SaltLength:  16,
		KeyLength:   32,
	}
}

// PasswordManager handles password operations
type PasswordManager struct {
	config *PasswordConfig
}

// NewPasswordManager creates a new password manager
func NewPasswordManager(config *PasswordConfig) *PasswordManager {
	if config == nil {
		config = DefaultPasswordConfig()
	}
	return &PasswordManager{config: config}
}

// HashPassword hashes a password using Argon2id
func (pm *PasswordManager) HashPassword(password string) (string, error) {
	// Generate a random salt
	salt := make([]byte, pm.config.SaltLength)
	if _, err := rand.Read(salt); err != nil {
		return "", err
	}

	// Hash the password
	hash := argon2.IDKey([]byte(password), salt, pm.config.Iterations, pm.config.Memory, pm.config.Parallelism, pm.config.KeyLength)

	// Encode the hash and salt
	b64Salt := base64.RawStdEncoding.EncodeToString(salt)
	b64Hash := base64.RawStdEncoding.EncodeToString(hash)

	// Format: $argon2id$v=19$m=65536,t=3,p=2$salt$hash
	encodedHash := fmt.Sprintf("$argon2id$v=%d$m=%d,t=%d,p=%d$%s$%s",
		argon2.Version, pm.config.Memory, pm.config.Iterations, pm.config.Parallelism, b64Salt, b64Hash)

	return encodedHash, nil
}

// VerifyPassword verifies a password against its hash
func (pm *PasswordManager) VerifyPassword(password, encodedHash string) (bool, error) {
	// Parse the encoded hash
	parts := strings.Split(encodedHash, "$")
	if len(parts) != 6 {
		return false, ErrInvalidPassword
	}

	var version int
	if _, err := fmt.Sscanf(parts[2], "v=%d", &version); err != nil {
		return false, err
	}
	if version != argon2.Version {
		return false, ErrInvalidPassword
	}

	var memory, iterations uint32
	var parallelism uint8
	if _, err := fmt.Sscanf(parts[3], "m=%d,t=%d,p=%d", &memory, &iterations, &parallelism); err != nil {
		return false, err
	}

	salt, err := base64.RawStdEncoding.DecodeString(parts[4])
	if err != nil {
		return false, err
	}

	hash, err := base64.RawStdEncoding.DecodeString(parts[5])
	if err != nil {
		return false, err
	}

	// Hash the provided password with the same parameters
	otherHash := argon2.IDKey([]byte(password), salt, iterations, memory, parallelism, uint32(len(hash)))

	// Compare the hashes using constant-time comparison
	return subtle.ConstantTimeCompare(hash, otherHash) == 1, nil
}

// PasswordStrengthRequirements defines password strength requirements
type PasswordStrengthRequirements struct {
	MinLength        int
	RequireUppercase bool
	RequireLowercase bool
	RequireNumbers   bool
	RequireSpecial   bool
	ForbiddenWords   []string
}

// DefaultPasswordRequirements returns default password requirements
func DefaultPasswordRequirements() *PasswordStrengthRequirements {
	return &PasswordStrengthRequirements{
		MinLength:        8,
		RequireUppercase: true,
		RequireLowercase: true,
		RequireNumbers:   true,
		RequireSpecial:   true,
		ForbiddenWords:   []string{"password", "123456", "qwerty", "admin", "user"},
	}
}

// ValidatePasswordStrength validates password strength
func ValidatePasswordStrength(password string, requirements *PasswordStrengthRequirements) error {
	if requirements == nil {
		requirements = DefaultPasswordRequirements()
	}

	// Check minimum length
	if len(password) < requirements.MinLength {
		return fmt.Errorf("password must be at least %d characters long", requirements.MinLength)
	}

	// Check for uppercase letters
	if requirements.RequireUppercase {
		if matched, _ := regexp.MatchString(`[A-Z]`, password); !matched {
			return errors.New("password must contain at least one uppercase letter")
		}
	}

	// Check for lowercase letters
	if requirements.RequireLowercase {
		if matched, _ := regexp.MatchString(`[a-z]`, password); !matched {
			return errors.New("password must contain at least one lowercase letter")
		}
	}

	// Check for numbers
	if requirements.RequireNumbers {
		if matched, _ := regexp.MatchString(`[0-9]`, password); !matched {
			return errors.New("password must contain at least one number")
		}
	}

	// Check for special characters
	if requirements.RequireSpecial {
		if matched, _ := regexp.MatchString(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`, password); !matched {
			return errors.New("password must contain at least one special character")
		}
	}

	// Check for forbidden words
	passwordLower := strings.ToLower(password)
	for _, word := range requirements.ForbiddenWords {
		if strings.Contains(passwordLower, strings.ToLower(word)) {
			return fmt.Errorf("password cannot contain the word '%s'", word)
		}
	}

	return nil
}

// GenerateRandomPassword generates a random password
func GenerateRandomPassword(length int) (string, error) {
	if length < 8 {
		length = 8
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)

	for i := range password {
		randomBytes := make([]byte, 1)
		if _, err := rand.Read(randomBytes); err != nil {
			return "", err
		}
		password[i] = charset[randomBytes[0]%byte(len(charset))]
	}

	return string(password), nil
}

// GenerateSecurePassword generates a secure password that meets requirements
func GenerateSecurePassword(length int, requirements *PasswordStrengthRequirements) (string, error) {
	if requirements == nil {
		requirements = DefaultPasswordRequirements()
	}

	if length < requirements.MinLength {
		length = requirements.MinLength
	}

	maxAttempts := 100
	for i := 0; i < maxAttempts; i++ {
		password, err := GenerateRandomPassword(length)
		if err != nil {
			return "", err
		}

		if err := ValidatePasswordStrength(password, requirements); err == nil {
			return password, nil
		}
	}

	return "", errors.New("failed to generate secure password after maximum attempts")
}

// HashPasswordWithDefaults hashes a password using default configuration
func HashPasswordWithDefaults(password string) (string, error) {
	pm := NewPasswordManager(nil)
	return pm.HashPassword(password)
}

// VerifyPasswordWithDefaults verifies a password using default configuration
func VerifyPasswordWithDefaults(password, hash string) (bool, error) {
	pm := NewPasswordManager(nil)
	return pm.VerifyPassword(password, hash)
}

// PasswordScore calculates a password strength score (0-100)
func PasswordScore(password string) int {
	score := 0
	length := len(password)

	// Length score (max 25 points)
	if length >= 8 {
		score += 10
	}
	if length >= 12 {
		score += 10
	}
	if length >= 16 {
		score += 5
	}

	// Character variety (max 40 points)
	if matched, _ := regexp.MatchString(`[a-z]`, password); matched {
		score += 10
	}
	if matched, _ := regexp.MatchString(`[A-Z]`, password); matched {
		score += 10
	}
	if matched, _ := regexp.MatchString(`[0-9]`, password); matched {
		score += 10
	}
	if matched, _ := regexp.MatchString(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`, password); matched {
		score += 10
	}

	// Complexity bonus (max 35 points)
	if length >= 8 {
		// Check for mixed case
		hasUpper, _ := regexp.MatchString(`[A-Z]`, password)
		hasLower, _ := regexp.MatchString(`[a-z]`, password)
		if hasUpper && hasLower {
			score += 10
		}

		// Check for numbers and letters
		hasNumber, _ := regexp.MatchString(`[0-9]`, password)
		hasLetter, _ := regexp.MatchString(`[a-zA-Z]`, password)
		if hasNumber && hasLetter {
			score += 10
		}

		// Check for special characters with letters/numbers
		hasSpecial, _ := regexp.MatchString(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`, password)
		if hasSpecial && (hasLetter || hasNumber) {
			score += 15
		}
	}

	// Ensure score doesn't exceed 100
	if score > 100 {
		score = 100
	}

	return score
}

// GetPasswordStrengthLabel returns a label for password strength
func GetPasswordStrengthLabel(score int) string {
	switch {
	case score >= 80:
		return "Very Strong"
	case score >= 60:
		return "Strong"
	case score >= 40:
		return "Medium"
	case score >= 20:
		return "Weak"
	default:
		return "Very Weak"
	}
}
