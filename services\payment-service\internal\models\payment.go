package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// Payment represents a payment record
type Payment struct {
	models.BaseModel
	
	// Basic payment information
	Amount          float64                `json:"amount" gorm:"not null;type:decimal(10,2)"`
	Currency        string                 `json:"currency" gorm:"not null;default:'USD';size:3"`
	Status          models.PaymentStatus   `json:"status" gorm:"not null;default:'PENDING'"`
	Method          models.PaymentMethod   `json:"method" gorm:"not null"`
	
	// Reference information
	StudentID       *uuid.UUID             `json:"student_id" gorm:"type:uuid;index"`
	CourseID        *uuid.UUID             `json:"course_id" gorm:"type:uuid;index"`
	EnrollmentID    *uuid.UUID             `json:"enrollment_id" gorm:"type:uuid;index"`
	InvoiceNumber   string                 `json:"invoice_number" gorm:"unique;not null"`
	
	// Gateway information
	GatewayType     string                 `json:"gateway_type" gorm:"not null"` // stripe, paypal, uzcard, etc.
	GatewayPaymentID string                `json:"gateway_payment_id" gorm:"index"`
	GatewayResponse  string                `json:"gateway_response" gorm:"type:text"`
	
	// Payment details
	Description     string                 `json:"description" gorm:"type:text"`
	PaymentDate     *time.Time             `json:"payment_date"`
	DueDate         *time.Time             `json:"due_date"`
	
	// User information
	ProcessedByID   *uuid.UUID             `json:"processed_by_id" gorm:"type:uuid;index"`
	
	// Metadata
	Metadata        string                 `json:"metadata" gorm:"type:jsonb"`
	
	// Relationships
	Transactions    []Transaction          `json:"transactions" gorm:"foreignKey:PaymentID"`
	Refunds         []Refund               `json:"refunds" gorm:"foreignKey:PaymentID"`
}

// TableName returns the table name for Payment
func (Payment) TableName() string {
	return "payments"
}

// Transaction represents a payment transaction
type Transaction struct {
	models.BaseModel
	
	// Payment reference
	PaymentID       uuid.UUID              `json:"payment_id" gorm:"type:uuid;not null;index"`
	
	// Transaction details
	Amount          float64                `json:"amount" gorm:"not null;type:decimal(10,2)"`
	Currency        string                 `json:"currency" gorm:"not null;size:3"`
	Status          TransactionStatus      `json:"status" gorm:"not null;default:'PENDING'"`
	Type            TransactionType        `json:"type" gorm:"not null"`
	
	// Gateway information
	GatewayType     string                 `json:"gateway_type" gorm:"not null"`
	GatewayTxnID    string                 `json:"gateway_txn_id" gorm:"index"`
	GatewayResponse string                 `json:"gateway_response" gorm:"type:text"`
	GatewayFee      float64                `json:"gateway_fee" gorm:"type:decimal(10,2);default:0"`
	
	// Processing information
	ProcessedAt     *time.Time             `json:"processed_at"`
	FailureReason   string                 `json:"failure_reason" gorm:"type:text"`
	RetryCount      int                    `json:"retry_count" gorm:"default:0"`
	
	// Metadata
	Metadata        string                 `json:"metadata" gorm:"type:jsonb"`
	
	// Relationships
	Payment         Payment                `json:"payment" gorm:"foreignKey:PaymentID"`
}

// TableName returns the table name for Transaction
func (Transaction) TableName() string {
	return "transactions"
}

// TransactionStatus represents transaction status
type TransactionStatus string

const (
	TransactionPending    TransactionStatus = "PENDING"
	TransactionProcessing TransactionStatus = "PROCESSING"
	TransactionCompleted  TransactionStatus = "COMPLETED"
	TransactionFailed     TransactionStatus = "FAILED"
	TransactionCancelled  TransactionStatus = "CANCELLED"
)

// IsValid checks if the transaction status is valid
func (t TransactionStatus) IsValid() bool {
	switch t {
	case TransactionPending, TransactionProcessing, TransactionCompleted, TransactionFailed, TransactionCancelled:
		return true
	}
	return false
}

// TransactionType represents transaction type
type TransactionType string

const (
	TransactionPayment TransactionType = "PAYMENT"
	TransactionRefund  TransactionType = "REFUND"
	TransactionFee     TransactionType = "FEE"
)

// IsValid checks if the transaction type is valid
func (t TransactionType) IsValid() bool {
	switch t {
	case TransactionPayment, TransactionRefund, TransactionFee:
		return true
	}
	return false
}

// PaymentMethod represents a stored payment method
type PaymentMethod struct {
	models.BaseModel
	
	// User reference
	UserID          uuid.UUID              `json:"user_id" gorm:"type:uuid;not null;index"`
	
	// Payment method details
	Type            models.PaymentMethod   `json:"type" gorm:"not null"`
	IsDefault       bool                   `json:"is_default" gorm:"default:false"`
	IsActive        bool                   `json:"is_active" gorm:"default:true"`
	
	// Card information (encrypted)
	CardLast4       string                 `json:"card_last4" gorm:"size:4"`
	CardBrand       string                 `json:"card_brand" gorm:"size:20"`
	CardExpMonth    int                    `json:"card_exp_month"`
	CardExpYear     int                    `json:"card_exp_year"`
	
	// Gateway information
	GatewayType     string                 `json:"gateway_type" gorm:"not null"`
	GatewayMethodID string                 `json:"gateway_method_id" gorm:"index"`
	
	// Metadata
	Metadata        string                 `json:"metadata" gorm:"type:jsonb"`
}

// TableName returns the table name for PaymentMethod
func (PaymentMethod) TableName() string {
	return "payment_methods"
}

// Refund represents a payment refund
type Refund struct {
	models.BaseModel
	
	// Payment reference
	PaymentID       uuid.UUID              `json:"payment_id" gorm:"type:uuid;not null;index"`
	
	// Refund details
	Amount          float64                `json:"amount" gorm:"not null;type:decimal(10,2)"`
	Currency        string                 `json:"currency" gorm:"not null;size:3"`
	Status          RefundStatus           `json:"status" gorm:"not null;default:'PENDING'"`
	Reason          string                 `json:"reason" gorm:"type:text"`
	
	// Gateway information
	GatewayRefundID string                 `json:"gateway_refund_id" gorm:"index"`
	GatewayResponse string                 `json:"gateway_response" gorm:"type:text"`
	
	// Processing information
	ProcessedAt     *time.Time             `json:"processed_at"`
	ProcessedByID   *uuid.UUID             `json:"processed_by_id" gorm:"type:uuid;index"`
	
	// Metadata
	Metadata        string                 `json:"metadata" gorm:"type:jsonb"`
	
	// Relationships
	Payment         Payment                `json:"payment" gorm:"foreignKey:PaymentID"`
}

// TableName returns the table name for Refund
func (Refund) TableName() string {
	return "refunds"
}

// RefundStatus represents refund status
type RefundStatus string

const (
	RefundPending   RefundStatus = "PENDING"
	RefundProcessing RefundStatus = "PROCESSING"
	RefundCompleted RefundStatus = "COMPLETED"
	RefundFailed    RefundStatus = "FAILED"
	RefundCancelled RefundStatus = "CANCELLED"
)

// IsValid checks if the refund status is valid
func (r RefundStatus) IsValid() bool {
	switch r {
	case RefundPending, RefundProcessing, RefundCompleted, RefundFailed, RefundCancelled:
		return true
	}
	return false
}


