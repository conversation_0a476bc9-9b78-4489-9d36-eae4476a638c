package handler

import (
	"net/http"
	"strconv"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LeadHandler handles lead-related HTTP requests
type LeadHandler struct {
	leadService service.LeadService
}

// NewLeadHandler creates a new lead handler
func NewLeadHandler(leadService service.LeadService) *LeadHandler {
	return &LeadHandler{
		leadService: leadService,
	}
}

// GetLeads retrieves leads with pagination and filtering
// @Summary Get leads
// @Description Get leads with pagination and filtering
// @Tags leads
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Lead status"
// @Param source query string false "Lead source"
// @Param search query string false "Search term"
// @Success 200 {object} models.LeadListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads [get]
func (h *LeadHandler) GetLeads(c *gin.Context) {
	req := &models.LeadListRequest{}

	// Parse pagination
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		} else {
			req.Page = 1
		}
	} else {
		req.Page = 1
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.PageSize = l
		} else {
			req.PageSize = 10
		}
	} else {
		req.PageSize = 10
	}

	// Parse filters
	if status := c.Query("status"); status != "" {
		leadStatus := models.LeadStatus(status)
		if leadStatus.IsValid() {
			req.Status = &leadStatus
		}
	}

	if source := c.Query("source"); source != "" {
		leadSource := models.LeadSource(source)
		if leadSource.IsValid() {
			req.Source = &leadSource
		}
	}

	if assignedTo := c.Query("assigned_to_id"); assignedTo != "" {
		if id, err := uuid.Parse(assignedTo); err == nil {
			req.AssignedToID = &id
		}
	}

	if createdBy := c.Query("created_by_id"); createdBy != "" {
		if id, err := uuid.Parse(createdBy); err == nil {
			req.CreatedByID = &id
		}
	}

	req.Search = c.Query("search")

	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = &date
		}
	}

	if followUpDue := c.Query("follow_up_due"); followUpDue == "true" {
		followUp := true
		req.FollowUpDue = &followUp
	}

	response, err := h.leadService.GetAll(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetLead retrieves a lead by ID
// @Summary Get lead by ID
// @Description Get a specific lead by ID
// @Tags leads
// @Accept json
// @Produce json
// @Param id path string true "Lead ID"
// @Success 200 {object} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/{id} [get]
func (h *LeadHandler) GetLead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lead ID"})
		return
	}

	response, err := h.leadService.GetByID(id)
	if err != nil {
		if err.Error() == "lead not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Lead not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateLead creates a new lead
// @Summary Create lead
// @Description Create a new lead
// @Tags leads
// @Accept json
// @Produce json
// @Param lead body models.LeadCreateRequest true "Lead data"
// @Success 201 {object} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads [post]
func (h *LeadHandler) CreateLead(c *gin.Context) {
	var req models.LeadCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.leadService.Create(&req, createdBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateLead updates a lead
// @Summary Update lead
// @Description Update an existing lead
// @Tags leads
// @Accept json
// @Produce json
// @Param id path string true "Lead ID"
// @Param lead body models.LeadUpdateRequest true "Lead update data"
// @Success 200 {object} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/{id} [put]
func (h *LeadHandler) UpdateLead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lead ID"})
		return
	}

	var req models.LeadUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.leadService.Update(id, &req)
	if err != nil {
		if err.Error() == "lead not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Lead not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteLead deletes a lead
// @Summary Delete lead
// @Description Delete a lead
// @Tags leads
// @Accept json
// @Produce json
// @Param id path string true "Lead ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/{id} [delete]
func (h *LeadHandler) DeleteLead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lead ID"})
		return
	}

	err = h.leadService.Delete(id)
	if err != nil {
		if err.Error() == "lead not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Lead not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetLeadStats retrieves lead statistics
// @Summary Get lead statistics
// @Description Get lead statistics
// @Tags leads
// @Accept json
// @Produce json
// @Success 200 {object} models.LeadStats
// @Failure 500 {object} map[string]interface{}
// @Router /leads/stats [get]
func (h *LeadHandler) GetLeadStats(c *gin.Context) {
	stats, err := h.leadService.GetStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// AssignLead assigns a lead to a user
// @Summary Assign lead
// @Description Assign a lead to a user
// @Tags leads
// @Accept json
// @Produce json
// @Param id path string true "Lead ID"
// @Param assignment body models.LeadAssignRequest true "Assignment data"
// @Success 200 {object} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/{id}/assign [post]
func (h *LeadHandler) AssignLead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lead ID"})
		return
	}

	var req models.LeadAssignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.leadService.AssignLead(id, &req)
	if err != nil {
		if err.Error() == "lead not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Lead not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ConvertLead converts a lead to a student
// @Summary Convert lead to student
// @Description Convert a lead to a student
// @Tags leads
// @Accept json
// @Produce json
// @Param id path string true "Lead ID"
// @Param conversion body models.LeadConvertRequest true "Conversion data"
// @Success 200 {object} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/{id}/convert [post]
func (h *LeadHandler) ConvertLead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lead ID"})
		return
	}

	var req models.LeadConvertRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	convertedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.leadService.ConvertToStudent(id, &req, convertedBy)
	if err != nil {
		if err.Error() == "lead not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Lead not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetFollowUpsDue retrieves leads with follow-ups due
// @Summary Get follow-ups due
// @Description Get leads with follow-ups due for a specific date
// @Tags leads
// @Accept json
// @Produce json
// @Param date query string false "Date (YYYY-MM-DD)" default(today)
// @Success 200 {array} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/follow-ups-due [get]
func (h *LeadHandler) GetFollowUpsDue(c *gin.Context) {
	dateStr := c.Query("date")
	var date time.Time
	var err error

	if dateStr != "" {
		date, err = time.Parse("2006-01-02", dateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date format. Use YYYY-MM-DD"})
			return
		}
	} else {
		date = time.Now()
	}

	responses, err := h.leadService.GetFollowUpsDue(date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetLeadsByAssignedUser retrieves leads assigned to a specific user
// @Summary Get leads by assigned user
// @Description Get leads assigned to a specific user
// @Tags leads
// @Accept json
// @Produce json
// @Param user_id path string true "User ID"
// @Success 200 {array} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/assigned/{user_id} [get]
func (h *LeadHandler) GetLeadsByAssignedUser(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	responses, err := h.leadService.GetByAssignedUser(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// BulkUpdateLeadStatus updates status for multiple leads
// @Summary Bulk update lead status
// @Description Update status for multiple leads
// @Tags leads
// @Accept json
// @Produce json
// @Param update body models.BulkLeadStatusUpdateRequest true "Bulk update data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/bulk-update-status [post]
func (h *LeadHandler) BulkUpdateLeadStatus(c *gin.Context) {
	var req models.BulkLeadStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	updatedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	err := h.leadService.BulkUpdateStatus(req.LeadIDs, req.Status, updatedBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Leads updated successfully"})
}

// UpdateFollowUp updates follow-up information for a lead
// @Summary Update follow-up
// @Description Update follow-up information for a lead
// @Tags leads
// @Accept json
// @Produce json
// @Param id path string true "Lead ID"
// @Param follow_up body models.LeadFollowUpRequest true "Follow-up data"
// @Success 200 {object} models.LeadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /leads/{id}/follow-up [post]
func (h *LeadHandler) UpdateFollowUp(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lead ID"})
		return
	}

	var req models.LeadFollowUpRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.leadService.UpdateFollowUp(id, *req.FollowUpDate, req.Notes)
	if err != nil {
		if err.Error() == "lead not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Lead not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}
