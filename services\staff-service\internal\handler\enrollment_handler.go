package handler

import (
	"net/http"
	"strconv"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// EnrollmentHandler handles enrollment-related HTTP requests
type EnrollmentHandler struct {
	enrollmentService service.EnrollmentService
}

// NewEnrollmentHandler creates a new enrollment handler
func NewEnrollmentHandler(enrollmentService service.EnrollmentService) *EnrollmentHandler {
	return &EnrollmentHandler{
		enrollmentService: enrollmentService,
	}
}

// GetEnrollments retrieves enrollments with pagination and filtering
// @Summary Get enrollments
// @Description Get enrollments with pagination and filtering
// @Tags enrollments
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Enrollment status"
// @Param student_id query string false "Student ID"
// @Param course_id query string false "Course ID"
// @Param payment_status query string false "Payment status"
// @Success 200 {object} models.EnrollmentListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments [get]
func (h *EnrollmentHandler) GetEnrollments(c *gin.Context) {
	req := &models.EnrollmentListRequest{}

	// Parse pagination
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		} else {
			req.Page = 1
		}
	} else {
		req.Page = 1
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.PageSize = l
		} else {
			req.PageSize = 10
		}
	} else {
		req.PageSize = 10
	}

	// Parse filters
	if status := c.Query("status"); status != "" {
		enrollmentStatus := models.EnrollmentStatus(status)
		if enrollmentStatus.IsValid() {
			req.Status = &enrollmentStatus
		}
	}

	if studentID := c.Query("student_id"); studentID != "" {
		if id, err := uuid.Parse(studentID); err == nil {
			req.StudentID = &id
		}
	}

	if courseID := c.Query("course_id"); courseID != "" {
		if id, err := uuid.Parse(courseID); err == nil {
			req.CourseID = &id
		}
	}

	if paymentStatus := c.Query("payment_status"); paymentStatus != "" {
		req.PaymentStatus = &paymentStatus
	}

	if enrolledBy := c.Query("enrolled_by_id"); enrolledBy != "" {
		if id, err := uuid.Parse(enrolledBy); err == nil {
			req.EnrolledByID = &id
		}
	}

	req.Search = c.Query("search")

	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			req.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			req.EndDate = &date
		}
	}

	response, err := h.enrollmentService.GetAll(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetEnrollment retrieves an enrollment by ID
// @Summary Get enrollment by ID
// @Description Get a specific enrollment by ID
// @Tags enrollments
// @Accept json
// @Produce json
// @Param id path string true "Enrollment ID"
// @Success 200 {object} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/{id} [get]
func (h *EnrollmentHandler) GetEnrollment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	response, err := h.enrollmentService.GetByID(id)
	if err != nil {
		if err.Error() == "enrollment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateEnrollment creates a new enrollment
// @Summary Create enrollment
// @Description Create a new enrollment
// @Tags enrollments
// @Accept json
// @Produce json
// @Param enrollment body models.EnrollmentCreateRequest true "Enrollment data"
// @Success 201 {object} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments [post]
func (h *EnrollmentHandler) CreateEnrollment(c *gin.Context) {
	var req models.EnrollmentCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	enrolledBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.enrollmentService.Create(&req, enrolledBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateEnrollment updates an enrollment
// @Summary Update enrollment
// @Description Update an existing enrollment
// @Tags enrollments
// @Accept json
// @Produce json
// @Param id path string true "Enrollment ID"
// @Param enrollment body models.EnrollmentUpdateRequest true "Enrollment update data"
// @Success 200 {object} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/{id} [put]
func (h *EnrollmentHandler) UpdateEnrollment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	var req models.EnrollmentUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.enrollmentService.Update(id, &req)
	if err != nil {
		if err.Error() == "enrollment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteEnrollment deletes an enrollment
// @Summary Delete enrollment
// @Description Delete an enrollment
// @Tags enrollments
// @Accept json
// @Produce json
// @Param id path string true "Enrollment ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/{id} [delete]
func (h *EnrollmentHandler) DeleteEnrollment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	err = h.enrollmentService.Delete(id)
	if err != nil {
		if err.Error() == "enrollment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetEnrollmentStats retrieves enrollment statistics
// @Summary Get enrollment statistics
// @Description Get enrollment statistics
// @Tags enrollments
// @Accept json
// @Produce json
// @Success 200 {object} models.EnrollmentStats
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/stats [get]
func (h *EnrollmentHandler) GetEnrollmentStats(c *gin.Context) {
	stats, err := h.enrollmentService.GetStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetEnrollmentsByStudent retrieves enrollments for a specific student
// @Summary Get enrollments by student
// @Description Get enrollments for a specific student
// @Tags enrollments
// @Accept json
// @Produce json
// @Param student_id path string true "Student ID"
// @Success 200 {array} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/by-student/{student_id} [get]
func (h *EnrollmentHandler) GetEnrollmentsByStudent(c *gin.Context) {
	studentIDStr := c.Param("student_id")
	studentID, err := uuid.Parse(studentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid student ID"})
		return
	}

	responses, err := h.enrollmentService.GetByStudent(studentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetEnrollmentsByCourse retrieves enrollments for a specific course
// @Summary Get enrollments by course
// @Description Get enrollments for a specific course
// @Tags enrollments
// @Accept json
// @Produce json
// @Param course_id path string true "Course ID"
// @Success 200 {array} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/by-course/{course_id} [get]
func (h *EnrollmentHandler) GetEnrollmentsByCourse(c *gin.Context) {
	courseIDStr := c.Param("course_id")
	courseID, err := uuid.Parse(courseIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	responses, err := h.enrollmentService.GetByCourse(courseID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetActiveEnrollments retrieves all active enrollments
// @Summary Get active enrollments
// @Description Get all active enrollments
// @Tags enrollments
// @Accept json
// @Produce json
// @Success 200 {array} models.EnrollmentResponse
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/active [get]
func (h *EnrollmentHandler) GetActiveEnrollments(c *gin.Context) {
	responses, err := h.enrollmentService.GetActiveEnrollments()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// GetOverduePayments retrieves enrollments with overdue payments
// @Summary Get overdue payments
// @Description Get enrollments with overdue payments
// @Tags enrollments
// @Accept json
// @Produce json
// @Success 200 {array} models.EnrollmentResponse
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/overdue-payments [get]
func (h *EnrollmentHandler) GetOverduePayments(c *gin.Context) {
	responses, err := h.enrollmentService.GetOverduePayments()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, responses)
}

// UpdatePaymentStatus updates payment status and amount for an enrollment
// @Summary Update payment status
// @Description Update payment status and amount for an enrollment
// @Tags enrollments
// @Accept json
// @Produce json
// @Param id path string true "Enrollment ID"
// @Param payment body models.EnrollmentPaymentUpdateRequest true "Payment update data"
// @Success 200 {object} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/{id}/payment [put]
func (h *EnrollmentHandler) UpdatePaymentStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	var req models.EnrollmentPaymentUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.enrollmentService.UpdatePaymentStatus(id, req.PaymentStatus, req.AmountPaid)
	if err != nil {
		if err.Error() == "enrollment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// BulkUpdatePaymentStatus updates payment status for multiple enrollments
// @Summary Bulk update payment status
// @Description Update payment status for multiple enrollments
// @Tags enrollments
// @Accept json
// @Produce json
// @Param update body models.BulkEnrollmentPaymentUpdateRequest true "Bulk payment update data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/bulk-update-payment [post]
func (h *EnrollmentHandler) BulkUpdatePaymentStatus(c *gin.Context) {
	var req models.BulkEnrollmentPaymentUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	updatedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	err := h.enrollmentService.BulkUpdatePaymentStatus(req.EnrollmentIDs, req.PaymentStatus, updatedBy)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Payment status updated successfully"})
}

// CompleteEnrollment marks an enrollment as completed
// @Summary Complete enrollment
// @Description Mark an enrollment as completed
// @Tags enrollments
// @Accept json
// @Produce json
// @Param id path string true "Enrollment ID"
// @Param completion body models.EnrollmentCompletionRequest true "Completion data"
// @Success 200 {object} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/{id}/complete [post]
func (h *EnrollmentHandler) CompleteEnrollment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	var req models.EnrollmentCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	completedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.enrollmentService.CompleteEnrollment(id, req.FinalGrade, completedBy)
	if err != nil {
		if err.Error() == "enrollment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DropEnrollment marks an enrollment as dropped
// @Summary Drop enrollment
// @Description Mark an enrollment as dropped
// @Tags enrollments
// @Accept json
// @Produce json
// @Param id path string true "Enrollment ID"
// @Param drop body models.EnrollmentDropRequest true "Drop data"
// @Success 200 {object} models.EnrollmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/{id}/drop [post]
func (h *EnrollmentHandler) DropEnrollment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	var req models.EnrollmentDropRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	droppedBy, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	response, err := h.enrollmentService.DropEnrollment(id, req.DropReason, droppedBy)
	if err != nil {
		if err.Error() == "enrollment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetRevenueByPeriod calculates revenue for a specific period
// @Summary Get revenue by period
// @Description Calculate revenue for a specific period
// @Tags enrollments
// @Accept json
// @Produce json
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /enrollments/revenue [get]
func (h *EnrollmentHandler) GetRevenueByPeriod(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Both start_date and end_date are required"})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
		return
	}

	if endDate.Before(startDate) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "End date cannot be before start date"})
		return
	}

	revenue, err := h.enrollmentService.GetRevenueByPeriod(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"start_date": startDate.Format("2006-01-02"),
		"end_date":   endDate.Format("2006-01-02"),
		"revenue":    revenue,
	})
}
