package database

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config holds database configuration
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
	TimeZone string
}

// ConnectionManager manages database connections
type ConnectionManager struct {
	config      *Config
	databaseURL string
	db          *gorm.DB
}

// NewConnectionManager creates a new connection manager
func NewConnectionManager(config *Config) *ConnectionManager {
	return &ConnectionManager{
		config: config,
	}
}

// NewConnectionManagerFromURL creates a new connection manager from a database URL
func NewConnectionManagerFromURL(databaseURL string) *ConnectionManager {
	return &ConnectionManager{
		config:      &Config{}, // Empty config since we'll use the URL directly
		databaseURL: databaseURL,
		db:          nil,
	}
}

// Connect establishes a database connection
func (cm *ConnectionManager) Connect() error {
	var dsn string
	if cm.databaseURL != "" {
		dsn = cm.databaseURL
	} else {
		dsn = cm.buildDSN()
	}

	// Configure GORM logger
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// Configure PostgreSQL connection with proper SSL handling
	config := postgres.Config{
		DSN: dsn,
		PreferSimpleProtocol: true, // disables implicit prepared statement usage
	}

	// Open database connection
	db, err := gorm.Open(postgres.New(config), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	cm.db = db
	return nil
}

// GetDB returns the database connection
func (cm *ConnectionManager) GetDB() *gorm.DB {
	return cm.db
}

// Close closes the database connection
func (cm *ConnectionManager) Close() error {
	if cm.db != nil {
		sqlDB, err := cm.db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// Ping checks if the database connection is alive
func (cm *ConnectionManager) Ping() error {
	if cm.db == nil {
		return fmt.Errorf("database connection is nil")
	}
	
	sqlDB, err := cm.db.DB()
	if err != nil {
		return err
	}
	
	return sqlDB.Ping()
}

// buildDSN builds the database connection string
func (cm *ConnectionManager) buildDSN() string {
	return fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		cm.config.Host,
		cm.config.Port,
		cm.config.User,
		cm.config.Password,
		cm.config.DBName,
		cm.config.SSLMode,
		cm.config.TimeZone,
	)
}

// HealthCheck performs a health check on the database
func (cm *ConnectionManager) HealthCheck() map[string]interface{} {
	result := map[string]interface{}{
		"status": "unhealthy",
	}

	if cm.db == nil {
		result["error"] = "database connection is nil"
		return result
	}

	// Check connection
	if err := cm.Ping(); err != nil {
		result["error"] = err.Error()
		return result
	}

	// Get connection stats
	sqlDB, err := cm.db.DB()
	if err != nil {
		result["error"] = err.Error()
		return result
	}

	stats := sqlDB.Stats()
	result["status"] = "healthy"
	result["open_connections"] = stats.OpenConnections
	result["in_use"] = stats.InUse
	result["idle"] = stats.Idle
	result["max_open_connections"] = stats.MaxOpenConnections

	return result
}

// Transaction executes a function within a database transaction
func (cm *ConnectionManager) Transaction(fn func(*gorm.DB) error) error {
	return cm.db.Transaction(fn)
}

// AutoMigrate runs auto migration for given models
func (cm *ConnectionManager) AutoMigrate(models ...interface{}) error {
	return cm.db.AutoMigrate(models...)
}

// CreateDatabase creates a database if it doesn't exist
func CreateDatabase(config *Config) error {
	// Connect to postgres database to create the target database
	tempConfig := *config
	tempConfig.DBName = "postgres"
	
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		tempConfig.Host,
		tempConfig.Port,
		tempConfig.User,
		tempConfig.Password,
		tempConfig.DBName,
		tempConfig.SSLMode,
		tempConfig.TimeZone,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to postgres database: %w", err)
	}

	// Get underlying sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	defer sqlDB.Close()

	// Check if database exists
	var exists bool
	err = db.Raw("SELECT EXISTS(SELECT datname FROM pg_catalog.pg_database WHERE datname = ?)", config.DBName).Scan(&exists).Error
	if err != nil {
		return fmt.Errorf("failed to check if database exists: %w", err)
	}

	// Create database if it doesn't exist
	if !exists {
		err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", config.DBName)).Error
		if err != nil {
			return fmt.Errorf("failed to create database: %w", err)
		}
	}

	return nil
}

// DropDatabase drops a database
func DropDatabase(config *Config) error {
	// Connect to postgres database to drop the target database
	tempConfig := *config
	tempConfig.DBName = "postgres"
	
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		tempConfig.Host,
		tempConfig.Port,
		tempConfig.User,
		tempConfig.Password,
		tempConfig.DBName,
		tempConfig.SSLMode,
		tempConfig.TimeZone,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to postgres database: %w", err)
	}

	// Get underlying sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	defer sqlDB.Close()

	// Drop database
	err = db.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", config.DBName)).Error
	if err != nil {
		return fmt.Errorf("failed to drop database: %w", err)
	}

	return nil
}

// GetConnectionStats returns connection statistics
func (cm *ConnectionManager) GetConnectionStats() map[string]interface{} {
	if cm.db == nil {
		return map[string]interface{}{
			"error": "database connection is nil",
		}
	}

	sqlDB, err := cm.db.DB()
	if err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections":     stats.MaxOpenConnections,
		"open_connections":         stats.OpenConnections,
		"in_use":                  stats.InUse,
		"idle":                    stats.Idle,
		"wait_count":              stats.WaitCount,
		"wait_duration":           stats.WaitDuration.String(),
		"max_idle_closed":         stats.MaxIdleClosed,
		"max_lifetime_closed":     stats.MaxLifetimeClosed,
	}
}

// SetConnectionPool configures the connection pool
func (cm *ConnectionManager) SetConnectionPool(maxIdle, maxOpen int, maxLifetime time.Duration) error {
	if cm.db == nil {
		return fmt.Errorf("database connection is nil")
	}

	sqlDB, err := cm.db.DB()
	if err != nil {
		return err
	}

	sqlDB.SetMaxIdleConns(maxIdle)
	sqlDB.SetMaxOpenConns(maxOpen)
	sqlDB.SetConnMaxLifetime(maxLifetime)

	return nil
}

// EnableQueryLogging enables or disables query logging
func (cm *ConnectionManager) EnableQueryLogging(enable bool) {
	if cm.db == nil {
		return
	}

	if enable {
		cm.db.Logger = logger.Default.LogMode(logger.Info)
	} else {
		cm.db.Logger = logger.Default.LogMode(logger.Silent)
	}
}
