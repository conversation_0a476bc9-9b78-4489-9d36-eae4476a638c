import { apiService } from './api';
import type {
  Payment,
  PaymentCreateRequest,
  PaymentUpdateRequest,
  PaymentFilters,
  PaymentStats,
  PaginatedResponse
} from '@/types/payment';

class PaymentService {
  private readonly API_PREFIX = '/api/v1/payment';

  // Get all payments with pagination and filters
  async getPayments(params?: {
    page?: number;
    limit?: number;
    filters?: PaymentFilters;
  }): Promise<PaginatedResponse<Payment>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.filters?.search) queryParams.append('search', params.filters.search);
    if (params?.filters?.status) queryParams.append('status', params.filters.status);
    if (params?.filters?.method) queryParams.append('method', params.filters.method);
    if (params?.filters?.student_id) queryParams.append('student_id', params.filters.student_id);
    if (params?.filters?.course_id) queryParams.append('course_id', params.filters.course_id);
    if (params?.filters?.date_from) queryParams.append('date_from', params.filters.date_from);
    if (params?.filters?.date_to) queryParams.append('date_to', params.filters.date_to);
    if (params?.filters?.amount_min) queryParams.append('amount_min', params.filters.amount_min.toString());
    if (params?.filters?.amount_max) queryParams.append('amount_max', params.filters.amount_max.toString());

    const url = queryParams.toString() ? `${this.API_PREFIX}/payments?${queryParams}` : `${this.API_PREFIX}/payments`;
    return apiService.get<PaginatedResponse<Payment>>(url);
  }

  // Get payment by ID
  async getPayment(id: string): Promise<Payment> {
    return apiService.get<Payment>(`${this.API_PREFIX}/payments/${id}`);
  }

  // Create new payment
  async createPayment(data: PaymentCreateRequest): Promise<Payment> {
    return apiService.post<Payment>(`${this.API_PREFIX}/payments`, data);
  }

  // Update payment
  async updatePayment(id: string, data: PaymentUpdateRequest): Promise<Payment> {
    return apiService.put<Payment>(`${this.API_PREFIX}/payments/${id}`, data);
  }

  // Delete payment
  async deletePayment(id: string): Promise<void> {
    return apiService.delete<void>(`${this.API_PREFIX}/payments/${id}`);
  }

  // Get payment statistics
  async getPaymentStats(): Promise<PaymentStats> {
    return apiService.get<PaymentStats>(`${this.API_PREFIX}/payments/stats`);
  }

  // Payment status management
  async updatePaymentStatus(id: string, status: string, notes?: string): Promise<Payment> {
    return apiService.patch<Payment>(`${this.API_PREFIX}/payments/${id}/status`, { 
      status,
      notes 
    });
  }

  // Payment verification
  async verifyPayment(id: string): Promise<Payment> {
    return apiService.post<Payment>(`${this.API_PREFIX}/payments/${id}/verify`);
  }

  // Refund management
  async refundPayment(id: string, data: {
    amount?: number;
    reason: string;
    refund_method?: string;
  }): Promise<Payment> {
    return apiService.post<Payment>(`${this.API_PREFIX}/payments/${id}/refund`, data);
  }

  // Payment receipts
  async generateReceipt(id: string): Promise<{ receipt_url: string }> {
    return apiService.post<{ receipt_url: string }>(`${this.API_PREFIX}/payments/${id}/receipt`);
  }

  async sendReceiptEmail(id: string, email?: string): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/payments/${id}/send-receipt`, { email });
  }

  // Payment analytics
  async getPaymentAnalytics(params?: {
    date_from?: string;
    date_to?: string;
    group_by?: 'day' | 'week' | 'month';
    breakdown_by?: 'method' | 'status' | 'course';
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.date_from) queryParams.append('date_from', params.date_from);
    if (params?.date_to) queryParams.append('date_to', params.date_to);
    if (params?.group_by) queryParams.append('group_by', params.group_by);
    if (params?.breakdown_by) queryParams.append('breakdown_by', params.breakdown_by);

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/analytics?${queryParams}` 
      : `${this.API_PREFIX}/analytics`;
    
    return apiService.get<any>(url);
  }

  // Student payment history
  async getStudentPayments(studentId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Payment>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/students/${studentId}/payments?${queryParams}` 
      : `${this.API_PREFIX}/students/${studentId}/payments`;
    
    return apiService.get<PaginatedResponse<Payment>>(url);
  }

  // Course payment summary
  async getCoursePayments(courseId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Payment>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = queryParams.toString() 
      ? `${this.API_PREFIX}/courses/${courseId}/payments?${queryParams}` 
      : `${this.API_PREFIX}/courses/${courseId}/payments`;
    
    return apiService.get<PaginatedResponse<Payment>>(url);
  }

  // Bulk operations
  async bulkUpdatePayments(ids: string[], data: Partial<PaymentUpdateRequest>): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/payments/bulk-update`, {
      payment_ids: ids,
      ...data
    });
  }

  async bulkVerifyPayments(ids: string[]): Promise<void> {
    return apiService.post<void>(`${this.API_PREFIX}/payments/bulk-verify`, {
      payment_ids: ids
    });
  }
}

// Create and export singleton instance
export const paymentService = new PaymentService();
export default paymentService;
