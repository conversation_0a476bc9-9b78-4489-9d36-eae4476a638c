#!/usr/bin/env node

/**
 * Critical Path System Test
 * Tests only essential services for CRM functionality
 */

const https = require('https');

// Service endpoints
const SERVICES = {
  frontend: 'crm-frontend-a1kp.onrender.com',
  apiGateway: 'crm-api-gateway.onrender.com',
  auth: 'crm-auth-service.onrender.com',
  admin: 'crm-admin-service.onrender.com',
  staff: 'crm-staff-service.onrender.com',
  payment: 'crm-payment-service.onrender.com'
};

function testService(hostname, path = '/', expectedStatus = 200) {
  return new Promise((resolve) => {
    const options = {
      hostname,
      port: 443,
      path,
      method: 'GET',
      timeout: 10000,
      headers: {
        'User-Agent': 'CRM-System-Test/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        const success = res.statusCode === expectedStatus;
        resolve({
          success,
          status: res.statusCode,
          message: success ? 'OK' : `Expected ${expectedStatus}, got ${res.statusCode}`,
          data: data.substring(0, 200)
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        status: 0,
        message: error.message,
        data: null
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        status: 0,
        message: 'Request timeout',
        data: null
      });
    });

    req.end();
  });
}

async function testCriticalPath() {
  console.log('🔍 Testing Critical Path Services...\n');

  const results = {};

  // Test Frontend
  console.log('1. Testing Frontend...');
  results.frontend = await testService(SERVICES.frontend);
  console.log(`   ${results.frontend.success ? '✅' : '❌'} Frontend: ${results.frontend.message}`);

  // Test Backend Services
  console.log('\n2. Testing Backend Services...');
  
  const backendServices = ['auth', 'admin', 'staff', 'payment'];
  for (const service of backendServices) {
    results[service] = await testService(SERVICES[service], '/health');
    console.log(`   ${results[service].success ? '✅' : '❌'} ${service.toUpperCase()}: ${results[service].message}`);
  }

  // Test API Gateway (Critical Blocker)
  console.log('\n3. Testing API Gateway (Critical)...');
  results.apiGateway = await testService(SERVICES.apiGateway, '/health');
  console.log(`   ${results.apiGateway.success ? '✅' : '❌'} API Gateway: ${results.apiGateway.message}`);

  // Summary
  console.log('\n📊 Critical Path Summary:');
  const totalServices = Object.keys(results).length;
  const healthyServices = Object.values(results).filter(r => r.success).length;
  
  console.log(`   Healthy Services: ${healthyServices}/${totalServices}`);
  
  if (results.frontend.success && results.apiGateway.success) {
    console.log('   🎉 Critical path is operational!');
  } else if (results.frontend.success && !results.apiGateway.success) {
    console.log('   🚨 BLOCKER: API Gateway is down - Frontend cannot communicate with backend');
  } else if (!results.frontend.success) {
    console.log('   🚨 CRITICAL: Frontend is down');
  }

  return results;
}

// Run the test
if (require.main === module) {
  testCriticalPath().catch(console.error);
}

module.exports = { testCriticalPath, testService };
