package repository

import (
	"fmt"
	"strings"
	"time"

	"staff-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TeacherRepository handles teacher data operations
type TeacherRepository interface {
	GetAll(req *models.TeacherListRequest) ([]*models.Teacher, int64, error)
	GetByID(id uuid.UUID) (*models.Teacher, error)
	GetByTeacherID(teacherID string) (*models.Teacher, error)
	GetByEmail(email string) (*models.Teacher, error)
	GetByUserID(userID uuid.UUID) (*models.Teacher, error)
	Create(teacher *models.Teacher) error
	Update(id uuid.UUID, updates map[string]interface{}) error
	Delete(id uuid.UUID) error
	GetStats() (*models.TeacherStats, error)
	GetAvailableTeachers() ([]*models.Teacher, error)
	GetTeacherWorkload(teacherID uuid.UUID) (*TeacherWorkload, error)
	GenerateTeacherID() (string, error)
	UpdatePerformanceMetrics(teacherID uuid.UUID) error
	GetTopRatedTeachers(limit int) ([]*models.Teacher, error)
}

type teacherRepository struct {
	db *gorm.DB
}

// TeacherWorkload represents a teacher's current workload
type TeacherWorkload struct {
	TeacherID       uuid.UUID `json:"teacher_id"`
	TeacherName     string    `json:"teacher_name"`
	TotalCourses    int       `json:"total_courses"`
	TotalStudents   int       `json:"total_students"`
	WeeklyHours     float64   `json:"weekly_hours"`
	UpcomingClasses int       `json:"upcoming_classes"`
}

// NewTeacherRepository creates a new teacher repository
func NewTeacherRepository(db *gorm.DB) TeacherRepository {
	return &teacherRepository{db: db}
}

// GetAll retrieves teachers with pagination and filtering
func (r *teacherRepository) GetAll(req *models.TeacherListRequest) ([]*models.Teacher, int64, error) {
	var teachers []*models.Teacher
	var total int64

	query := r.db.Model(&models.Teacher{}).Preload("User").Preload("CreatedBy")

	// Apply filters
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	if req.CreatedByID != nil {
		query = query.Where("created_by_id = ?", *req.CreatedByID)
	}

	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where(
			"LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ? OR LOWER(teacher_id) LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm,
		)
	}

	if req.StartDate != nil {
		query = query.Where("hire_date >= ?", *req.StartDate)
	}

	if req.EndDate != nil {
		query = query.Where("hire_date <= ?", *req.EndDate)
	}

	if req.Available != nil && *req.Available {
		// Filter teachers available for new assignments
		query = query.Where("status = ?", models.TeacherStatusActive)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count teachers: %w", err)
	}

	// Apply pagination
	offset := req.GetOffset()
	limit := req.GetLimit()

	if err := query.Offset(offset).Limit(limit).Order("hire_date DESC").Find(&teachers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get teachers: %w", err)
	}

	return teachers, total, nil
}

// GetByID retrieves a teacher by ID
func (r *teacherRepository) GetByID(id uuid.UUID) (*models.Teacher, error) {
	var teacher models.Teacher
	if err := r.db.Preload("User").Preload("CreatedBy").Preload("Courses").Preload("Schedules").
		First(&teacher, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("teacher not found")
		}
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}
	return &teacher, nil
}

// GetByTeacherID retrieves a teacher by teacher ID
func (r *teacherRepository) GetByTeacherID(teacherID string) (*models.Teacher, error) {
	var teacher models.Teacher
	if err := r.db.Preload("User").Preload("CreatedBy").First(&teacher, "teacher_id = ?", teacherID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("teacher not found")
		}
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}
	return &teacher, nil
}

// GetByEmail retrieves a teacher by email
func (r *teacherRepository) GetByEmail(email string) (*models.Teacher, error) {
	var teacher models.Teacher
	if err := r.db.Preload("User").Preload("CreatedBy").First(&teacher, "email = ?", email).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("teacher not found")
		}
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}
	return &teacher, nil
}

// GetByUserID retrieves a teacher by user ID
func (r *teacherRepository) GetByUserID(userID uuid.UUID) (*models.Teacher, error) {
	var teacher models.Teacher
	if err := r.db.Preload("User").Preload("CreatedBy").First(&teacher, "user_id = ?", userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("teacher not found")
		}
		return nil, fmt.Errorf("failed to get teacher: %w", err)
	}
	return &teacher, nil
}

// Create creates a new teacher
func (r *teacherRepository) Create(teacher *models.Teacher) error {
	// Generate teacher ID if not provided
	if teacher.TeacherID == "" {
		teacherID, err := r.GenerateTeacherID()
		if err != nil {
			return fmt.Errorf("failed to generate teacher ID: %w", err)
		}
		teacher.TeacherID = teacherID
	}

	if err := r.db.Create(teacher).Error; err != nil {
		return fmt.Errorf("failed to create teacher: %w", err)
	}
	return nil
}

// Update updates a teacher
func (r *teacherRepository) Update(id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.Model(&models.Teacher{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update teacher: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("teacher not found")
	}
	return nil
}

// Delete soft deletes a teacher
func (r *teacherRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&models.Teacher{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete teacher: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("teacher not found")
	}
	return nil
}

// GetStats retrieves teacher statistics
func (r *teacherRepository) GetStats() (*models.TeacherStats, error) {
	stats := &models.TeacherStats{
		TeachersByStatus: make(map[models.TeacherStatus]int64),
	}

	// Total teachers
	if err := r.db.Model(&models.Teacher{}).Count(&stats.TotalTeachers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total teachers: %w", err)
	}

	// Teachers by status
	statuses := []models.TeacherStatus{
		models.TeacherStatusActive, models.TeacherStatusInactive,
		models.TeacherStatusSuspended, models.TeacherStatusOnLeave,
	}

	for _, status := range statuses {
		var count int64
		if err := r.db.Model(&models.Teacher{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("failed to count teachers by status %s: %w", status, err)
		}
		stats.TeachersByStatus[status] = count
	}

	// Average rating
	var avgRating float64
	if err := r.db.Model(&models.Teacher{}).Where("rating IS NOT NULL").Select("AVG(rating)").Scan(&avgRating).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average rating: %w", err)
	}
	if avgRating > 0 {
		stats.AverageRating = &avgRating
	}

	// Average salary
	var avgSalary float64
	if err := r.db.Model(&models.Teacher{}).Where("salary IS NOT NULL").Select("AVG(salary)").Scan(&avgSalary).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average salary: %w", err)
	}
	if avgSalary > 0 {
		stats.AverageSalary = &avgSalary
	}

	// Average experience
	var avgExperience float64
	if err := r.db.Model(&models.Teacher{}).Select("AVG(experience)").Scan(&avgExperience).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average experience: %w", err)
	}
	if avgExperience > 0 {
		stats.AverageExperience = &avgExperience
	}

	// Top rated teachers
	var topTeachers []*models.Teacher
	if err := r.db.Where("rating IS NOT NULL").Order("rating DESC").Limit(5).Preload("User").Find(&topTeachers).Error; err != nil {
		return nil, fmt.Errorf("failed to get top rated teachers: %w", err)
	}

	// Convert to responses
	topTeachersResponse := make([]*models.TeacherResponse, len(topTeachers))
	for i, teacher := range topTeachers {
		topTeachersResponse[i] = teacher.ToResponse()
	}
	stats.TopRatedTeachers = topTeachersResponse

	// New teachers today
	if err := r.db.Model(&models.Teacher{}).Where("DATE(hire_date) = CURRENT_DATE").Count(&stats.NewTeachersToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count new teachers today: %w", err)
	}

	// New teachers this week
	if err := r.db.Model(&models.Teacher{}).Where("hire_date >= DATE_TRUNC('week', CURRENT_DATE)").Count(&stats.NewTeachersThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count new teachers this week: %w", err)
	}

	return stats, nil
}

// GetAvailableTeachers retrieves teachers available for course assignment
func (r *teacherRepository) GetAvailableTeachers() ([]*models.Teacher, error) {
	var teachers []*models.Teacher
	if err := r.db.Where("status = ?", models.TeacherStatusActive).
		Preload("User").Preload("CreatedBy").Find(&teachers).Error; err != nil {
		return nil, fmt.Errorf("failed to get available teachers: %w", err)
	}
	return teachers, nil
}

// GetTeacherWorkload calculates a teacher's current workload
func (r *teacherRepository) GetTeacherWorkload(teacherID uuid.UUID) (*TeacherWorkload, error) {
	teacher, err := r.GetByID(teacherID)
	if err != nil {
		return nil, err
	}

	workload := &TeacherWorkload{
		TeacherID:   teacherID,
		TeacherName: teacher.GetFullName(),
	}

	// Count total courses
	var totalCourses int64
	if err := r.db.Model(&models.Course{}).Where("instructor_id = ? AND status = ?",
		teacherID, models.CourseStatusActive).Count(&totalCourses).Error; err != nil {
		return nil, fmt.Errorf("failed to count courses: %w", err)
	}
	workload.TotalCourses = int(totalCourses)

	// Count total students (through enrollments)
	var totalStudents int64
	if err := r.db.Table("enrollments").
		Joins("JOIN courses ON courses.id = enrollments.course_id").
		Where("courses.instructor_id = ? AND enrollments.status = ?", 
			teacherID, models.EnrollmentStatusActive).
		Count(&totalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count students: %w", err)
	}
	workload.TotalStudents = int(totalStudents)

	// Calculate weekly hours from schedules
	var weeklyHours float64
	if err := r.db.Model(&models.Schedule{}).
		Where("teacher_id = ? AND status = ?", teacherID, models.ScheduleStatusActive).
		Select("SUM(duration)").Scan(&weeklyHours).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate weekly hours: %w", err)
	}
	workload.WeeklyHours = weeklyHours / 60.0 // Convert minutes to hours

	// Count upcoming classes (next 7 days)
	nextWeek := time.Now().AddDate(0, 0, 7)
	var upcomingClasses int64
	if err := r.db.Model(&models.Schedule{}).
		Where("teacher_id = ? AND status = ? AND start_time BETWEEN ? AND ?",
			teacherID, models.ScheduleStatusActive, time.Now(), nextWeek).
		Count(&upcomingClasses).Error; err != nil {
		return nil, fmt.Errorf("failed to count upcoming classes: %w", err)
	}
	workload.UpcomingClasses = int(upcomingClasses)

	return workload, nil
}

// GenerateTeacherID generates a unique teacher ID
func (r *teacherRepository) GenerateTeacherID() (string, error) {
	year := time.Now().Year()
	
	// Get the count of teachers created this year
	var count int64
	if err := r.db.Model(&models.Teacher{}).Where("EXTRACT(YEAR FROM hire_date) = ?", year).Count(&count).Error; err != nil {
		return "", fmt.Errorf("failed to count teachers for year: %w", err)
	}

	// Generate ID in format: TCH2024001, TCH2024002, etc.
	teacherID := fmt.Sprintf("TCH%d%03d", year, count+1)

	// Check if ID already exists (unlikely but safe)
	var existingTeacher models.Teacher
	if err := r.db.Where("teacher_id = ?", teacherID).First(&existingTeacher).Error; err == nil {
		// ID exists, try with incremented number
		for i := 1; i <= 1000; i++ {
			teacherID = fmt.Sprintf("TCH%d%03d", year, count+1+int64(i))
			if err := r.db.Where("teacher_id = ?", teacherID).First(&existingTeacher).Error; err == gorm.ErrRecordNotFound {
				break
			}
		}
	}

	return teacherID, nil
}

// UpdatePerformanceMetrics updates teacher's performance metrics
func (r *teacherRepository) UpdatePerformanceMetrics(teacherID uuid.UUID) error {
	// Count total students taught
	var totalStudents int64
	if err := r.db.Table("enrollments").
		Joins("JOIN courses ON courses.id = enrollments.course_id").
		Where("courses.instructor_id = ?", teacherID).
		Count(&totalStudents).Error; err != nil {
		return fmt.Errorf("failed to count total students: %w", err)
	}

	// Count total courses taught
	var totalCourses int64
	if err := r.db.Model(&models.Course{}).Where("instructor_id = ?", teacherID).Count(&totalCourses).Error; err != nil {
		return fmt.Errorf("failed to count total courses: %w", err)
	}

	// Update teacher record
	updates := map[string]interface{}{
		"total_students": totalStudents,
		"total_courses":  totalCourses,
		"updated_at":     time.Now(),
	}

	return r.Update(teacherID, updates)
}

// GetTopRatedTeachers retrieves top-rated teachers
func (r *teacherRepository) GetTopRatedTeachers(limit int) ([]*models.Teacher, error) {
	var teachers []*models.Teacher
	if err := r.db.Where("rating IS NOT NULL").Order("rating DESC").Limit(limit).
		Preload("User").Find(&teachers).Error; err != nil {
		return nil, fmt.Errorf("failed to get top rated teachers: %w", err)
	}
	return teachers, nil
}
