package handlers

import (
	"net/http"
	"notification-service/internal/services"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AnalyticsHandler handles analytics-related HTTP requests
type AnalyticsHandler struct {
	analyticsService *services.AnalyticsService
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService *services.AnalyticsService) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
	}
}

// GetNotificationAnalytics returns comprehensive notification analytics
func (h *AnalyticsHandler) GetNotificationAnalytics(c *gin.Context) {
	// Parse date range
	startDate := time.Now().AddDate(0, 0, -30) // Default: last 30 days
	endDate := time.Now()
	
	if start := c.Query("start_date"); start != "" {
		if date, err := time.Parse("2006-01-02", start); err == nil {
			startDate = date
		}
	}
	if end := c.Query("end_date"); end != "" {
		if date, err := time.Parse("2006-01-02", end); err == nil {
			endDate = date.Add(24 * time.Hour) // End of day
		}
	}
	
	// Parse include details flag
	includeDetails := c.Query("include_details") == "true"
	
	analytics, err := h.analyticsService.GetNotificationAnalytics(startDate, endDate, includeDetails)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "ANALYTICS_FAILED",
				"message": "Failed to retrieve notification analytics",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// GetUserAnalytics returns analytics for a specific user
func (h *AnalyticsHandler) GetUserAnalytics(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_USER_ID",
				"message": "Invalid user ID format",
			},
		})
		return
	}
	
	// Parse date range
	startDate := time.Now().AddDate(0, 0, -30) // Default: last 30 days
	endDate := time.Now()
	
	if start := c.Query("start_date"); start != "" {
		if date, err := time.Parse("2006-01-02", start); err == nil {
			startDate = date
		}
	}
	if end := c.Query("end_date"); end != "" {
		if date, err := time.Parse("2006-01-02", end); err == nil {
			endDate = date.Add(24 * time.Hour) // End of day
		}
	}
	
	analytics, err := h.analyticsService.GetUserNotificationAnalytics(userID, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "USER_ANALYTICS_FAILED",
				"message": "Failed to retrieve user analytics",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// GetTemplateAnalytics returns template usage analytics
func (h *AnalyticsHandler) GetTemplateAnalytics(c *gin.Context) {
	// Parse date range
	startDate := time.Now().AddDate(0, 0, -30) // Default: last 30 days
	endDate := time.Now()
	
	if start := c.Query("start_date"); start != "" {
		if date, err := time.Parse("2006-01-02", start); err == nil {
			startDate = date
		}
	}
	if end := c.Query("end_date"); end != "" {
		if date, err := time.Parse("2006-01-02", end); err == nil {
			endDate = date.Add(24 * time.Hour) // End of day
		}
	}
	
	analytics, err := h.analyticsService.GetTemplateAnalytics(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "TEMPLATE_ANALYTICS_FAILED",
				"message": "Failed to retrieve template analytics",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// GetTopPerformingTemplates returns the top performing templates
func (h *AnalyticsHandler) GetTopPerformingTemplates(c *gin.Context) {
	// Parse limit
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 50 {
		limit = 10
	}
	
	// Parse date range
	startDate := time.Now().AddDate(0, 0, -30) // Default: last 30 days
	endDate := time.Now()
	
	if start := c.Query("start_date"); start != "" {
		if date, err := time.Parse("2006-01-02", start); err == nil {
			startDate = date
		}
	}
	if end := c.Query("end_date"); end != "" {
		if date, err := time.Parse("2006-01-02", end); err == nil {
			endDate = date.Add(24 * time.Hour) // End of day
		}
	}
	
	templates, err := h.analyticsService.GetTopPerformingTemplates(limit, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "TOP_TEMPLATES_FAILED",
				"message": "Failed to retrieve top performing templates",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    templates,
	})
}

// GetFailureAnalysis returns detailed failure analysis
func (h *AnalyticsHandler) GetFailureAnalysis(c *gin.Context) {
	// Parse date range
	startDate := time.Now().AddDate(0, 0, -7) // Default: last 7 days
	endDate := time.Now()
	
	if start := c.Query("start_date"); start != "" {
		if date, err := time.Parse("2006-01-02", start); err == nil {
			startDate = date
		}
	}
	if end := c.Query("end_date"); end != "" {
		if date, err := time.Parse("2006-01-02", end); err == nil {
			endDate = date.Add(24 * time.Hour) // End of day
		}
	}
	
	analysis, err := h.analyticsService.GetFailureAnalysis(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "FAILURE_ANALYSIS_FAILED",
				"message": "Failed to retrieve failure analysis",
				"details": err.Error(),
			},
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analysis,
	})
}

// GetDashboardData returns dashboard summary data
func (h *AnalyticsHandler) GetDashboardData(c *gin.Context) {
	// Get analytics for different time periods
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	yesterday := today.AddDate(0, 0, -1)
	last7Days := today.AddDate(0, 0, -7)
	last30Days := today.AddDate(0, 0, -30)
	
	// Get today's analytics
	todayAnalytics, err := h.analyticsService.GetNotificationAnalytics(today, now, false)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "DASHBOARD_FAILED",
				"message": "Failed to retrieve dashboard data",
				"details": err.Error(),
			},
		})
		return
	}
	
	// Get yesterday's analytics for comparison
	yesterdayAnalytics, err := h.analyticsService.GetNotificationAnalytics(yesterday, today, false)
	if err != nil {
		yesterdayAnalytics = &services.NotificationAnalytics{} // Default empty if failed
	}
	
	// Get 7-day analytics
	weekAnalytics, err := h.analyticsService.GetNotificationAnalytics(last7Days, now, true)
	if err != nil {
		weekAnalytics = &services.NotificationAnalytics{} // Default empty if failed
	}
	
	// Get 30-day analytics
	monthAnalytics, err := h.analyticsService.GetNotificationAnalytics(last30Days, now, false)
	if err != nil {
		monthAnalytics = &services.NotificationAnalytics{} // Default empty if failed
	}
	
	// Calculate growth rates
	todayGrowth := calculateGrowthRate(todayAnalytics.TotalNotifications, yesterdayAnalytics.TotalNotifications)
	deliveryGrowth := calculateGrowthRate(todayAnalytics.DeliveredNotifications, yesterdayAnalytics.DeliveredNotifications)
	
	dashboard := gin.H{
		"today": gin.H{
			"total_notifications":     todayAnalytics.TotalNotifications,
			"delivered_notifications": todayAnalytics.DeliveredNotifications,
			"failed_notifications":    todayAnalytics.FailedNotifications,
			"delivery_rate":           todayAnalytics.DeliveryRate,
			"growth_rate":             todayGrowth,
			"delivery_growth_rate":    deliveryGrowth,
		},
		"this_week": gin.H{
			"total_notifications":     weekAnalytics.TotalNotifications,
			"delivered_notifications": weekAnalytics.DeliveredNotifications,
			"failed_notifications":    weekAnalytics.FailedNotifications,
			"delivery_rate":           weekAnalytics.DeliveryRate,
			"avg_delivery_time":       weekAnalytics.AvgDeliveryTime,
			"daily_trends":            weekAnalytics.DailyTrends,
			"channel_trends":          weekAnalytics.ChannelTrends,
		},
		"this_month": gin.H{
			"total_notifications":     monthAnalytics.TotalNotifications,
			"delivered_notifications": monthAnalytics.DeliveredNotifications,
			"failed_notifications":    monthAnalytics.FailedNotifications,
			"delivery_rate":           monthAnalytics.DeliveryRate,
			"email_notifications":     monthAnalytics.EmailNotifications,
			"sms_notifications":       monthAnalytics.SMSNotifications,
			"in_app_notifications":    monthAnalytics.InAppNotifications,
			"push_notifications":      monthAnalytics.PushNotifications,
		},
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    dashboard,
	})
}

// calculateGrowthRate calculates the growth rate between two values
func calculateGrowthRate(current, previous int64) float64 {
	if previous == 0 {
		if current > 0 {
			return 100.0 // 100% growth from 0
		}
		return 0.0
	}
	
	return float64(current-previous) / float64(previous) * 100
}
