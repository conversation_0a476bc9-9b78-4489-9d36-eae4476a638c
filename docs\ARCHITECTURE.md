# 🏗️ System Architecture - Go Docker Platform

Detailed system architecture, design decisions, and technical implementation of the CRM microservices platform.

## 📋 Architecture Overview

The Go Docker Platform implements a modern microservices architecture designed for scalability, maintainability, and high availability. The system follows domain-driven design principles with clear service boundaries and responsibilities.

## 🎯 Design Principles

### Core Principles
- **Single Responsibility**: Each service has a single, well-defined purpose
- **Database Per Service**: Data isolation and service independence
- **API-First Design**: RESTful APIs with consistent patterns
- **Stateless Services**: Horizontal scaling and fault tolerance
- **Event-Driven Communication**: Loose coupling between services
- **Security by Design**: Authentication and authorization at every layer

### Architectural Patterns
- **Microservices Architecture**: Independent, deployable services
- **API Gateway Pattern**: Single entry point for client requests
- **Database Per Service**: Data ownership and isolation
- **Circuit Breaker Pattern**: Fault tolerance and resilience
- **Saga Pattern**: Distributed transaction management
- **CQRS Pattern**: Command and Query Responsibility Segregation

## 🏗️ System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                          Client Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  Web App  │  Mobile App  │  Admin Panel  │  Third-party APIs   │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API Gateway                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │  Routing │ Auth │ Rate Limiting │ Load Balancing │ CORS │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    ▼               ▼               ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│Auth Service │ │Admin Service│ │Staff Service│ │Payment Svc  │
│             │ │             │ │             │ │             │
│ ┌─────────┐ │ │ ┌─────────┐ │ │ ┌─────────┐ │ │ ┌─────────┐ │
│ │Auth DB  │ │ │ │Admin DB │ │ │ │Staff DB │ │ │ │Payment  │ │
│ │         │ │ │ │         │ │ │ │         │ │ │ │DB       │ │
│ └─────────┘ │ │ └─────────┘ │ │ └─────────┘ │ │ └─────────┘ │
└─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────┐
                                              │Notification │
                                              │Service      │
                                              │ ┌─────────┐ │
                                              │ │Notif DB │ │
                                              │ └─────────┘ │
                                              └─────────────┘
```

## 🔧 Service Architecture

### API Gateway (Port: 8080)
**Purpose**: Single entry point, routing, and cross-cutting concerns

**Responsibilities**:
- Request routing to appropriate microservices
- Authentication and authorization
- Rate limiting and throttling
- CORS handling
- Load balancing
- Request/response logging
- Circuit breaker implementation

**Technology Stack**:
- **Framework**: Gin (Go)
- **Routing**: Path-based routing with middleware
- **Load Balancing**: Round-robin algorithm
- **Rate Limiting**: Token bucket algorithm
- **Circuit Breaker**: Hystrix-style implementation

### Auth Service (Port: 8081)
**Purpose**: Authentication, authorization, and user management

**Responsibilities**:
- User registration and login
- JWT token generation and validation
- Password management and security
- Session management
- Role-based access control
- User profile management

**Database Schema**:
- **Users**: User accounts and profiles
- **Sessions**: Active user sessions
- **Roles**: User roles and permissions

**Security Features**:
- bcrypt password hashing (cost: 12)
- JWT tokens with RS256 signing
- Session timeout and cleanup
- Rate limiting for auth endpoints
- Account lockout after failed attempts

### Admin Service (Port: 8082)
**Purpose**: Administrative functions and system management

**Responsibilities**:
- User management (CRUD operations)
- System analytics and reporting
- Audit logging and compliance
- System configuration
- Data export and reporting

**Database Schema**:
- **Analytics**: System metrics and KPIs
- **Audit Logs**: User actions and system events
- **Reports**: Generated reports and exports

**Access Control**:
- **ADMIN**: Full system access
- **CASHIER**: Financial operations access

### Staff Service (Port: 8083)
**Purpose**: Daily operations and business logic

**Responsibilities**:
- Lead management and conversion
- Student enrollment and management
- Course creation and scheduling
- Academic progress tracking
- Attendance management

**Database Schema**:
- **Leads**: Potential student inquiries
- **Students**: Enrolled student records
- **Courses**: Course catalog and schedules
- **Enrollments**: Student-course relationships
- **Attendance**: Class attendance records

**Access Control**:
- **RECEPTION**: Lead and student management
- **TEACHER**: Course and attendance management
- **MANAGER**: Full operational access
- **ACADEMIC_MANAGER**: Academic oversight

### Payment Service (Port: 8084)
**Purpose**: Payment processing and financial operations

**Responsibilities**:
- Payment gateway integration
- Transaction processing
- Refund management
- Financial reporting
- Invoice generation

**Database Schema**:
- **Payments**: Payment transactions
- **Refunds**: Refund requests and processing
- **Invoices**: Generated invoices
- **Gateway Logs**: Payment gateway responses

**Gateway Integrations**:
- **UzCard**: Local card processing
- **Humo**: Local payment system
- **Payme**: Digital wallet
- **Click**: Online payment platform

### Notification Service (Port: 8085)
**Purpose**: Multi-channel communication

**Responsibilities**:
- Email delivery (SMTP)
- SMS delivery (API integration)
- Push notifications
- Template management
- Delivery tracking

**Database Schema**:
- **Notifications**: Sent notifications log
- **Templates**: Message templates
- **Delivery Logs**: Delivery status tracking

**Communication Channels**:
- **Email**: SMTP with Gmail integration
- **SMS**: Eskiz SMS provider
- **Push**: Future implementation

## 🗄️ Data Architecture

### Database Strategy
**Pattern**: Database per Service
**Provider**: Neon PostgreSQL
**Approach**: Database-first with manual schema management

### Database Distribution
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Auth Service  │    │  Admin Service  │    │  Staff Service  │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ep-wandering-│ │    │ │ ep-red-heart│ │    │ │ ep-shy-fire │ │
│ │   fire      │ │    │ │             │ │    │ │             │ │
│ │             │ │    │ │             │ │    │ │             │ │
│ │ • users     │ │    │ │ • analytics │ │    │ │ • leads     │ │
│ │ • sessions  │ │    │ │ • audit_logs│ │    │ │ • students  │ │
│ │ • roles     │ │    │ │ • reports   │ │    │ │ • courses   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ │ • enrollments│ │
└─────────────────┘    └─────────────────┘    │ └─────────────┘ │
                                              └─────────────────┘

┌─────────────────┐    ┌─────────────────┐
│ Payment Service │    │Notification Svc │
│                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ep-floral-   │ │    │ │ ep-tight-fog│ │
│ │   rice      │ │    │ │             │ │
│ │             │ │    │ │             │ │
│ │ • payments  │ │    │ │ • notifications│ │
│ │ • refunds   │ │    │ │ • templates │ │
│ │ • invoices  │ │    │ │ • delivery_logs│ │
│ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘
```

### Data Consistency
**Strategy**: Eventual Consistency with Saga Pattern
**Implementation**: Event-driven updates between services
**Conflict Resolution**: Last-write-wins with timestamp ordering

## 🔐 Security Architecture

### Authentication Flow
```
1. Client → API Gateway: Login request
2. API Gateway → Auth Service: Validate credentials
3. Auth Service → Database: Verify user
4. Auth Service → API Gateway: JWT token
5. API Gateway → Client: Token response
6. Client → API Gateway: Authenticated request (with token)
7. API Gateway: Validate token
8. API Gateway → Target Service: Forward request
```

### Authorization Model
**Pattern**: Role-Based Access Control (RBAC)
**Roles**: ADMIN, MANAGER, TEACHER, RECEPTION, CASHIER, STUDENT, ACADEMIC_MANAGER
**Implementation**: JWT claims with role validation at service level

### Security Layers
1. **Transport Security**: TLS 1.2+ for all communications
2. **API Security**: JWT authentication and rate limiting
3. **Database Security**: SSL connections and parameterized queries
4. **Application Security**: Input validation and output encoding
5. **Infrastructure Security**: Network isolation and access controls

## 🚀 Deployment Architecture

### Platform: Render
**Deployment Model**: Container-based deployment
**Scaling**: Horizontal auto-scaling
**Load Balancing**: Built-in load balancer
**Health Monitoring**: Automated health checks

### Service Deployment
```
┌─────────────────────────────────────────────────────────────────┐
│                        Render Platform                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │API Gateway  │ │Auth Service │ │Admin Service│ │Staff Service││
│  │             │ │             │ │             │ │             ││
│  │Port: 8080   │ │Port: 8081   │ │Port: 8082   │ │Port: 8083   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
│  ┌─────────────┐ ┌─────────────┐                                │
│  │Payment Svc  │ │Notification │                                │
│  │             │ │Service      │                                │
│  │Port: 8084   │ │Port: 8085   │                                │
│  └─────────────┘ └─────────────┘                                │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Neon PostgreSQL                            │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ep-wandering-│ │ ep-red-heart│ │ ep-shy-fire │ │ep-floral-   ││
│  │fire (Auth)  │ │(Admin)      │ │(Staff)      │ │rice (Pay)   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
│  ┌─────────────┐                                                │
│  │ep-tight-fog │                                                │
│  │(Notification)│                                               │
│  └─────────────┘                                                │
└─────────────────────────────────────────────────────────────────┘
```

## 📊 Performance Architecture

### Caching Strategy
**Layer 1**: Application-level caching (in-memory)
**Layer 2**: Redis caching (distributed)
**Layer 3**: Database query optimization

### Connection Pooling
```go
// Database connection pool configuration
MaxOpenConns:    25,  // Maximum open connections
MaxIdleConns:    5,   // Maximum idle connections
ConnMaxLifetime: 5m,  // Connection maximum lifetime
```

### Load Balancing
**Algorithm**: Round-robin with health checks
**Health Check**: HTTP GET /health every 30 seconds
**Failover**: Automatic removal of unhealthy instances

## 🔄 Communication Patterns

### Synchronous Communication
**Pattern**: HTTP/REST APIs
**Use Cases**: Real-time operations, user interactions
**Implementation**: Direct service-to-service HTTP calls

### Asynchronous Communication
**Pattern**: Event-driven messaging
**Use Cases**: Notifications, audit logging, analytics
**Implementation**: Redis Pub/Sub (future: Message queues)

### Service Discovery
**Method**: Environment-based configuration
**Implementation**: Service URLs in environment variables
**Future**: Consul or Kubernetes service discovery

## 🛡️ Resilience Patterns

### Circuit Breaker
**Implementation**: Hystrix-style circuit breaker
**Thresholds**: 5 failures in 60 seconds
**Recovery**: 30-second timeout before retry

### Retry Logic
**Strategy**: Exponential backoff
**Max Retries**: 3 attempts
**Backoff**: 1s, 2s, 4s intervals

### Timeout Management
**Request Timeout**: 30 seconds
**Database Timeout**: 10 seconds
**Health Check Timeout**: 5 seconds

## 📈 Monitoring & Observability

### Health Monitoring
**Endpoint**: GET /health on each service
**Metrics**: Service status, database connectivity, version
**Frequency**: 30-second intervals

### Logging Strategy
**Format**: Structured JSON logging
**Levels**: DEBUG, INFO, WARN, ERROR
**Correlation**: Request ID tracking across services

### Metrics Collection
**Application Metrics**: Response times, error rates, throughput
**System Metrics**: CPU, memory, disk usage
**Business Metrics**: User registrations, payments, enrollments

## 🔮 Future Enhancements

### Planned Improvements
1. **Message Queue Integration**: RabbitMQ or Apache Kafka
2. **Service Mesh**: Istio for advanced traffic management
3. **Distributed Tracing**: Jaeger for request tracing
4. **Advanced Monitoring**: Prometheus + Grafana
5. **Container Orchestration**: Kubernetes deployment
6. **API Versioning**: Backward-compatible API evolution

### Scalability Roadmap
1. **Horizontal Scaling**: Auto-scaling based on metrics
2. **Database Sharding**: Partition large tables
3. **Read Replicas**: Separate read/write operations
4. **CDN Integration**: Static content delivery
5. **Edge Computing**: Regional service deployment

---

**Architecture documentation complete! 🏗️**

This architecture provides a solid foundation for a scalable, maintainable, and secure CRM system with clear separation of concerns and modern design patterns.
