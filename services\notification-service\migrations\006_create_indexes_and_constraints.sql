-- Additional indexes and constraints for optimization

-- Create partial indexes for active records
CREATE INDEX IF NOT EXISTS idx_notifications_active 
    ON notifications(type, status, created_at) 
    WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_templates_active 
    ON notification_templates(type, is_active) 
    WHERE deleted_at IS NULL AND is_active = true;

-- Create indexes for analytics queries
CREATE INDEX IF NOT EXISTS idx_notifications_analytics 
    ON notifications(type, status, created_at, sent_at) 
    WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_delivery_logs_analytics 
    ON delivery_logs(status, attempted_at, delivered_at) 
    WHERE deleted_at IS NULL;

-- Create indexes for queue management
CREATE INDEX IF NOT EXISTS idx_queue_ready_for_processing 
    ON notification_queue(priority, scheduled_at) 
    WHERE status = 'PENDING' AND deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_queue_failed_retries 
    ON notification_queue(next_retry_at) 
    WHERE status = 'FAILED' AND retry_count < max_retries AND deleted_at IS NULL;

-- Create indexes for user preferences lookup
CREATE INDEX IF NOT EXISTS idx_preferences_user_lookup 
    ON notification_preferences(user_id, email_enabled, sms_enabled, in_app_enabled, push_enabled) 
    WHERE deleted_at IS NULL;

-- Add check constraints for data integrity
ALTER TABLE notifications 
ADD CONSTRAINT chk_notifications_type 
CHECK (type IN ('EMAIL', 'SMS', 'IN_APP', 'PUSH'));

ALTER TABLE notifications 
ADD CONSTRAINT chk_notifications_status 
CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'CANCELLED'));

ALTER TABLE notifications 
ADD CONSTRAINT chk_notifications_priority 
CHECK (priority IN ('LOW', 'NORMAL', 'HIGH', 'URGENT'));

ALTER TABLE notification_templates 
ADD CONSTRAINT chk_templates_type 
CHECK (type IN ('EMAIL', 'SMS', 'IN_APP', 'PUSH'));

ALTER TABLE delivery_logs 
ADD CONSTRAINT chk_delivery_logs_status 
CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'OPENED', 'CLICKED'));

ALTER TABLE notification_queue 
ADD CONSTRAINT chk_queue_status 
CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'));

ALTER TABLE notification_queue 
ADD CONSTRAINT chk_queue_priority 
CHECK (priority >= 1 AND priority <= 10);

-- Add check constraints for retry logic
ALTER TABLE notifications 
ADD CONSTRAINT chk_notifications_retry_count 
CHECK (retry_count >= 0 AND retry_count <= max_retries);

ALTER TABLE notification_queue 
ADD CONSTRAINT chk_queue_retry_count 
CHECK (retry_count >= 0 AND retry_count <= max_retries);

-- Create function to clean up old records
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    -- Delete old completed notifications (older than 90 days)
    DELETE FROM notifications 
    WHERE status IN ('DELIVERED', 'CANCELLED') 
    AND created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    -- Delete old delivery logs (older than 90 days)
    DELETE FROM delivery_logs 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    -- Delete old completed queue items (older than 30 days)
    DELETE FROM notification_queue 
    WHERE status IN ('COMPLETED', 'CANCELLED') 
    AND created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create function to get notification statistics
CREATE OR REPLACE FUNCTION get_notification_stats(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP - INTERVAL '30 days',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
RETURNS TABLE (
    total_notifications BIGINT,
    sent_notifications BIGINT,
    delivered_notifications BIGINT,
    failed_notifications BIGINT,
    email_notifications BIGINT,
    sms_notifications BIGINT,
    in_app_notifications BIGINT,
    push_notifications BIGINT,
    avg_delivery_time_minutes NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_notifications,
        COUNT(*) FILTER (WHERE status = 'SENT') as sent_notifications,
        COUNT(*) FILTER (WHERE status = 'DELIVERED') as delivered_notifications,
        COUNT(*) FILTER (WHERE status = 'FAILED') as failed_notifications,
        COUNT(*) FILTER (WHERE type = 'EMAIL') as email_notifications,
        COUNT(*) FILTER (WHERE type = 'SMS') as sms_notifications,
        COUNT(*) FILTER (WHERE type = 'IN_APP') as in_app_notifications,
        COUNT(*) FILTER (WHERE type = 'PUSH') as push_notifications,
        AVG(EXTRACT(EPOCH FROM (delivered_at - created_at))/60) FILTER (WHERE delivered_at IS NOT NULL) as avg_delivery_time_minutes
    FROM notifications 
    WHERE created_at >= start_date 
    AND created_at <= end_date 
    AND deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql;
