package services

import (
	"fmt"
	"notification-service/internal/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AnalyticsService handles notification analytics and reporting
type AnalyticsService struct {
	db *gorm.DB
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(db *gorm.DB) *AnalyticsService {
	return &AnalyticsService{
		db: db,
	}
}

// NotificationAnalytics represents comprehensive notification analytics
type NotificationAnalytics struct {
	// Overview metrics
	TotalNotifications     int64   `json:"total_notifications"`
	SentNotifications      int64   `json:"sent_notifications"`
	DeliveredNotifications int64   `json:"delivered_notifications"`
	FailedNotifications    int64   `json:"failed_notifications"`
	PendingNotifications   int64   `json:"pending_notifications"`
	
	// Rates
	DeliveryRate  float64 `json:"delivery_rate"`
	FailureRate   float64 `json:"failure_rate"`
	OpenRate      float64 `json:"open_rate"`
	ClickRate     float64 `json:"click_rate"`
	BounceRate    float64 `json:"bounce_rate"`
	
	// Channel breakdown
	EmailNotifications  int64 `json:"email_notifications"`
	SMSNotifications    int64 `json:"sms_notifications"`
	InAppNotifications  int64 `json:"in_app_notifications"`
	PushNotifications   int64 `json:"push_notifications"`
	
	// Time-based metrics
	AvgDeliveryTime    float64 `json:"avg_delivery_time_minutes"`
	AvgProcessingTime  float64 `json:"avg_processing_time_minutes"`
	
	// Period information
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	
	// Trends
	DailyTrends   []DailyTrend   `json:"daily_trends,omitempty"`
	ChannelTrends []ChannelTrend `json:"channel_trends,omitempty"`
}

// DailyTrend represents daily notification trends
type DailyTrend struct {
	Date      string `json:"date"`
	Total     int64  `json:"total"`
	Sent      int64  `json:"sent"`
	Delivered int64  `json:"delivered"`
	Failed    int64  `json:"failed"`
}

// ChannelTrend represents channel-specific trends
type ChannelTrend struct {
	Channel      string  `json:"channel"`
	Total        int64   `json:"total"`
	DeliveryRate float64 `json:"delivery_rate"`
	FailureRate  float64 `json:"failure_rate"`
}

// GetNotificationAnalytics returns comprehensive notification analytics
func (s *AnalyticsService) GetNotificationAnalytics(startDate, endDate time.Time, includeDetails bool) (*NotificationAnalytics, error) {
	analytics := &NotificationAnalytics{
		StartDate: startDate,
		EndDate:   endDate,
	}
	
	// Base query for the time period
	baseQuery := s.db.Model(&models.Notification{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate)
	
	// Get total counts
	if err := baseQuery.Count(&analytics.TotalNotifications).Error; err != nil {
		return nil, fmt.Errorf("failed to get total notifications: %w", err)
	}
	
	// Get counts by status
	baseQuery.Where("status = ?", models.NotificationStatusSent).Count(&analytics.SentNotifications)
	baseQuery.Where("status = ?", models.NotificationStatusDelivered).Count(&analytics.DeliveredNotifications)
	baseQuery.Where("status = ?", models.NotificationStatusFailed).Count(&analytics.FailedNotifications)
	baseQuery.Where("status = ?", models.NotificationStatusPending).Count(&analytics.PendingNotifications)
	
	// Reset query for type counts
	baseQuery = s.db.Model(&models.Notification{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate)
	
	// Get counts by type
	baseQuery.Where("type = ?", models.NotificationTypeEmail).Count(&analytics.EmailNotifications)
	baseQuery.Where("type = ?", models.NotificationTypeSMS).Count(&analytics.SMSNotifications)
	baseQuery.Where("type = ?", models.NotificationTypeInApp).Count(&analytics.InAppNotifications)
	baseQuery.Where("type = ?", models.NotificationTypePush).Count(&analytics.PushNotifications)
	
	// Calculate rates
	if analytics.TotalNotifications > 0 {
		analytics.DeliveryRate = float64(analytics.DeliveredNotifications) / float64(analytics.TotalNotifications) * 100
		analytics.FailureRate = float64(analytics.FailedNotifications) / float64(analytics.TotalNotifications) * 100
	}
	
	// Get delivery analytics from delivery logs
	if err := s.calculateDeliveryMetrics(analytics, startDate, endDate); err != nil {
		return nil, fmt.Errorf("failed to calculate delivery metrics: %w", err)
	}
	
	// Get timing metrics
	if err := s.calculateTimingMetrics(analytics, startDate, endDate); err != nil {
		return nil, fmt.Errorf("failed to calculate timing metrics: %w", err)
	}
	
	// Include detailed trends if requested
	if includeDetails {
		if err := s.calculateDailyTrends(analytics, startDate, endDate); err != nil {
			return nil, fmt.Errorf("failed to calculate daily trends: %w", err)
		}
		
		if err := s.calculateChannelTrends(analytics, startDate, endDate); err != nil {
			return nil, fmt.Errorf("failed to calculate channel trends: %w", err)
		}
	}
	
	return analytics, nil
}

// calculateDeliveryMetrics calculates delivery-related metrics from delivery logs
func (s *AnalyticsService) calculateDeliveryMetrics(analytics *NotificationAnalytics, startDate, endDate time.Time) error {
	var deliveryStats struct {
		TotalDeliveries int64 `json:"total_deliveries"`
		OpenedCount     int64 `json:"opened_count"`
		ClickedCount    int64 `json:"clicked_count"`
		BouncedCount    int64 `json:"bounced_count"`
	}
	
	// Get delivery log counts
	baseQuery := s.db.Model(&models.DeliveryLog{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate)
	
	baseQuery.Count(&deliveryStats.TotalDeliveries)
	baseQuery.Where("status = ?", models.DeliveryStatusOpened).Count(&deliveryStats.OpenedCount)
	baseQuery.Where("status = ?", models.DeliveryStatusClicked).Count(&deliveryStats.ClickedCount)
	baseQuery.Where("status = ?", models.DeliveryStatusBounced).Count(&deliveryStats.BouncedCount)
	
	// Calculate rates
	if deliveryStats.TotalDeliveries > 0 {
		analytics.OpenRate = float64(deliveryStats.OpenedCount) / float64(deliveryStats.TotalDeliveries) * 100
		analytics.ClickRate = float64(deliveryStats.ClickedCount) / float64(deliveryStats.TotalDeliveries) * 100
		analytics.BounceRate = float64(deliveryStats.BouncedCount) / float64(deliveryStats.TotalDeliveries) * 100
	}
	
	return nil
}

// calculateTimingMetrics calculates timing-related metrics
func (s *AnalyticsService) calculateTimingMetrics(analytics *NotificationAnalytics, startDate, endDate time.Time) error {
	// Average delivery time (from creation to delivery)
	var avgDeliveryTime struct {
		AvgMinutes float64 `json:"avg_minutes"`
	}
	
	err := s.db.Model(&models.Notification{}).
		Select("AVG(EXTRACT(EPOCH FROM (delivered_at - created_at))/60) as avg_minutes").
		Where("created_at >= ? AND created_at <= ? AND delivered_at IS NOT NULL", startDate, endDate).
		Scan(&avgDeliveryTime).Error
	
	if err == nil {
		analytics.AvgDeliveryTime = avgDeliveryTime.AvgMinutes
	}
	
	// Average processing time from queue
	var avgProcessingTime struct {
		AvgMinutes float64 `json:"avg_minutes"`
	}
	
	err = s.db.Model(&models.NotificationQueue{}).
		Select("AVG(EXTRACT(EPOCH FROM (completed_at - processed_at))/60) as avg_minutes").
		Where("created_at >= ? AND created_at <= ? AND completed_at IS NOT NULL AND processed_at IS NOT NULL", startDate, endDate).
		Scan(&avgProcessingTime).Error
	
	if err == nil {
		analytics.AvgProcessingTime = avgProcessingTime.AvgMinutes
	}
	
	return nil
}

// calculateDailyTrends calculates daily notification trends
func (s *AnalyticsService) calculateDailyTrends(analytics *NotificationAnalytics, startDate, endDate time.Time) error {
	var trends []DailyTrend
	
	// Generate daily trends for the period
	current := startDate
	for current.Before(endDate) || current.Equal(endDate) {
		nextDay := current.AddDate(0, 0, 1)
		
		trend := DailyTrend{
			Date: current.Format("2006-01-02"),
		}
		
		// Get counts for this day
		dayQuery := s.db.Model(&models.Notification{}).
			Where("created_at >= ? AND created_at < ?", current, nextDay)
		
		dayQuery.Count(&trend.Total)
		dayQuery.Where("status = ?", models.NotificationStatusSent).Count(&trend.Sent)
		dayQuery.Where("status = ?", models.NotificationStatusDelivered).Count(&trend.Delivered)
		dayQuery.Where("status = ?", models.NotificationStatusFailed).Count(&trend.Failed)
		
		trends = append(trends, trend)
		current = nextDay
	}
	
	analytics.DailyTrends = trends
	return nil
}

// calculateChannelTrends calculates channel-specific trends
func (s *AnalyticsService) calculateChannelTrends(analytics *NotificationAnalytics, startDate, endDate time.Time) error {
	channels := []models.NotificationType{
		models.NotificationTypeEmail,
		models.NotificationTypeSMS,
		models.NotificationTypeInApp,
		models.NotificationTypePush,
	}
	
	var trends []ChannelTrend
	
	for _, channel := range channels {
		trend := ChannelTrend{
			Channel: string(channel),
		}
		
		// Get counts for this channel
		channelQuery := s.db.Model(&models.Notification{}).
			Where("created_at >= ? AND created_at <= ? AND type = ?", startDate, endDate, channel)
		
		channelQuery.Count(&trend.Total)
		
		if trend.Total > 0 {
			var delivered, failed int64
			channelQuery.Where("status = ?", models.NotificationStatusDelivered).Count(&delivered)
			channelQuery.Where("status = ?", models.NotificationStatusFailed).Count(&failed)
			
			trend.DeliveryRate = float64(delivered) / float64(trend.Total) * 100
			trend.FailureRate = float64(failed) / float64(trend.Total) * 100
		}
		
		trends = append(trends, trend)
	}
	
	analytics.ChannelTrends = trends
	return nil
}

// GetUserNotificationAnalytics returns analytics for a specific user
func (s *AnalyticsService) GetUserNotificationAnalytics(userID uuid.UUID, startDate, endDate time.Time) (*NotificationAnalytics, error) {
	analytics := &NotificationAnalytics{
		StartDate: startDate,
		EndDate:   endDate,
	}
	
	// Base query for the user and time period
	baseQuery := s.db.Model(&models.Notification{}).
		Where("recipient_id = ? AND created_at >= ? AND created_at <= ?", userID, startDate, endDate)
	
	// Get total counts
	if err := baseQuery.Count(&analytics.TotalNotifications).Error; err != nil {
		return nil, fmt.Errorf("failed to get user notifications: %w", err)
	}
	
	// Get counts by status
	baseQuery.Where("status = ?", models.NotificationStatusSent).Count(&analytics.SentNotifications)
	baseQuery.Where("status = ?", models.NotificationStatusDelivered).Count(&analytics.DeliveredNotifications)
	baseQuery.Where("status = ?", models.NotificationStatusFailed).Count(&analytics.FailedNotifications)
	baseQuery.Where("status = ?", models.NotificationStatusPending).Count(&analytics.PendingNotifications)
	
	// Calculate rates
	if analytics.TotalNotifications > 0 {
		analytics.DeliveryRate = float64(analytics.DeliveredNotifications) / float64(analytics.TotalNotifications) * 100
		analytics.FailureRate = float64(analytics.FailedNotifications) / float64(analytics.TotalNotifications) * 100
	}
	
	return analytics, nil
}

// GetTemplateAnalytics returns analytics for notification templates
func (s *AnalyticsService) GetTemplateAnalytics(startDate, endDate time.Time) ([]TemplateAnalytics, error) {
	var results []TemplateAnalytics
	
	rows, err := s.db.Raw(`
		SELECT 
			t.id,
			t.name,
			t.type,
			COUNT(n.id) as usage_count,
			COUNT(CASE WHEN n.status = 'DELIVERED' THEN 1 END) as delivered_count,
			COUNT(CASE WHEN n.status = 'FAILED' THEN 1 END) as failed_count,
			COALESCE(AVG(EXTRACT(EPOCH FROM (n.delivered_at - n.created_at))/60), 0) as avg_delivery_time
		FROM notification_templates t
		LEFT JOIN notifications n ON t.id = n.template_id 
			AND n.created_at >= ? AND n.created_at <= ?
		WHERE t.deleted_at IS NULL
		GROUP BY t.id, t.name, t.type
		ORDER BY usage_count DESC
	`, startDate, endDate).Rows()
	
	if err != nil {
		return nil, fmt.Errorf("failed to get template analytics: %w", err)
	}
	defer rows.Close()
	
	for rows.Next() {
		var analytics TemplateAnalytics
		err := rows.Scan(
			&analytics.TemplateID,
			&analytics.TemplateName,
			&analytics.TemplateType,
			&analytics.UsageCount,
			&analytics.DeliveredCount,
			&analytics.FailedCount,
			&analytics.AvgDeliveryTime,
		)
		if err != nil {
			continue
		}
		
		// Calculate rates
		if analytics.UsageCount > 0 {
			analytics.DeliveryRate = float64(analytics.DeliveredCount) / float64(analytics.UsageCount) * 100
			analytics.FailureRate = float64(analytics.FailedCount) / float64(analytics.UsageCount) * 100
		}
		
		results = append(results, analytics)
	}
	
	return results, nil
}

// TemplateAnalytics represents template usage analytics
type TemplateAnalytics struct {
	TemplateID      uuid.UUID `json:"template_id"`
	TemplateName    string    `json:"template_name"`
	TemplateType    string    `json:"template_type"`
	UsageCount      int64     `json:"usage_count"`
	DeliveredCount  int64     `json:"delivered_count"`
	FailedCount     int64     `json:"failed_count"`
	DeliveryRate    float64   `json:"delivery_rate"`
	FailureRate     float64   `json:"failure_rate"`
	AvgDeliveryTime float64   `json:"avg_delivery_time_minutes"`
}

// GetTopPerformingTemplates returns the top performing templates
func (s *AnalyticsService) GetTopPerformingTemplates(limit int, startDate, endDate time.Time) ([]TemplateAnalytics, error) {
	templates, err := s.GetTemplateAnalytics(startDate, endDate)
	if err != nil {
		return nil, err
	}
	
	// Sort by delivery rate and usage count
	// Implementation would include sorting logic here
	
	if len(templates) > limit {
		templates = templates[:limit]
	}
	
	return templates, nil
}

// GetFailureAnalysis returns detailed failure analysis
func (s *AnalyticsService) GetFailureAnalysis(startDate, endDate time.Time) (*FailureAnalysis, error) {
	analysis := &FailureAnalysis{
		StartDate: startDate,
		EndDate:   endDate,
	}
	
	// Get failure counts by type
	rows, err := s.db.Raw(`
		SELECT 
			type,
			COUNT(*) as failure_count,
			COUNT(DISTINCT recipient) as affected_recipients
		FROM notifications 
		WHERE status = 'FAILED' 
			AND created_at >= ? AND created_at <= ?
		GROUP BY type
	`, startDate, endDate).Rows()
	
	if err != nil {
		return nil, fmt.Errorf("failed to get failure analysis: %w", err)
	}
	defer rows.Close()
	
	for rows.Next() {
		var failure ChannelFailure
		err := rows.Scan(&failure.Channel, &failure.FailureCount, &failure.AffectedRecipients)
		if err != nil {
			continue
		}
		analysis.ChannelFailures = append(analysis.ChannelFailures, failure)
	}
	
	// Get common error messages
	rows, err = s.db.Raw(`
		SELECT 
			last_error,
			COUNT(*) as occurrence_count
		FROM notifications 
		WHERE status = 'FAILED' 
			AND last_error IS NOT NULL 
			AND last_error != ''
			AND created_at >= ? AND created_at <= ?
		GROUP BY last_error
		ORDER BY occurrence_count DESC
		LIMIT 10
	`, startDate, endDate).Rows()
	
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var error ErrorPattern
			err := rows.Scan(&error.ErrorMessage, &error.OccurrenceCount)
			if err != nil {
				continue
			}
			analysis.CommonErrors = append(analysis.CommonErrors, error)
		}
	}
	
	return analysis, nil
}

// FailureAnalysis represents failure analysis data
type FailureAnalysis struct {
	StartDate       time.Time        `json:"start_date"`
	EndDate         time.Time        `json:"end_date"`
	ChannelFailures []ChannelFailure `json:"channel_failures"`
	CommonErrors    []ErrorPattern   `json:"common_errors"`
}

// ChannelFailure represents failure data for a specific channel
type ChannelFailure struct {
	Channel            string `json:"channel"`
	FailureCount       int64  `json:"failure_count"`
	AffectedRecipients int64  `json:"affected_recipients"`
}

// ErrorPattern represents common error patterns
type ErrorPattern struct {
	ErrorMessage     string `json:"error_message"`
	OccurrenceCount  int64  `json:"occurrence_count"`
}
