package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Simple User model for testing
type User struct {
	ID        string    `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string    `gorm:"uniqueIndex;not null"`
	Username  string    `gorm:"uniqueIndex;not null"`
	FirstName string    `gorm:"not null"`
	LastName  string    `gorm:"not null"`
	Role      string    `gorm:"not null"`
	Status    string    `gorm:"default:ACTIVE"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func main() {
	fmt.Println("🔗 Testing Neon Database Connection...")
	
	// Neon database connection string
	dsn := "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
	
	// Configure GORM logger
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		log.Fatalf("❌ Failed to connect to database: %v", err)
	}

	fmt.Println("✅ Successfully connected to Neon database!")

	// Get underlying sql.DB to test connection
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("❌ Failed to get underlying sql.DB: %v", err)
	}

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		log.Fatalf("❌ Failed to ping database: %v", err)
	}

	fmt.Println("✅ Database ping successful!")

	// Test auto-migration
	fmt.Println("🔄 Running auto-migration...")
	if err := db.AutoMigrate(&User{}); err != nil {
		log.Fatalf("❌ Failed to run auto-migration: %v", err)
	}

	fmt.Println("✅ Auto-migration completed!")

	// Test creating a user
	fmt.Println("👤 Testing user creation...")
	testUser := User{
		Email:     "<EMAIL>",
		Username:  "testuser",
		FirstName: "Test",
		LastName:  "User",
		Role:      "RECEPTION",
		Status:    "ACTIVE",
	}

	// Try to create user (might fail if already exists)
	result := db.Create(&testUser)
	if result.Error != nil {
		if result.Error.Error() == "duplicate key value violates unique constraint" {
			fmt.Println("⚠️ User already exists (expected for repeated tests)")
		} else {
			fmt.Printf("❌ Failed to create user: %v\n", result.Error)
		}
	} else {
		fmt.Printf("✅ User created successfully with ID: %s\n", testUser.ID)
	}

	// Test reading users
	fmt.Println("📖 Testing user retrieval...")
	var users []User
	if err := db.Find(&users).Error; err != nil {
		log.Fatalf("❌ Failed to retrieve users: %v", err)
	}

	fmt.Printf("✅ Found %d users in database\n", len(users))
	for _, user := range users {
		fmt.Printf("   - %s (%s) - %s\n", user.Email, user.Username, user.Role)
	}

	// Test connection stats
	stats := sqlDB.Stats()
	fmt.Println("\n📊 Connection Statistics:")
	fmt.Printf("   - Open Connections: %d\n", stats.OpenConnections)
	fmt.Printf("   - In Use: %d\n", stats.InUse)
	fmt.Printf("   - Idle: %d\n", stats.Idle)

	// Close connection
	sqlDB.Close()
	fmt.Println("\n🎉 Database connection test completed successfully!")
	fmt.Println("\n📋 Next steps:")
	fmt.Println("   1. The database connection is working")
	fmt.Println("   2. Auto-migration is successful")
	fmt.Println("   3. Basic CRUD operations work")
	fmt.Println("   4. Ready to run the full authentication service")
}
