package handlers

import (
	"strconv"

	"admin-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userService services.UserService
}

// NewUserHandler creates a new user handler
func NewUserHandler(userService services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// GetUsers handles GET /users
func (h *UserHandler) GetUsers(c *gin.Context) {
	var req models.UserListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}

	if role := c.Query("role"); role != "" {
		userRole := models.UserRole(role)
		if userRole.IsValid() {
			req.Role = &userRole
		}
	}

	if status := c.Query("status"); status != "" {
		userStatus := models.UserStatus(status)
		if userStatus.IsValid() {
			req.Status = &userStatus
		}
	}

	req.Search = c.Query("search")

	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	response, err := h.userService.GetUsers(&req)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, response, "Users retrieved successfully")
}

// GetUser handles GET /users/:id
func (h *UserHandler) GetUser(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	user, err := h.userService.GetUser(id)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, user, "User retrieved successfully")
}

// CreateUser handles POST /users
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request data")
		return
	}

	// Get current user info from context
	currentUserID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userID, ok := currentUserID.(uuid.UUID)
	if !ok {
		utils.InternalServerErrorResponse(c, "Invalid user ID in context")
		return
	}

	// Get client info
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	user, err := h.userService.CreateUser(&req, userID, ipAddress, userAgent)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.CreatedResponse(c, user, "User created successfully")
}

// UpdateUser handles PUT /users/:id
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequestResponse(c, "Invalid request data")
		return
	}

	// Get current user info from context
	currentUserID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userID, ok := currentUserID.(uuid.UUID)
	if !ok {
		utils.InternalServerErrorResponse(c, "Invalid user ID in context")
		return
	}

	// Get client info
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	user, err := h.userService.UpdateUser(id, &req, userID, ipAddress, userAgent)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, user, "User updated successfully")
}

// DeleteUser handles DELETE /users/:id
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		utils.BadRequestResponse(c, "Invalid user ID")
		return
	}

	// Get current user info from context
	currentUserID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userID, ok := currentUserID.(uuid.UUID)
	if !ok {
		utils.InternalServerErrorResponse(c, "Invalid user ID in context")
		return
	}

	// Prevent self-deletion
	if id == userID {
		utils.BadRequestResponse(c, "Cannot delete your own account")
		return
	}

	// Get client info
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	err = h.userService.DeleteUser(id, userID, ipAddress, userAgent)
	if err != nil {
		utils.HandleError(c, err)
		return
	}

	utils.SuccessResponse(c, nil, "User deleted successfully")
}
