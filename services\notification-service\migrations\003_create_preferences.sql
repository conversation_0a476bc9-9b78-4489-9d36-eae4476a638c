-- Create notification_preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE,
    
    -- Email preferences
    email_enabled BOOLEAN DEFAULT true,
    email_marketing BOOLEAN DEFAULT true,
    email_transactional BOOLEAN DEFAULT true,
    email_reminders BOOLEAN DEFAULT true,
    email_announcements BOOLEAN DEFAULT true,
    
    -- SMS preferences
    sms_enabled BOOLEAN DEFAULT true,
    sms_marketing BOOLEAN DEFAULT false,
    sms_transactional BOOLEAN DEFAULT true,
    sms_reminders B<PERSON><PERSON>EAN DEFAULT true,
    sms_urgent BOOLEAN DEFAULT true,
    
    -- In-app preferences
    in_app_enabled BOOLEAN DEFAULT true,
    in_app_marketing BOOLEAN DEFAULT true,
    in_app_transactional BOOLEAN DEFAULT true,
    in_app_reminders BOOLEAN DEFAULT true,
    in_app_announcements BOOLEAN DEFAULT true,
    
    -- Push notification preferences
    push_enabled <PERSON>O<PERSON>EAN DEFAULT true,
    push_marketing BOOLEAN DEFAULT false,
    push_transactional BOOLEAN DEFAULT true,
    push_reminders BOOLEAN DEFAULT true,
    push_urgent BOOLEAN DEFAULT true,
    
    -- Timing preferences
    quiet_hours_enabled BOOLEAN DEFAULT false,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- Frequency preferences
    digest_enabled BOOLEAN DEFAULT false,
    digest_frequency VARCHAR(20) DEFAULT 'DAILY',
    max_notifications_per_day INTEGER DEFAULT 50,
    
    -- Contact information
    email VARCHAR(255),
    phone_number VARCHAR(50),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_email ON notification_preferences(email);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_phone_number ON notification_preferences(phone_number);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_deleted_at ON notification_preferences(deleted_at);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_notification_preferences_updated_at 
    BEFORE UPDATE ON notification_preferences 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
