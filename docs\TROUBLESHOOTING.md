# 🔧 Troubleshooting Guide - Go Docker Platform

Comprehensive troubleshooting guide for common issues, debugging techniques, and solutions based on real deployment experience.

## 📋 Quick Diagnostics

### System Health Check
```bash
# Check all services health
curl https://crm-api-gateway.onrender.com/health
curl https://crm-auth-service.onrender.com/health
curl https://crm-admin-service.onrender.com/health
curl https://crm-staff-service.onrender.com/health
curl https://crm-payment-service.onrender.com/health
curl https://crm-notification-service.onrender.com/health

# Expected response for healthy service
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00Z",
  "database": "connected",
  "version": "1.0.0"
}
```

### Database Connectivity Test
```bash
# Test database connections
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -c "SELECT 1;"

# Check database status through service
curl https://crm-auth-service.onrender.com/health/db
```

## 🚨 Common Issues & Solutions

### 1. Service Not Starting

#### Symptoms
- Service shows "Build failed" in Render dashboard
- Health check endpoints return 404 or timeout
- Service logs show startup errors

#### Diagnosis
```bash
# Check Render service logs
# Go to Render Dashboard > Service > Logs

# Common error patterns to look for:
# - "failed to connect to database"
# - "invalid environment variable"
# - "port already in use"
# - "module not found"
```

#### Solutions

**Database Connection Issues:**
```bash
# Verify DATABASE_URL format
echo $DATABASE_URL
# Should be: postgresql://user:pass@host:port/db?sslmode=require&channel_binding=require

# Test connection manually
psql "$DATABASE_URL" -c "SELECT version();"
```

**Environment Variable Issues:**
```bash
# Check required environment variables
echo $PORT
echo $JWT_SECRET
echo $DATABASE_URL

# Verify JWT_SECRET length (must be >= 32 characters)
echo -n "$JWT_SECRET" | wc -c
```

**Build Issues:**
```bash
# Check go.mod and go.sum files
go mod tidy
go mod verify

# Test local build
go build -o main .
./main
```

### 2. Database Connection Errors

#### Symptoms
- "connection refused" errors
- "SSL connection required" errors
- "authentication failed" errors
- Intermittent connection timeouts

#### Diagnosis
```bash
# Test direct database connection
psql "postgresql://connection-string" -c "SELECT 1;"

# Check connection pool status
curl https://service-url.onrender.com/health/db

# Monitor connection count
psql "$DATABASE_URL" -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"
```

#### Solutions

**SSL Configuration:**
```env
# Ensure SSL is properly configured
DATABASE_URL=postgresql://user:pass@host:port/db?sslmode=require&channel_binding=require
```

**Connection Pool Tuning:**
```env
# Adjust connection pool settings
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m
```

**Network Issues:**
```bash
# Check network connectivity
ping ep-wandering-fire-a87g5rkj-pooler.eastus2.azure.neon.tech

# Verify DNS resolution
nslookup ep-wandering-fire-a87g5rkj-pooler.eastus2.azure.neon.tech
```

### 3. Authentication & Authorization Issues

#### Symptoms
- "invalid token" errors
- "unauthorized access" errors
- Token expiration issues
- Cross-service authentication failures

#### Diagnosis
```bash
# Test login endpoint
curl -X POST https://crm-auth-service.onrender.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Verify JWT token
echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | base64 -d

# Test token validation
curl -H "Authorization: Bearer TOKEN" https://crm-admin-service.onrender.com/api/v1/admin/users
```

#### Solutions

**JWT Secret Mismatch:**
```bash
# Ensure all services use the same JWT_SECRET
# Check environment variables across all services
echo $JWT_SECRET  # Should be identical for all services
```

**Token Expiry Issues:**
```env
# Adjust token expiry settings
JWT_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d
```

**Role-Based Access:**
```bash
# Verify user roles in database
psql "$DATABASE_URL" -c "SELECT email, role FROM users WHERE email = '<EMAIL>';"

# Check role permissions in code
# Ensure user has required role for endpoint
```

### 4. Service Communication Errors

#### Symptoms
- "service unavailable" errors
- Timeout errors between services
- Inconsistent responses
- Circuit breaker activation

#### Diagnosis
```bash
# Test service-to-service communication
curl -I https://crm-auth-service.onrender.com/health
curl -I https://crm-admin-service.onrender.com/health

# Check service URLs in environment variables
echo $AUTH_SERVICE_URL
echo $ADMIN_SERVICE_URL
echo $STAFF_SERVICE_URL
```

#### Solutions

**Service URL Configuration:**
```env
# Ensure correct service URLs
AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
STAFF_SERVICE_URL=https://crm-staff-service.onrender.com
PAYMENT_SERVICE_URL=https://crm-payment-service.onrender.com
NOTIFICATION_SERVICE_URL=https://crm-notification-service.onrender.com
```

**Timeout Configuration:**
```env
# Adjust timeout settings
REQUEST_TIMEOUT=30s
IDLE_TIMEOUT=60s
HEALTH_CHECK_TIMEOUT=5s
```

**Circuit Breaker Tuning:**
```env
# Configure circuit breaker
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60s
CIRCUIT_BREAKER_RESET_TIMEOUT=30s
```

### 5. Performance Issues

#### Symptoms
- Slow response times
- High memory usage
- Database query timeouts
- Rate limiting errors

#### Diagnosis
```bash
# Monitor response times
time curl https://crm-api-gateway.onrender.com/health

# Check database performance
psql "$DATABASE_URL" -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

# Monitor resource usage
curl https://crm-service.onrender.com/metrics
```

#### Solutions

**Database Optimization:**
```sql
-- Add missing indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_payments_student_id ON payments(student_id);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';
```

**Caching Implementation:**
```env
# Enable Redis caching
REDIS_URL=redis://localhost:6379
CACHE_TTL=1h
CACHE_ENABLED=true
```

**Connection Pool Optimization:**
```env
# Tune connection pools
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m
```

### 6. Payment Gateway Issues

#### Symptoms
- Payment processing failures
- Gateway timeout errors
- Invalid payment responses
- Webhook delivery failures

#### Diagnosis
```bash
# Test payment gateway connectivity
curl -X POST https://api.uzcard.uz/test-endpoint \
  -H "Authorization: Bearer $UZCARD_API_KEY"

# Check payment service logs
curl https://crm-payment-service.onrender.com/health

# Verify webhook endpoints
curl -X POST https://crm-payment-service.onrender.com/webhooks/uzcard \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

#### Solutions

**Gateway Configuration:**
```env
# Verify gateway credentials
UZCARD_API_KEY=your-valid-api-key
UZCARD_MERCHANT_ID=your-merchant-id
UZCARD_API_URL=https://api.uzcard.uz

# Test mode configuration
PAYMENT_TEST_MODE=false
PAYMENT_SANDBOX_MODE=false
```

**Webhook Configuration:**
```env
# Configure webhook URLs
WEBHOOK_BASE_URL=https://crm-payment-service.onrender.com
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_TIMEOUT=30s
```

### 7. Notification Delivery Issues

#### Symptoms
- Emails not being sent
- SMS delivery failures
- Template rendering errors
- High bounce rates

#### Diagnosis
```bash
# Test SMTP configuration
curl -X POST https://crm-notification-service.onrender.com/api/v1/notifications/test-email \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"to":"<EMAIL>","subject":"Test","message":"Test message"}'

# Check notification service logs
curl https://crm-notification-service.onrender.com/health

# Verify template rendering
curl https://crm-notification-service.onrender.com/api/v1/notifications/templates/welcome
```

#### Solutions

**SMTP Configuration:**
```env
# Verify SMTP settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password  # Use app-specific password
SMTP_FROM=<EMAIL>
```

**SMS Configuration:**
```env
# Configure SMS provider
SMS_PROVIDER=eskiz
SMS_API_URL=https://notify.eskiz.uz/api
SMS_API_TOKEN=your-valid-token
SMS_FROM=4546
```

## 🔍 Debugging Techniques

### Log Analysis

#### Centralized Logging
```bash
# View service logs in Render
# Go to Render Dashboard > Service > Logs

# Search for specific errors
grep -i "error" service.log
grep -i "failed" service.log
grep -i "timeout" service.log
```

#### Structured Log Analysis
```bash
# Parse JSON logs
cat service.log | jq '.level == "error"'
cat service.log | jq '.msg | contains("database")'

# Filter by timestamp
cat service.log | jq 'select(.time > "2025-01-15T10:00:00Z")'
```

### Performance Monitoring

#### Response Time Monitoring
```bash
# Monitor API response times
for i in {1..10}; do
  time curl -s https://crm-api-gateway.onrender.com/health > /dev/null
  sleep 1
done

# Load testing
ab -n 100 -c 10 https://crm-api-gateway.onrender.com/health
```

#### Database Performance
```sql
-- Monitor slow queries
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
WHERE mean_time > 1000
ORDER BY total_time DESC;

-- Check table sizes
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Monitor connection usage
SELECT count(*), state 
FROM pg_stat_activity 
GROUP BY state;
```

### Network Diagnostics

#### Connectivity Testing
```bash
# Test service connectivity
curl -I https://crm-auth-service.onrender.com
curl -I https://crm-admin-service.onrender.com
curl -I https://crm-staff-service.onrender.com

# DNS resolution testing
nslookup crm-auth-service.onrender.com
dig crm-auth-service.onrender.com

# Network latency testing
ping -c 5 crm-auth-service.onrender.com
```

#### SSL/TLS Verification
```bash
# Check SSL certificate
openssl s_client -connect crm-auth-service.onrender.com:443 -servername crm-auth-service.onrender.com

# Verify certificate chain
curl -vI https://crm-auth-service.onrender.com
```

## 🛠️ Recovery Procedures

### Service Recovery

#### Restart Services
```bash
# Manual restart in Render Dashboard
# Go to Service > Settings > Manual Deploy

# Health check after restart
curl https://service-url.onrender.com/health
```

#### Database Recovery
```bash
# Check database status
psql "$DATABASE_URL" -c "SELECT version();"

# Verify table integrity
psql "$DATABASE_URL" -c "SELECT count(*) FROM users;"

# Restore from backup (if needed)
# Contact Neon support for backup restoration
```

### Data Recovery

#### Backup Verification
```bash
# Check recent backups in Neon Console
# Go to Neon Console > Project > Backups

# Test backup restoration
# Use Neon's point-in-time recovery feature
```

#### Data Consistency Checks
```sql
-- Check for orphaned records
SELECT COUNT(*) FROM enrollments e 
LEFT JOIN students s ON e.student_id = s.id 
WHERE s.id IS NULL;

-- Verify foreign key constraints
SELECT conname, conrelid::regclass, confrelid::regclass 
FROM pg_constraint 
WHERE contype = 'f';

-- Check data integrity
SELECT COUNT(*) FROM users WHERE email IS NULL OR email = '';
SELECT COUNT(*) FROM payments WHERE amount <= 0;
```

## 📞 Support Contacts

### Platform Support
- **Render Support**: https://render.com/support
- **Neon Support**: https://neon.tech/support
- **GitHub Issues**: https://github.com/MrFarrukhT/Go-Docker-Platform/issues

### Emergency Procedures
1. **Check service status** using health endpoints
2. **Review recent logs** in Render dashboard
3. **Verify database connectivity** using psql
4. **Test service communication** using curl
5. **Contact platform support** if infrastructure issues
6. **Create GitHub issue** for application bugs

### Escalation Matrix
- **Level 1**: Service health check failures
- **Level 2**: Database connectivity issues
- **Level 3**: Data corruption or security breaches
- **Level 4**: Complete system outage

## 📊 Monitoring & Alerting

### Key Metrics to Monitor
- **Service Health**: All services responding to /health
- **Response Times**: < 500ms for API calls
- **Error Rates**: < 1% error rate
- **Database Connections**: < 80% of max connections
- **Memory Usage**: < 80% of allocated memory

### Alert Thresholds
```yaml
# Example alert configuration
alerts:
  - name: service_down
    condition: health_check_failed
    threshold: 2_consecutive_failures
    
  - name: high_response_time
    condition: response_time > 2s
    threshold: 5_minutes
    
  - name: database_connections
    condition: active_connections > 20
    threshold: 1_minute
```

---

**Troubleshooting guide complete! 🔧**

This guide covers the most common issues encountered during deployment and operation. Keep this handy for quick problem resolution and system maintenance.
