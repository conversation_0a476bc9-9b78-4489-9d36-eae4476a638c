package services

import (
	"errors"
	"fmt"
	"time"

	"github.com/crm-microservices/auth-service/internal/repository"
	"github.com/crm-microservices/shared/models"
	"github.com/crm-microservices/shared/utils"
	"github.com/google/uuid"
)

var (
	ErrInvalidCredentials  = errors.New("invalid credentials")
	ErrUserNotFound        = errors.New("user not found")
	ErrUserInactive        = errors.New("user account is inactive")
	ErrSessionNotFound     = errors.New("session not found")
	ErrSessionExpired      = errors.New("session has expired")
	ErrInvalidRefreshToken = errors.New("invalid refresh token")
)

// AuthService handles authentication business logic
type AuthService struct {
	userRepo        *repository.UserRepository
	sessionRepo     *repository.SessionRepository
	jwtManager      *utils.JWTManager
	passwordManager *utils.PasswordManager
}

// NewAuthService creates a new auth service
func NewAuthService(
	userRepo *repository.UserRepository,
	sessionRepo *repository.SessionRepository,
	jwtManager *utils.JWTManager,
	passwordManager *utils.PasswordManager,
) *AuthService {
	return &AuthService{
		userRepo:        userRepo,
		sessionRepo:     sessionRepo,
		jwtManager:      jwtManager,
		passwordManager: passwordManager,
	}
}

// Login authenticates a user and returns tokens
func (s *AuthService) Login(req *models.LoginRequest, ipAddress, userAgent string) (*models.LoginResponse, error) {
	fmt.Printf("Login attempt for email: %s from IP: %s\n", req.Email, ipAddress)

	// Validate input
	validator := utils.NewValidator()
	validator.ValidateRequired("email", req.Email)
	validator.ValidateEmail("email", req.Email)
	validator.ValidateRequired("password", req.Password)

	if validator.HasErrors() {
		fmt.Printf("Login validation failed for email: %s, errors: %v\n", req.Email, validator.GetErrors())
		return nil, validator.GetErrors()
	}

	// Get user by email
	user, err := s.userRepo.GetByEmail(req.Email)
	if err != nil {
		fmt.Printf("Failed to get user by email %s: %v\n", req.Email, err)
		return nil, ErrInvalidCredentials
	}

	// Check if user is active
	if !user.IsActive() {
		fmt.Printf("User %s is inactive\n", req.Email)
		return nil, ErrUserInactive
	}

	// Verify password
	fmt.Printf("Verifying password for user %s\n", req.Email)
	isValid, err := s.passwordManager.VerifyPassword(req.Password, user.Password)
	if err != nil {
		fmt.Printf("Password verification error for user %s: %v\n", req.Email, err)
		return nil, fmt.Errorf("failed to verify password: %w", err)
	}

	if !isValid {
		fmt.Printf("Invalid password for user %s\n", req.Email)
		return nil, ErrInvalidCredentials
	}

	fmt.Printf("Password verification successful for user %s\n", req.Email)

	// Create session
	session := &models.Session{
		UserID:    user.ID,
		ExpiresAt: time.Now().Add(7 * 24 * time.Hour), // 7 days
		IsActive:  true,
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	// Generate tokens
	fmt.Printf("Generating tokens for user %s\n", req.Email)
	tokenPair, err := s.jwtManager.GenerateTokenPair(user, session.ID)
	if err != nil {
		fmt.Printf("Failed to generate tokens for user %s: %v\n", req.Email, err)
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Set tokens in session
	session.Token = tokenPair.AccessToken
	session.RefreshToken = tokenPair.RefreshToken
	session.ExpiresAt = tokenPair.RefreshExpiresAt

	// Save session
	fmt.Printf("Saving session for user %s\n", req.Email)
	if err := s.sessionRepo.Create(session); err != nil {
		fmt.Printf("Failed to create session for user %s: %v\n", req.Email, err)
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Update user's last login
	if err := s.userRepo.UpdateLastLogin(user.ID); err != nil {
		// Log error but don't fail the login
		fmt.Printf("Failed to update last login for user %s: %v\n", user.ID, err)
	}

	return &models.LoginResponse{
		User:         user.ToResponse(),
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.AccessExpiresAt,
	}, nil
}

// Register creates a new user account
func (s *AuthService) Register(req *models.UserCreateRequest) (*models.UserResponse, error) {
	// Validate input
	validator := utils.NewValidator()
	validator.ValidateRequired("email", req.Email)
	validator.ValidateEmail("email", req.Email)
	validator.ValidateRequired("username", req.Username)
	validator.ValidateUsername("username", req.Username)
	validator.ValidateRequired("password", req.Password)
	validator.ValidateRequired("first_name", req.FirstName)
	validator.ValidateRequired("last_name", req.LastName)
	validator.ValidateRequired("phone", req.Phone)
	validator.ValidatePhone("phone", req.Phone)

	if validator.HasErrors() {
		return nil, validator.GetErrors()
	}

	// Validate password strength
	if err := utils.ValidatePasswordComplexity(req.Password); err != nil {
		validator.AddError("password", err.Error(), req.Password)
		return nil, validator.GetErrors()
	}

	// Check if user already exists
	if exists, err := s.userRepo.ExistsByEmail(req.Email); err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	} else if exists {
		validator.AddError("email", "email already exists", req.Email)
		return nil, validator.GetErrors()
	}

	if exists, err := s.userRepo.ExistsByUsername(req.Username); err != nil {
		return nil, fmt.Errorf("failed to check username existence: %w", err)
	} else if exists {
		validator.AddError("username", "username already exists", req.Username)
		return nil, validator.GetErrors()
	}

	if exists, err := s.userRepo.ExistsByPhone(req.Phone); err != nil {
		return nil, fmt.Errorf("failed to check phone existence: %w", err)
	} else if exists {
		validator.AddError("phone", "phone number already exists", req.Phone)
		return nil, validator.GetErrors()
	}

	// Hash password
	hashedPassword, err := s.passwordManager.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &models.User{
		Email:       utils.NormalizeEmail(req.Email),
		Username:    req.Username,
		Password:    hashedPassword,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		Phone:       utils.NormalizePhone(req.Phone),
		Role:        req.Role,
		Status:      models.StatusActive,
		DateOfBirth: req.DateOfBirth,
		Address:     req.Address,
		City:        req.City,
		Country:     req.Country,
	}

	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user.ToResponse(), nil
}

// RefreshToken generates new tokens using refresh token
func (s *AuthService) RefreshToken(req *models.RefreshTokenRequest) (*models.LoginResponse, error) {
	// Validate refresh token
	claims, err := s.jwtManager.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, ErrInvalidRefreshToken
	}

	// Get session by refresh token
	session, err := s.sessionRepo.GetByRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	// Check if session is valid
	if !session.IsValid() {
		return nil, ErrSessionExpired
	}

	// Get user
	user, err := s.userRepo.GetByID(claims.UserID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	// Check if user is active
	if !user.IsActive() {
		return nil, ErrUserInactive
	}

	// Generate new tokens
	tokenPair, err := s.jwtManager.GenerateTokenPair(user, session.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Update session with new tokens
	session.Token = tokenPair.AccessToken
	session.RefreshToken = tokenPair.RefreshToken
	session.ExpiresAt = tokenPair.RefreshExpiresAt
	session.LastUsedAt = time.Now()

	if err := s.sessionRepo.Update(session); err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	return &models.LoginResponse{
		User:         user.ToResponse(),
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.AccessExpiresAt,
	}, nil
}

// Logout invalidates a user session
func (s *AuthService) Logout(token string) error {
	// Validate token
	claims, err := s.jwtManager.ValidateAccessToken(token)
	if err != nil {
		return ErrInvalidRefreshToken
	}

	// Deactivate session
	if err := s.sessionRepo.Deactivate(claims.SessionID); err != nil {
		return fmt.Errorf("failed to deactivate session: %w", err)
	}

	return nil
}

// LogoutAll invalidates all user sessions
func (s *AuthService) LogoutAll(userID uuid.UUID) error {
	if err := s.sessionRepo.DeactivateAllByUserID(userID); err != nil {
		return fmt.Errorf("failed to deactivate all sessions: %w", err)
	}
	return nil
}

// ValidateToken validates an access token
func (s *AuthService) ValidateToken(token string) (*utils.JWTClaims, error) {
	claims, err := s.jwtManager.ValidateAccessToken(token)
	if err != nil {
		return nil, err
	}

	// Check if session is still valid
	isValid, err := s.sessionRepo.IsValidSession(token)
	if err != nil {
		return nil, fmt.Errorf("failed to check session validity: %w", err)
	}

	if !isValid {
		return nil, ErrSessionExpired
	}

	// Update session last used time
	if err := s.sessionRepo.UpdateLastUsed(claims.SessionID); err != nil {
		// Log error but don't fail validation
		fmt.Printf("Failed to update session last used time: %v\n", err)
	}

	return claims, nil
}

// ChangePassword changes a user's password
func (s *AuthService) ChangePassword(userID uuid.UUID, req *models.ChangePasswordRequest) error {
	// Get user
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return ErrUserNotFound
	}

	// Verify current password
	isValid, err := s.passwordManager.VerifyPassword(req.CurrentPassword, user.Password)
	if err != nil {
		return fmt.Errorf("failed to verify current password: %w", err)
	}

	if !isValid {
		return ErrInvalidCredentials
	}

	// Validate new password
	if err := utils.ValidatePasswordComplexity(req.NewPassword); err != nil {
		return err
	}

	// Hash new password
	hashedPassword, err := s.passwordManager.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	// Invalidate all sessions except current one (optional)
	// This forces re-login on all other devices
	if err := s.sessionRepo.DeactivateAllByUserID(userID); err != nil {
		// Log error but don't fail password change
		fmt.Printf("Failed to invalidate sessions after password change: %v\n", err)
	}

	return nil
}

// ForgotPassword initiates password reset process
func (s *AuthService) ForgotPassword(req *models.ResetPasswordRequest) error {
	// Get user by email
	user, err := s.userRepo.GetByEmail(req.Email)
	if err != nil {
		// Don't reveal if email exists or not
		return nil
	}

	// Generate password reset token (implement this)
	// For now, we'll just return success
	// In a real implementation, you would:
	// 1. Generate a secure reset token
	// 2. Store it with expiration
	// 3. Send email with reset link

	fmt.Printf("Password reset requested for user: %s\n", user.Email)
	return nil
}

// ResetPassword resets password using reset token
func (s *AuthService) ResetPassword(req *models.ConfirmResetPasswordRequest) error {
	// Validate reset token (implement this)
	// For now, we'll just return an error
	// In a real implementation, you would:
	// 1. Validate the reset token
	// 2. Check if it's not expired
	// 3. Update the user's password
	// 4. Invalidate the reset token

	return errors.New("password reset not implemented")
}

// CleanupExpiredSessions removes expired sessions
func (s *AuthService) CleanupExpiredSessions() error {
	return s.sessionRepo.CleanupSessions()
}
