package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	sharedConfig "github.com/crm-microservices/shared/config"
)

// Config holds configuration for the auth service
type Config struct {
	*sharedConfig.Config
	
	// Auth service specific configuration
	Service ServiceConfig
}

// ServiceConfig holds auth service specific configuration
type ServiceConfig struct {
	Name    string
	Version string
	Port    string
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load shared configuration
	sharedCfg, err := sharedConfig.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load shared config: %w", err)
	}

	// Override database name for auth service
	sharedCfg.Database.DBName = getEnv("DB_NAME_AUTH", "crm_auth")
	
	// Override server port for auth service
	sharedCfg.Server.Port = getEnv("AUTH_SERVICE_PORT", "8081")

	config := &Config{
		Config: sharedCfg,
		Service: ServiceConfig{
			Name:    "auth-service",
			Version: getEnv("SERVICE_VERSION", "1.0.0"),
			Port:    sharedCfg.Server.Port,
		},
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate shared configuration
	if err := c.Config.Validate(); err != nil {
		return err
	}

	// Validate auth service specific configuration
	if c.Service.Name == "" {
		return fmt.Errorf("service name must be set")
	}

	if c.Service.Port == "" {
		return fmt.Errorf("service port must be set")
	}

	// Validate port is a valid number
	if _, err := strconv.Atoi(c.Service.Port); err != nil {
		return fmt.Errorf("service port must be a valid number: %w", err)
	}

	return nil
}

// GetServiceAddr returns the service address
func (c *Config) GetServiceAddr() string {
	return fmt.Sprintf("%s:%s", c.Server.Host, c.Service.Port)
}

// Helper functions

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
