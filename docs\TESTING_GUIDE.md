# 🧪 Full Stack Platform Testing Guide

## 📋 Overview

This document provides comprehensive testing procedures for the Go Docker Platform CRM system, including frontend-backend communication, authentication, and all microservices.

## 🏗️ Platform Architecture

### System Components
- **Frontend**: https://crm-frontend-k8b7.onrender.com (Go + HTMX + Tailwind)
- **API Gateway**: https://crm-api-gateway.onrender.com:8080 (Request routing & auth)
- **Auth Service**: https://crm-auth-service.onrender.com:8081 (Authentication & JWT)
- **Admin Service**: https://crm-admin-service.onrender.com:8082 (User management)
- **Staff Service**: https://crm-staff-service.onrender.com:8083 (Student/Course management)
- **Payment Service**: https://crm-payment-service.onrender.com:8084 (Payment processing)
- **Notification Service**: https://crm-notification-service.onrender.com:8085 (Email/SMS)

### Communication Flow
```
Frontend (HTMX) → API Gateway → Microservices → Neon PostgreSQL Databases
                      ↓
                 JWT Authentication
```

## 🔐 Testing Credentials

### Primary Admin Account (Pre-seeded)
```
Email: <EMAIL>
Username: admin
Password: admin123
Role: SUPER_ADMIN
Status: ACTIVE
```

### Test Accounts
```
# Admin Test Account
Email: <EMAIL>
Username: admin
Password: Admin123!@#
Role: ADMIN

# Staff Test Account  
Email: <EMAIL>
Username: staff
Password: Staff123!@#
Role: RECEPTION

# Benchmark Account
Email: <EMAIL>
Username: benchmark
Password: Benchmark123!@#
Role: ADMIN
```

## 🌐 Database Connections

### Neon PostgreSQL Databases
```bash
# Auth Database (ep-falling-forest)
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Admin Database (ep-red-heart)  
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Staff Database (ep-shy-fire)
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Payment Database (ep-floral-rice)
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Notification Database (ep-tight-fog)
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

## 🧪 Test Procedures

### 1. Health Check Tests

#### All Services Health Check
```bash
# Test all service health endpoints
curl https://crm-api-gateway.onrender.com/health
curl https://crm-auth-service.onrender.com/health
curl https://crm-admin-service.onrender.com/health
curl https://crm-staff-service.onrender.com/health
curl https://crm-payment-service.onrender.com/health
curl https://crm-notification-service.onrender.com/health
```

**Expected Response (each service):**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00Z",
  "service": "service-name"
}
```

### 2. Frontend Authentication Tests

#### Manual Frontend Test
1. **Access Frontend**: https://crm-frontend-k8b7.onrender.com
2. **Login with Primary Admin**:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. **Expected Result**: Redirect to dashboard with navigation menu
4. **Verify**: Check browser localStorage for `auth_token`

#### API Authentication Test
```bash
# Login via API Gateway
curl -X POST https://crm-api-gateway.onrender.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_here",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "SUPER_ADMIN",
      "first_name": "System",
      "last_name": "Administrator"
    }
  }
}
```

### 3. Frontend-Backend Integration Tests

#### HTMX Request Testing
1. **Login to Frontend** with `<EMAIL>` / `admin123`
2. **Navigate to Students Page**
3. **Open Browser Developer Tools** (F12)
4. **Check Network Tab** for:
   - HTMX request to: `GET /api/v1/staff/students`
   - Authorization header: `Bearer <jwt_token>`
   - Response status: 200 OK
   - No CORS errors

#### Student Management Test
1. **Go to Students Page**
2. **Click "Add Student"** - Modal should open
3. **Fill Student Form**:
   ```
   First Name: John
   Last Name: Doe
   Email: <EMAIL>
   Phone: +1234567890
   ```
4. **Submit Form**
5. **Expected**: HTMX POST request to `/api/v1/staff/students`
6. **Verify**: New student appears in list

### 4. Database Connectivity Tests

#### Connect to Auth Database
```bash
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
```

#### Verify User Data
```sql
-- Check if admin user exists
SELECT id, email, username, role, status, created_at 
FROM users 
WHERE email = '<EMAIL>';

-- Check all users
SELECT email, username, role, status, created_at 
FROM users 
ORDER BY created_at DESC;

-- Check user sessions
SELECT user_id, expires_at, created_at 
FROM user_sessions 
WHERE expires_at > NOW() 
ORDER BY created_at DESC;
```

### 5. API Endpoint Tests

#### User Registration
```bash
curl -X POST https://crm-api-gateway.onrender.com/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "newuser",
    "password": "NewUser123!@#",
    "first_name": "New",
    "last_name": "User",
    "role": "STUDENT"
  }'
```

#### Protected Endpoint Access
```bash
# Replace YOUR_JWT_TOKEN with actual token from login response
export JWT_TOKEN="your_jwt_token_here"

# Get user profile
curl -X GET https://crm-api-gateway.onrender.com/api/v1/users/profile \
  -H "Authorization: Bearer $JWT_TOKEN"

# List students (staff access)
curl -X GET https://crm-api-gateway.onrender.com/api/v1/staff/students \
  -H "Authorization: Bearer $JWT_TOKEN"

# Admin operations
curl -X GET https://crm-api-gateway.onrender.com/api/v1/admin/users \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### 6. Complete User Journey Tests

#### Admin Workflow Test
1. **Login**: `<EMAIL>` / `admin123`
2. **Dashboard**: Verify metrics and recent activity
3. **User Management**: 
   - Create new staff user
   - Assign roles and permissions
4. **Student Management**: 
   - Add new student
   - Update student information
5. **Course Management**: 
   - Create new course
   - Set course details and pricing
6. **Payment Processing**: 
   - Process student payment
   - Generate receipt
7. **Reports**: 
   - View financial reports
   - Export data

#### Staff Workflow Test
1. **Create Staff User** via admin panel
2. **Login with Staff Credentials**
3. **Student Management**: 
   - View student list
   - Update student records
4. **Lead Management**: 
   - Add new lead
   - Convert lead to student
5. **Course Enrollment**: 
   - Enroll student in course
   - Track progress

## 🔍 Performance Tests

### Response Time Benchmarks
```bash
# Test API response times
time curl -s https://crm-api-gateway.onrender.com/health

# Test frontend load time
time curl -s https://crm-frontend-k8b7.onrender.com/auth/login
```

**Expected Performance:**
- Health endpoints: < 100ms
- API responses: < 500ms
- Frontend page load: < 2 seconds
- HTMX updates: < 200ms

## ✅ Success Criteria

### Functional Tests
- [ ] All health endpoints return 200 OK
- [ ] Admin login successful with correct credentials
- [ ] Frontend loads without JavaScript errors
- [ ] HTMX requests include proper authentication headers
- [ ] Database connections successful
- [ ] CRUD operations work for all entities
- [ ] JWT tokens properly generated and validated
- [ ] Role-based access control enforced

### Integration Tests
- [ ] Frontend-backend communication working
- [ ] API Gateway routing requests correctly
- [ ] Microservices responding to proxied requests
- [ ] Database transactions completing successfully
- [ ] Real-time updates via HTMX functioning
- [ ] File uploads and downloads working
- [ ] Email notifications sending (if configured)

### Performance Tests
- [ ] All responses under performance thresholds
- [ ] No memory leaks during extended use
- [ ] Concurrent user handling (up to expected load)
- [ ] Database query optimization effective

## 🚨 Troubleshooting

### Common Issues

#### CORS Errors
- **Issue**: Frontend requests blocked by CORS policy
- **Solution**: Verify frontend URL in API Gateway CORS origins
- **Check**: `https://crm-frontend-k8b7.onrender.com` is included

#### Authentication Failures
- **Issue**: JWT token expired or invalid
- **Solution**: Re-login to get fresh token
- **Check**: Token format and expiration time

#### Database Connection Issues
- **Issue**: Service cannot connect to Neon database
- **Solution**: Verify connection strings and database status
- **Check**: Neon console for database availability

#### Service Unavailable
- **Issue**: Microservice not responding
- **Solution**: Check Render service logs and restart if needed
- **Check**: Service deployment status in Render dashboard

### Debug Commands
```bash
# Check service logs (replace with actual service name)
# Access via Render dashboard -> Service -> Logs

# Test database connectivity
psql "connection_string" -c "SELECT 1;"

# Validate JWT token
curl -X GET https://crm-api-gateway.onrender.com/api/v1/auth/validate \
  -H "Authorization: Bearer $JWT_TOKEN"
```

## 📊 Test Results Template

### Test Execution Log
```
Date: ___________
Tester: ___________
Environment: Production

Health Checks:
- API Gateway: [ ] Pass [ ] Fail
- Auth Service: [ ] Pass [ ] Fail
- Admin Service: [ ] Pass [ ] Fail
- Staff Service: [ ] Pass [ ] Fail
- Payment Service: [ ] Pass [ ] Fail
- Notification Service: [ ] Pass [ ] Fail

Authentication:
- Frontend Login: [ ] Pass [ ] Fail
- API Login: [ ] Pass [ ] Fail
- JWT Validation: [ ] Pass [ ] Fail

Integration:
- HTMX Requests: [ ] Pass [ ] Fail
- Database Operations: [ ] Pass [ ] Fail
- File Operations: [ ] Pass [ ] Fail

Performance:
- Response Times: [ ] Pass [ ] Fail
- Load Handling: [ ] Pass [ ] Fail

Issues Found:
1. ___________
2. ___________
3. ___________

Overall Status: [ ] Pass [ ] Fail
```

---

**🎯 Testing Complete!**

This comprehensive testing guide ensures your Go Docker Platform CRM system is fully functional and ready for production use. Execute these tests systematically to validate all components and integrations.
