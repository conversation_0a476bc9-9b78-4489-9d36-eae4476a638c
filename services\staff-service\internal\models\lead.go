package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// LeadStatus represents the status of a lead
type LeadStatus string

const (
	LeadStatusNew        LeadStatus = "NEW"
	LeadStatusContacted  LeadStatus = "CONTACTED"
	LeadStatusQualified  LeadStatus = "QUALIFIED"
	LeadStatusConverted  LeadStatus = "CONVERTED"
	LeadStatusLost       LeadStatus = "LOST"
	LeadStatusFollowUp   LeadStatus = "FOLLOW_UP"
)

// IsValid checks if the lead status is valid
func (ls LeadStatus) IsValid() bool {
	switch ls {
	case LeadStatusNew, LeadStatusContacted, LeadStatusQualified, 
		 LeadStatusConverted, LeadStatusLost, LeadStatusFollowUp:
		return true
	}
	return false
}

// LeadSource represents the source of a lead
type LeadSource string

const (
	LeadSourceWebsite     LeadSource = "WEBSITE"
	LeadSourceSocialMedia LeadSource = "SOCIAL_MEDIA"
	LeadSourceReferral    LeadSource = "REFERRAL"
	LeadSourceAdvertising LeadSource = "ADVERTISING"
	LeadSourceWalkIn      LeadSource = "WALK_IN"
	LeadSourcePhone       LeadSource = "PHONE"
	LeadSourceEmail       LeadSource = "EMAIL"
	LeadSourceOther       LeadSource = "OTHER"
)

// IsValid checks if the lead source is valid
func (ls LeadSource) IsValid() bool {
	switch ls {
	case LeadSourceWebsite, LeadSourceSocialMedia, LeadSourceReferral,
		 LeadSourceAdvertising, LeadSourceWalkIn, LeadSourcePhone,
		 LeadSourceEmail, LeadSourceOther:
		return true
	}
	return false
}

// Lead represents a potential student lead
type Lead struct {
	models.BaseModel
	FirstName        string     `json:"first_name" gorm:"not null;size:100"`
	LastName         string     `json:"last_name" gorm:"not null;size:100"`
	Email            string     `json:"email" gorm:"not null;size:255;index"`
	Phone            string     `json:"phone" gorm:"size:20;index"`
	DateOfBirth      *time.Time `json:"date_of_birth"`
	Address          string     `json:"address" gorm:"size:500"`
	City             string     `json:"city" gorm:"size:100"`
	Country          string     `json:"country" gorm:"size:100"`
	
	// Lead specific fields
	Status           LeadStatus `json:"status" gorm:"not null;default:'NEW';index"`
	Source           LeadSource `json:"source" gorm:"not null;index"`
	InterestedCourse string     `json:"interested_course" gorm:"size:200"`
	Budget           *float64   `json:"budget"`
	Notes            string     `json:"notes" gorm:"type:text"`
	
	// Assignment and tracking (no foreign key constraint - references auth service)
	AssignedToUserID *uuid.UUID `json:"assigned_to_id" gorm:"column:assigned_to_id;index"`
	AssignedTo       *models.User `json:"assigned_to,omitempty" gorm:"-"` // Excluded from database, populated by service layer
	LastContactedAt  *time.Time `json:"last_contacted_at"`
	NextFollowUpAt   *time.Time `json:"next_follow_up_at" gorm:"index"`
	ConvertedAt      *time.Time `json:"converted_at"`
	ConvertedToID    *uuid.UUID `json:"converted_to_id"` // Student ID if converted
	
	// Metadata (no foreign key constraint - references auth service)
	CreatedByUserID  uuid.UUID    `json:"created_by_id" gorm:"column:created_by_id;not null"`
	CreatedBy        *models.User `json:"created_by,omitempty" gorm:"-"` // Excluded from database, populated by service layer
}

// GetFullName returns the full name of the lead
func (l *Lead) GetFullName() string {
	return l.FirstName + " " + l.LastName
}

// IsConverted checks if the lead has been converted
func (l *Lead) IsConverted() bool {
	return l.Status == LeadStatusConverted && l.ConvertedAt != nil
}

// CanBeConverted checks if the lead can be converted
func (l *Lead) CanBeConverted() bool {
	return l.Status == LeadStatusQualified
}

// LeadCreateRequest represents a request to create a lead
type LeadCreateRequest struct {
	FirstName        string     `json:"first_name" binding:"required,min=2,max=100"`
	LastName         string     `json:"last_name" binding:"required,min=2,max=100"`
	Email            string     `json:"email" binding:"required,email,max=255"`
	Phone            string     `json:"phone" binding:"max=20"`
	DateOfBirth      *time.Time `json:"date_of_birth"`
	Address          string     `json:"address" binding:"max=500"`
	City             string     `json:"city" binding:"max=100"`
	Country          string     `json:"country" binding:"max=100"`
	Source           LeadSource `json:"source" binding:"required"`
	InterestedCourse string     `json:"interested_course" binding:"max=200"`
	Budget           *float64   `json:"budget" binding:"omitempty,min=0"`
	Notes            string     `json:"notes"`
}

// LeadUpdateRequest represents a request to update a lead
type LeadUpdateRequest struct {
	FirstName        *string     `json:"first_name" binding:"omitempty,min=2,max=100"`
	LastName         *string     `json:"last_name" binding:"omitempty,min=2,max=100"`
	Email            *string     `json:"email" binding:"omitempty,email,max=255"`
	Phone            *string     `json:"phone" binding:"omitempty,max=20"`
	DateOfBirth      *time.Time  `json:"date_of_birth"`
	Address          *string     `json:"address" binding:"omitempty,max=500"`
	City             *string     `json:"city" binding:"omitempty,max=100"`
	Country          *string     `json:"country" binding:"omitempty,max=100"`
	Status           *LeadStatus `json:"status"`
	Source           *LeadSource `json:"source"`
	InterestedCourse *string     `json:"interested_course" binding:"omitempty,max=200"`
	Budget           *float64    `json:"budget" binding:"omitempty,min=0"`
	Notes            *string     `json:"notes"`
	NextFollowUpAt   *time.Time  `json:"next_follow_up_at"`
}

// LeadAssignRequest represents a request to assign a lead
type LeadAssignRequest struct {
	AssignedToID uuid.UUID `json:"assigned_to_id" binding:"required"`
}

// LeadConvertRequest represents a request to convert a lead to student
type LeadConvertRequest struct {
	CourseID uuid.UUID `json:"course_id" binding:"required"`
	Notes    string    `json:"notes"`
}

// LeadResponse represents a lead response
type LeadResponse struct {
	ID               uuid.UUID    `json:"id"`
	FirstName        string       `json:"first_name"`
	LastName         string       `json:"last_name"`
	FullName         string       `json:"full_name"`
	Email            string       `json:"email"`
	Phone            string       `json:"phone"`
	DateOfBirth      *time.Time   `json:"date_of_birth"`
	Address          string       `json:"address"`
	City             string       `json:"city"`
	Country          string       `json:"country"`
	Status           LeadStatus   `json:"status"`
	Source           LeadSource   `json:"source"`
	InterestedCourse string       `json:"interested_course"`
	Budget           *float64     `json:"budget"`
	Notes            string       `json:"notes"`
	AssignedToID     *uuid.UUID   `json:"assigned_to_id"`
	AssignedToName   string       `json:"assigned_to_name"`
	LastContactedAt  *time.Time   `json:"last_contacted_at"`
	NextFollowUpAt   *time.Time   `json:"next_follow_up_at"`
	ConvertedAt      *time.Time   `json:"converted_at"`
	ConvertedToID    *uuid.UUID   `json:"converted_to_id"`
	CreatedByID      uuid.UUID    `json:"created_by_id"`
	CreatedByName    string       `json:"created_by_name"`
	CreatedAt        time.Time    `json:"created_at"`
	UpdatedAt        time.Time    `json:"updated_at"`
}

// ToResponse converts a Lead to LeadResponse
func (l *Lead) ToResponse() *LeadResponse {
	response := &LeadResponse{
		ID:               l.ID,
		FirstName:        l.FirstName,
		LastName:         l.LastName,
		FullName:         l.GetFullName(),
		Email:            l.Email,
		Phone:            l.Phone,
		DateOfBirth:      l.DateOfBirth,
		Address:          l.Address,
		City:             l.City,
		Country:          l.Country,
		Status:           l.Status,
		Source:           l.Source,
		InterestedCourse: l.InterestedCourse,
		Budget:           l.Budget,
		Notes:            l.Notes,
		AssignedToID:     l.AssignedToUserID,
		LastContactedAt:  l.LastContactedAt,
		NextFollowUpAt:   l.NextFollowUpAt,
		ConvertedAt:      l.ConvertedAt,
		ConvertedToID:    l.ConvertedToID,
		CreatedByID:      l.CreatedByUserID,
		CreatedAt:        l.CreatedAt,
		UpdatedAt:        l.UpdatedAt,
	}

	// Include assigned user information if available
	if l.AssignedTo != nil {
		response.AssignedToName = l.AssignedTo.GetFullName()
	}

	// Include created by user information if available
	if l.CreatedBy != nil {
		response.CreatedByName = l.CreatedBy.GetFullName()
	}

	return response
}

// LeadListRequest represents a request to list leads
type LeadListRequest struct {
	models.PaginationRequest
	Status         *LeadStatus `json:"status" form:"status"`
	Source         *LeadSource `json:"source" form:"source"`
	AssignedToID   *uuid.UUID  `json:"assigned_to_id" form:"assigned_to_id"`
	CreatedByID    *uuid.UUID  `json:"created_by_id" form:"created_by_id"`
	Search         string      `json:"search" form:"search"`
	StartDate      *time.Time  `json:"start_date" form:"start_date"`
	EndDate        *time.Time  `json:"end_date" form:"end_date"`
	FollowUpDue    *bool       `json:"follow_up_due" form:"follow_up_due"`
}

// LeadListResponse represents a response for lead list
type LeadListResponse struct {
	Leads      []*LeadResponse            `json:"leads"`
	Pagination *models.PaginationResponse `json:"pagination"`
}

// LeadStats represents lead statistics
type LeadStats struct {
	TotalLeads       int64                    `json:"total_leads"`
	LeadsByStatus    map[LeadStatus]int64     `json:"leads_by_status"`
	LeadsBySource    map[LeadSource]int64     `json:"leads_by_source"`
	ConversionRate   float64                  `json:"conversion_rate"`
	FollowUpsDue     int64                    `json:"follow_ups_due"`
	NewLeadsToday    int64                    `json:"new_leads_today"`
	NewLeadsThisWeek int64                    `json:"new_leads_this_week"`
}

// BulkLeadStatusUpdateRequest represents a request to bulk update lead status
type BulkLeadStatusUpdateRequest struct {
	LeadIDs []uuid.UUID `json:"lead_ids" binding:"required,min=1"`
	Status  LeadStatus  `json:"status" binding:"required"`
	Notes   string      `json:"notes"`
}

// LeadFollowUpRequest represents a request to schedule a follow-up for a lead
type LeadFollowUpRequest struct {
	FollowUpDate *time.Time `json:"follow_up_date" binding:"required"`
	FollowUpType string     `json:"follow_up_type" binding:"required"`
	Notes        string     `json:"notes"`
}
