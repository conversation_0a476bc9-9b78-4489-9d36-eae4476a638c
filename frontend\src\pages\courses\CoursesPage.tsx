import { useState, useEffect } from 'react'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  AcademicCapIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import {
  Course,
  CourseCreateRequest,
  CourseUpdateRequest,
  CourseFilters,
  CourseStats
} from '@/types/student'

// Mock data for development
const mockCourses: Course[] = [
  {
    id: '1',
    name: 'Advanced Mathematics',
    code: 'MATH101',
    description: 'Comprehensive mathematics course covering algebra, geometry, and calculus',
    duration_months: 12,
    price: 1200,
    max_students: 25,
    status: 'ACTIVE',
    start_date: '2024-01-15',
    end_date: '2024-12-15',
    teacher_id: 'teacher1',
    teacher_name: 'Dr. <PERSON>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'English Literature',
    code: 'ENG201',
    description: 'Study of classic and modern English literature',
    duration_months: 10,
    price: 1000,
    max_students: 20,
    status: 'ACTIVE',
    start_date: '2024-02-01',
    end_date: '2024-11-30',
    teacher_id: 'teacher2',
    teacher_name: 'Prof. Sarah Johnson',
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z'
  },
  {
    id: '3',
    name: 'Computer Science Fundamentals',
    code: 'CS101',
    description: 'Introduction to programming and computer science concepts',
    duration_months: 8,
    price: 1500,
    max_students: 30,
    status: 'ACTIVE',
    start_date: '2024-03-01',
    end_date: '2024-10-31',
    teacher_id: 'teacher3',
    teacher_name: 'Dr. Ahmed Hassan',
    created_at: '2024-02-15T00:00:00Z',
    updated_at: '2024-02-15T00:00:00Z'
  },
  {
    id: '4',
    name: 'Physics Laboratory',
    code: 'PHY301',
    description: 'Hands-on physics experiments and laboratory work',
    duration_months: 6,
    price: 800,
    max_students: 15,
    status: 'COMPLETED',
    start_date: '2023-09-01',
    end_date: '2024-02-29',
    teacher_id: 'teacher4',
    teacher_name: 'Dr. Maria Garcia',
    created_at: '2023-08-15T00:00:00Z',
    updated_at: '2024-02-29T00:00:00Z'
  }
]

const mockStats: CourseStats = {
  total_courses: 24,
  active_courses: 18,
  completed_courses: 6,
  total_enrolled_students: 342,
  average_enrollment_rate: 78.5,
  total_course_revenue: 156800
}

const CoursesPage = () => {
  const [courses, setCourses] = useState<Course[]>([])
  const [stats, setStats] = useState<CourseStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<CourseFilters>({
    search: '',
    status: 'ALL',
    teacher: 'ALL'
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null)

  // Load courses on component mount
  useEffect(() => {
    loadCourses()
    loadStats()
  }, [])

  const loadCourses = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setCourses(mockCourses)
    } catch (error) {
      console.error('Failed to load courses:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  // Filter courses based on search and filters
  const filteredCourses = courses.filter(course => {
    const matchesSearch =
      course.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      course.code.toLowerCase().includes(filters.search.toLowerCase()) ||
      course.description?.toLowerCase().includes(filters.search.toLowerCase()) ||
      course.teacher_name?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = filters.status === 'ALL' || course.status === filters.status
    const matchesTeacher = filters.teacher === 'ALL' || course.teacher_id === filters.teacher

    return matchesSearch && matchesStatus && matchesTeacher
  })

  const handleCreateCourse = async (courseData: CourseCreateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newCourse: Course = {
        id: Date.now().toString(),
        ...courseData,
        status: 'ACTIVE',
        teacher_name: courseData.teacher_id ? 'Assigned Teacher' : undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      setCourses(prev => [...prev, newCourse])
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create course:', error)
    }
  }

  const handleUpdateCourse = async (courseId: string, courseData: CourseUpdateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setCourses(prev => prev.map(course =>
        course.id === courseId
          ? {
              ...course,
              ...courseData,
              updated_at: new Date().toISOString()
            }
          : course
      ))
      setShowEditModal(false)
      setSelectedCourse(null)
    } catch (error) {
      console.error('Failed to update course:', error)
    }
  }

  const handleDeleteCourse = async (courseId: string) => {
    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setCourses(prev => prev.filter(course => course.id !== courseId))
    } catch (error) {
      console.error('Failed to delete course:', error)
    }
  }

  const getStatusBadgeColor = (status: Course['status']) => {
    const colors = {
      ACTIVE: 'bg-green-100 text-green-800',
      INACTIVE: 'bg-gray-100 text-gray-800',
      COMPLETED: 'bg-blue-100 text-blue-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Course Management</h1>
          <p className="text-gray-600 mt-1">
            Create and manage courses, schedules, and academic programs
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Course
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <AcademicCapIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Courses</p>
                <p className="text-xl font-semibold text-gray-900">{stats.total_courses}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <ClockIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Courses</p>
                <p className="text-xl font-semibold text-gray-900">{stats.active_courses}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <CalendarIcon className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-xl font-semibold text-gray-900">{stats.completed_courses}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <UserGroupIcon className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Enrolled Students</p>
                <p className="text-xl font-semibold text-gray-900">{stats.total_enrolled_students}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ChartBarIcon className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Enrollment Rate</p>
                <p className="text-xl font-semibold text-gray-900">{stats.average_enrollment_rate}%</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <CurrencyDollarIcon className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-xl font-semibold text-gray-900">{formatCurrency(stats.total_course_revenue)}</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search courses..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as Course['status'] | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="COMPLETED">Completed</option>
          </select>

          <select
            value={filters.teacher}
            onChange={(e) => setFilters(prev => ({ ...prev, teacher: e.target.value }))}
            className="input"
          >
            <option value="ALL">All Teachers</option>
            <option value="teacher1">Dr. John Smith</option>
            <option value="teacher2">Prof. Sarah Johnson</option>
            <option value="teacher3">Dr. Ahmed Hassan</option>
            <option value="teacher4">Dr. Maria Garcia</option>
          </select>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4" />
            {filteredCourses.length} of {courses.length} courses
          </div>
        </div>
      </div>

      {/* Courses Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-head">Course</th>
                <th className="table-head">Code</th>
                <th className="table-head">Teacher</th>
                <th className="table-head">Duration</th>
                <th className="table-head">Price</th>
                <th className="table-head">Status</th>
                <th className="table-head">Start Date</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredCourses.length === 0 ? (
                <tr>
                  <td colSpan={8} className="table-cell text-center py-8 text-gray-500">
                    No courses found matching your criteria
                  </td>
                </tr>
              ) : (
                filteredCourses.map((course) => (
                  <tr key={course.id} className="table-row">
                    <td className="table-cell">
                      <div>
                        <div className="font-medium text-gray-900">{course.name}</div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {course.description || 'No description'}
                        </div>
                        <div className="text-xs text-gray-400">
                          Max students: {course.max_students}
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="font-mono text-sm font-medium text-gray-900">
                        {course.code}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {course.teacher_name || 'Not assigned'}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {course.duration_months} months
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(course.price)}
                      </div>
                    </td>
                    <td className="table-cell">
                      <Badge className={getStatusBadgeColor(course.status)}>
                        {course.status}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {formatDate(course.start_date)}
                      </div>
                      {course.end_date && (
                        <div className="text-xs text-gray-500">
                          Ends: {formatDate(course.end_date)}
                        </div>
                      )}
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedCourse(course)
                            setShowViewModal(true)
                          }}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedCourse(course)
                            setShowEditModal(true)
                          }}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteCourse(course.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <CourseCreateModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateCourse}
        />
      )}

      {showEditModal && selectedCourse && (
        <CourseEditModal
          course={selectedCourse}
          onClose={() => {
            setShowEditModal(false)
            setSelectedCourse(null)
          }}
          onSubmit={(courseData) => handleUpdateCourse(selectedCourse.id, courseData)}
        />
      )}

      {showViewModal && selectedCourse && (
        <CourseViewModal
          course={selectedCourse}
          onClose={() => {
            setShowViewModal(false)
            setSelectedCourse(null)
          }}
        />
      )}
    </div>
  )
}

// Modal Components (placeholder implementations)
const CourseCreateModal = ({ onClose, onSubmit }: {
  onClose: () => void
  onSubmit: (data: CourseCreateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Create New Course</h2>
        <p className="text-gray-600 mb-4">Course creation form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Create Course</Button>
        </div>
      </div>
    </div>
  )
}

const CourseEditModal = ({ course, onClose, onSubmit }: {
  course: Course
  onClose: () => void
  onSubmit: (data: CourseUpdateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Edit Course: {course.name}</h2>
        <p className="text-gray-600 mb-4">Course editing form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Save Changes</Button>
        </div>
      </div>
    </div>
  )
}

const CourseViewModal = ({ course, onClose }: {
  course: Course
  onClose: () => void
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Course Details: {course.name}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Basic Information</h3>
            <div className="space-y-1">
              <p><strong>Course Name:</strong> {course.name}</p>
              <p><strong>Course Code:</strong> {course.code}</p>
              <p><strong>Status:</strong> {course.status}</p>
              <p><strong>Duration:</strong> {course.duration_months} months</p>
              <p><strong>Price:</strong> {formatCurrency(course.price)}</p>
              <p><strong>Max Students:</strong> {course.max_students}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Schedule & Teacher</h3>
            <div className="space-y-1">
              <p><strong>Teacher:</strong> {course.teacher_name || 'Not assigned'}</p>
              <p><strong>Start Date:</strong> {formatDate(course.start_date)}</p>
              {course.end_date && (
                <p><strong>End Date:</strong> {formatDate(course.end_date)}</p>
              )}
              <p><strong>Created:</strong> {formatDate(course.created_at)}</p>
              <p><strong>Last Updated:</strong> {formatDate(course.updated_at)}</p>
            </div>
          </div>
          {course.description && (
            <div className="md:col-span-2">
              <h3 className="font-medium text-gray-900 mb-2">Description</h3>
              <p className="text-gray-600">{course.description}</p>
            </div>
          )}
        </div>
        <div className="mt-6">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}

export default CoursesPage
