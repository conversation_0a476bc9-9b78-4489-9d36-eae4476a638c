# Build stage
FROM golang:1.23-alpine AS builder

# Set working directory
WORKDIR /app

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates wget

# Copy go mod files first
COPY services/admin-service/go.mod services/admin-service/go.sum ./

# Create shared directory and copy shared files
# Note: shared files should be copied from the build context
RUN mkdir -p shared
COPY shared/ ./shared/

# Copy source code
COPY services/admin-service/ .

# Temporarily modify go.mod to use local shared path for Docker build
RUN sed -i 's|=> ../../shared|=> ./shared|g' go.mod

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Create app directory
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main .

# Expose port
EXPOSE 8082

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8082/health || exit 1

# Run the application
CMD ["./main"]
