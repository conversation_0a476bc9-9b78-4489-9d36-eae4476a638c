package service

import (
	"fmt"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/repository"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
)

// CourseService handles course business logic
type CourseService interface {
	GetAll(req *models.CourseListRequest) (*models.CourseListResponse, error)
	GetByID(id uuid.UUID) (*models.CourseResponse, error)
	GetByCourseCode(courseCode string) (*models.CourseResponse, error)
	Create(req *models.CourseCreateRequest, createdBy uuid.UUID) (*models.CourseResponse, error)
	Update(id uuid.UUID, req *models.CourseUpdateRequest) (*models.CourseResponse, error)
	Delete(id uuid.UUID) error
	GetStats() (*models.CourseStats, error)
	GetByInstructor(instructorID uuid.UUID) ([]*models.CourseResponse, error)
	GetAvailableCourses() ([]*models.CourseResponse, error)
	GetPopularCourses(limit int) ([]*models.CourseResponse, error)
	GetUpcomingCourses() ([]*models.CourseResponse, error)
	AssignInstructor(courseID, instructorID uuid.UUID) (*models.CourseResponse, error)
	UpdateStatus(id uuid.UUID, status models.CourseStatus) (*models.CourseResponse, error)
	ValidateCourseCode(courseCode string, excludeID *uuid.UUID) error
}

type courseService struct {
	courseRepo     repository.CourseRepository
	teacherRepo    repository.TeacherRepository
	enrollmentRepo repository.EnrollmentRepository
}

// NewCourseService creates a new course service
func NewCourseService(
	courseRepo repository.CourseRepository,
	teacherRepo repository.TeacherRepository,
	enrollmentRepo repository.EnrollmentRepository,
) CourseService {
	return &courseService{
		courseRepo:     courseRepo,
		teacherRepo:    teacherRepo,
		enrollmentRepo: enrollmentRepo,
	}
}

// GetAll retrieves all courses with pagination and filtering
func (s *courseService) GetAll(req *models.CourseListRequest) (*models.CourseListResponse, error) {
	courses, total, err := s.courseRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get courses: %w", err)
	}

	courseResponses := make([]*models.CourseResponse, len(courses))
	for i, course := range courses {
		courseResponses[i] = course.ToResponse()
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &models.CourseListResponse{
		Courses:    courseResponses,
		Pagination: pagination,
	}, nil
}

// GetByID retrieves a course by ID
func (s *courseService) GetByID(id uuid.UUID) (*models.CourseResponse, error) {
	course, err := s.courseRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	return course.ToResponse(), nil
}

// GetByCourseCode retrieves a course by course code
func (s *courseService) GetByCourseCode(courseCode string) (*models.CourseResponse, error) {
	course, err := s.courseRepo.GetByCourseCode(courseCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	return course.ToResponse(), nil
}

// Create creates a new course
func (s *courseService) Create(req *models.CourseCreateRequest, createdBy uuid.UUID) (*models.CourseResponse, error) {
	// Validate course level
	if !req.Level.IsValid() {
		return nil, fmt.Errorf("invalid course level: %s", req.Level)
	}

	// Validate course code uniqueness
	if err := s.courseRepo.ValidateCourseCode(req.CourseCode, nil); err != nil {
		return nil, fmt.Errorf("course code validation failed: %w", err)
	}

	// Validate instructor if provided
	if req.InstructorID != nil {
		if _, err := s.teacherRepo.GetByID(*req.InstructorID); err != nil {
			return nil, fmt.Errorf("invalid instructor: %w", err)
		}
	}

	course := &models.Course{
		Name:             req.Name,
		CourseCode:       req.CourseCode,
		Description:      req.Description,
		Level:            req.Level,
		Status:           models.CourseStatusDraft, // Default status
		Duration:         req.Duration,
		MaxStudents:      req.MaxStudents,
		Price:            req.Price,
		Currency:         req.Currency,
		StartDate:        req.StartDate,
		EndDate:          req.EndDate,
		InstructorID:     req.InstructorID,
		Prerequisites:    req.Prerequisites,
		Requirements:     req.Requirements,
		LearningOutcomes: req.LearningOutcomes,
		CreatedByUserID:  createdBy,
	}

	if err := s.courseRepo.Create(course); err != nil {
		return nil, fmt.Errorf("failed to create course: %w", err)
	}

	// Reload with associations
	createdCourse, err := s.courseRepo.GetByID(course.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created course: %w", err)
	}

	return createdCourse.ToResponse(), nil
}

// Update updates a course
func (s *courseService) Update(id uuid.UUID, req *models.CourseUpdateRequest) (*models.CourseResponse, error) {
	// Check if course exists
	_, err := s.courseRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	// Validate status if provided
	if req.Status != nil && !req.Status.IsValid() {
		return nil, fmt.Errorf("invalid course status: %s", *req.Status)
	}

	// Validate level if provided
	if req.Level != nil && !req.Level.IsValid() {
		return nil, fmt.Errorf("invalid course level: %s", *req.Level)
	}



	// Validate instructor if provided
	if req.InstructorID != nil {
		if _, err := s.teacherRepo.GetByID(*req.InstructorID); err != nil {
			return nil, fmt.Errorf("invalid instructor: %w", err)
		}
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Level != nil {
		updates["level"] = *req.Level
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Duration != nil {
		updates["duration"] = *req.Duration
	}
	if req.MaxStudents != nil {
		updates["max_students"] = *req.MaxStudents
	}
	if req.Price != nil {
		updates["price"] = *req.Price
	}
	if req.StartDate != nil {
		updates["start_date"] = *req.StartDate
	}
	if req.EndDate != nil {
		updates["end_date"] = *req.EndDate
	}
	if req.InstructorID != nil {
		updates["instructor_id"] = *req.InstructorID
	}
	if req.Prerequisites != nil {
		updates["prerequisites"] = *req.Prerequisites
	}


	updates["updated_at"] = time.Now()

	if err := s.courseRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update course: %w", err)
	}

	// Get updated course
	updatedCourse, err := s.courseRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated course: %w", err)
	}

	return updatedCourse.ToResponse(), nil
}

// Delete deletes a course
func (s *courseService) Delete(id uuid.UUID) error {
	// Check if course exists
	if _, err := s.courseRepo.GetByID(id); err != nil {
		return fmt.Errorf("failed to get course: %w", err)
	}

	// Check if course has active enrollments
	enrollments, err := s.enrollmentRepo.GetByCourse(id)
	if err != nil {
		return fmt.Errorf("failed to check course enrollments: %w", err)
	}

	for _, enrollment := range enrollments {
		if enrollment.Status == models.EnrollmentStatusActive {
			return fmt.Errorf("cannot delete course with active enrollments")
		}
	}

	if err := s.courseRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete course: %w", err)
	}

	return nil
}

// GetStats retrieves course statistics
func (s *courseService) GetStats() (*models.CourseStats, error) {
	stats, err := s.courseRepo.GetStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get course stats: %w", err)
	}

	return stats, nil
}

// GetByInstructor retrieves courses assigned to a specific instructor
func (s *courseService) GetByInstructor(instructorID uuid.UUID) ([]*models.CourseResponse, error) {
	courses, err := s.courseRepo.GetByInstructor(instructorID)
	if err != nil {
		return nil, fmt.Errorf("failed to get courses by instructor: %w", err)
	}

	responses := make([]*models.CourseResponse, len(courses))
	for i, course := range courses {
		responses[i] = course.ToResponse()
	}

	return responses, nil
}

// GetAvailableCourses retrieves courses available for enrollment
func (s *courseService) GetAvailableCourses() ([]*models.CourseResponse, error) {
	courses, err := s.courseRepo.GetAvailableCourses()
	if err != nil {
		return nil, fmt.Errorf("failed to get available courses: %w", err)
	}

	responses := make([]*models.CourseResponse, len(courses))
	for i, course := range courses {
		responses[i] = course.ToResponse()
	}

	return responses, nil
}

// GetPopularCourses retrieves most popular courses by enrollment count
func (s *courseService) GetPopularCourses(limit int) ([]*models.CourseResponse, error) {
	courses, err := s.courseRepo.GetPopularCourses(limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular courses: %w", err)
	}

	responses := make([]*models.CourseResponse, len(courses))
	for i, course := range courses {
		responses[i] = course.ToResponse()
	}

	return responses, nil
}

// GetUpcomingCourses retrieves courses starting soon
func (s *courseService) GetUpcomingCourses() ([]*models.CourseResponse, error) {
	courses, err := s.courseRepo.GetUpcomingCourses()
	if err != nil {
		return nil, fmt.Errorf("failed to get upcoming courses: %w", err)
	}

	responses := make([]*models.CourseResponse, len(courses))
	for i, course := range courses {
		responses[i] = course.ToResponse()
	}

	return responses, nil
}

// AssignInstructor assigns an instructor to a course
func (s *courseService) AssignInstructor(courseID, instructorID uuid.UUID) (*models.CourseResponse, error) {
	// Check if course exists
	if _, err := s.courseRepo.GetByID(courseID); err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	// Check if instructor exists and is available
	instructor, err := s.teacherRepo.GetByID(instructorID)
	if err != nil {
		return nil, fmt.Errorf("failed to get instructor: %w", err)
	}

	if instructor.Status != models.TeacherStatusActive {
		return nil, fmt.Errorf("instructor is not active")
	}

	updates := map[string]interface{}{
		"instructor_id": instructorID,
		"updated_at":    time.Now(),
	}

	if err := s.courseRepo.Update(courseID, updates); err != nil {
		return nil, fmt.Errorf("failed to assign instructor: %w", err)
	}

	// Get updated course
	updatedCourse, err := s.courseRepo.GetByID(courseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated course: %w", err)
	}

	return updatedCourse.ToResponse(), nil
}

// UpdateStatus updates the status of a course
func (s *courseService) UpdateStatus(id uuid.UUID, status models.CourseStatus) (*models.CourseResponse, error) {
	// Validate status
	if !status.IsValid() {
		return nil, fmt.Errorf("invalid course status: %s", status)
	}

	// Check if course exists
	if _, err := s.courseRepo.GetByID(id); err != nil {
		return nil, fmt.Errorf("failed to get course: %w", err)
	}

	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if err := s.courseRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update course status: %w", err)
	}

	// Get updated course
	updatedCourse, err := s.courseRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated course: %w", err)
	}

	return updatedCourse.ToResponse(), nil
}

// ValidateCourseCode checks if a course code is unique
func (s *courseService) ValidateCourseCode(courseCode string, excludeID *uuid.UUID) error {
	return s.courseRepo.ValidateCourseCode(courseCode, excludeID)
}
