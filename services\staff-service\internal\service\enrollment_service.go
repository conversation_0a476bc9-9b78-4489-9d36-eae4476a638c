package service

import (
	"fmt"
	"time"

	"staff-service/internal/models"
	"staff-service/internal/repository"
	"github.com/google/uuid"
	sharedModels "github.com/crm-microservices/shared/models"
)

// EnrollmentService handles enrollment business logic
type EnrollmentService interface {
	GetAll(req *models.EnrollmentListRequest) (*models.EnrollmentListResponse, error)
	GetByID(id uuid.UUID) (*models.EnrollmentResponse, error)
	Create(req *models.EnrollmentCreateRequest, enrolledBy uuid.UUID) (*models.EnrollmentResponse, error)
	Update(id uuid.UUID, req *models.EnrollmentUpdateRequest) (*models.EnrollmentResponse, error)
	Delete(id uuid.UUID) error
	GetStats() (*models.EnrollmentStats, error)
	GetByStudent(studentID uuid.UUID) ([]*models.EnrollmentResponse, error)
	GetByCourse(courseID uuid.UUID) ([]*models.EnrollmentResponse, error)
	GetActiveEnrollments() ([]*models.EnrollmentResponse, error)
	GetOverduePayments() ([]*models.EnrollmentResponse, error)
	UpdatePaymentStatus(id uuid.UUID, status string, paidAmount float64) (*models.EnrollmentResponse, error)
	BulkUpdatePaymentStatus(enrollmentIDs []uuid.UUID, status string, updatedBy uuid.UUID) error
	CompleteEnrollment(id uuid.UUID, finalGrade *float64, completedBy uuid.UUID) (*models.EnrollmentResponse, error)
	DropEnrollment(id uuid.UUID, reason string, droppedBy uuid.UUID) (*models.EnrollmentResponse, error)
	GetRevenueByPeriod(startDate, endDate time.Time) (float64, error)
}

type enrollmentService struct {
	enrollmentRepo repository.EnrollmentRepository
	studentRepo    repository.StudentRepository
	courseRepo     repository.CourseRepository
}

// NewEnrollmentService creates a new enrollment service
func NewEnrollmentService(
	enrollmentRepo repository.EnrollmentRepository,
	studentRepo repository.StudentRepository,
	courseRepo repository.CourseRepository,
) EnrollmentService {
	return &enrollmentService{
		enrollmentRepo: enrollmentRepo,
		studentRepo:    studentRepo,
		courseRepo:     courseRepo,
	}
}

// GetAll retrieves all enrollments with pagination and filtering
func (s *enrollmentService) GetAll(req *models.EnrollmentListRequest) (*models.EnrollmentListResponse, error) {
	enrollments, total, err := s.enrollmentRepo.GetAll(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollments: %w", err)
	}

	enrollmentResponses := make([]*models.EnrollmentResponse, len(enrollments))
	for i, enrollment := range enrollments {
		enrollmentResponses[i] = enrollment.ToResponse()
	}

	pagination := sharedModels.NewPaginationResponse(req.Page, req.PageSize, total)

	return &models.EnrollmentListResponse{
		Enrollments: enrollmentResponses,
		Pagination:  pagination,
	}, nil
}

// GetByID retrieves an enrollment by ID
func (s *enrollmentService) GetByID(id uuid.UUID) (*models.EnrollmentResponse, error) {
	enrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}

	return enrollment.ToResponse(), nil
}

// Create creates a new enrollment
func (s *enrollmentService) Create(req *models.EnrollmentCreateRequest, enrolledBy uuid.UUID) (*models.EnrollmentResponse, error) {

	// Validate student exists
	_, err := s.studentRepo.GetByID(req.StudentID)
	if err != nil {
		return nil, fmt.Errorf("invalid student: %w", err)
	}

	// Validate course exists
	_, err = s.courseRepo.GetByID(req.CourseID)
	if err != nil {
		return nil, fmt.Errorf("invalid course: %w", err)
	}

	// Validate enrollment (check duplicates and capacity)
	if err := s.enrollmentRepo.ValidateEnrollment(req.StudentID, req.CourseID); err != nil {
		return nil, fmt.Errorf("enrollment validation failed: %w", err)
	}

	enrollment := &models.Enrollment{
		StudentID:       req.StudentID,
		CourseID:        req.CourseID,
		Status:          models.EnrollmentStatusPending, // Default status
		EnrollmentDate:  time.Now(),                     // Current time
		TotalFee:        req.TotalFee,
		PaidAmount:      0,                              // Default to 0
		Currency:        req.Currency,
		PaymentStatus:   "PENDING",                      // Default payment status
		Notes:           req.Notes,
		EnrolledByUserID: enrolledBy,
	}

	if err := s.enrollmentRepo.Create(enrollment); err != nil {
		return nil, fmt.Errorf("failed to create enrollment: %w", err)
	}

	// Update course enrollment count
	if err := s.courseRepo.UpdateEnrollmentCount(req.CourseID); err != nil {
		// Log error but don't fail the enrollment
		fmt.Printf("Warning: failed to update course enrollment count: %v\n", err)
	}

	// Reload with associations
	createdEnrollment, err := s.enrollmentRepo.GetByID(enrollment.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created enrollment: %w", err)
	}

	return createdEnrollment.ToResponse(), nil
}

// Update updates an enrollment
func (s *enrollmentService) Update(id uuid.UUID, req *models.EnrollmentUpdateRequest) (*models.EnrollmentResponse, error) {
	// Check if enrollment exists
	existingEnrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}

	// Validate status if provided
	if req.Status != nil && !req.Status.IsValid() {
		return nil, fmt.Errorf("invalid enrollment status: %s", *req.Status)
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.Status != nil {
		updates["status"] = *req.Status
		// Update completion date if status changes to completed
		if *req.Status == models.EnrollmentStatusCompleted {
			updates["completion_date"] = time.Now()
		}
	}
	if req.PaidAmount != nil {
		updates["paid_amount"] = *req.PaidAmount
		// Update payment status based on amount paid
		if *req.PaidAmount >= existingEnrollment.TotalFee {
			updates["payment_status"] = "PAID"
		} else if *req.PaidAmount > 0 {
			updates["payment_status"] = "PARTIAL"
		}
	}
	if req.PaymentStatus != nil {
		updates["payment_status"] = *req.PaymentStatus
	}
	if req.FinalGrade != nil {
		updates["final_grade"] = *req.FinalGrade
	}
	if req.AttendanceRate != nil {
		updates["attendance_rate"] = *req.AttendanceRate
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}

	updates["updated_at"] = time.Now()

	if err := s.enrollmentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update enrollment: %w", err)
	}

	// Get updated enrollment
	updatedEnrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated enrollment: %w", err)
	}

	return updatedEnrollment.ToResponse(), nil
}

// Delete deletes an enrollment
func (s *enrollmentService) Delete(id uuid.UUID) error {
	// Check if enrollment exists
	enrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("failed to get enrollment: %w", err)
	}

	// Only allow deletion of pending enrollments
	if enrollment.Status != models.EnrollmentStatusPending {
		return fmt.Errorf("can only delete pending enrollments")
	}

	if err := s.enrollmentRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete enrollment: %w", err)
	}

	// Update course enrollment count
	if err := s.courseRepo.UpdateEnrollmentCount(enrollment.CourseID); err != nil {
		// Log error but don't fail the deletion
		fmt.Printf("Warning: failed to update course enrollment count: %v\n", err)
	}

	return nil
}

// GetStats retrieves enrollment statistics
func (s *enrollmentService) GetStats() (*models.EnrollmentStats, error) {
	stats, err := s.enrollmentRepo.GetStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment stats: %w", err)
	}

	return stats, nil
}

// GetByStudent retrieves enrollments for a specific student
func (s *enrollmentService) GetByStudent(studentID uuid.UUID) ([]*models.EnrollmentResponse, error) {
	enrollments, err := s.enrollmentRepo.GetByStudent(studentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollments by student: %w", err)
	}

	responses := make([]*models.EnrollmentResponse, len(enrollments))
	for i, enrollment := range enrollments {
		responses[i] = enrollment.ToResponse()
	}

	return responses, nil
}

// GetByCourse retrieves enrollments for a specific course
func (s *enrollmentService) GetByCourse(courseID uuid.UUID) ([]*models.EnrollmentResponse, error) {
	enrollments, err := s.enrollmentRepo.GetByCourse(courseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollments by course: %w", err)
	}

	responses := make([]*models.EnrollmentResponse, len(enrollments))
	for i, enrollment := range enrollments {
		responses[i] = enrollment.ToResponse()
	}

	return responses, nil
}

// GetActiveEnrollments retrieves all active enrollments
func (s *enrollmentService) GetActiveEnrollments() ([]*models.EnrollmentResponse, error) {
	enrollments, err := s.enrollmentRepo.GetActiveEnrollments()
	if err != nil {
		return nil, fmt.Errorf("failed to get active enrollments: %w", err)
	}

	responses := make([]*models.EnrollmentResponse, len(enrollments))
	for i, enrollment := range enrollments {
		responses[i] = enrollment.ToResponse()
	}

	return responses, nil
}

// GetOverduePayments retrieves enrollments with overdue payments
func (s *enrollmentService) GetOverduePayments() ([]*models.EnrollmentResponse, error) {
	enrollments, err := s.enrollmentRepo.GetOverduePayments()
	if err != nil {
		return nil, fmt.Errorf("failed to get overdue payments: %w", err)
	}

	responses := make([]*models.EnrollmentResponse, len(enrollments))
	for i, enrollment := range enrollments {
		responses[i] = enrollment.ToResponse()
	}

	return responses, nil
}

// UpdatePaymentStatus updates payment status and amount for an enrollment
func (s *enrollmentService) UpdatePaymentStatus(id uuid.UUID, status string, paidAmount float64) (*models.EnrollmentResponse, error) {
	// Check if enrollment exists
	enrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}

	// Validate payment amount
	if paidAmount < 0 {
		return nil, fmt.Errorf("paid amount cannot be negative")
	}

	if paidAmount > enrollment.TotalFee {
		return nil, fmt.Errorf("paid amount cannot exceed total fee")
	}

	updates := map[string]interface{}{
		"payment_status": status,
		"paid_amount":    paidAmount,
		"updated_at":     time.Now(),
	}

	// Update payment date if fully paid
	if paidAmount >= enrollment.TotalFee {
		updates["payment_date"] = time.Now()
	}

	if err := s.enrollmentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update payment status: %w", err)
	}

	// Get updated enrollment
	updatedEnrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated enrollment: %w", err)
	}

	return updatedEnrollment.ToResponse(), nil
}

// BulkUpdatePaymentStatus updates payment status for multiple enrollments
func (s *enrollmentService) BulkUpdatePaymentStatus(enrollmentIDs []uuid.UUID, status string, updatedBy uuid.UUID) error {
	if err := s.enrollmentRepo.BulkUpdatePaymentStatus(enrollmentIDs, status, updatedBy); err != nil {
		return fmt.Errorf("failed to bulk update payment status: %w", err)
	}

	return nil
}

// CompleteEnrollment marks an enrollment as completed
func (s *enrollmentService) CompleteEnrollment(id uuid.UUID, finalGrade *float64, completedBy uuid.UUID) (*models.EnrollmentResponse, error) {
	// Check if enrollment exists
	enrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}

	if enrollment.Status == models.EnrollmentStatusCompleted {
		return nil, fmt.Errorf("enrollment is already completed")
	}

	if enrollment.Status != models.EnrollmentStatusActive {
		return nil, fmt.Errorf("only active enrollments can be completed")
	}

	updates := map[string]interface{}{
		"status":          models.EnrollmentStatusCompleted,
		"completion_date": time.Now(),
		"updated_at":      time.Now(),
	}

	if finalGrade != nil {
		if *finalGrade < 0 || *finalGrade > 100 {
			return nil, fmt.Errorf("final grade must be between 0 and 100")
		}
		updates["final_grade"] = *finalGrade
	}

	if err := s.enrollmentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to complete enrollment: %w", err)
	}

	// Get updated enrollment
	updatedEnrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated enrollment: %w", err)
	}

	return updatedEnrollment.ToResponse(), nil
}

// DropEnrollment marks an enrollment as dropped
func (s *enrollmentService) DropEnrollment(id uuid.UUID, reason string, droppedBy uuid.UUID) (*models.EnrollmentResponse, error) {
	// Check if enrollment exists
	enrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment: %w", err)
	}

	if enrollment.Status == models.EnrollmentStatusDropped {
		return nil, fmt.Errorf("enrollment is already dropped")
	}

	if enrollment.Status == models.EnrollmentStatusCompleted {
		return nil, fmt.Errorf("cannot drop completed enrollment")
	}

	updates := map[string]interface{}{
		"status":     models.EnrollmentStatusDropped,
		"updated_at": time.Now(),
	}

	if reason != "" {
		existingNotes := enrollment.Notes
		dropNote := fmt.Sprintf("\n\nDropped on %s: %s", time.Now().Format("2006-01-02"), reason)
		if existingNotes != "" {
			updates["notes"] = existingNotes + dropNote
		} else {
			updates["notes"] = dropNote
		}
	}

	if err := s.enrollmentRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to drop enrollment: %w", err)
	}

	// Update course enrollment count
	if err := s.courseRepo.UpdateEnrollmentCount(enrollment.CourseID); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Warning: failed to update course enrollment count: %v\n", err)
	}

	// Get updated enrollment
	updatedEnrollment, err := s.enrollmentRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated enrollment: %w", err)
	}

	return updatedEnrollment.ToResponse(), nil
}

// GetRevenueByPeriod calculates revenue for a specific period
func (s *enrollmentService) GetRevenueByPeriod(startDate, endDate time.Time) (float64, error) {
	revenue, err := s.enrollmentRepo.GetRevenueByPeriod(startDate, endDate)
	if err != nil {
		return 0, fmt.Errorf("failed to get revenue by period: %w", err)
	}

	return revenue, nil
}
