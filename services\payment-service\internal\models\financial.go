package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/crm-microservices/shared/models"
)

// FinancialReport represents a financial report
type FinancialReport struct {
	models.BaseModel
	
	// Report details
	Title           string                 `json:"title" gorm:"not null"`
	Type            ReportType             `json:"type" gorm:"not null"`
	Period          ReportPeriod           `json:"period" gorm:"not null"`
	
	// Date range
	StartDate       time.Time              `json:"start_date" gorm:"not null"`
	EndDate         time.Time              `json:"end_date" gorm:"not null"`
	
	// Report data
	TotalRevenue    float64                `json:"total_revenue" gorm:"type:decimal(12,2);default:0"`
	TotalRefunds    float64                `json:"total_refunds" gorm:"type:decimal(12,2);default:0"`
	NetRevenue      float64                `json:"net_revenue" gorm:"type:decimal(12,2);default:0"`
	TotalFees       float64                `json:"total_fees" gorm:"type:decimal(12,2);default:0"`
	
	// Transaction counts
	TotalPayments   int                    `json:"total_payments" gorm:"default:0"`
	SuccessfulPayments int                 `json:"successful_payments" gorm:"default:0"`
	FailedPayments  int                    `json:"failed_payments" gorm:"default:0"`
	RefundCount     int                    `json:"refund_count" gorm:"default:0"`
	
	// Status
	Status          ReportStatus           `json:"status" gorm:"not null;default:'PENDING'"`
	GeneratedAt     *time.Time             `json:"generated_at"`
	GeneratedByID   *uuid.UUID             `json:"generated_by_id" gorm:"type:uuid;index"`
	
	// File information
	FilePath        string                 `json:"file_path"`
	FileSize        int64                  `json:"file_size" gorm:"default:0"`
	
	// Metadata
	Metadata        string                 `json:"metadata" gorm:"type:jsonb"`
}

// TableName returns the table name for FinancialReport
func (FinancialReport) TableName() string {
	return "financial_reports"
}

// ReportType represents the type of financial report
type ReportType string

const (
	ReportTypeRevenue     ReportType = "REVENUE"
	ReportTypePayments    ReportType = "PAYMENTS"
	ReportTypeRefunds     ReportType = "REFUNDS"
	ReportTypeGateway     ReportType = "GATEWAY"
	ReportTypeReconciliation ReportType = "RECONCILIATION"
)

// IsValid checks if the report type is valid
func (r ReportType) IsValid() bool {
	switch r {
	case ReportTypeRevenue, ReportTypePayments, ReportTypeRefunds, ReportTypeGateway, ReportTypeReconciliation:
		return true
	}
	return false
}

// ReportPeriod represents the period of the report
type ReportPeriod string

const (
	ReportPeriodDaily     ReportPeriod = "DAILY"
	ReportPeriodWeekly    ReportPeriod = "WEEKLY"
	ReportPeriodMonthly   ReportPeriod = "MONTHLY"
	ReportPeriodQuarterly ReportPeriod = "QUARTERLY"
	ReportPeriodYearly    ReportPeriod = "YEARLY"
	ReportPeriodCustom    ReportPeriod = "CUSTOM"
)

// IsValid checks if the report period is valid
func (r ReportPeriod) IsValid() bool {
	switch r {
	case ReportPeriodDaily, ReportPeriodWeekly, ReportPeriodMonthly, ReportPeriodQuarterly, ReportPeriodYearly, ReportPeriodCustom:
		return true
	}
	return false
}

// ReportStatus represents the status of a report
type ReportStatus string

const (
	ReportStatusPending    ReportStatus = "PENDING"
	ReportStatusGenerating ReportStatus = "GENERATING"
	ReportStatusCompleted  ReportStatus = "COMPLETED"
	ReportStatusFailed     ReportStatus = "FAILED"
)

// IsValid checks if the report status is valid
func (r ReportStatus) IsValid() bool {
	switch r {
	case ReportStatusPending, ReportStatusGenerating, ReportStatusCompleted, ReportStatusFailed:
		return true
	}
	return false
}

// PaymentAnalytics represents payment analytics data
type PaymentAnalytics struct {
	// Time period
	Date            time.Time              `json:"date"`
	
	// Revenue metrics
	TotalRevenue    float64                `json:"total_revenue"`
	NetRevenue      float64                `json:"net_revenue"`
	AveragePayment  float64                `json:"average_payment"`
	
	// Volume metrics
	PaymentCount    int                    `json:"payment_count"`
	SuccessRate     float64                `json:"success_rate"`
	RefundRate      float64                `json:"refund_rate"`
	
	// Gateway breakdown
	GatewayBreakdown map[string]GatewayMetrics `json:"gateway_breakdown"`
	
	// Currency breakdown
	CurrencyBreakdown map[string]float64   `json:"currency_breakdown"`
}

// GatewayMetrics represents metrics for a specific payment gateway
type GatewayMetrics struct {
	GatewayType     string                 `json:"gateway_type"`
	Revenue         float64                `json:"revenue"`
	PaymentCount    int                    `json:"payment_count"`
	SuccessRate     float64                `json:"success_rate"`
	AverageAmount   float64                `json:"average_amount"`
	TotalFees       float64                `json:"total_fees"`
}

// RevenueMetrics represents revenue metrics for a specific period
type RevenueMetrics struct {
	Period          string                 `json:"period"`
	TotalRevenue    float64                `json:"total_revenue"`
	NetRevenue      float64                `json:"net_revenue"`
	GrowthRate      float64                `json:"growth_rate"`
	PaymentCount    int                    `json:"payment_count"`
	AveragePayment  float64                `json:"average_payment"`
}

// PaymentTrend represents payment trend data
type PaymentTrend struct {
	Date            time.Time              `json:"date"`
	Amount          float64                `json:"amount"`
	Count           int                    `json:"count"`
	SuccessRate     float64                `json:"success_rate"`
}

// ReconciliationRecord represents a reconciliation record
type ReconciliationRecord struct {
	models.BaseModel
	
	// Reconciliation details
	GatewayType     string                 `json:"gateway_type" gorm:"not null"`
	Date            time.Time              `json:"date" gorm:"not null;index"`
	
	// System records
	SystemRevenue   float64                `json:"system_revenue" gorm:"type:decimal(12,2);default:0"`
	SystemCount     int                    `json:"system_count" gorm:"default:0"`
	
	// Gateway records
	GatewayRevenue  float64                `json:"gateway_revenue" gorm:"type:decimal(12,2);default:0"`
	GatewayCount    int                    `json:"gateway_count" gorm:"default:0"`
	
	// Differences
	RevenueDiff     float64                `json:"revenue_diff" gorm:"type:decimal(12,2);default:0"`
	CountDiff       int                    `json:"count_diff" gorm:"default:0"`
	
	// Status
	Status          ReconciliationStatus   `json:"status" gorm:"not null;default:'PENDING'"`
	ReconciledAt    *time.Time             `json:"reconciled_at"`
	ReconciledByID  *uuid.UUID             `json:"reconciled_by_id" gorm:"type:uuid;index"`
	
	// Notes
	Notes           string                 `json:"notes" gorm:"type:text"`
	
	// Metadata
	Metadata        string                 `json:"metadata" gorm:"type:jsonb"`
}

// TableName returns the table name for ReconciliationRecord
func (ReconciliationRecord) TableName() string {
	return "reconciliation_records"
}

// ReconciliationStatus represents reconciliation status
type ReconciliationStatus string

const (
	ReconciliationPending   ReconciliationStatus = "PENDING"
	ReconciliationMatched   ReconciliationStatus = "MATCHED"
	ReconciliationMismatch  ReconciliationStatus = "MISMATCH"
	ReconciliationResolved  ReconciliationStatus = "RESOLVED"
)

// IsValid checks if the reconciliation status is valid
func (r ReconciliationStatus) IsValid() bool {
	switch r {
	case ReconciliationPending, ReconciliationMatched, ReconciliationMismatch, ReconciliationResolved:
		return true
	}
	return false
}
